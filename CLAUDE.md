# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Development
- `npm run dev` - Start development server with .env.dev.local
- `npm run prd` - Start production server with .env.prd.local
- `npm run build` - Build for production
- `npm run start` - Start production server

### Testing
- `npm run test` - Run all tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:ci` - Run tests in CI mode (silent)
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:detect-leaks` - Run tests with memory leak detection

### Linting and Formatting
- `npm run lint` - Run ESLint
- `npm run lint:ci` - Run ESLint in CI mode (quiet)
- `npm run type` - Run TypeScript type checking
- `npm run format` - Format code with Prettier

### Database Operations
- `npm run db:init` - Initialize database with scripts/init-database.ts
- `npm run db:migrate` - Run Knex migrations
- `npm run db:sync` - Run migrations and migrate Firestore to MySQL
- `npm run db:rollback` - Rollback last migration
- `npm run db:reset` - Reset database (rollback all + migrate)

## Architecture Overview

This is a Next.js TypeScript application serving as a Doctor/Staff Portal for managing patient calls and appointments. The application features a dual-database architecture migrating from Firestore to MySQL.

### Key Architectural Patterns

**Dual Database System**
- `lib/database/dual-database-service.ts` - Base service supporting both Firestore and MySQL
- `lib/database/base-repository.ts` - Abstract repository with CRUD operations for both databases
- `lib/repositories/` - Domain-specific repositories extending BaseRepository
- Database preference controlled by environment variables

**External API Provider Pattern**
- `lib/external-api/v2/providers/` - Provider-based architecture for integrating with practice management systems
- `lib/external-api/v2/providers/factory.ts` - Factory pattern for provider instantiation
- `lib/external-api/v2/providers/nextech/` - Nextech Practice+ API integration
- `lib/external-api/v2/providers/registry.ts` - Provider registry for managing available providers

**Repository Pattern**
- `lib/repositories/` - Data access layer with repositories for each entity
- `lib/database/unit-of-work.ts` - Unit of work pattern for transaction management
- Repositories handle both Firestore and MySQL operations transparently

**Authentication & Authorization**
- Firebase Authentication for user management
- Role-based access control (Super Admin, Doctor, Staff)
- Custom middleware for API authentication (`utils/middleware/externalApiAuth.ts`)

### Key Directories Structure

- `pages/api/external-api/v2/` - External API endpoints following provider pattern
- `lib/external-api/v2/` - External API implementation with provider abstractions
- `lib/repositories/` - Data access repositories
- `lib/database/` - Database abstraction layer
- `lib/services/` - Business logic services
- `components/` - React components using Flowbite React
- `models/` - TypeScript interfaces and types
- `utils/` - Utility functions and helpers

### Database Schema

The application uses a dual-database approach:
- **Firestore** (legacy) - NoSQL document database
- **MySQL** (target) - Relational database with proper schema

Key entities: Call, Patient/Client, Appointment, Location, CallSession, User, Staff

### External API Integration

The v2 External API uses a provider-based architecture:
- Supports multiple practice management systems
- Currently implements Nextech Practice+ integration
- Extensible for additional providers
- Includes authentication, rate limiting, and error handling

### Testing Strategy

- Comprehensive test coverage with 80% threshold
- Unit tests for services and repositories
- Integration tests for external API providers
- API endpoint tests for all external API routes
- MSW (Mock Service Worker) for API mocking

### Development Workflow

1. **Pre-commit hooks** (Husky) run tests and linting
2. **Lint-staged** ensures code quality on commits
3. **TypeScript** strict mode enabled
4. **Environment-specific** configs (.env.dev.local, .env.prd.local)

### Key Technologies

- **Next.js 14** - React framework
- **TypeScript** - Type safety
- **Tailwind CSS + Flowbite React** - UI components
- **Firebase Auth + Firestore** - Authentication and legacy database
- **MySQL2 + Knex** - Target database with migrations
- **Jest** - Testing framework
- **Zod** - Runtime validation
- **Pino** - Logging

### Important Notes

- Always run `npm run lint` and `npm run type` before committing
- Use the dual-database repositories instead of direct Firestore access
- Follow the provider pattern for external API integrations
- External API endpoints require `x-api-key` authentication
- Database operations should use the repository pattern
- Test coverage must meet 80% threshold
- Use existing Flowbite React components for UI consistency