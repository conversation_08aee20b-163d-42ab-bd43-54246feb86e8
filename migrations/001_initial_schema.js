const fs = require('fs');
const path = require('path');

exports.up = async function (knex) {
  console.log('Starting MySQL table creation...');

  // Create tables in dependency order
  const tableCreationOrder = [
    // ------------------------------------------------------------------
    //  Core reference tables (Practices, Clinics, Locations, Users, etc.)
    // ------------------------------------------------------------------
    {
      name: 'practices',
      sql: `CREATE TABLE practices (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid CHAR(36) NOT NULL UNIQUE,
        clinic_id INT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_practices_uuid (uuid),
        INDEX idx_practices_name (name),
        INDEX idx_practices_clinic_id (clinic_id)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'clinics',
      sql: `CREATE TABLE clinics (
        id INT AUTO_INCREMENT PRIMARY KEY,
        uuid CHAR(36) NOT NULL UNIQUE,
        practice_id INT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_clinics_uuid (uuid),
        INDEX idx_clinics_practice_id (practice_id),
        INDEX idx_clinics_name (name)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'locations',
      sql: `CREATE TABLE locations (
        id CHAR(36) PRIMARY KEY,
        clinic_id INT NULL,
        practice_id CHAR(36) NULL,
        practice_name VARCHAR(255),
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(20),
        time_zone VARCHAR(100),
        office_hours JSON,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_locations_clinic_id (clinic_id),
        INDEX idx_locations_practice_id (practice_id),
        INDEX idx_locations_name (name),
        INDEX idx_locations_is_active (is_active)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'users',
      sql: `CREATE TABLE users (
        id CHAR(36) PRIMARY KEY,
        firebase_uid CHAR(36) UNIQUE,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(20),
        clinic_id INT,
        role VARCHAR(50) DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        preferences JSON,
        specialty VARCHAR(100),
        profile_picture VARCHAR(500),
        can_take_appointments BOOLEAN DEFAULT FALSE,
        location_ids JSON,
        current_location_id CHAR(36),
        practice_ids JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_users_firebase_uid (firebase_uid),
        INDEX idx_users_clinic_id (clinic_id),
        INDEX idx_users_role (role),
        INDEX idx_users_is_active (is_active)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'clients',
      sql: `CREATE TABLE clients (
        id CHAR(36) PRIMARY KEY,
        full_name VARCHAR(255) NOT NULL,
        phone_number VARCHAR(20),
        clinic_id INT,
        birthday DATE,
        insurance_company VARCHAR(255),
        insurance_group_number VARCHAR(100),
        subscriber_name VARCHAR(255),
        email VARCHAR(255),
        medical_history TEXT,
        recent_notes TEXT,
        list_of_calls JSON,
        tags JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_clients_full_name (full_name),
        INDEX idx_clients_phone_number (phone_number),
        INDEX idx_clients_clinic_id (clinic_id),
        INDEX idx_clients_birthday (birthday),
        INDEX idx_clients_name_birthday (full_name, birthday)
      ) ENGINE=InnoDB`,
    },

    // ------------------------------------------------------------------
    //  Patients table (individual patients records)
    // ------------------------------------------------------------------
    {
      name: 'patients',
      sql: `CREATE TABLE patients (
        id CHAR(36) PRIMARY KEY,
        first_name VARCHAR(255) NOT NULL,
        last_name VARCHAR(255) NOT NULL,
        date_of_birth DATE,
        gender VARCHAR(50),
        email VARCHAR(255),
        phone_number VARCHAR(20),
        address JSON,
        provider VARCHAR(50),
        provider_id VARCHAR(255),
        notes TEXT,
        insurances JSON,
        identifiers JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_patients_phone_number (phone_number),
        INDEX idx_patients_provider (provider),
        INDEX idx_patients_provider_id (provider_id),
        INDEX idx_patients_date_of_birth (date_of_birth)
      ) ENGINE=InnoDB`,
    },

    // ------------------------------------------------------------------
    //  Transactional & communication tables (Calls, Call Sessions, etc.)
    // ------------------------------------------------------------------
    {
      name: 'calls',
      sql: `CREATE TABLE calls (
        id CHAR(36) PRIMARY KEY,
        client_id CHAR(36),
        client_name VARCHAR(255),
        user_id CHAR(36),
        clinic_id INT,
        location_id CHAR(36),
        transfer_to_location_id CHAR(36),
        call_date DATETIME NOT NULL,
        reason TEXT,
        summary TEXT,
        voicemail_summary TEXT,
        transcription LONGTEXT,
        transcription_with_audio LONGTEXT,
        recording_url VARCHAR(500),
        notes TEXT,
        priority_score INT DEFAULT 0,
        is_urgent BOOLEAN DEFAULT FALSE,
        tags JSON,
        phone_number VARCHAR(20),
        session_id VARCHAR(255),
        has_voicemail BOOLEAN DEFAULT FALSE,
        is_outbound_call BOOLEAN DEFAULT FALSE,
        voicemail_url VARCHAR(500),
        duration VARCHAR(20),
        call_type INT,
        call_types JSON,
        agent_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_calls_client_id (client_id),
        INDEX idx_calls_user_id (user_id),
        INDEX idx_calls_clinic_id (clinic_id),
        INDEX idx_calls_location_id (location_id),
        INDEX idx_calls_transfer_to_location_id (transfer_to_location_id),
        INDEX idx_calls_call_date (call_date),
        INDEX idx_calls_phone_number (phone_number),
        INDEX idx_calls_session_id (session_id),
        INDEX idx_calls_priority_score (priority_score),
        INDEX idx_calls_is_urgent (is_urgent),
        INDEX idx_calls_has_voicemail (has_voicemail),
        INDEX idx_calls_is_outbound_call (is_outbound_call),
        INDEX idx_calls_agent_id (agent_id),
        INDEX idx_calls_clinic_date (clinic_id, call_date),
        INDEX idx_calls_location_date (location_id, call_date)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'call_sessions',
      sql: `CREATE TABLE call_sessions (
        id CHAR(36) PRIMARY KEY,
        session_id VARCHAR(255) NOT NULL UNIQUE,
        agent_id VARCHAR(255),
        has_voicemail BOOLEAN DEFAULT FALSE,
        is_redirected BOOLEAN DEFAULT FALSE,
        call_type INT,
        caller_phone VARCHAR(20),
        client_id CHAR(36),
        appointment_id CHAR(36),
        call_id CHAR(36),
        trigger_event VARCHAR(255),
        status VARCHAR(100),
        telephony_call_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_call_sessions_session_id (session_id),
        INDEX idx_call_sessions_agent_id (agent_id),
        INDEX idx_call_sessions_caller_phone (caller_phone),
        INDEX idx_call_sessions_client_id (client_id),
        INDEX idx_call_sessions_appointment_id (appointment_id),
        INDEX idx_call_sessions_call_id (call_id),
        INDEX idx_call_sessions_trigger_event (trigger_event),
        INDEX idx_call_sessions_status (status),
        INDEX idx_call_sessions_created_at (created_at)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'appointments',
      sql: `CREATE TABLE appointments (
        id CHAR(36) PRIMARY KEY,
        user_id CHAR(36),
        client_id CHAR(36),
        client_name VARCHAR(255),
        slot_id CHAR(36),
        call_id CHAR(36),
        appointment_date VARCHAR(50),
        appointment_time VARCHAR(50),
        status VARCHAR(50) DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_appointments_user_id (user_id),
        INDEX idx_appointments_client_id (client_id),
        INDEX idx_appointments_appointment_date (appointment_date),
        INDEX idx_appointments_status (status)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'calendar_slots',
      sql: `CREATE TABLE calendar_slots (
        id CHAR(36) PRIMARY KEY,
        user_id CHAR(36),
        location_id CHAR(36),
        slot_date VARCHAR(50),
        time_slots JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_calendar_slots_user_id (user_id),
        INDEX idx_calendar_slots_location_id (location_id),
        INDEX idx_calendar_slots_slot_date (slot_date)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'emails',
      sql: `CREATE TABLE emails (
        id CHAR(36) PRIMARY KEY,
        recipients JSON,
        from_address VARCHAR(255),
        template_name VARCHAR(100),
        template_data JSON,
        status VARCHAR(50) DEFAULT 'pending',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        processed_at DATETIME,
        
        INDEX idx_emails_status (status),
        INDEX idx_emails_template_name (template_name),
        INDEX idx_emails_created_at (created_at)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'otp',
      sql: `CREATE TABLE otp (
        otp_key VARCHAR(255) PRIMARY KEY,
        email VARCHAR(255) NOT NULL,
        code VARCHAR(10) NOT NULL,
        expire_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_otp_email (email),
        INDEX idx_otp_expire_at (expire_at)
      ) ENGINE=InnoDB`,
    },

    // ------------------------------------------------------------------
    //  On-call scheduling & staff management-----------------------------
    // ------------------------------------------------------------------
    {
      name: 'on_call_schedules',
      sql: `CREATE TABLE on_call_schedules (
        id CHAR(36) PRIMARY KEY,
        doctor_id CHAR(36),
        doctor_name VARCHAR(255),
        doctor_phone VARCHAR(20),
        location_id CHAR(36),
        clinic_id INT,
        schedule_date VARCHAR(20),
        start_time VARCHAR(10),
        end_time VARCHAR(10),
        is_active BOOLEAN DEFAULT TRUE,
        timezone VARCHAR(100),
        notes TEXT,
        created_by CHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_on_call_schedules_doctor_id (doctor_id),
        INDEX idx_on_call_schedules_location_id (location_id),
        INDEX idx_on_call_schedules_clinic_id (clinic_id),
        INDEX idx_on_call_schedules_schedule_date (schedule_date),
        INDEX idx_on_call_schedules_is_active (is_active)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'on_call_notifications',
      sql: `CREATE TABLE on_call_notifications (
        id CHAR(36) PRIMARY KEY,
        schedule_id CHAR(36),
        call_session_id VARCHAR(255),
        doctor_id CHAR(36),
        clinic_id INT,
        notification_time DATETIME,
        sms_message_id VARCHAR(255),
        status VARCHAR(50),
        call_type VARCHAR(50),
        caller_phone VARCHAR(20),
        error_message TEXT,
        retry_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_on_call_notifications_schedule_id (schedule_id),
        INDEX idx_on_call_notifications_call_session_id (call_session_id),
        INDEX idx_on_call_notifications_doctor_id (doctor_id),
        INDEX idx_on_call_notifications_status (status),
        INDEX idx_on_call_notifications_notification_time (notification_time)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'agent_location_mappings',
      sql: `CREATE TABLE agent_location_mappings (
        agent_id VARCHAR(255) PRIMARY KEY,
        location_id CHAR(36),
        clinic_id INT,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_agent_location_mappings_location_id (location_id),
        INDEX idx_agent_location_mappings_clinic_id (clinic_id),
        INDEX idx_agent_location_mappings_is_active (is_active)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'staff_invite_codes',
      sql: `CREATE TABLE staff_invite_codes (
        id CHAR(36) PRIMARY KEY,
        code VARCHAR(50) UNIQUE,
        clinic_id INT,
        used BOOLEAN DEFAULT FALSE,
        expires_at DATETIME,
        created_by CHAR(36),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        used_at DATETIME,
        
        INDEX idx_staff_invite_codes_code (code),
        INDEX idx_staff_invite_codes_clinic_id (clinic_id),
        INDEX idx_staff_invite_codes_used (used)
      ) ENGINE=InnoDB`,
    },

    // ------------------------------------------------------------------
    //  External reference / sync tables
    // ------------------------------------------------------------------
    {
      name: 'patient_references',
      sql: `CREATE TABLE patient_references (
        id CHAR(36) PRIMARY KEY,
        provider VARCHAR(50),
        provider_id VARCHAR(255),
        phone_number VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_patient_references_provider (provider),
        INDEX idx_patient_references_provider_id (provider_id),
        INDEX idx_patient_references_phone_number (phone_number)
      ) ENGINE=InnoDB`,
    },
    {
      name: 'appointment_references',
      sql: `CREATE TABLE appointment_references (
        id CHAR(36) PRIMARY KEY,
        provider VARCHAR(50),
        external_id VARCHAR(255),
        provider_id VARCHAR(255),
        patient_id VARCHAR(255),
        patient_name VARCHAR(255),
        practitioner_id VARCHAR(255),
        practitioner_name VARCHAR(255),
        location_id VARCHAR(255),
        location_name VARCHAR(255),
        start_time DATETIME,
        end_time DATETIME,
        type VARCHAR(100),
        status VARCHAR(50),
        reason TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_appointment_references_provider (provider),
        INDEX idx_appointment_references_external_id (external_id),
        INDEX idx_appointment_references_provider_id (provider_id),
        INDEX idx_appointment_references_patient_id (patient_id),
        INDEX idx_appointment_references_practitioner_id (practitioner_id),
        INDEX idx_appointment_references_location_id (location_id),
        INDEX idx_appointment_references_status (status),
        INDEX idx_appointment_references_start_time (start_time)
      ) ENGINE=InnoDB`,
    },

    // ------------------------------------------------------------------
    //  Support tables
    // ------------------------------------------------------------------
    {
      name: 'email_templates',
      sql: `CREATE TABLE email_templates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        subject VARCHAR(500) NOT NULL,
        html_content LONGTEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_email_templates_name (name),
        INDEX idx_email_templates_is_active (is_active)
      ) ENGINE=InnoDB`,
    },
  ];

  // Create tables in order (skip if already exists)
  for (const table of tableCreationOrder) {
    const exists = await knex.schema.hasTable(table.name);
    if (exists) {
      console.log(`ℹ️  Table ${table.name} already exists, skipping creation`);
      continue;
    }

    try {
      console.log(`Creating table: ${table.name}`);
      await knex.raw(table.sql);
      console.log(`✅ Created table: ${table.name}`);
    } catch (error) {
      console.error(`❌ Error creating table ${table.name}:`, error.message);
      throw error;
    }
  }

  // Insert initial data
  try {
    console.log('Inserting initial data...');

    // Insert default practice
    await knex.raw(`INSERT INTO practices (uuid, name, description) VALUES 
      ('default-practice-uuid', 'Default Practice', 'Default practice for migration')`);

    // Insert default email templates
    await knex.raw(`INSERT INTO email_templates (name, subject, html_content, is_active) VALUES
      ('verify-email', 'Verify Your Email Address', '<h1>Please verify your email</h1>', TRUE),
      ('reset-password', 'Reset Your Password', '<h1>Reset your password</h1>', TRUE),
      ('2fa-verification', 'Two-Factor Authentication Code', '<h1>Your verification code</h1>', TRUE),
      ('new-appointment', 'New Appointment Scheduled', '<h1>New appointment notification</h1>', TRUE),
      ('new-call', 'New Call Received', '<h1>New call notification</h1>', TRUE),
      ('new-voice-mail', 'New Voice Mail Received', '<h1>New voicemail notification</h1>', TRUE)`);

    console.log('✅ Inserted initial data');
  } catch (error) {
    console.error('❌ Error inserting initial data:', error.message);
    // Don't throw here, just warn
  }

  // ------------------------------------------------------------------
  //  Backfill missing columns on existing installations (idempotent)
  // ------------------------------------------------------------------
  async function ensureColumn(tableName, columnName, alterCallback) {
    const hasCol = await knex.schema.hasColumn(tableName, columnName);
    if (!hasCol) {
      console.log(`➕ Adding missing column ${tableName}.${columnName}`);
      await knex.schema.alterTable(tableName, alterCallback);
    }
  }

  // Practices: is_active
  await ensureColumn('practices', 'is_active', t => t.boolean('is_active').defaultTo(true).index());

  // Users: new columns
  await ensureColumn('users', 'specialty', t => t.string('specialty', 100));
  await ensureColumn('users', 'profile_picture', t => t.string('profile_picture', 500));
  await ensureColumn('users', 'can_take_appointments', t =>
    t.boolean('can_take_appointments').defaultTo(false),
  );
  await ensureColumn('users', 'location_ids', t => t.json('location_ids'));
  await ensureColumn('users', 'current_location_id', t => t.string('current_location_id', 36));
  await ensureColumn('users', 'practice_ids', t => t.json('practice_ids'));

  // Clients: new columns
  await ensureColumn('clients', 'email', t => t.string('email', 255));
  await ensureColumn('clients', 'medical_history', t => t.text('medical_history'));
  await ensureColumn('clients', 'recent_notes', t => t.text('recent_notes'));
  await ensureColumn('clients', 'list_of_calls', t => t.json('list_of_calls'));

  // Calls: new column call_types (JSON array of call types)
  await ensureColumn('calls', 'call_types', t => t.json('call_types'));

  // Calls: new column transfer_to_location_id (CHAR(36))
  await ensureColumn('calls', 'transfer_to_location_id', t =>
    t.string('transfer_to_location_id', 36).index(),
  );

  // Backfill call_types based on existing call_type values
  try {
    console.log('Backfilling call_types column...');
    await knex.raw(
      `UPDATE calls
        SET call_types = JSON_ARRAY(
                CASE
                    WHEN LOWER(call_type) = 'other'          THEN 0            -- map text → enum 0
                    WHEN call_type REGEXP '^[0-9]+$'        THEN CAST(call_type AS UNSIGNED)
                    ELSE 0                                   -- any other junk → 0
                END
        )
        WHERE call_type IS NOT NULL
          AND (call_types IS NULL OR JSON_LENGTH(call_types) = 0);`,
    );
    console.log('✅ Backfilled call_types');
  } catch (e) {
    console.warn('⚠️  Failed to backfill call_types:', e.message);
  }

  console.log('🎉 MySQL migration completed successfully!');
};

exports.down = async function (knex) {
  // Drop all tables in reverse order to handle foreign key constraints
  const tables = [
    // Drop support & reference tables first
    'appointment_references',
    'patient_references',
    'staff_invite_codes',
    'agent_location_mappings',
    'on_call_notifications',
    'on_call_schedules',
    'emails',
    'otp',
    // Transactional tables
    'calendar_slots',
    'appointments',
    'call_sessions',
    'calls',
    // Core tables
    'patients',
    'clients',
    'users',
    'locations',
    'clinics',
    'practices',
    // Other support tables
    'email_templates',
  ];

  // Drop views first
  await knex.raw('DROP VIEW IF EXISTS v_appointments_with_details');
  await knex.raw('DROP VIEW IF EXISTS v_calls_with_details');

  // Drop stored procedures
  await knex.raw('DROP PROCEDURE IF EXISTS GetActiveAppointmentsByClient');
  await knex.raw('DROP PROCEDURE IF EXISTS GetCallsWithPagination');

  // Drop tables
  for (const table of tables) {
    await knex.raw(`DROP TABLE IF EXISTS ${table}`);
  }
};

function splitSqlStatements(sql) {
  const statements = [];
  let currentStatement = '';
  let inDelimiterBlock = false;
  let currentDelimiter = ';';

  const lines = sql.split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();

    // Skip comments and empty lines
    if (trimmedLine.startsWith('--') || trimmedLine.startsWith('#') || !trimmedLine) {
      continue;
    }

    // Handle delimiter changes
    if (trimmedLine.toUpperCase().startsWith('DELIMITER')) {
      const parts = trimmedLine.split(/\s+/);
      if (parts.length > 1) {
        currentDelimiter = parts[1];
        inDelimiterBlock = currentDelimiter !== ';';
      }
      continue;
    }

    currentStatement += line + '\n';

    // Check if statement is complete
    if (trimmedLine.endsWith(currentDelimiter)) {
      // Remove the delimiter from the statement
      currentStatement = currentStatement.replace(new RegExp(currentDelimiter + '\\s*$'), '');

      if (currentStatement.trim()) {
        statements.push(currentStatement.trim());
      }

      currentStatement = '';

      // Reset delimiter after stored procedure block
      if (inDelimiterBlock && currentDelimiter !== ';') {
        currentDelimiter = ';';
        inDelimiterBlock = false;
      }
    }
  }

  // Add remaining statement if any
  if (currentStatement.trim()) {
    statements.push(currentStatement.trim());
  }

  return statements;
}
