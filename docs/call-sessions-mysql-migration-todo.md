# Call Sessions MySQL Migration Implementation

## Overview
Implement dual-database support for CallSession entities to ensure data consistency between Firestore and MySQL, and migrate existing call sessions data.

## Current State
- ✅ Call sessions are stored in Firestore only
- ✅ MySQL `call_sessions` table exists but is unused
- ✅ `sendNewPatientForm` field added to CallSession interface
- ❌ No dual-database repository for call sessions
- ❌ No migration of existing Firestore data to MySQL

---

## Phase 1: MySQL Schema Updates

### 1.1 Update MySQL Schema
**File:** `mysql-migration-schema.sql` or create new migration

```sql
-- Add missing sendNewPatientForm column
ALTER TABLE call_sessions 
ADD COLUMN send_new_patient_form BOOLEAN DEFAULT FALSE AFTER status;

-- Add missing columns that exist in Firestore but not MySQL
ALTER TABLE call_sessions 
ADD COLUMN call_types JSON AFTER call_type;

-- Update indexes if needed
CREATE INDEX idx_call_sessions_send_new_patient_form ON call_sessions(send_new_patient_form);
```

### 1.2 Verify Column Mapping
Ensure all CallSession interface fields have corresponding MySQL columns:
- ✅ `id` → `uuid`
- ✅ `sessionId` → `session_id`
- ✅ `agentId` → `agent_id`
- ✅ `hasVoiceMail` → `has_voicemail`
- ✅ `callType` → `call_type`
- ❌ `callTypes` → `call_types` (needs to be added)
- ✅ `callerPhone` → `caller_phone`
- ✅ `patientId` → `client_id` (or add `patient_id`)
- ✅ `appointmentId` → `appointment_id`
- ✅ `isRedirected` → `is_redirected`
- ✅ `callId` → `call_id`
- ❌ `sendNewPatientForm` → `send_new_patient_form` (needs to be added)
- ✅ `createdAt` → `created_at`
- ✅ `updatedAt` → `updated_at`

---

## Phase 2: Repository Implementation

### 2.1 Create CallSessionsRepository
**File:** `lib/repositories/call-sessions-repository.ts`

```typescript
import { BaseRepository } from '../database/base-repository';
import { CallSession } from '../../models/CallSession';

export class CallSessionsRepository extends BaseRepository<CallSession> {
  constructor() {
    super('call_sessions', 'call-sessions', {
      id: 'uuid',
      sessionId: 'session_id',
      agentId: 'agent_id',
      triggerEvent: 'trigger_event',
      hasVoiceMail: 'has_voicemail',
      callType: 'call_type',
      callTypes: 'call_types',
      callerPhone: 'caller_phone',
      patientId: 'client_id', // or 'patient_id' if added
      appointmentId: 'appointment_id',
      isRedirected: 'is_redirected',
      callId: 'call_id',
      status: 'status',
      telephonyCallId: 'telephony_call_id',
      sendNewPatientForm: 'send_new_patient_form',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  // Custom methods for call sessions
  async findBySessionId(sessionId: string): Promise<CallSession | null> {
    const result = await this.findOne({ where: { sessionId } });
    return result || null;
  }

  async findByAppointmentId(appointmentId: string): Promise<CallSession[]> {
    const result = await this.findMany({ where: { appointmentId } });
    return result.items;
  }

  async findByPatientId(patientId: string): Promise<CallSession[]> {
    const result = await this.findMany({ where: { patientId } });
    return result.items;
  }
}
```

### 2.2 Add Repository to RepositoryManager
**File:** `lib/repositories/index.ts`

```typescript
// Add import
import { CallSessionsRepository } from './call-sessions-repository';

// Add to class
private _callSessions: CallSessionsRepository;

// Add to constructor
this._callSessions = new CallSessionsRepository();

// Add getter
get callSessions(): CallSessionsRepository {
  return this._callSessions;
}
```

---

## Phase 3: Service Migration

### 3.1 Create New CallSessionService
**File:** `lib/services/call-session-service.ts`

```typescript
import { CallSessionsRepository } from '../repositories/call-sessions-repository';
import { CallSession } from '../../models/CallSession';
import { repositoryManager } from '../repositories';

export class CallSessionService {
  private repository: CallSessionsRepository;

  constructor() {
    this.repository = repositoryManager.callSessions;
  }

  async getCallSessionBySessionId(sessionId: string): Promise<CallSession | null> {
    return this.repository.findBySessionId(sessionId);
  }

  async createCallSession(sessionData: Omit<CallSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<CallSession> {
    return this.repository.create(sessionData);
  }

  async updateCallSession(id: string, updates: Partial<CallSession>): Promise<void> {
    await this.repository.update(id, updates);
  }

  async addOrUpdateCallSession(sessionId: string, data: Partial<CallSession>): Promise<CallSession> {
    const existingSession = await this.getCallSessionBySessionId(sessionId);
    
    if (existingSession) {
      await this.updateCallSession(existingSession.id, data);
      // Return updated session
      return this.getCallSessionBySessionId(sessionId) as Promise<CallSession>;
    } else {
      return this.createCallSession({ ...data, sessionId } as Omit<CallSession, 'id' | 'createdAt' | 'updatedAt'>);
    }
  }

  async findCallSessionsByAppointmentId(appointmentId: string): Promise<CallSession[]> {
    return this.repository.findByAppointmentId(appointmentId);
  }

  async findCallSessionsByPatientId(patientId: string): Promise<CallSession[]> {
    return this.repository.findByPatientId(patientId);
  }
}

export const callSessionService = new CallSessionService();
```

### 3.2 Update Existing Service Usage
**Files to Update:**
- `utils/firestore.ts` - Replace direct Firestore calls with service calls
- `pages/api/external-api/v2/appointments/booking.ts`
- `pages/api/external-api/v2/finalize-conversation/index.ts`
- `pages/api/external-api/v2/appointments/send-confirmation.ts`
- Any other files using `callSessionsService`

**Example Migration:**
```typescript
// OLD
import { callSessionsService } from '@/utils/firestore';
await callSessionsService.addOrUpdateCallSession(sessionId, data);

// NEW
import { callSessionService } from '@/lib/services/call-session-service';
await callSessionService.addOrUpdateCallSession(sessionId, data);
```

---

## Phase 4: Data Migration

### 4.1 Create Migration Script
**File:** `scripts/migrate-call-sessions-to-mysql.ts`

```typescript
import { callSessionsService as firestoreService } from '../utils/firestore';
import { callSessionService as mysqlService } from '../lib/services/call-session-service';
import admin from 'firebase-admin';

export async function migrateCallSessionsToMySQL() {
  console.log('Starting call sessions migration from Firestore to MySQL...');
  
  const firestore = admin.firestore();
  const callSessionsRef = firestore.collection('call-sessions');
  
  let migrated = 0;
  let errors = 0;
  let batch = 0;
  const batchSize = 100;
  
  try {
    // Get all call sessions from Firestore
    const snapshot = await callSessionsRef.get();
    const totalDocs = snapshot.docs.length;
    
    console.log(`Found ${totalDocs} call sessions to migrate`);
    
    for (let i = 0; i < snapshot.docs.length; i += batchSize) {
      batch++;
      const batchDocs = snapshot.docs.slice(i, i + batchSize);
      
      console.log(`Processing batch ${batch} (${batchDocs.length} documents)...`);
      
      for (const doc of batchDocs) {
        try {
          const firestoreData = doc.data();
          
          // Check if already exists in MySQL
          const existing = await mysqlService.getCallSessionBySessionId(firestoreData.sessionId);
          if (existing) {
            console.log(`Skipping ${firestoreData.sessionId} - already exists in MySQL`);
            continue;
          }
          
          // Transform Firestore data to match CallSession interface
          const callSessionData = {
            sessionId: firestoreData.sessionId,
            agentId: firestoreData.agentId,
            triggerEvent: firestoreData.triggerEvent,
            hasVoiceMail: firestoreData.hasVoiceMail || false,
            callType: firestoreData.callType,
            callTypes: firestoreData.callTypes,
            callerPhone: firestoreData.callerPhone,
            patientId: firestoreData.patientId,
            appointmentId: firestoreData.appointmentId,
            isRedirected: firestoreData.isRedirected || false,
            callId: firestoreData.callId,
            status: firestoreData.status,
            telephonyCallId: firestoreData.telephonyCallId,
            sendNewPatientForm: firestoreData.sendNewPatientForm || false,
            createdAt: firestoreData.createdAt?.toDate?.() || new Date(),
            updatedAt: firestoreData.updatedAt?.toDate?.() || new Date(),
          };
          
          await mysqlService.createCallSession(callSessionData);
          migrated++;
          
        } catch (error) {
          console.error(`Error migrating call session ${doc.id}:`, error);
          errors++;
        }
      }
      
      console.log(`Batch ${batch} completed. Migrated: ${migrated}, Errors: ${errors}`);
    }
    
    console.log(`Migration completed! Total migrated: ${migrated}, Errors: ${errors}`);
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateCallSessionsToMySQL()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Migration failed:', error);
      process.exit(1);
    });
}
```

### 4.2 Create Migration Verification Script
**File:** `scripts/verify-call-sessions-migration.ts`

```typescript
import { callSessionsService as firestoreService } from '../utils/firestore';
import { callSessionService as mysqlService } from '../lib/services/call-session-service';

export async function verifyCallSessionsMigration() {
  console.log('Verifying call sessions migration...');
  
  // Get counts from both databases
  const firestoreSnapshot = await admin.firestore().collection('call-sessions').get();
  const firestoreCount = firestoreSnapshot.docs.length;
  
  // Count MySQL records (implement count method in repository if needed)
  const mysqlResult = await mysqlService.repository.findMany({ limit: 1 });
  const mysqlCount = mysqlResult.total;
  
  console.log(`Firestore call sessions: ${firestoreCount}`);
  console.log(`MySQL call sessions: ${mysqlCount}`);
  
  if (firestoreCount === mysqlCount) {
    console.log('✅ Migration verification passed!');
  } else {
    console.log('❌ Migration verification failed - counts do not match');
  }
  
  // Sample data comparison
  const sampleFirestore = firestoreSnapshot.docs[0]?.data();
  if (sampleFirestore) {
    const mysqlSample = await mysqlService.getCallSessionBySessionId(sampleFirestore.sessionId);
    if (mysqlSample) {
      console.log('✅ Sample data found in both databases');
    } else {
      console.log('❌ Sample data not found in MySQL');
    }
  }
}
```

---

## Phase 5: Testing & Validation

### 5.1 Unit Tests
**File:** `__tests__/lib/repositories/call-sessions-repository.test.ts`

```typescript
import { CallSessionsRepository } from '@/lib/repositories/call-sessions-repository';
import { CallSession } from '@/models/CallSession';

describe('CallSessionsRepository', () => {
  let repository: CallSessionsRepository;
  
  beforeEach(() => {
    repository = new CallSessionsRepository();
  });
  
  describe('findBySessionId', () => {
    it('should find call session by session ID', async () => {
      // Test implementation
    });
  });
  
  describe('findByAppointmentId', () => {
    it('should find call sessions by appointment ID', async () => {
      // Test implementation
    });
  });
  
  // Add more tests...
});
```

### 5.2 Integration Tests
**File:** `__tests__/lib/services/call-session-service.test.ts`

```typescript
import { callSessionService } from '@/lib/services/call-session-service';

describe('CallSessionService', () => {
  describe('addOrUpdateCallSession', () => {
    it('should create new call session when none exists', async () => {
      // Test implementation
    });
    
    it('should update existing call session', async () => {
      // Test implementation
    });
  });
  
  // Add more tests...
});
```

---

## Phase 6: Production Deployment

### 6.1 Pre-Deployment Checklist
- [ ] MySQL schema updated with new columns
- [ ] CallSessionsRepository implemented and tested
- [ ] CallSessionService implemented and tested
- [ ] Migration script tested on staging data
- [ ] All existing callSessionsService references updated
- [ ] Unit and integration tests passing
- [ ] Performance testing completed

### 6.2 Deployment Steps
1. **Deploy schema changes** to MySQL
2. **Deploy new repository and service code**
3. **Run migration script** to migrate existing data
4. **Verify migration** with verification script
5. **Monitor** for any issues
6. **Gradually phase out** direct Firestore usage

### 6.3 Rollback Plan
If issues occur:
1. **Revert code** to use Firestore service
2. **Keep MySQL data** for future retry
3. **Investigate and fix** issues
4. **Retry migration** when ready

---

## Implementation Priority

### High Priority (Phase 1-2)
1. MySQL schema updates
2. CallSessionsRepository implementation
3. Basic service functionality

### Medium Priority (Phase 3-4)
1. Service migration
2. Data migration script
3. Update all references

### Low Priority (Phase 5-6)
1. Comprehensive testing
2. Performance optimization
3. Monitoring and alerts

---

## Notes & Considerations

### Performance
- Call sessions are frequently accessed, ensure proper indexing
- Consider read replicas for heavy read operations
- Monitor query performance after migration

### Data Consistency
- Implement proper error handling in dual-database operations
- Consider eventual consistency patterns
- Plan for potential MySQL/Firestore sync issues

### Backwards Compatibility
- Keep Firestore service available during transition period
- Implement feature flags for gradual rollout
- Monitor both data sources during transition

### Monitoring
- Add logging for all call session operations
- Monitor migration progress and success rates
- Set up alerts for data inconsistencies