# After-Hours Call Logs API

## Overview

The After-Hours Call Logs API provides functionality to retrieve and create after-hours call log records by `call_id` with doctor names from the `users` table. This is useful for displaying who viewed or contacted patients for after-hours calls.

## API Endpoints

### GET `/api/calls/after-hours/call-logs/[callId]`

Retrieves after-hours call logs by call ID with user names for UI display.

#### Parameters

- `callId` (string, required): The call ID to retrieve logs for

#### Response Format

```json
{
  "success": true,
  "data": [
    {
      "type": "viewed",
      "message": "Dr <PERSON> Viewed at 11:23pm on July 31 2026",
      "timestamp": "2026-07-31T23:23:00.000Z",
      "userName": "Dr <PERSON>"
    },
    {
      "type": "viewed", 
      "message": "Dr <PERSON> Viewed at 11:28pm on July 31 2026",
      "timestamp": "2026-07-31T23:28:00.000Z",
      "userName": "Dr <PERSON>"
    }
  ]
}
```

#### Example Usage

```typescript
// Fetch after-hours call logs for a specific call
const response = await fetch('/api/calls/after-hours/call-logs/call-123');
const result = await response.json();

if (result.success) {
  result.data.forEach(log => {
    console.log(log.message);
    // Output:
    // "Dr <PERSON> Viewed at 11:23pm on July 31 2026"
    // "Dr <PERSON> <PERSON> Viewed at 11:28pm on July 31 2026"
  });
}
```

### POST `/api/calls/after-hours/contact`

Creates a new after-hours call log record with the `contacted_by` field when a doctor contacts a patient.

#### Request Body

```json
{
  "callId": "call-123",
  "doctorId": "doctor-456"
}
```

#### Response Format

```json
{
  "success": true,
  "message": "Contact log created successfully",
  "data": {
    "logId": "log-789",
    "afterHoursCallId": "ahc-123",
    "contactedBy": "doctor-456",
    "createdAt": "2026-07-31T23:30:00.000Z"
  }
}
```

#### Example Usage

```typescript
// Create a contact log when a doctor contacts a patient
const response = await fetch('/api/calls/after-hours/contact', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    callId: 'call-123',
    doctorId: 'doctor-456',
  }),
});

const result = await response.json();
if (result.success) {
  console.log('Contact log created:', result.data.logId);
}
```

## Database Queries

### Retrieving Logs

The GET API performs a JOIN query across three tables:

1. `after_hours_calls_log` - Contains the log records
2. `after_hours_calls` - Contains the call_id reference
3. `users` - Contains the doctor names

```sql
SELECT 
  ahl.id,
  ahl.after_hours_call_id,
  ahl.viewed_by,
  ahl.contacted_by,
  ahl.created_at,
  ahl.updated_at,
  viewed_user.name as viewed_by_user_name,
  contacted_user.name as contacted_by_user_name
FROM after_hours_calls_log ahl
INNER JOIN after_hours_calls ahc ON ahl.after_hours_call_id = ahc.id
LEFT JOIN users viewed_user ON ahl.viewed_by = viewed_user.id
LEFT JOIN users contacted_user ON ahl.contacted_by = contacted_user.id
WHERE ahc.call_id = ?
ORDER BY ahl.created_at ASC
```

### Creating Contact Logs

The POST API creates a new record in the `after_hours_calls_log` table:

```sql
INSERT INTO after_hours_calls_log (
  id,
  after_hours_call_id,
  contacted_by,
  created_at,
  updated_at
) VALUES (?, ?, ?, ?, ?)
```

## Repository Method

The repository method `findByCallIdWithUserNames` is available in the `AfterHoursCallsLogRepository`:

```typescript
import { afterHoursCallsLogService } from '@/utils/firestore';

const logs = await afterHoursCallsLogService.getAfterHoursCallLogsByCallIdWithUserNames('call-123');
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200` - Success
- `400` - Bad Request (missing callId)
- `405` - Method Not Allowed (non-GET requests)
- `500` - Internal Server Error

## Notes

- Results are ordered by `created_at` in ascending order (oldest first)
- Only logs with valid user names are returned in the formatted response
- The API handles both "viewed" and "contacted" log types
- Date formatting follows US locale (e.g., "July 31 2026")
- Time formatting uses 12-hour format with AM/PM 