# External API v2 Logging and Monitoring

This document describes the logging and monitoring system implemented for the External API v2 to help with troubleshooting, observability, and performance tracking.

## Logging System

The External API v2 uses a structured logging system based on [Pino](https://getpino.io/), a very low overhead Node.js logger.

### Log Format

All logs are output in JSON format with the following standard fields:

```json
{
  "level": "info",
  "time": "2023-06-01T12:34:56.789Z",
  "pid": 1234,
  "hostname": "server-name",
  "requestId": "uuid-request-id",
  "msg": "Human readable message"
}
```

Additional contextual information is included based on the log entry type:

#### Request Logs
```json
{
  "method": "GET",
  "url": "/api/external-api/v2/users",
  "headers": { ... },  // Sensitive data redacted
  "body": { ... }      // Sensitive data redacted
}
```

#### Response Logs
```json
{
  "statusCode": 200,
  "duration": 123      // Response time in milliseconds
}
```

#### Error Logs
```json
{
  "err": {
    "message": "Error message",
    "stack": "Error stack trace",
    "statusCode": 500,
    "code": "ERROR_CODE"
  }
}
```

### Sensitive Data Handling

To ensure security and compliance, the following fields are automatically redacted in logs:

- Authentication headers (`Authorization`)
- API keys (`x-api-key`)
- Cookies
- Passwords
- Tokens
- Secrets
- Common PII fields (SSN, DOB, etc.)

Redacted values are replaced with `[REDACTED]` in the logs.

### Log Levels

The system uses the following log levels in order of increasing severity:

1. `debug` - Detailed information for debugging
2. `info` - Successful operations, regular activity
3. `warn` - Potential issues, client errors (4xx)
4. `error` - Failed operations, server errors (5xx)

The log level can be configured using the `LOG_LEVEL` environment variable. In development environments, the default is `debug`, while in production it's `info`.

## Monitoring System

The API includes a built-in monitoring system that tracks performance metrics for all endpoints.

### Metrics Tracked

For each API endpoint, the following metrics are tracked:

- **Total Calls**: Number of requests to the endpoint
- **Errors**: Number of failed requests (HTTP 4xx/5xx or exceptions)
- **Error Rate**: Percentage of requests that resulted in errors
- **Average Latency**: Average response time in milliseconds
- **P95 Latency**: 95th percentile response time in milliseconds

### Accessing Metrics

Metrics are available through the admin endpoint:

```
GET /api/external-api/v2/admin/metrics
```

This endpoint requires authentication with an admin token in the `x-admin-token` header that matches the `ADMIN_API_KEY` environment variable.

Sample response:

```json
{
  "timestamp": "2023-06-01T12:00:00.000Z",
  "metrics": {
    "GET /api/external-api/v2/users": {
      "totalCalls": 1250,
      "errors": 15,
      "errorRate": 0.012,
      "avgLatencyMs": 87.5,
      "p95LatencyMs": 210
    },
    "GET /api/external-api/v2/appointments": {
      "totalCalls": 2500,
      "errors": 50,
      "errorRate": 0.02,
      "avgLatencyMs": 110.3,
      "p95LatencyMs": 380
    }
  }
}
```

### Health Check Endpoint

A health check endpoint is available for external monitoring systems:

```
GET /api/external-api/v2/health
```

This endpoint doesn't require authentication and returns basic health information:

```json
{
  "status": "healthy",
  "timestamp": "2023-06-01T12:00:00.000Z",
  "version": "2.0",
  "environment": "production"
}
```

## Integration with External Monitoring

For production environments, it's recommended to integrate with external monitoring systems like:

- **Datadog**
- **New Relic**
- **Prometheus/Grafana**
- **AWS CloudWatch**

The structured logs and metrics endpoints can be used as data sources for these systems.

## Troubleshooting Guide

### Request ID Tracking

Each request is assigned a unique ID (UUID) that is included in all log entries related to that request. This ID can be used to trace a request through the system.

To correlate frontend and backend logs, the request ID is also returned in the `x-request-id` response header.

### Common Error Patterns

1. **Authentication errors**: Look for error logs with status code 401 and error code `AUTHENTICATION_ERROR`
2. **Rate limiting**: Status code 429 with error code `RATE_LIMIT_EXCEEDED`
3. **Provider errors**: Error codes starting with `PROVIDER_` indicate issues with the underlying API provider

### Log Retention

Log retention periods depend on the environment:
- Development: 7 days
- Staging: 30 days
- Production: 90 days 