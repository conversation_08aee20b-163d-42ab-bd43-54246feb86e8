# Call Type Classification with LLM

This document describes the enhanced call type classification system using LLM (Large Language Model) to automatically determine call types based on conversation transcripts.

## Overview

The system uses a comprehensive prompt to analyze call transcripts and classify them into one or more of 18 different call types. The classification considers conversation content, flow, duration, and outcomes to provide accurate categorization.

## Available Call Types

| ID | Name | Description |
|----|------|-------------|
| 0 | OTHER | Default/fallback for unspecified or unclear call purposes |
| 1 | VOICEMAIL | Caller left a voicemail message |
| 2 | TRANSFER_TO_HUMAN | Call was transferred to a human agent (legacy) |
| 3 | NEW_PATIENT_NEW_APPOINTMENT | New patient scheduling their first appointment |
| 4 | NEW_APPOINTMENT_EXISTING_PATIENT | Existing patient scheduling a new appointment |
| 5 | RESCHEDULE | Patient rescheduling an existing appointment |
| 6 | CANCELLATION | Patient cancelling an existing appointment |
| 7 | VOICEMAIL_SYSTEM_ERROR | Voicemail left after a system error occurred |
| 8 | GENERAL_INFO | Caller requesting general information |
| 9 | LOOKUP | Lookup actions (appointment search, patient info) |
| 10 | TRANSFER_TO_CLINIC | Call was transferred to a clinic/department |
| 11 | AFTER_HOURS | Call was made during after hours |
| 12 | CONFIRM_APPOINTMENT | Appointment confirmation or verification |
| 13 | DISCONNECTED | Call disconnected without clear purpose (>20s, abrupt end) |
| 14 | IMMEDIATE_TRANSFER | Call transferred to human within 35 seconds |
| 15 | TRANSFER_DUE_TO_SCHEDULING | Call transferred after unable to schedule |
| 16 | TRANSFER_DUE_TO_UNABLE_TO_ASSIST | Call transferred after multiple assistance attempts |
| 17 | IMMEDIATE_HANGUP | Call ended within 3 conversation turns |

## LLM Client Methods

### `classifyCallTypes(call: Call): Promise<CallType[] | null>`

The new comprehensive method that returns an array of call type IDs.

```typescript
import { LLMClient } from '@/utils/llm/llm.client';

const llmClient = LLMClient.getInstance();
const callTypes = await llmClient.classifyCallTypes(call);

// Example result: [4, 9] (Existing patient appointment with lookup)
```

### `classifyCallType(call: Call): Promise<string | null>` (Legacy)

The legacy method for backward compatibility that returns a single call type string.

## Test Endpoint

### POST `/api/test/classify-call-types`

A test endpoint that takes a call ID and updates its type and duration based on GCP conversation data.

**Request Body:**
```json
{
  "callId": "your-call-id-here"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Call types classified and updated successfully",
  "callId": "your-call-id-here",
  "classification": {
    "classifiedTypes": [4, 9],
    "primaryType": 4,
    "finalCallTypes": [4, 9]
  },
  "changes": {
    "type": {
      "old": 0,
      "new": 4
    },
    "callTypes": {
      "old": [0],
      "new": [4, 9]
    },
    "duration": {
      "old": "0 min",
      "new": "2.3 min"
    }
  },
  "updatedCall": { /* full call object */ }
}
```

## Usage Examples

### Using the Test Endpoint

1. **Via curl:**
```bash
curl -X POST http://localhost:3000/api/test/classify-call-types \
  -H "Content-Type: application/json" \
  -d '{"callId": "your-call-id"}'
```

2. **Via the test script:**
```bash
node scripts/test-call-classification.js your-call-id
```

### Programmatic Usage

```typescript
import { LLMClient } from '@/utils/llm/llm.client';
import { callsService } from '@/utils/firestore';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';

async function classifyAndUpdateCall(callId: string) {
  // Get the call
  const call = await callsService.getCallById(callId);
  if (!call) return null;

  // Classify using LLM
  const llmClient = LLMClient.getInstance();
  const classifiedTypes = await llmClient.classifyCallTypes(call);
  
  if (!classifiedTypes || classifiedTypes.length === 0) {
    return null;
  }

  // Build call types array
  const primaryType = classifiedTypes[0];
  let callTypes = [primaryType];
  
  for (let i = 1; i < classifiedTypes.length; i++) {
    callTypes = mergeCallTypes(callTypes, classifiedTypes[i]);
  }

  // Update the call
  await callsService.updateCall(callId, {
    type: primaryType,
    callTypes: callTypes
  });

  return { primaryType, callTypes };
}
```

## Classification Logic

The LLM uses sophisticated rules to classify calls:

1. **Multiple Types**: Calls can have multiple types representing the conversation flow
2. **Priority Rules**: Voicemail > Transfer > Scheduling > Lookup > General Info
3. **Duration-Based**: Immediate transfers (≤35s), immediate hangups (≤3 turns)
4. **Content Analysis**: Analyzes conversation content for scheduling, information requests, etc.

## Error Handling

The system includes comprehensive error handling:

- Invalid call IDs return 404
- Missing transcription data is handled gracefully
- LLM failures fall back to appropriate defaults
- All operations are logged for debugging

## Testing

Use the provided test endpoint and script to validate classification accuracy:

```bash
# Test a specific call
node scripts/test-call-classification.js abc123

# The script will show:
# - Original call type and duration
# - LLM classification results
# - Changes made to the call record
```

## Integration

The classification system integrates with:

- **Call Sessions**: Updates both call records and sessions
- **Call Type Utils**: Uses existing merge logic for multiple types
- **GCP Storage**: Fetches fresh transcription data when available
- **Logging**: Comprehensive logging for monitoring and debugging
