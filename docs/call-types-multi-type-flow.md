# Call Types Multi-Type Flow System

## Overview

The call types system supports tracking multiple types during a single call to provide a chronological story of what happened. The system uses **5 categories** with **only one type per category** allowed to keep the sequence meaningful and manageable.

## Architecture

### Call Type Categories

The system organizes call types into logical categories:

| Category | Types | Purpose |
|----------|-------|---------|
| **`scheduling`** | NEW_PATIENT_NEW_APPOINTMENT (3)<br/>NEW_APPOINTMENT_EXISTING_PATIENT (4)<br/>RESCHEDULE (5)<br/>CANCELLATION (6)<br/>CONFIRM_APPOINTMENT (12) | Appointment-related actions |
| **`lookup`** | LOOKUP (9) | Information retrieval |
| **`transfer`** | TRANSFER_TO_HUMAN (2)<br/>TRANSFER_TO_CLINIC (10)<br/>IMMEDIATE_TRANSFER (14)<br/>TRANSFER_DUE_TO_SCHEDULING (15)<br/>TRANSFER_DUE_TO_UNABLE_TO_ASSIST (16) | Call routing |
| **`voicemail`** | VOICEMAIL (1)<br/>VOICEMAIL_SYSTEM_ERROR (7) | Message recording |
| **`misc`** | OTHER (0)<br/>GENERAL_INFO (8) | General/uncategorized |
| **`after_hours`** | AFTER_HOURS (11) | After-hours calls |
| **`disconnected`** | DISCONNECTED (13) | Disconnected calls |

### Data Structure

Each call has two fields:
- **`type`**: Single number representing the latest/most recent call type
- **`callTypes`**: Array of numbers representing the chronological sequence of call types

```typescript
interface Call {
  type: number;        // Latest call type (e.g., 5)
  callTypes: number[]; // Chronological sequence (e.g., [9, 5])
}
```

## Merge Logic

The `mergeCallTypes` function handles adding new types to the sequence:

```typescript
function mergeCallTypes(existing: number[], newType: CallType): number[] {
  // 1. Remove any existing type from the same category
  // 2. Add the new type to the end
  // 3. Avoid duplicates
}
```

### Examples:
```typescript
mergeCallTypes([9, 4], 5) → [9, 5]     // Replace scheduling type 4 with 5
mergeCallTypes([9, 4], 2) → [9, 4, 2]  // Add transfer type (different category)
mergeCallTypes([9], 9) → [9]           // Avoid duplicates
```

## Multi-Type Flow Examples

### Example 1: Complex Scheduling Call

**Scenario**: Patient calls to reschedule, but needs to look up appointment first

```json
{
  "flow": [
    {
      "action": "Patient calls: 'I need to reschedule my appointment'",
      "system": "Looks up patient appointments",
      "callTypes": [9],
      "type": 9,
      "meaning": "LOOKUP"
    },
    {
      "action": "System finds appointment, patient reschedules",
      "system": "Processes reschedule",
      "callTypes": [9, 5],
      "type": 5,
      "meaning": "LOOKUP + RESCHEDULE"
    }
  ],
  "final": {
    "type": 5,
    "callTypes": [9, 5],
    "interpretation": "Lookup followed by reschedule"
  }
}
```

### Example 2: Transfer After Failed Scheduling

**Scenario**: Patient tries to schedule but needs human help

```json
{
  "flow": [
    {
      "action": "Patient: 'I want to schedule an appointment'",
      "system": "Attempts to schedule",
      "callTypes": [4],
      "type": 4,
      "meaning": "NEW_APPOINTMENT_EXISTING_PATIENT"
    },
    {
      "action": "System can't handle request, transfers to human",
      "system": "Transfers call",
      "callTypes": [4, 2],
      "type": 2,
      "meaning": "SCHEDULING_ATTEMPT + TRANSFER_TO_HUMAN"
    }
  ],
  "final": {
    "type": 2,
    "callTypes": [4, 2],
    "interpretation": "Scheduling attempt that required human assistance"
  }
}
```

### Example 3: Voicemail After System Error

**Scenario**: System crashes during appointment lookup

```json
{
  "flow": [
    {
      "action": "Patient: 'When is my next appointment?'",
      "system": "Attempts lookup",
      "callTypes": [9],
      "type": 9,
      "meaning": "LOOKUP"
    },
    {
      "action": "System error occurs, goes to voicemail",
      "system": "Records voicemail",
      "callTypes": [9, 7],
      "type": 7,
      "meaning": "LOOKUP + VOICEMAIL_SYSTEM_ERROR"
    }
  ],
  "final": {
    "type": 7,
    "callTypes": [9, 7],
    "interpretation": "Lookup attempt that failed due to system error"
  }
}
```

### Example 4: Category Replacement

**Scenario**: Patient changes mind during call

```json
{
  "flow": [
    {
      "action": "Patient: 'I want to schedule an appointment'",
      "system": "Starts scheduling",
      "callTypes": [4],
      "type": 4,
      "meaning": "NEW_APPOINTMENT_EXISTING_PATIENT"
    },
    {
      "action": "Patient: 'Actually, I want to cancel my existing appointment'",
      "system": "Switches to cancellation",
      "callTypes": [6],
      "type": 6,
      "meaning": "CANCELLATION"
    }
  ],
  "final": {
    "type": 6,
    "callTypes": [6],
    "interpretation": "Cancellation (scheduling category was replaced)"
  }
}
```

### Example 5: Smart Transfer Classification

**Scenario**: Different transfer scenarios are automatically classified

```json
{
  "scenarios": [
    {
      "name": "Immediate Transfer (≤35 seconds)",
      "flow": [
        {
          "action": "Patient: 'I need to speak to someone right away'",
          "system": "Transfers immediately",
          "callTypes": [14],
          "type": 14,
          "duration": "25 seconds",
          "meaning": "IMMEDIATE_TRANSFER"
        }
      ],
      "final": {
        "type": 14,
        "callTypes": [14],
        "interpretation": "Quick transfer - patient needed immediate human assistance"
      }
    },
    {
      "name": "Transfer Due to Scheduling",
      "flow": [
        {
          "action": "Patient: 'I want to schedule an appointment'",
          "system": "Attempts scheduling",
          "callTypes": [4],
          "type": 4,
          "meaning": "NEW_APPOINTMENT_EXISTING_PATIENT"
        },
        {
          "action": "Complex scheduling needs, transfers to human",
          "system": "Smart classification detects scheduling attempt",
          "callTypes": [4, 15],
          "type": 15,
          "meaning": "SCHEDULING + TRANSFER_DUE_TO_SCHEDULING"
        }
      ],
      "final": {
        "type": 15,
        "callTypes": [4, 15],
        "interpretation": "Scheduling attempt that required human assistance"
      }
    },
    {
      "name": "Transfer Due to Unable to Assist",
      "flow": [
        {
          "action": "Patient: 'What are your hours?'",
          "system": "Provides general info",
          "callTypes": [8],
          "type": 8,
          "meaning": "GENERAL_INFO"
        },
        {
          "action": "Patient: 'Can you check my appointment?'",
          "system": "Looks up appointment",
          "callTypes": [8, 9],
          "type": 9,
          "meaning": "GENERAL_INFO + LOOKUP"
        },
        {
          "action": "Multiple complex requests, transfers to human",
          "system": "Smart classification detects multiple assistance attempts",
          "callTypes": [8, 9, 16],
          "type": 16,
          "duration": "180 seconds",
          "meaning": "GENERAL_INFO + LOOKUP + TRANSFER_DUE_TO_UNABLE_TO_ASSIST"
        }
      ],
      "final": {
        "type": 16,
        "callTypes": [8, 9, 16],
        "interpretation": "Complex call with multiple assistance attempts before transfer"
      }
    }
  ]
}
```

### Example 6: Maximum Complexity

**Scenario**: Patient calls for info, looks up appointment, reschedules, then transfers

```json
{
  "flow": [
    {
      "action": "Patient: 'What are your hours?'",
      "system": "Provides general info",
      "callTypes": [8],
      "type": 8,
      "meaning": "GENERAL_INFO"
    },
    {
      "action": "Patient: 'Can you check my appointment?'",
      "system": "Looks up appointment",
      "callTypes": [8, 9],
      "type": 9,
      "meaning": "GENERAL_INFO + LOOKUP"
    },
    {
      "action": "Patient: 'I need to reschedule it'",
      "system": "Processes reschedule",
      "callTypes": [8, 9, 5],
      "type": 5,
      "meaning": "GENERAL_INFO + LOOKUP + RESCHEDULE"
    },
    {
      "action": "Complex issue, transfers to human",
      "system": "Smart classification: scheduling + multiple attempts = TRANSFER_DUE_TO_SCHEDULING",
      "callTypes": [8, 9, 15],
      "type": 15,
      "meaning": "GENERAL_INFO + LOOKUP + TRANSFER_DUE_TO_SCHEDULING"
    }
  ],
  "final": {
    "type": 15,
    "callTypes": [8, 9, 15],
    "interpretation": "Complex call with lookup and scheduling issue requiring human transfer"
  }
}
```

## Key Rules

1. **One per category**: Only one type from each category can exist in the array
2. **Sequence matters**: Array shows the chronological order of operations
3. **Latest wins**: `type` field always shows the most recent classification
4. **Category replacement**: New type replaces old type in the same category
5. **Cross-category accumulation**: Types from different categories accumulate

## What WON'T Happen

```typescript
// ❌ Multiple scheduling types (all are 'scheduling' category)
callTypes: [4, 5, 6]  // WRONG

// ✅ Only latest scheduling type
callTypes: [6]  // CORRECT - only cancellation remains

// ❌ Duplicate types
callTypes: [9, 9, 4]  // WRONG

// ✅ No duplicates
callTypes: [9, 4]  // CORRECT - unique types only
```

## Implementation Details

### Core Functions

- **`mergeCallTypes(existing, newType)`**: Merges a new call type into existing array
- **`updateCallSessionType(sessionId, callType, context)`**: Updates call session with new type
- **`updateCallSessionTypeForPatient(patientId, callType, context)`**: Updates all sessions for a patient
- **`updateCallSessionTypeForAppointment(appointmentId, callType, context)`**: Updates all sessions for an appointment

### Category Mapping

```typescript
const CALL_TYPE_CATEGORY: Record<CallType, string> = {
  [CallType.OTHER]: 'misc',
  [CallType.VOICEMAIL]: 'voicemail',
  [CallType.TRANSFER_TO_HUMAN]: 'transfer',
  [CallType.NEW_PATIENT_NEW_APPOINTMENT]: 'scheduling',
  [CallType.NEW_APPOINTMENT_EXISTING_PATIENT]: 'scheduling',
  [CallType.RESCHEDULE]: 'scheduling',
  [CallType.CANCELLATION]: 'scheduling',
  [CallType.VOICEMAIL_SYSTEM_ERROR]: 'voicemail',
  [CallType.GENERAL_INFO]: 'misc',
  [CallType.LOOKUP]: 'lookup',
  [CallType.TRANSFER_TO_CLINIC]: 'transfer',
  [CallType.AFTER_HOURS]: 'after_hours',
  [CallType.CONFIRM_APPOINTMENT]: 'scheduling',
  [CallType.DISCONNECTED]: 'disconnected',
  [CallType.IMMEDIATE_TRANSFER]: 'transfer',
  [CallType.TRANSFER_DUE_TO_SCHEDULING]: 'transfer',
  [CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST]: 'transfer',
};
```

## Benefits

1. **Chronological story**: Understand the complete flow of what happened during a call
2. **Manageable complexity**: Limited to 5 categories prevents overwhelming arrays
3. **Meaningful sequence**: Each entry represents a significant state change
4. **Backward compatibility**: Single `type` field maintained for legacy systems
5. **Analytics friendly**: Easy to analyze call patterns and success rates

## Usage in Analytics

The multi-type system enables rich analytics:

- **Success rate analysis**: How often do lookups lead to successful scheduling?
- **Transfer patterns**: What types of calls most often require human intervention?
- **System reliability**: How often do calls end in voicemail due to system errors?
- **User journey mapping**: Common paths users take through the system

Example queries:
```sql
-- Calls that started with lookup and ended with scheduling
SELECT * FROM calls WHERE JSON_CONTAINS(callTypes, '[9]') AND type IN (3,4,5,6);

-- Calls that required human transfer (all types)
SELECT * FROM calls WHERE type IN (2, 10, 14, 15, 16);

-- Transfer breakdown analysis
SELECT 
  CASE 
    WHEN type = 14 THEN 'Immediate Transfer (≤35s)'
    WHEN type = 15 THEN 'Transfer - Scheduling Issue'
    WHEN type = 16 THEN 'Transfer - Unable to Assist'
    WHEN type = 2 THEN 'Generic Transfer'
    WHEN type = 10 THEN 'Transfer to Clinic'
  END as transfer_type,
  COUNT(*) as count
FROM calls 
WHERE type IN (2, 10, 14, 15, 16)
GROUP BY type;

-- System error rate
SELECT COUNT(*) FROM calls WHERE type = 7;

-- Immediate transfers (potential system issues)
SELECT * FROM calls WHERE type = 14 ORDER BY call_date DESC;
```

This system provides a **complete picture** of call interactions while maintaining **simplicity and performance**. 