# Google Cloud Storage Integration

This document explains how to use the Google Cloud Storage integration to access audio files and other content from GCP buckets.

## Setup

### 1. Create a GCP Service Account

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "IAM & Admin" > "Service Accounts"
3. Click "Create Service Account"
4. Give it a name and description
5. Assign the "Storage Object Viewer" role (or more permissions if needed)
6. Create a JSON key for the service account
7. Download the key file and store it securely

### 2. Configure Environment Variables

You have two options for authentication:

#### Option 1: Use Individual Credential Values (Recommended)

Extract the necessary values from your service account JSON key file and add them to your `.env.local` file:

```
# GCP Storage Configuration
GCP_PROJECT_ID="your-project-id"
GCP_SERVICE_ACCOUNT_CLIENT_EMAIL="<EMAIL>"
GCP_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key content here...\n-----END PRIVATE KEY-----"
GCP_AUDIO_BUCKET_NAME="your-audio-bucket-name"
```

Make sure to preserve the newlines in the private key by using `\n` characters.

#### Option 2: Use Application Default Credentials

If the individual credential values are not configured, the system will fall back to Application Default Credentials:

1. Install the [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
2. Run `gcloud auth application-default login`

## Usage

### Backend API

The application provides API endpoints for accessing files in GCP buckets:

#### General Audio Files API

```
GET /api/storage/audio-files
```

**Query Parameters:**
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `prefix` (optional): Filter files by prefix
- `fileName` (optional): Get a signed URL for a specific file
- `expiresInMinutes` (optional): Expiration time for signed URLs (default: 15)
- `audioOnly` (optional): Filter for audio files only (default: false)

**Examples:**

List all files in the default bucket:
```
GET /api/storage/audio-files
```

List audio files with a specific prefix:
```
GET /api/storage/audio-files?prefix=calls/2023/&audioOnly=true
```

Get a signed URL for a specific file:
```
GET /api/storage/audio-files?fileName=calls/recording123.mp3
```

#### Session Audio API

Retrieve the latest audio file for a specific session ID:

```
GET /api/storage/session-audio
```

**Query Parameters:**
- `sessionId` (required): The session ID to find audio for
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `minTimestamp` (optional): Only return files with timestamps after this value (in milliseconds)

**Example:**

Get the latest audio file for a session:
```
GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA
```

Get audio files after a specific timestamp:
```
GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA&minTimestamp=****************
```

#### Session Audio All API

Retrieve all audio files for a specific session ID as an array of interactions:

```
GET /api/storage/session-audio-all
```

**Query Parameters:**
- `sessionId` (required): The session ID to find audio for
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `minTimestamp` (optional): Only return files with timestamps after this value (in milliseconds)

**Example:**

Get all audio files for a session:
```
GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA
```

Get all audio files for a session after a specific timestamp:
```
GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA&minTimestamp=****************
```

**Response Format:**
```json
[
  {
    "text": "Patient: I need to schedule an appointment",
    "recordUrl": "gcp://bucket-name/audio/session123_1609545600000.mp3"
  },
  {
    "text": "Agent: I can help you with that. What day works for you?",
    "recordUrl": "gcp://bucket-name/audio/session123_1609545660000.mp3"
  }
]
```

**Note:** The `recordUrl` is in the format `gcp://bucket-name/file-path` which can be passed directly to the `/api/storage/convert-audio` endpoint for conversion to a web-compatible format.

### Frontend Components

#### AudioFilesList Component

The application includes a reusable `AudioFilesList` component for displaying and playing audio files:

```tsx
import AudioFilesList from '@/components/AudioFilesList';

// In your component:
<AudioFilesList
  bucketName="your-bucket-name"
  prefix="optional/prefix/"
/>
```

#### SessionAudioPlayer Component

For playing the latest audio file from a specific session ID:

```tsx
import SessionAudioPlayer from '@/components/SessionAudioPlayer';

// In your component:
<SessionAudioPlayer
  sessionId="0650dnenrFtRsK9R-fHCONQCA"
  minTimestamp={****************} // Optional: minimum timestamp in milliseconds
  autoPlay={true} // Optional: auto-play the audio when loaded
  onError={(error) => console.error(error)} // Optional: error handler
/>
```

This component will automatically fetch and play the latest audio file for the specified session ID. It's particularly useful for displaying call recordings in your application.

### Direct API Usage

You can also use the GCP Storage service directly in your code:

```typescript
import { getGcpStorageService } from '@/utils/gcp-storage';

// Get the storage service
const storageService = getGcpStorageService();

// List files in a bucket
const files = await storageService.listFiles('your-bucket-name', { prefix: 'optional/prefix/' });

// Get a signed URL for a file
const url = await storageService.getSignedUrl('your-bucket-name', 'path/to/file.mp3');

// Download a file
const fileBuffer = await storageService.downloadFile('your-bucket-name', 'path/to/file.mp3');

// Get the latest audio file for a specific session ID
const audioFile = await storageService.getLatestAudioFileForSession(
  'your-bucket-name',
  '0650dnenrFtRsK9R-fHCONQCA',
  **************** // Optional: minimum timestamp in milliseconds
);

if (audioFile) {
  console.log('File name:', audioFile.fileName);
  console.log('Audio URL:', audioFile.url);
}

// Get all audio files for a specific session ID
const audioFiles = await storageService.getAllAudioFilesForSession(
  'your-bucket-name',
  '0650dnenrFtRsK9R-fHCONQCA',
  **************** // Optional: minimum timestamp in milliseconds
);

if (audioFiles.length > 0) {
  console.log('Found', audioFiles.length, 'audio files');
  audioFiles.forEach((file, index) => {
    console.log(`Interaction ${index + 1}:`);
    console.log('  Text:', file.text);
    console.log('  URL:', file.url);
  });
}
```

## Security Considerations

- The API endpoint requires authentication
- Signed URLs have a limited lifetime (default: 15 minutes)
- Service accounts should have the minimum required permissions
- Keep your service account key secure and never commit it to version control
