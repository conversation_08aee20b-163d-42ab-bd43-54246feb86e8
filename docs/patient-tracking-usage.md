# Patient Tracking Usage Guide

This document provides information on how to use the Patient Tracking functionality in the Front Desk Staff Portal.

## Overview

The Patient Tracking system allows you to:

1. Create patients in external provider systems (e.g., Nextech)
2. Store references to these patients in Firestore
3. Retrieve patient information by provider ID

Nextech remains the source of truth for patient data. The Firestore database only stores references to patients created through our service.

## API Endpoints

### Create a Patient

**Endpoint:** `POST /api/external-api/v2/patients`

**Authentication:** API Key Required

**Request Body:**

```json
{
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "dateOfBirth": "1980-01-01",
  "email": "<EMAIL>",
  "phoneNumber": "************",
  "address": {
    "line1": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "postalCode": "12345",
    "country": "US"
  },
  "gender": "male"
}
```

**Response:**

The response will be the created patient in the external provider's format. A patient reference will be automatically created in Firestore.

### Get a Patient by Provider ID

**Endpoint:** `GET /api/patients/tracking?provider=nextech&providerId=ext-123`

**Authentication:** Required

**Query Parameters:**

- `provider` (required): The name of the provider (e.g., "nextech")
- `providerId` (required): The ID of the patient in the provider's system

**Response:**

```json
{
  "id": "ref-123",
  "provider": "nextech",
  "providerId": "ext-123",
  "firstName": "John",
  "lastName": "Doe",
  "dateOfBirth": "1980-01-01",
  "email": "<EMAIL>",
  "phoneNumber": "************",
  "createdAt": "2023-06-01T12:00:00.000Z",
  "updatedAt": "2023-06-01T12:00:00.000Z"
}
```

## Using the Services Directly

### PatientReferenceService

The `PatientReferenceService` provides methods for managing patient references in Firestore:

```typescript
import { patientFactory } from '@/lib/factories/patient-factory';

// Get the service
const patientReferenceService = patientFactory.getPatientReferenceService();

// Store a new patient reference
const patientReference = await patientReferenceService.storeNewPatient({
  provider: 'nextech',
  providerId: 'ext-123',
  firstName: 'John',
  lastName: 'Doe',
  dateOfBirth: '1980-01-01',
  email: '<EMAIL>',
  phoneNumber: '************',
});

// Find a patient reference by provider ID
const foundReference = await patientReferenceService.findByProviderId('nextech', 'ext-123');

// Update a patient reference if data has changed
const updatedReference = await patientReferenceService.updateIfChanged(patientReference);
```

### PatientCoordinatorService

The `PatientCoordinatorService` coordinates between external providers and local storage:

```typescript
import { patientFactory } from '@/lib/factories/patient-factory';

// Get the service
const patientCoordinatorService = patientFactory.getPatientCoordinatorService();

// Store a reference to a patient
const patientReference = await patientCoordinatorService.storePatientReference({
  provider: 'nextech',
  providerId: 'ext-123',
  firstName: 'John',
  lastName: 'Doe',
  dateOfBirth: '1980-01-01',
  email: '<EMAIL>',
  phoneNumber: '************',
});

// Get a patient by provider ID
const foundReference = await patientCoordinatorService.getPatientByProviderId('nextech', 'ext-123');
```

## Important Notes

1. **Nextech is the source of truth** for patient data. The Firestore database only stores references to patients created through our service.

2. **No background sync jobs** are implemented. References are only updated when explicitly requested through the API.

3. **Only track patients created through our service**. This system is not intended to track all patients in the external provider system.

4. **Security model**:
   - Patient references can only be created or updated through the server-side API
   - Direct client-side access to create or update patient references is not allowed
   - Only authenticated users can read patient references
   - Only admin users can delete patient references
