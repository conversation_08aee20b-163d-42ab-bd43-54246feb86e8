# External API Usage Guide

This document describes how to use the Front Desk Portal External API for third-party integrations.

## Authentication

All API endpoints require an API key for authentication:

```
x-api-key: YOUR_API_KEY
```

Contact the system administrator to obtain your API key.

## Base URL

### V1 API (Legacy)

```
https://your-domain.com/api/external-api/v1
```

### V2 API (Current)

```
https://your-domain.com/api/external-api/v2
```

## Pagination (V2)

All V2 list endpoints support standardized pagination with the following parameters:

```
limit: Number of items per page (default: 10, max: 50)
offset: Starting position for results (default: 0)
```

Example:
```
GET /api/external-api/v2/patients?limit=20&offset=40
```

Pagination responses include the following structure:

```json
{
  "items": [...],  // Array of items for the current page
  "pagination": {
    "totalCount": 100,  // Total number of items
    "limit": 20,        // Current limit
    "offset": 40,       // Current offset
    "hasMore": true,    // Whether more items exist
    "links": {
      "first": "/api/external-api/v2/patients?limit=20&offset=0",
      "prev": "/api/external-api/v2/patients?limit=20&offset=20",
      "next": "/api/external-api/v2/patients?limit=20&offset=60",
      "last": "/api/external-api/v2/patients?limit=20&offset=80"
    }
  }
}
```

## Endpoints

## Code Examples

### JavaScript/Node.js

```javascript
// Example for using the external API
const fetchData = async () => {
  const response = await fetch('https://your-domain.com/api/external-api/v1/', {
    method: 'GET',
    headers: {
      'x-api-key': 'YOUR_API_KEY'
    }
  });
  
  const data = await response.json();
  console.log(data);
};
```

### Pagination Example (V2 API)

```javascript
// Example for using pagination with the V2 API
const fetchPaginatedData = async (limit = 10, offset = 0) => {
  const response = await fetch(
    `https://your-domain.com/api/external-api/v2/patients?limit=${limit}&offset=${offset}`, 
    {
      method: 'GET',
      headers: {
        'x-api-key': 'YOUR_API_KEY'
      }
    }
  );
  
  const data = await response.json();
  
  // Process current page of data
  console.log(`Showing items ${offset+1}-${offset+data.items.length} of ${data.pagination.totalCount}`);
  
  // To load next page if available
  if (data.pagination.hasMore) {
    const nextPageUrl = data.pagination.links.next;
    console.log(`Next page available at: ${nextPageUrl}`);
  }
  
  return data;
};
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error messages when applicable:

- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid API key
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side error

Error responses include a message field and sometimes an errors array with specific validation errors:

```json
{
  "message": "Invalid input data",
  "errors": [
    "clientId is required",
    "date must be a valid date format"
  ]
}
``` 