# Firestore ➜ MySQL Migration Guide

> **Status:** draft – validated on dev-cursor branch, June 2025

## ☑️ Prerequisites

1. **MySQL database** created and reachable.
2. `.env.local` populated with ⬇️ 
   ```dotenv
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_DATABASE=frontdesk
   MYSQL_USER=root
   MYSQL_PASSWORD=secret
   FIREBASE_PROJECT_ID=frontdeskdev
   FIREBASE_CLIENT_EMAIL=…
   FIREBASE_PRIVATE_KEY="…"
   ```
3. Install dependencies & build:
   ```bash
   pnpm install
   ```
4. Install the database schema (initial tables and recent patches):
   ```bash
   pnpm knex migrate:latest
   ```

---

## 🏗️ 0. Create / update MySQL tables

Run the migration command below.  It examines `migrations/*.js` and brings the database up-to-date by **creating any missing tables or altering existing ones**.

```bash
pnpm knex migrate:latest   # creates/updates all tables
```

If you ever need to roll back:

```bash
# undo last batch
pnpm knex migrate:rollback

# undo _all_ migrations (danger!)
pnpm knex migrate:down
```

Only after the schema is current should you proceed with wiping/filling data.

---

## 🧹 1. Clear existing MySQL data (optional)
If you want a **fresh import**, wipe current rows (FK checks are disabled temporarily):
```bash
pnpm node scripts/clear-mysql-tables.js
```
> ⚠️ Take a backup first if the DB contains valuable data:
> ```bash
> mysqldump -h $MYSQL_HOST -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE > backup.sql
> ```

---

## 🚀 2. Run the Firestore → MySQL migrator
The script lives at `scripts/migrate-firestore-to-mysql.js` and supports several flags.

### Full migration
```bash
pnpm ts-node scripts/migrate-firestore-to-mysql.js
```

### Migrate a subset of collections
```bash
# Only locations & clients
pnpm ts-node scripts/migrate-firestore-to-mysql.js --collections=locations,clients
```

### Validate without writing (dry-run)
```bash
pnpm ts-node scripts/migrate-firestore-to-mysql.js --dryRun
```

### Other useful flags
| Flag | Default | Purpose |
|------|---------|---------|
| `--batchSize` | `100` | Number of docs written per TX |
| `--callsLimit` | `500` | Max docs for large `calls` / `callSessions` |
| `--resumeFromCollection` | | Continue after a previously failed run |
| `--noBackup` | `false` | Skip automatic SQL dump before first write |

---

## 🔍 3. Verify
After completion:
```sql
SELECT COUNT(*) FROM locations;
SELECT COUNT(*) FROM clients;
```
The counts should match those shown by the migrator summary.

---

## 🆘 Troubleshooting

| Symptom | Fix |
|---------|-----|
| `Duplicate entry …` during initial inserts | Safe to ignore – `INSERT`s are wrapped in try/catch. |
| FK mismatch error for `call_sessions` | Ensure migrations were reapplied after **20250629** patch. |
| Missing `practice_name` / `office_hours` columns | Run `pnpm knex migrate:latest` again – patch **20250701** adds them. |

---

## 📝 4. Incremental sync / future migrations
Currently the script is one-off. For ongoing bi-directional sync consider:
1. Cloud Functions to forward new Firestore writes into MySQL.
2. Periodic cron job re-running the migrator with `--resumeFromCollection`.

Contributions welcome! Feel free to improve this guide as the tooling evolves. 