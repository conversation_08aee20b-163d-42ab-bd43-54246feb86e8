# Schema Migration: Version 1 to Version 2

This document outlines the schema changes from v1 to v2 and provides instructions for performing the migration.

## What's New in Version 2

1. **Multi-location Support**
   - New `Location` collection to store multiple physical locations per clinic
   - `CalendarSlot` now includes `locationId` to support scheduling at different locations

2. **Denormalized Client Name**
   - `Appointment` now includes `clientName` copied from `Client.fullName`
   - Reduces read operations when displaying appointment lists

3. **Separate Call Detail Collection**
   - Large text fields (`summary` and `transcription`) moved from `Call` to `CallDetail`
   - Reduces data transfer for frequent calls listing operations

## Migration Steps

### 1. Install Dependencies

Make sure you have the required dependencies:

```bash
pnpm install
```

### 2. Run the Migration Script

```bash
pnpm migrate
```

This will perform the following:
- Create default locations for each clinic
- Update all CalendarSlots to include locationId
- Extract summary and transcription from Call to CallDetail
- Denormalize clientName into Appointment

### 3. Update Firestore Rules

The Firestore security rules have been updated to include rules for the new collections. These are automatically deployed when you push to production.

### 4. Code Changes

The codebase has been updated to support the new schema:
- New model interfaces for `Location` and `CallDetail`
- Updated services for interacting with these collections
- Modified `CalendarSlot` to include `locationId`
- Updated `Appointment` to include `clientName`

## Rollback Plan

If issues occur, you can:

1. Keep using the v1 schema fields which are still available
2. Manually restore data from backups if necessary

## Monitoring

After migrating:

1. Monitor application errors in the logs
2. Watch Firestore read/write operations to verify improved performance
3. Check that appointment listings display client names correctly
4. Verify that calls with large transcripts load properly

## Additional Resources

- [Database Schema v2](../models/database-schema-v2.yaml)
- [Migration Script](../scripts/migrate-schema-v1-to-v2.ts) 