# Database Schema Documentation

## Office Hours & On-Call Doctors Feature

<version>1.0.0</version>  
<last-updated>2024-01-15</last-updated>

## Overview

This document describes the complete database schema for the Office Hours & On-Call Doctors feature, including Firestore collections, indexes, security rules, and future MySQL migration reference.

## Architecture

The database schema is designed to support:
- **Agent-Location Mappings**: Link DialogFlow agents to specific clinic locations
- **On-Call Schedules**: Define when doctors are available for on-call notifications
- **On-Call Notifications**: Track SMS notifications sent to on-call doctors
- **Clinic Isolation**: Ensure data security and multi-tenancy

## Collections

### 1. Agent-Location Mappings

**Collection:** `agent-location-mappings`  
**Purpose:** Maps DialogFlow agent IDs to clinic locations for proper call routing

```typescript
interface AgentLocationMappingDoc {
  agentId: string;          // Primary key, DialogFlow agent ID
  locationId: string;       // References locations collection
  clinicId: number;         // References clinic (for security isolation)
  isActive: boolean;        // Soft delete flag
  createdAt: Timestamp;     // Auto-generated
  updatedAt: Timestamp;     // Auto-updated
}
```

#### Example Document
```json
{
  "agentId": "projects/my-project/locations/us-central1/agents/abc123",
  "locationId": "location-uuid-123",
  "clinicId": 1,
  "isActive": true,
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z"
}
```

#### Indexes
- `agentId + isActive` - Find active mapping for specific agent
- `clinicId + isActive` - List all active mappings for clinic

#### Security Rules
- **Read**: Admin users only
- **Write**: Admin users only
- **Isolation**: By clinic ID

---

### 2. On-Call Schedules

**Collection:** `on-call-schedules`  
**Purpose:** Define when doctors are available for on-call notifications

```typescript
interface OnCallScheduleDoc {
  id: string;               // Auto-generated document ID
  doctorId: string;         // References users collection
  doctorName: string;       // Denormalized for quick access
  doctorPhone: string;      // For SMS notifications (validated format)
  locationId: string;       // References locations collection
  clinicId: number;         // References clinic (for security isolation) 
  date: string;             // ISO date format YYYY-MM-DD
  startTime: string;        // 24-hour format HH:MM
  endTime: string;          // 24-hour format HH:MM
  isActive: boolean;        // Soft delete flag
  timezone: string;         // Location timezone (e.g., "America/Chicago")
  notes?: string;           // Optional notes
  createdAt: Timestamp;     // Auto-generated
  updatedAt: Timestamp;     // Auto-updated
  createdBy: string;        // User ID who created the schedule
}
```

#### Example Document
```json
{
  "id": "schedule-uuid-456",
  "doctorId": "doctor-uuid-789",
  "doctorName": "Dr. John Smith",
  "doctorPhone": "+1234567890",
  "locationId": "location-uuid-123",
  "clinicId": 1,
  "date": "2024-01-15",
  "startTime": "09:00",
  "endTime": "17:00",
  "isActive": true,
  "timezone": "America/Chicago",
  "notes": "Regular day shift",
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z",
  "createdBy": "admin-uuid-123"
}
```

#### Indexes
- `locationId + date + isActive` - Find schedules for location on specific date
- `doctorId + date + isActive` - Find doctor's schedules for specific date
- `clinicId + date + isActive` - Find all clinic schedules for date
- `locationId + date + startTime + endTime + isActive` - Find current on-call doctor

#### Security Rules
- **Read**: Admin users and clinic staff (clinic isolation)
- **Create**: Admin users only (with clinic validation)
- **Update/Delete**: Admin users only (with clinic isolation)

---

### 3. On-Call Notifications

**Collection:** `on-call-notifications`  
**Purpose:** Track SMS notifications sent to on-call doctors

```typescript
interface OnCallNotificationDoc {
  id: string;               // Auto-generated document ID
  scheduleId: string;       // References on-call-schedules collection
  callSessionId: string;    // References callSessions collection
  doctorId: string;         // References users collection
  clinicId: number;         // References clinic (for security isolation)
  notificationTime: Timestamp; // When SMS was sent
  smsMessageId?: string;    // Twilio message SID
  status: 'sent' | 'failed' | 'delivered'; // SMS delivery status
  callType?: string;        // Type of call that triggered notification
  callerPhone?: string;     // Phone number of caller
  errorMessage?: string;    // Error details if status is 'failed'
  createdAt: Timestamp;     // Auto-generated
}
```

#### Example Document
```json
{
  "id": "notification-uuid-789",
  "scheduleId": "schedule-uuid-456",
  "callSessionId": "call-uuid-123",
  "doctorId": "doctor-uuid-789",
  "clinicId": 1,
  "notificationTime": "2024-01-15T14:30:00Z",
  "smsMessageId": "SM1234567890abcdef",
  "status": "delivered",
  "callType": "urgent",
  "callerPhone": "+1234567891",
  "createdAt": "2024-01-15T14:30:00Z"
}
```

#### Indexes
- `scheduleId + createdAt` - Find notifications for specific schedule
- `doctorId + createdAt` - Find notifications for specific doctor
- `clinicId + createdAt` - Find notifications for clinic
- `callSessionId` - Find notifications for specific call

#### Security Rules
- **Read**: Admin users and clinic staff (clinic isolation)
- **Create/Update/Delete**: Server-side only (no client writes)

---

## Relationships

```mermaid
erDiagram
    AGENT-LOCATION-MAPPINGS {
        string agentId PK
        string locationId FK
        number clinicId FK
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
    }
    
    ON-CALL-SCHEDULES {
        string id PK
        string doctorId FK
        string doctorName
        string doctorPhone
        string locationId FK
        number clinicId FK
        string date
        string startTime
        string endTime
        boolean isActive
        string timezone
        string notes
        timestamp createdAt
        timestamp updatedAt
        string createdBy FK
    }
    
    ON-CALL-NOTIFICATIONS {
        string id PK
        string scheduleId FK
        string callSessionId FK
        string doctorId FK
        number clinicId FK
        timestamp notificationTime
        string smsMessageId
        string status
        string callType
        string callerPhone
        string errorMessage
        timestamp createdAt
    }
    
    LOCATIONS {
        string id PK
        string name
        number clinicId FK
        string address
        string timezone
    }
    
    USERS {
        string id PK
        string name
        string email
        string phone
        string role
        number clinicId FK
    }
    
    CALL-SESSIONS {
        string id PK
        string phoneNumber
        number clinicId FK
        string locationId FK
        timestamp startTime
    }
    
    AGENT-LOCATION-MAPPINGS ||--|| LOCATIONS : "locationId"
    ON-CALL-SCHEDULES ||--|| LOCATIONS : "locationId"
    ON-CALL-SCHEDULES ||--|| USERS : "doctorId"
    ON-CALL-NOTIFICATIONS ||--|| ON-CALL-SCHEDULES : "scheduleId"
    ON-CALL-NOTIFICATIONS ||--|| USERS : "doctorId"
    ON-CALL-NOTIFICATIONS ||--|| CALL-SESSIONS : "callSessionId"
```

## Query Patterns

### Common Queries

#### 1. Find Active Agent Mapping
```typescript
const mapping = await db.collection('agent-location-mappings')
  .where('agentId', '==', agentId)
  .where('isActive', '==', true)
  .limit(1)
  .get();
```

#### 2. Find Current On-Call Doctor
```typescript
const now = new Date();
const currentTime = now.toTimeString().slice(0, 5); // HH:MM
const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD

const schedule = await db.collection('on-call-schedules')
  .where('locationId', '==', locationId)
  .where('date', '==', currentDate)
  .where('startTime', '<=', currentTime)
  .where('endTime', '>=', currentTime)
  .where('isActive', '==', true)
  .limit(1)
  .get();
```

#### 3. Find Doctor's Schedules for Date Range
```typescript
const schedules = await db.collection('on-call-schedules')
  .where('doctorId', '==', doctorId)
  .where('date', '>=', startDate)
  .where('date', '<=', endDate)
  .where('isActive', '==', true)
  .orderBy('date')
  .get();
```

#### 4. Find Recent Notifications for Doctor
```typescript
const notifications = await db.collection('on-call-notifications')
  .where('doctorId', '==', doctorId)
  .orderBy('createdAt', 'desc')
  .limit(10)
  .get();
```

## Security Rules

### Clinic Isolation Pattern

All collections implement clinic isolation using the `belongsToSameClinic` helper function:

```javascript
function belongsToSameClinic(resourceId, collection) {
  let userClinicId = get(/databases/$(database)/documents/users/$(request.auth.uid)).data.clinicId;
  let resourceClinicId = get(/databases/$(database)/documents/$(collection)/$(resourceId)).data.clinicId;
  return userClinicId == resourceClinicId;
}
```

### Role-Based Access

- **Admin**: Full access to all data within their clinic
- **Staff**: Read-only access to schedules and notifications within their clinic
- **System**: Server-side writes to notifications only

## Data Validation

### Phone Number Format
- Must be in international format: `+1234567890`
- Regex pattern: `^\+\d{10,15}$`

### Date Format
- Must be ISO format: `YYYY-MM-DD`
- Regex pattern: `^\d{4}-\d{2}-\d{2}$`

### Time Format
- Must be 24-hour format: `HH:MM`
- Regex pattern: `^\d{2}:\d{2}$`

### Timezone Format
- Must be valid IANA timezone identifier
- Examples: `America/Chicago`, `America/New_York`, `Europe/London`

### Status Values
- `on-call-notifications.status`: `sent`, `failed`, `delivered`

## Migration Scripts

### Setup Scripts
- `scripts/setup-on-call-collections.ts` - Initialize collections with sample data
- `scripts/migrate-agent-location-mappings.ts` - Migrate existing agent mappings
- `scripts/validate-database-integrity.ts` - Validate data integrity

### Validation Scripts
- Data type validation
- Required field validation
- Reference integrity validation
- Format validation (phone, date, time)

## Future MySQL Migration Schema

### Agent Location Mappings Table
```sql
CREATE TABLE agent_location_mappings (
  agent_id VARCHAR(255) PRIMARY KEY,
  location_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_clinic_active (clinic_id, is_active),
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id)
);
```

### On-Call Schedules Table
```sql
CREATE TABLE on_call_schedules (
  id VARCHAR(255) PRIMARY KEY,
  doctor_id VARCHAR(255) NOT NULL,
  doctor_name VARCHAR(255) NOT NULL,
  doctor_phone VARCHAR(20) NOT NULL,
  location_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  timezone VARCHAR(50) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(255) NOT NULL,
  INDEX idx_location_date_active (location_id, date, is_active),
  INDEX idx_doctor_date_active (doctor_id, date, is_active),
  INDEX idx_clinic_date_active (clinic_id, date, is_active),
  INDEX idx_current_oncall (location_id, date, start_time, end_time, is_active),
  FOREIGN KEY (doctor_id) REFERENCES users(id),
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id),
  FOREIGN KEY (created_by) REFERENCES users(id)
);
```

### On-Call Notifications Table
```sql
CREATE TABLE on_call_notifications (
  id VARCHAR(255) PRIMARY KEY,
  schedule_id VARCHAR(255) NOT NULL,
  call_session_id VARCHAR(255) NOT NULL, 
  doctor_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  notification_time TIMESTAMP NOT NULL,
  sms_message_id VARCHAR(255),
  status ENUM('sent', 'failed', 'delivered') NOT NULL,
  call_type VARCHAR(50),
  caller_phone VARCHAR(20),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_schedule_created (schedule_id, created_at),
  INDEX idx_doctor_created (doctor_id, created_at),
  INDEX idx_clinic_created (clinic_id, created_at),
  INDEX idx_call_session (call_session_id),
  FOREIGN KEY (schedule_id) REFERENCES on_call_schedules(id),
  FOREIGN KEY (call_session_id) REFERENCES call_sessions(id),
  FOREIGN KEY (doctor_id) REFERENCES users(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id)
);
```

## Performance Considerations

### Index Usage
- All queries should use existing indexes
- Avoid queries that require multiple inequality filters
- Use composite indexes for multi-field queries

### Data Denormalization
- Doctor name and phone stored in schedules for quick access
- Reduces joins and improves query performance
- Trade-off: Update complexity when doctor info changes

### Cleanup Strategies
- Use soft deletes (`isActive: false`) instead of hard deletes
- Periodic cleanup of old notification records
- Archive old schedules after end date + retention period

## Backup and Recovery

### Firestore Backup
- Daily automated backups via Firebase
- Point-in-time recovery available
- Cross-region replication configured

### Data Export
- Export scripts for data migration
- JSON format for easy import/export
- Validation scripts for data integrity

## Monitoring and Alerting

### Key Metrics
- Collection document counts
- Query performance metrics
- Index usage statistics
- Failed notification rates

### Alerts
- Missing on-call schedules for active locations
- Failed SMS notifications
- Data validation errors
- Unusual query patterns

---

## Changelog

### Version 1.0.0 (2024-01-15)
- Initial database schema design
- Complete Firestore implementation
- Security rules and indexes
- MySQL migration reference
- Comprehensive documentation 