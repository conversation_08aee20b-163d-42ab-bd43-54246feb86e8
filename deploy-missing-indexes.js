const fs = require('fs');
const { exec } = require('child_process');

console.log('🚀 DEPLOYING MISSING FIRESTORE INDEXES');
console.log('======================================\n');

// Read the deploy-indexes.json file (contains only missing indexes)
if (!fs.existsSync('deploy-indexes.json')) {
  console.error('❌ deploy-indexes.json not found. Run node compare-indexes.js first.');
  process.exit(1);
}

const missingIndexes = JSON.parse(fs.readFileSync('deploy-indexes.json', 'utf8'));
console.log(`📋 Found ${missingIndexes.indexes.length} missing indexes to deploy`);

// Backup current indexes file
const backupFile = 'firestore.indexes.backup.json';
fs.copyFileSync('firestore.indexes.json', backupFile);
console.log(`💾 Backed up current indexes to ${backupFile}`);

// Replace indexes file with only missing indexes
fs.writeFileSync('firestore.indexes.json', JSON.stringify(missingIndexes, null, 2));
console.log('📝 Temporarily replaced firestore.indexes.json with missing indexes only');

// Deploy the missing indexes
console.log('\n🔄 Deploying missing indexes...');
exec('firebase deploy --only firestore:indexes', (error, stdout, stderr) => {
  // Restore the original indexes file regardless of deployment result
  fs.copyFileSync(backupFile, 'firestore.indexes.json');
  fs.unlinkSync(backupFile);
  console.log('\n🔄 Restored original firestore.indexes.json');

  if (error) {
    console.error('❌ Deployment failed:');
    console.error(stderr);
    process.exit(1);
  } else {
    console.log('✅ DEPLOYMENT SUCCESSFUL!');
    console.log(stdout);
    console.log('\n🎉 All missing indexes have been deployed to GCP');
    console.log('💡 You can now use all filter combinations without FAILED_PRECONDITION errors');
  }
});
