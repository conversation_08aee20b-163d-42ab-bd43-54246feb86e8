# External API Architecture

This directory contains the implementation of the External API for integrating with various provider systems.

## Versions

- **v1**: Legacy implementation (located in `pages/api/external-api/v1`)
- **v2**: New modular implementation with provider adapter pattern

## Organization

The implementation follows Next.js best practices:

- **lib/external-api/**: Core implementation with business logic, models, and providers
- **pages/api/external-api/**: API route handlers only, which use the core implementation

This separation ensures that the `pages/` directory only contains route definitions, while all business logic, data models, and provider implementations are maintained in the `lib/` directory.

## Documentation

- [v2 Documentation](./v2/README.md): Detailed documentation for the v2 API implementation
