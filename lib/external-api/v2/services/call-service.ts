import { callsService, callSessionsService } from '@/utils/firestore';
import { Call } from '@/models/Call';
import { CallType } from '@/models/CallTypes';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { eventEmitter, EVENTS } from '../utils/events';
import logger from '../utils/logger';
import {
  getEffectiveCallDuration,
  formatDialogflowDuration,
  parseDurationToSeconds,
} from '@/utils/call-duration-utils';

/**
 * Service for managing call-related operations
 */
export class CallService {
  /**
   * Create an empty call record in Firestore without processing transcripts or making API calls
   * This is used for creating placeholder calls that will be updated later
   * @param callData Minimal call data to create
   * @param patientId Patient ID associated with the call
   * @returns Created call record
   */
  async createEmptyCall(
    callData: {
      callId?: string;
      sessionId?: string;
      phoneNumber?: string;
      reason?: string;
      notes?: string;
      hasVoiceMail?: boolean;
      type?: CallType;
      isOutboundCall?: boolean;
      agentId?: string;
      practitionerId?: string;
      clinicId?: number;
      locationId?: number;
    },
    patientId: string,
  ) {
    // Determine the correct locationId based on clinic
    let defaultLocationId = '0'; // Default for other clinics
    const clinicId = callData.clinicId || 12; // Default to clinic 12

    // For clinic 12 (University Retina), use the Lombard location
    if (clinicId === 12) {
      defaultLocationId = '118';
    }

    const callToCreate: Omit<Call, 'id'> = {
      clientId: patientId,
      userId: callData.practitionerId || 'system',
      clinicId: clinicId,
      locationId: callData.locationId || parseInt(defaultLocationId, 10),
      date: new Date(),
      recordingUrl: 'https://example.com/dummy-recording.mp3',
      reason: callData.reason || 'Incoming call',
      summary: '',
      transcription: '',
      transcriptionWithAudio: '',
      notes: callData.notes || '',
      priorityScore: 0,
      urgent: false,
      tags: [],
      phoneNumber: callData.phoneNumber || '',
      sessionId: callData.sessionId || '',
      agentId: callData.agentId || '',
      hasVoiceMail: callData.hasVoiceMail || false,
      voicemailUrl: '',
      duration: '0 min',
      type: callData.type || CallType.OTHER,
      isOutboundCall: callData.isOutboundCall || false,
    };

    const call = await callsService.createCall(callToCreate, callData.callId);
    eventEmitter.emit(EVENTS.CALL.CREATED, call);

    logger.info(
      { callId: call.id, sessionId: callData.sessionId, phoneNumber: callData.phoneNumber },
      'Created empty call record',
    );

    return call;
  }

  /**
   * Fetches transcription data for a session
   * @param sessionId The session ID to fetch transcription for
   * @returns Object containing transcription, transcriptionWithAudio and duration
   */
  async fetchTranscriptionData(sessionId: string): Promise<{
    transcription: string;
    transcriptionWithAudio: string;
    duration: string;
  }> {
    let dialogflowTranscript = '';
    let transcriptionWithAudio = '';
    let dialogflowDuration = '';

    try {
      // Get GCP API credentials from environment variables
      const projectId = process.env.GCP_PROJECT_ID;
      const locationId = process.env.GCP_LOCATION_ID || 'global';
      const agentId = process.env.GCP_AGENT_ID;

      if (!projectId || !agentId) {
        logger.warn(
          { projectId, agentId },
          'Missing required GCP configuration for fetching transcription',
        );
        return { transcription: '', transcriptionWithAudio: '', duration: '' };
      }

      // Get access token for Dialogflow API
      const accessToken = await DialogflowAuthService.getAccessToken();
      if (!accessToken) {
        throw new Error('Failed to obtain access token for Dialogflow API');
      }

      // Fetch conversation data from Dialogflow API
      const conversation = await getDialogflowConversation({
        projectId,
        locationId,
        agentId,
        sessionId,
        accessToken,
      });

      // Extract transcript from conversation data
      dialogflowTranscript = extractTranscriptFromConversation(conversation);

      // Extract and format the duration from the conversation
      if (conversation.duration) {
        dialogflowDuration = formatDialogflowDuration(conversation.duration);
        if (dialogflowDuration) {
          logger.info(
            { sessionId, duration: dialogflowDuration },
            `Extracted call duration from Dialogflow`,
          );
        }
      }

      // Get transcription with audio
      try {
        const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
        if (bucketName) {
          const storageService = getGcpStorageService();
          const interactions = await storageService.getTranscriptWithAudioRecords(
            bucketName,
            sessionId,
            undefined,
          );
          logger.info(
            { sessionId, interactionsCount: interactions.length },
            `Retrieved audio transcription data`,
          );

          if (interactions && interactions.length > 0) {
            // Stringify the interactions array to store in the call record
            transcriptionWithAudio = JSON.stringify(interactions);
          }
        } else {
          logger.warn('Missing GCP_AUDIO_BUCKET_NAME environment variable');
        }
      } catch (audioError) {
        logger.warn({ error: audioError, sessionId }, `Failed to get transcription with audio`);
      }

      logger.info({ sessionId }, `Successfully retrieved transcription data`);

      // Calculate effective duration from transcript interactions as fallback
      let effectiveDurationSeconds = 0;
      let effectiveDuration = '';

      if (dialogflowDuration) {
        // If we have Dialogflow duration, convert it to seconds for consistency
        effectiveDurationSeconds = parseDurationToSeconds(dialogflowDuration);
        effectiveDuration = dialogflowDuration;
      } else if (transcriptionWithAudio) {
        // If no Dialogflow duration, calculate from transcript interactions
        effectiveDurationSeconds = getEffectiveCallDuration({
          duration: '',
          transcriptionWithAudio,
        });

        if (effectiveDurationSeconds > 0) {
          effectiveDuration =
            effectiveDurationSeconds < 60
              ? `${effectiveDurationSeconds} sec`
              : `${(effectiveDurationSeconds / 60).toFixed(1)} min`;

          logger.info(
            { sessionId, originalDuration: '0 min', effectiveDuration },
            'Calculated effective duration from transcript analysis',
          );
        }
      }

      return {
        transcription: dialogflowTranscript,
        transcriptionWithAudio,
        duration: effectiveDuration,
      };
    } catch (error) {
      logger.error({ error, sessionId }, 'Error retrieving transcription data');
      return { transcription: '', transcriptionWithAudio: '', duration: '' };
    }
  }

  /**
   * Create a call record in Firestore
   * @param callData Call data to create
   * @param patientId Patient ID associated with the call
   * @param practitionerId Practitioner ID associated with the call
   * @param appointmentData Optional appointment data to use for defaults
   * @returns Created call record
   */
  async createCall(
    callData: {
      sessionId?: string;
      practitionerId?: string;
      clinicId?: number;
      locationId?: number;
      date?: string;
      recordingUrl?: string;
      reason?: string;
      notes?: string;
      summary?: string;
      transcription?: string;
      transcriptionWithAudio?: string;
      priorityScore?: number;
      urgent?: boolean;
      tags?: string[];
      phoneNumber?: string;
      hasVoiceMail?: boolean;
      voicemailUrl?: string;
      type?: CallType;
      callTypes?: CallType[];
      isOutboundCall?: boolean;
      agentId?: string;
    },
    patientId: string,
    practitionerId?: string,
    appointmentData?: {
      practitionerId?: string;
      clinicId?: string;
      locationId: string;
      reason?: string;
    },
  ) {
    // 1. Get Dialogflow conversation data if sessionId is provided
    let dialogflowTranscript = '';
    let transcriptionWithAudio = '';
    let dialogflowDuration = '';

    if (callData.sessionId) {
      try {
        // Get GCP API credentials from environment variables with better fallbacks
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (!projectId) {
          console.warn('Missing GCP_PROJECT_ID environment variable');
        }

        if (!agentId) {
          console.warn('Missing GCP_AGENT_ID environment variable');
        }

        if (projectId && agentId) {
          try {
            console.log(
              `Fetching Dialogflow conversation data for session ID: ${callData.sessionId}`,
            );

            // Get access token for Dialogflow API using auth service
            const accessToken = await DialogflowAuthService.getAccessToken();

            if (!accessToken) {
              throw new Error('Failed to obtain access token for Dialogflow API');
            }

            // Fetch conversation data from Dialogflow API
            const conversation = await getDialogflowConversation({
              projectId,
              locationId,
              agentId,
              sessionId: callData.sessionId,
              accessToken,
            });

            // Extract transcript, from conversation data
            dialogflowTranscript = extractTranscriptFromConversation(conversation);

            // Extract and format the duration from the conversation
            if (conversation.duration) {
              dialogflowDuration = formatDialogflowDuration(conversation.duration);
              if (dialogflowDuration) {
                console.log(
                  `Extracted call duration: ${dialogflowDuration} from ${conversation.duration}`,
                );
              }
            }

            // Get transcription with audio using the service method
            try {
              const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
              if (bucketName) {
                const storageService = getGcpStorageService();
                const interactions = await storageService.getTranscriptWithAudioRecords(
                  bucketName,
                  callData.sessionId,
                  undefined,
                );

                if (interactions && interactions.length > 0) {
                  // Stringify the interactions array to store in the call record
                  transcriptionWithAudio = JSON.stringify(interactions);
                }
              }
            } catch (audioError) {
              console.warn(
                `Failed to get transcription with audio for session ${callData.sessionId}:`,
                audioError instanceof Error ? audioError.message : String(audioError),
              );
              // Continue without audio data - it's not critical to creating the call record
            }

            // Calculate effective duration from transcript interactions as fallback
            let effectiveDurationSeconds = 0;
            if (dialogflowDuration) {
              // If we have Dialogflow duration, convert it to seconds for consistency
              effectiveDurationSeconds = parseDurationToSeconds(dialogflowDuration);
            } else if (transcriptionWithAudio) {
              // If no Dialogflow duration, calculate from transcript interactions
              effectiveDurationSeconds = getEffectiveCallDuration({
                duration: '',
                transcriptionWithAudio,
              });

              if (effectiveDurationSeconds > 0) {
                dialogflowDuration =
                  effectiveDurationSeconds < 60
                    ? `${effectiveDurationSeconds} sec`
                    : `${(effectiveDurationSeconds / 60).toFixed(1)} min`;

                console.log(
                  `Calculated call duration from transcript: ${dialogflowDuration} (${effectiveDurationSeconds} seconds)`,
                );
              }
            }

            console.log(
              `Successfully retrieved Dialogflow conversation data for ID ${callData.sessionId}`,
            );
          } catch (dialogflowError) {
            // Handle authentication and other Dialogflow-specific errors gracefully
            console.error(
              'Failed to fetch Dialogflow conversation:',
              dialogflowError instanceof Error ? dialogflowError.message : String(dialogflowError),
            );
            console.log('Continuing with call creation without Dialogflow data');
            // Don't throw, just continue with empty transcript and summary
          }
        } else {
          console.warn(
            'Missing required GCP configuration. Make sure GCP_PROJECT_ID and GCP_AGENT_ID are set in environment variables.',
          );
        }
      } catch (error) {
        console.error(
          'Error retrieving Dialogflow conversation data:',
          error instanceof Error ? error.message : String(error),
        );
        // Continue without Dialogflow data - it's not critical to creating the call record
      }
    }

    // 2. Find sessionId if not provided but phone number is available
    let sessionId = callData.sessionId;
    const phoneNumber = callData.phoneNumber;
    let callType = callData.type;
    let callTypes = callData.callTypes; // Get callTypes array from callData

    if ((!sessionId || sessionId === '') && phoneNumber) {
      try {
        // Find the most recent call session by phone number
        const callSessionsByPhone = await callSessionsService.findCallSessionsByPhone(phoneNumber);
        if (callSessionsByPhone && callSessionsByPhone.length > 0) {
          // Use the most recent session (assuming list is sorted by date descending)
          const mostRecentSession = callSessionsByPhone[0];
          sessionId = mostRecentSession.sessionId;

          // If call type is not provided, use the one from the session
          if (callType === undefined && mostRecentSession.callType !== undefined) {
            callType = mostRecentSession.callType;
            console.log(`Using call type ${callType} from session ${sessionId}`);
          }

          // If callTypes array is not provided, use the one from the session
          if (!callTypes && mostRecentSession.callTypes) {
            callTypes = mostRecentSession.callTypes;
            console.log(`Using call types ${JSON.stringify(callTypes)} from session ${sessionId}`);
          }

          console.log(`Found session ID ${sessionId} for phone number ${phoneNumber}`);
        } else {
          console.warn(`No call sessions found for phone number ${phoneNumber}`);
        }
      } catch (error) {
        console.warn(`Failed to find session ID for phone number ${phoneNumber}:`, error);
        // Continue with empty sessionId - it's not critical to creating the call record
      }
    } else if (sessionId) {
      // If we have a sessionId but no call type/types, try to get them from the session
      if (callType === undefined || !callTypes) {
        try {
          const session = await callSessionsService.getCallSessionBySessionId(sessionId);
          if (session) {
            if (callType === undefined && session.callType !== undefined) {
              callType = session.callType;
              console.log(`Using call type ${callType} from session ${sessionId}`);
            }
            if (!callTypes && session.callTypes) {
              callTypes = session.callTypes;
              console.log(
                `Using call types ${JSON.stringify(callTypes)} from session ${sessionId}`,
              );
            }
          }
        } catch (error) {
          console.warn(`Failed to get call type/types from session ${sessionId}:`, error);
          // Continue without call type - it's not critical to creating the call record
        }
      }
    }

    // 3. Create and save the call
    // Fill in required fields with defaults if not provided

    // Determine the correct locationId based on clinic
    let defaultLocationId = '0'; // Default for other clinics
    const clinicId = callData.clinicId || parseInt(appointmentData?.clinicId || '12', 10);

    // For clinic 12 (University Retina), use the Lombard location
    if (clinicId === 12) {
      defaultLocationId = '118';
    }

    const callToCreate: Omit<Call, 'id'> = {
      clientId: patientId, // Use patient ID as client ID
      userId: callData.practitionerId || appointmentData?.practitionerId || 'system', // Default to "system" if not provided
      clinicId: clinicId, // Use the clinicId we determined above
      locationId:
        callData.locationId || parseInt(appointmentData?.locationId || defaultLocationId, 10), // Use appointment locationId or clinic-specific default
      date: callData.date ? new Date(callData.date) : new Date(), // Use current date if not provided
      recordingUrl: callData.recordingUrl || 'https://example.com/dummy-recording.mp3', // Dummy URL if not provided
      reason: callData.reason || appointmentData?.reason || 'Appointment scheduling', // Use appointment reason or default

      // Use provided values or Dialogflow data
      summary: callData.summary || '',
      transcription: callData.transcription || dialogflowTranscript || '',
      transcriptionWithAudio: callData.transcriptionWithAudio || transcriptionWithAudio || '',

      // Include the rest of the data with defaults for all potentially undefined fields
      notes: callData.notes || '',
      priorityScore: callData.priorityScore || 0,
      urgent: callData.urgent || false,
      tags: callData.tags || [],
      phoneNumber: phoneNumber || '', // Add patient's phone number to the call record
      sessionId: sessionId || '', // Use the session ID from payload or found from phone number
      agentId: callData.agentId || '', // Add agent ID to the call record
      hasVoiceMail: callData.hasVoiceMail || false,
      voicemailUrl: callData.voicemailUrl || '',
      duration: dialogflowDuration || '0 min', // Use provided duration, or extract from Dialogflow, or default to "0 min"
      type: callType, // Use the call type from the call data or session
      callTypes: callTypes, // Use the call types array from the call data or session
      isOutboundCall: callData.isOutboundCall || false,
    };

    const call = await callsService.createCall(callToCreate);

    // Duration calculation is now handled earlier in the Dialogflow processing
    // No need for duplicate calculation here

    eventEmitter.emit(EVENTS.CALL.CREATED, call);

    return call;
  }

  /**
   * Find a call session by phone number
   * @param phoneNumber Phone number to search for
   * @returns Most recent call session for the phone number
   */
  async findCallSessionByPhone(phoneNumber: string) {
    const callSessions = await callSessionsService.findCallSessionsByPhone(phoneNumber);
    if (callSessions && callSessions.length > 0) {
      return callSessions[0]; // Return the most recent session
    }
    return null;
  }
}

// Singleton instance
export const callService = new CallService();
