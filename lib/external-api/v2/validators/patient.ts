import { z } from 'zod';

/**
 * Schema for GET /api/external-api/v2/patients query parameters
 */
export const getPatientQuerySchema = z.object({
  id: z.string().optional(),
  fullName: z.string().optional(),
  dateOfBirth: z.string().optional(), // Format: YYYY-MM-DD
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
  sessionId: z.string().optional(),
});

export type GetPatientQueryParams = z.infer<typeof getPatientQuerySchema>;

/**
 * Schema for insurance information when creating a patient
 * patientId is omitted because it will be added automatically during creation
 */
const insuranceSchema = z.object({
  companyName: z.string().min(1, 'Insurance company name is required'),
  memberId: z.string().min(1, 'Member ID is required'),
  groupNumber: z.string().optional(),
  planName: z.string().optional(),
  isPrimary: z.boolean().default(true),
  subscriberName: z.string().optional(),
  subscriberRelationship: z.string().optional(),
  effectiveDate: z.string().optional(),
  expirationDate: z.string().optional(),
  copay: z.number().optional(),
  // patientId will be added by the service
});

/**
 * Schema for POST /api/external-api/v2/patients request body
 * Based on Nextech API requirements: first name, last name, email address,
 * birthdate, zip code, and at least one phone number are required.
 */
export const createPatientSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  dateOfBirth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'Date of birth must be in YYYY-MM-DD format',
  }),
  gender: z.string().optional(),
  email: z.string().email('Valid email address is required'),
  phoneNumber: z.string().min(10, 'Phone number is required'),
  address: z.object({
    line1: z.string().min(1, 'Address line 1 is required'),
    line2: z.string().optional(),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(1, 'State is required'),
    postalCode: z.string().min(1, 'Zip code is required'),
    country: z.string().default('US'),
  }),
  patientTypeId: z.string().optional(),
  referralSourceId: z.string().optional(),
  notes: z.string().optional(),
  insurances: z.array(insuranceSchema).optional(),
});

export type CreatePatientParams = z.infer<typeof createPatientSchema>;

/**
 * Schema for PATCH /api/external-api/v2/patients/[id] request body
 */
export const updatePatientSchema = z
  .object({
    firstName: z.string().min(1, 'First name is required').optional(),
    lastName: z.string().min(1, 'Last name is required').optional(),
    dateOfBirth: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, {
        message: 'Date of birth must be in YYYY-MM-DD format',
      })
      .optional(),
    phoneNumber: z.string().min(10, 'Phone number must be at least 10 digits').optional(),
    notes: z.string().optional(),
  })
  .refine(data => Object.keys(data).length > 0, {
    message: 'At least one field must be provided for update',
  });

export type UpdatePatientParams = z.infer<typeof updatePatientSchema>;
