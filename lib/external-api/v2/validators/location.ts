import { z } from 'zod';

/**
 * Schema for GET /api/external-api/v2/locations query parameters
 */
export const getLocationQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  clinicId: z.string().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

export type GetLocationQueryParams = z.infer<typeof getLocationQuerySchema>;
