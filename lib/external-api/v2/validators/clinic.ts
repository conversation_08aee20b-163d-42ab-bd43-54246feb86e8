import { z } from 'zod';

/**
 * Schema for GET /api/external-api/v2/clinics query parameters
 */
export const getClinicQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

export type GetClinicQueryParams = z.infer<typeof getClinicQuerySchema>;
