import { z } from 'zod';
import { AppointmentStatus } from '../models/types';

/**
 * Schema for GET /api/external-api/v2/appointments query parameters
 */
export const getAppointmentQuerySchema = z.object({
  id: z.string().optional(),
  patientId: z.string().optional(),
  providerId: z.string().optional(),
  practitionerId: z.string().optional(),
  locationId: z.string().optional(),
  clinicId: z.string().optional(),
  startDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(), // YYYY-MM-DD
  endDate: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/)
    .optional(), // YYYY-MM-DD
  status: z.nativeEnum(AppointmentStatus).optional(),
  type: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
  identifier: z.string().transform(Number).optional(),
  sessionId: z.string().optional(),
});

export type GetAppointmentQueryParams = z.infer<typeof getAppointmentQuerySchema>;

/**
 * Schema for POST /api/external-api/v2/appointments request body
 */
export const createAppointmentSchema = z
  .object({
    patientId: z.string().min(1, 'Patient ID is required'),
    practitionerId: z.string().min(1, 'Practitioner ID is required'),
    locationId: z.string().min(1, 'Location ID is required'),
    clinicId: z.string().optional(),
    startTime: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
        message: 'Start time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
      }),
    endTime: z.string().regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
      message: 'End time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
    }),
    type: z.string().min(1, 'Appointment type is required'),
    reason: z.string().optional(),
    notes: z.string().optional(),
  })
  .refine(data => new Date(data.startTime) < new Date(data.endTime), {
    message: 'End time must be after start time',
    path: ['endTime'],
  });

export type CreateAppointmentParams = z.infer<typeof createAppointmentSchema>;

/**
 * Schema for PATCH /api/external-api/v2/appointments/[id] request body
 */
export const updateAppointmentSchema = z
  .object({
    patientId: z.string().optional(),
    practitionerId: z.string().optional(),
    locationId: z.string().optional(),
    clinicId: z.string().optional(),
    startTime: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
        message: 'Start time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
      })
      .optional(),
    endTime: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
        message: 'End time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
      })
      .optional(),
    status: z.nativeEnum(AppointmentStatus).optional(),
    type: z.string().optional(),
    reason: z.string().optional(),
    notes: z.string().optional(),
    sessionId: z.string().optional(),
  })
  .refine(data => Object.keys(data).length > 0, {
    message: 'At least one field must be provided for update',
  })
  .refine(
    data => {
      if (data.startTime && data.endTime) {
        return new Date(data.startTime) < new Date(data.endTime);
      }
      return true;
    },
    {
      message: 'End time must be after start time',
      path: ['endTime'],
    },
  );

export type UpdateAppointmentParams = z.infer<typeof updateAppointmentSchema>;

/**
 * Schema for DELETE /api/external-api/v2/appointments/[id] query parameters
 */
export const cancelAppointmentQuerySchema = z.object({
  id: z.string({ required_error: 'Appointment ID is required' }),
  reason: z.string().optional(),
  provider: z.string().optional(),
  sessionId: z.string().optional(),
});

export type CancelAppointmentQueryParams = z.infer<typeof cancelAppointmentQuerySchema>;

/**
 * Schema for POST /api/external-api/v2/appointments/[id]/confirm query parameters
 */
export const confirmAppointmentQuerySchema = z.object({
  id: z.string({ required_error: 'Appointment ID is required' }),
  provider: z.string().optional(),
  sessionId: z.string().optional(),
});

export type ConfirmAppointmentQueryParams = z.infer<typeof confirmAppointmentQuerySchema>;

/**
 * Schema for GET /api/external-api/v2/appointment-types query parameters
 */
export const appointmentTypesQuerySchema = z.object({
  id: z.string().optional(),
  practitionerId: z.string().optional(),
  locationId: z.string().optional(),
  clinicId: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

export type AppointmentTypesQueryParams = z.infer<typeof appointmentTypesQuerySchema>;

/**
 * Schema for GET /api/external-api/v2/appointment-availability query parameters
 */
export const appointmentAvailabilityQuerySchema = z
  .object({
    appointmentTypeId: z.string().optional(),
    practitionerId: z.string().optional(),
    locationId: z.string().optional(),
    startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, {
      message: 'Start date must be in format: YYYY-MM-DD',
    }),
    endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, {
      message: 'End date must be in format: YYYY-MM-DD',
    }),
    provider: z.string().optional(),
    sessionId: z.string().optional(),
  })
  .refine(
    data => {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      return startDate <= endDate;
    },
    {
      message: 'End date must be equal to or after start date',
      path: ['endDate'],
    },
  );

export type AppointmentAvailabilityQueryParams = z.infer<typeof appointmentAvailabilityQuerySchema>;

/**
 * Schema for GET /api/external-api/v2/appointments/for-rescheduling query parameters
 */
export const getAppointmentsForPatientQuerySchema = z.object({
  patientId: z.string({ required_error: 'Patient ID is required' }),
  isForRescheduling: z
    .union([z.literal('true'), z.literal('false'), z.literal('1'), z.literal('0')])
    .transform(val => val === 'true' || val === '1')
    .optional()
    .default('false'),
  provider: z.string().optional(),
  sessionId: z.string().optional(),
});

export type GetAppointmentsForPatientQueryParams = z.infer<
  typeof getAppointmentsForPatientQuerySchema
>;
