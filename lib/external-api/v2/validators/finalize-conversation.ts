import { z } from 'zod';

/**
 * Schema for POST /api/external-api/v2/finalize-conversation request body
 */
export const finalizeConversationSchema = z.object({
  // Patient information
  patient: z.object({
    id: z.string().optional(), // Optional - include if using existing patient
    firstName: z.string().min(1, 'First name is required'),
    lastName: z.string().min(1, 'Last name is required'),
    dateOfBirth: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, {
      message: 'Date of birth must be in YYYY-MM-DD format',
    }),
    gender: z.string().optional(),
    email: z.string().email('Valid email address is required'),
    phoneNumber: z.string().min(10, 'Phone number is required'),
    address: z.object({
      line1: z.string().min(1, 'Address line 1 is required'),
      line2: z.string().optional(),
      city: z.string().min(1, 'City is required'),
      state: z.string().min(1, 'State is required'),
      postalCode: z.string().min(1, 'Zip code is required'),
      country: z.string().default('US'),
    }),
    patientTypeId: z.string().optional(),
    referralSourceId: z.string().optional(),
    notes: z.string().optional(),
  }),

  // Call information
  call: z.object({
    sessionId: z.string().optional(),
    conversationId: z.string().optional(),
    practitionerId: z.string().optional(),
    clinicId: z.number().optional(),
    locationId: z.number().optional(),
    date: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
        message: 'Date must be in ISO format: YYYY-MM-DDTHH:MM:SS',
      })
      .optional(),
    reason: z.string().optional(),
    recordingUrl: z.string().optional(),
    notes: z.string().optional(),
    summary: z.string().optional(),
    transcription: z.string().optional(),
    priorityScore: z.number().optional(),
    urgent: z.boolean().optional(),
    tags: z.array(z.string()).optional(),
  }),

  // Appointment information
  appointment: z
    .object({
      practitionerId: z.string().min(1, 'Practitioner ID is required'),
      locationId: z.string().min(1, 'Location ID is required'),
      clinicId: z.string().optional(),
      startTime: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
          message: 'Start time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
        }),
      endTime: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(.\d+)?(Z|[+-]\d{2}:\d{2})?$/, {
          message: 'End time must be in ISO format: YYYY-MM-DDTHH:MM:SS',
        }),
      type: z.string().min(1, 'Appointment type is required'),
      reason: z.string().optional(),
      notes: z.string().optional(),
    })
    .refine(data => new Date(data.startTime) < new Date(data.endTime), {
      message: 'End time must be after start time',
      path: ['endTime'],
    }),
});

export type FinalizeConversationParams = z.infer<typeof finalizeConversationSchema>;
