import { z } from 'zod';

/**
 * Schema for GET /api/external-api/v2/users query parameters
 */
export const getUserQuerySchema = z.object({
  id: z.string().optional(),
  fullName: z.string().optional(),
  email: z.string().email().optional(),
  role: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

export type GetUserQueryParams = z.infer<typeof getUserQuerySchema>;
