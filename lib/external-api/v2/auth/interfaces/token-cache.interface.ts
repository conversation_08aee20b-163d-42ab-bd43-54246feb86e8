/**
 * Interface for token caching
 * Abstracts token storage mechanism (memory, redis, etc.)
 */
export interface ITokenCache {
  /**
   * Get token for provider if valid
   * @returns The token if found and valid, null otherwise
   */
  getToken(provider: string): Promise<string | null>;

  /**
   * Store token with expiration
   * @param provider The provider name
   * @param token The token to store
   * @param expiresIn Token expiration time in seconds
   */
  setToken(provider: string, token: string, expiresIn: number): Promise<void>;

  /**
   * Clear token for provider
   */
  clearToken(provider: string): Promise<void>;
}
