import axios, { AxiosError } from 'axios';
import { IAuthService } from '../interfaces/auth-service.interface';
import { ICredentialProvider } from '../interfaces/credential-provider.interface';
import { ITokenCache } from '../interfaces/token-cache.interface';
import { AuthenticationError, RateLimitError } from '../errors/auth-error';

interface TokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * Nextech-specific authentication service implementation
 * Handles OAuth 2.0 authentication with the Nextech API
 */
export class NextechAuthService implements IAuthService {
  private readonly providerName = 'nextech';
  private readonly maxRetries = 3;
  private readonly initialRetryDelayMs = 1000;
  private timeoutRefs: Set<NodeJS.Timeout> = new Set(); // Track timeouts
  private isTestMode: boolean;

  constructor(
    private readonly credentialProvider: ICredentialProvider,
    private readonly tokenCache: ITokenCache,
    options: { isTestMode?: boolean } = {},
  ) {
    this.isTestMode = options.isTestMode || false;
  }

  /**
   * Get provider name
   */
  getProviderName(): string {
    return this.providerName;
  }

  /**
   * Get a valid access token for Nextech API
   * First tries to retrieve from cache, then fetches a new one if needed
   */
  async getAccessToken(): Promise<string> {
    // Try to get token from cache first
    const cachedToken = await this.tokenCache.getToken(this.providerName);
    if (cachedToken) {
      return cachedToken;
    }

    // If no cached token, fetch a new one with retry logic
    return this.fetchTokenWithRetry();
  }

  /**
   * Attempt to fetch a token with retry logic and exponential backoff
   */
  private async fetchTokenWithRetry(): Promise<string> {
    let attempt = 0;
    let lastError: Error | null = null;

    while (attempt < this.maxRetries) {
      try {
        return await this.fetchNewToken();
      } catch (error) {
        lastError = error as Error;

        // Don't retry rate limiting errors
        if (error instanceof RateLimitError) {
          throw error;
        }

        // Skip delay in test mode
        if (!this.isTestMode) {
          // Exponential backoff for retries
          const delay = this.initialRetryDelayMs * Math.pow(2, attempt);
          await new Promise<void>(resolve => {
            const timeoutRef = setTimeout(() => {
              this.timeoutRefs.delete(timeoutRef);
              resolve();
            }, delay);
            this.timeoutRefs.add(timeoutRef);
          });
        }
        attempt++;
      }
    }

    // If we've exhausted all retries, throw the last error
    throw (
      lastError ||
      new AuthenticationError(
        'Failed to get access token after multiple attempts',
        'MAX_RETRIES_EXCEEDED',
      )
    );
  }

  /**
   * Fetch a new token from the Nextech API
   */
  private async fetchNewToken(): Promise<string> {
    try {
      // Get credentials
      const clientId = await this.credentialProvider.getCredential('NEXTECH_CLIENT_ID');
      const clientSecret = await this.credentialProvider.getCredential('NEXTECH_CLIENT_SECRET');
      const resource = await this.credentialProvider.getCredential('NEXTECH_RESOURCE');
      const baseUrl = await this.credentialProvider.getCredential('NEXTECH_BASE_URL');

      // Validate inputs to prevent injection
      this.validateInputs({ clientId, clientSecret, resource, baseUrl });

      // Make token request
      const response = await axios.post(
        `https://login.microsoftonline.com/nextech-api.com/oauth2/token`,
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: clientId,
          client_secret: clientSecret,
          resource: resource,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );

      const tokenData = response.data as TokenResponse;

      // Store token in cache
      await this.tokenCache.setToken(
        this.providerName,
        tokenData.access_token,
        tokenData.expires_in,
      );

      return tokenData.access_token;
    } catch (error) {
      this.handleAuthError(error);
      // This line is never reached as handleAuthError always throws
      // But TypeScript needs it
      throw error;
    }
  }

  /**
   * Handle authentication errors
   * Transforms various error types into standardized auth errors
   */
  private handleAuthError(error: unknown): never {
    // Handle axios errors - need to check differently since jest mock doesn't properly implement isAxiosError
    if (
      axios.isAxiosError(error) ||
      (error && typeof error === 'object' && 'isAxiosError' in error && 'response' in error)
    ) {
      const axiosError = error as AxiosError;

      // Handle rate limiting (HTTP 429)
      if (axiosError.response?.status === 429) {
        const retryAfter = parseInt(
          (axiosError.response.headers['retry-after'] as string) || '60',
          10,
        );
        throw new RateLimitError(
          'Rate limit exceeded on Nextech API authentication',
          retryAfter,
          axiosError as unknown as Error,
        );
      }

      // Handle authentication errors (HTTP 401)
      if (axiosError.response?.status === 401) {
        throw new AuthenticationError(
          `Failed to authenticate with Nextech API: ${axiosError.message}`,
          `HTTP_${axiosError.response?.status || 'UNKNOWN'}`,
          axiosError as unknown as Error,
        );
      }

      // Handle other HTTP errors
      throw new AuthenticationError(
        `HTTP error during Nextech authentication: ${axiosError.message}`,
        `HTTP_${axiosError.response?.status || 'UNKNOWN'}`,
        axiosError as unknown as Error,
      );
    }

    // Handle validation errors
    if (error instanceof Error && error.message.includes('Invalid empty value for')) {
      throw new AuthenticationError(error.message, 'INVALID_INPUT', error);
    }

    // Handle other errors
    throw new AuthenticationError(
      `Unexpected error during Nextech authentication: ${(error as Error).message}`,
      'UNKNOWN',
      error as Error,
    );
  }

  /**
   * Validate input values to prevent injection
   */
  private validateInputs(inputs: Record<string, string>): void {
    for (const [key, value] of Object.entries(inputs)) {
      if (!value || value.trim() === '') {
        throw new AuthenticationError(`Invalid empty value for ${key}`, 'INVALID_INPUT');
      }
    }
  }

  /**
   * Clear token cache
   * Used when token becomes invalid
   */
  clearTokenCache(): void {
    this.tokenCache.clearToken(this.providerName);
  }

  /**
   * Clean up all resources used by this service
   * Should be called when the service is no longer needed or during test cleanup
   */
  cleanup(): void {
    // Clean up any pending timeouts
    this.timeoutRefs.forEach(timeoutRef => clearTimeout(timeoutRef));
    this.timeoutRefs.clear();
  }
}
