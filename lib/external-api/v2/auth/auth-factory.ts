import { IAuthService } from './interfaces/auth-service.interface';
import { ICredentialProvider } from './interfaces/credential-provider.interface';
import { ITokenCache } from './interfaces/token-cache.interface';
import { EnvCredentialProvider } from './providers/env-credential-provider';
import { MemoryTokenCache } from './cache/memory-token-cache';
import { NextechAuthService } from './services/nextech-auth-service';

/**
 * Factory for creating authentication services
 */
export class AuthFactory {
  private static credentialProvider: ICredentialProvider = new EnvCredentialProvider();
  private static tokenCache: ITokenCache = new MemoryTokenCache();

  /**
   * Create an auth service for the specified provider
   * @param provider Provider name (e.g., 'nextech')
   * @returns An authentication service instance
   * @throws Error if provider is not supported
   */
  static createAuthService(provider: string): IAuthService {
    switch (provider.toLowerCase()) {
      case 'nextech':
        return new NextechAuthService(AuthFactory.credentialProvider, AuthFactory.tokenCache);
      default:
        throw new Error(`Unsupported auth provider: ${provider}`);
    }
  }

  /**
   * Set credential provider (useful for testing or changing implementations)
   * @param provider Credential provider implementation
   */
  static setCredentialProvider(provider: ICredentialProvider): void {
    AuthFactory.credentialProvider = provider;
  }

  /**
   * Set token cache (useful for testing or changing implementations)
   * @param cache Token cache implementation
   */
  static setTokenCache(cache: ITokenCache): void {
    AuthFactory.tokenCache = cache;
  }
}
