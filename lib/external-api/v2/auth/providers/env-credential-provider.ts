import { ICredentialProvider } from '../interfaces/credential-provider.interface';
import { AuthenticationError } from '../errors/auth-error';

/**
 * Credential provider that retrieves credentials from environment variables
 */
export class EnvCredentialProvider implements ICredentialProvider {
  /**
   * Get credential from environment variables
   * @param key The environment variable name
   * @returns The credential value
   * @throws AuthenticationError if the credential is not found
   */
  async getCredential(key: string): Promise<string> {
    const value = process.env[key];
    if (!value) {
      throw new AuthenticationError(
        `Environment variable not found: ${key}`,
        'CREDENTIAL_NOT_FOUND',
      );
    }
    return value;
  }

  /**
   * Check if credential exists in environment variables
   * @param key The environment variable name
   * @returns True if the credential exists, false otherwise
   */
  async hasCredential(key: string): Promise<boolean> {
    return !!process.env[key];
  }
}
