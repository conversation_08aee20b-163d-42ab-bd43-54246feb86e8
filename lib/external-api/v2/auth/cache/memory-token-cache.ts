import { ITokenCache } from '../interfaces/token-cache.interface';

interface TokenData {
  token: string;
  expiresAt: number;
}

/**
 * In-memory implementation of token cache
 * Suitable for development and single-instance deployments
 */
export class MemoryTokenCache implements ITokenCache {
  private tokens: Map<string, TokenData> = new Map();

  /**
   * Get token from cache if valid
   * @param provider Provider name
   * @returns The token if found and not expired, null otherwise
   */
  async getToken(provider: string): Promise<string | null> {
    const tokenData = this.tokens.get(provider);
    if (!tokenData) {
      return null;
    }

    // Check if token is expired (with 30 second buffer)
    if (tokenData.expiresAt < Date.now()) {
      this.tokens.delete(provider);
      return null;
    }

    return tokenData.token;
  }

  /**
   * Store token with expiration
   * @param provider Provider name
   * @param token Token to store
   * @param expiresIn Expiration time in seconds
   */
  async setToken(provider: string, token: string, expiresIn: number): Promise<void> {
    // Add a 30-second buffer to ensure we refresh before actual expiration
    const expiresAt = Date.now() + (expiresIn - 30) * 1000;
    this.tokens.set(provider, { token, expiresAt });
  }

  /**
   * Clear token from cache
   * @param provider Provider name
   */
  async clearToken(provider: string): Promise<void> {
    this.tokens.delete(provider);
  }
}
