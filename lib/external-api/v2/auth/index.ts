// Interfaces
export type { IAuthService } from './interfaces/auth-service.interface';
export type { ICredentialProvider } from './interfaces/credential-provider.interface';
export type { ITokenCache } from './interfaces/token-cache.interface';

// Implementations
export { EnvCredentialProvider } from './providers/env-credential-provider';
export { MemoryTokenCache } from './cache/memory-token-cache';
export { NextechAuthService } from './services/nextech-auth-service';

// Factory
export { AuthFactory } from './auth-factory';

// Errors
export { AuthenticationError, RateLimitError } from './errors/auth-error';
