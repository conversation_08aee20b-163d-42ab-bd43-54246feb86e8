import { AppointmentType, Location, Clinic, User, AppointmentPurpose } from '../models/types';
import {
  IAppointmentService,
  ILocationService,
  IClinicService,
  IUserService,
} from '../providers/types';
import { PaginatedResult, PaginationParams } from '../models/pagination';
import { CacheFactory, CACHE_TTL, generateCacheKey } from './generic-cache';

// Get cache instances for different entity types
const appointmentTypesCache = CacheFactory.getCache<PaginatedResult<AppointmentType>>(
  'appointmentTypes',
  CACHE_TTL.APPOINTMENT_TYPES,
);

const appointmentPurposesCache = CacheFactory.getCache<PaginatedResult<AppointmentPurpose>>(
  'appointmentPurposes',
  CACHE_TTL.APPOINTMENT_PURPOSES,
);

const locationsCache = CacheFactory.getCache<PaginatedResult<Location>>(
  'locations',
  CACHE_TTL.LOCATIONS,
);

const locationByIdCache = CacheFactory.getCache<Location | null>(
  'locationById',
  CACHE_TTL.LOCATIONS,
);

const clinicsCache = CacheFactory.getCache<PaginatedResult<Clinic>>('clinics', CACHE_TTL.CLINICS);

const clinicByIdCache = CacheFactory.getCache<Clinic | null>('clinicById', CACHE_TTL.CLINICS);

const usersCache = CacheFactory.getCache<PaginatedResult<User>>('users', CACHE_TTL.USERS);

const userByIdCache = CacheFactory.getCache<User | null>('userById', CACHE_TTL.USERS);

/**
 * Get appointment types from cache or fetch fresh data if cache is stale
 * @param appointmentService The appointment service to fetch data from
 * @param query The query parameters to filter appointment types
 * @returns A promise that resolves to the cached or fresh appointment types
 */
export async function getCachedAppointmentTypes(
  appointmentService: IAppointmentService,
  query: Record<string, unknown>,
): Promise<PaginatedResult<AppointmentType>> {
  const cacheKey = generateCacheKey('appointmentTypes', query);

  // Try to get from cache first
  const cachedData = appointmentTypesCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const appointmentTypes = await appointmentService.getAppointmentTypes(query);
  appointmentTypesCache.set(cacheKey, appointmentTypes);

  return appointmentTypes;
}

/**
 * Get appointment purposes from cache or fetch fresh data if cache is stale
 * @param appointmentService The appointment service to fetch data from
 * @param query The query parameters to filter appointment purposes
 * @returns A promise that resolves to the cached or fresh appointment purposes
 */
export async function getCachedAppointmentPurposes(
  appointmentService: IAppointmentService,
  query: Record<string, unknown>,
): Promise<PaginatedResult<AppointmentPurpose>> {
  const cacheKey = generateCacheKey('appointmentPurposes', query);

  // Try to get from cache first
  const cachedData = appointmentPurposesCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const appointmentPurposes = await appointmentService.getAppointmentPurposes(query);
  appointmentPurposesCache.set(cacheKey, appointmentPurposes);

  return appointmentPurposes;
}

/**
 * Get locations from cache or fetch fresh data if cache is stale
 * @param locationService The location service to fetch data from
 * @param query The query parameters to filter locations
 * @param pagination Optional pagination parameters
 * @returns A promise that resolves to the cached or fresh locations
 */
export async function getCachedLocations(
  locationService: ILocationService,
  query?: Record<string, unknown>,
  pagination?: PaginationParams,
): Promise<PaginatedResult<Location>> {
  const cacheKey = generateCacheKey('locations', {
    ...query,
    pagination: pagination ? { limit: pagination.limit, offset: pagination.offset } : undefined,
  });

  // Try to get from cache first
  const cachedData = locationsCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const locations = await locationService.getLocations(query, pagination);
  locationsCache.set(cacheKey, locations);

  return locations;
}

/**
 * Get location by ID from cache or fetch fresh data if cache is stale
 * @param locationService The location service to fetch data from
 * @param id The location ID
 * @returns A promise that resolves to the cached or fresh location
 */
export async function getCachedLocationById(
  locationService: ILocationService,
  id: string,
): Promise<Location | null> {
  const cacheKey = `location:${id}`;

  // Try to get from cache first
  const cachedData = locationByIdCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const location = await locationService.getLocationById(id);
  locationByIdCache.set(cacheKey, location);

  return location;
}

/**
 * Get clinics from cache or fetch fresh data if cache is stale
 * @param clinicService The clinic service to fetch data from
 * @param query The query parameters to filter clinics
 * @param pagination Optional pagination parameters
 * @returns A promise that resolves to the cached or fresh clinics
 */
export async function getCachedClinics(
  clinicService: IClinicService,
  query?: Record<string, unknown>,
  pagination?: PaginationParams,
): Promise<PaginatedResult<Clinic>> {
  const cacheKey = generateCacheKey('clinics', {
    ...query,
    pagination: pagination ? { limit: pagination.limit, offset: pagination.offset } : undefined,
  });

  // Try to get from cache first
  const cachedData = clinicsCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const clinics = await clinicService.getClinics(query, pagination);
  clinicsCache.set(cacheKey, clinics);

  return clinics;
}

/**
 * Get clinic by ID from cache or fetch fresh data if cache is stale
 * @param clinicService The clinic service to fetch data from
 * @param id The clinic ID
 * @returns A promise that resolves to the cached or fresh clinic
 */
export async function getCachedClinicById(
  clinicService: IClinicService,
  id: string,
): Promise<Clinic | null> {
  const cacheKey = `clinic:${id}`;

  // Try to get from cache first
  const cachedData = clinicByIdCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const clinic = await clinicService.getClinicById(id);
  clinicByIdCache.set(cacheKey, clinic);

  return clinic;
}

/**
 * Get users from cache or fetch fresh data if cache is stale
 * @param userService The user service to fetch data from
 * @param query The query parameters to filter users
 * @param pagination Optional pagination parameters
 * @returns A promise that resolves to the cached or fresh users
 */
export async function getCachedUsers(
  userService: IUserService,
  query?: Record<string, unknown>,
  pagination?: PaginationParams,
): Promise<PaginatedResult<User>> {
  const cacheKey = generateCacheKey('users', {
    ...query,
    pagination: pagination ? { limit: pagination.limit, offset: pagination.offset } : undefined,
  });

  // Try to get from cache first
  const cachedData = usersCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const users = await userService.getUsers(query, pagination);
  usersCache.set(cacheKey, users);

  return users;
}

/**
 * Get user by ID from cache or fetch fresh data if cache is stale
 * @param userService The user service to fetch data from
 * @param id The user ID
 * @returns A promise that resolves to the cached or fresh user
 */
export async function getCachedUserById(
  userService: IUserService,
  id: string,
): Promise<User | null> {
  const cacheKey = `user:${id}`;

  // Try to get from cache first
  const cachedData = userByIdCache.get(cacheKey);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // Fetch fresh data and update cache
  const user = await userService.getUserById(id);
  userByIdCache.set(cacheKey, user);

  return user;
}

/**
 * Clear all caches
 */
export function clearAllCaches(): void {
  CacheFactory.clearAllCaches();
}

/**
 * Clear specific cache by name
 * @param cacheName The name of the cache to clear
 */
export function clearCache(cacheName: string): void {
  CacheFactory.clearCache(cacheName);
}

/**
 * Get cache statistics for monitoring
 * @returns Object containing cache statistics for all caches
 */
export function getCacheStats(): Record<string, unknown> {
  return {
    appointmentTypes: appointmentTypesCache.getStats(),
    appointmentPurposes: appointmentPurposesCache.getStats(),
    locations: locationsCache.getStats(),
    locationById: locationByIdCache.getStats(),
    clinics: clinicsCache.getStats(),
    clinicById: clinicByIdCache.getStats(),
    users: usersCache.getStats(),
    userById: userByIdCache.getStats(),
  };
}
