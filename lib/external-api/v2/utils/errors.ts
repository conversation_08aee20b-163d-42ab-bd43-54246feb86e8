import { NextApiResponse } from 'next';
import { ZodError } from 'zod'; // Import ZodError

/**
 * Standard error response format
 */
export interface ErrorResponse {
  status: number;
  code: string;
  message: string;
  details?: Record<string, unknown>;
}

/**
 * Base API error class
 */
export class ApiError extends Error {
  status: number;
  code: string;
  details?: Record<string, unknown>;

  constructor(status: number, code: string, message: string, details?: Record<string, unknown>) {
    super(message);
    this.status = status;
    this.code = code;
    this.details = details;
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }

  /**
   * Convert to a response object
   */
  toResponseObject(): ErrorResponse {
    return {
      status: this.status,
      code: this.code,
      message: this.message,
      details: this.details,
    };
  }
}

/**
 * 400 Bad Request error
 */
export class BadRequestError extends ApiError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(400, 'BAD_REQUEST', message, details);
  }
}

/**
 * 401 Unauthorized error
 */
export class UnauthorizedError extends ApiError {
  constructor(message = 'Unauthorized', details?: Record<string, unknown>) {
    super(401, 'UNAUTHORIZED', message, details);
  }
}

/**
 * 403 Forbidden error
 */
export class ForbiddenError extends ApiError {
  constructor(message = 'Forbidden', details?: Record<string, unknown>) {
    super(403, 'FORBIDDEN', message, details);
  }
}

/**
 * 404 Not Found error
 */
export class NotFoundError extends ApiError {
  constructor(message = 'Resource not found', details?: Record<string, unknown>) {
    super(404, 'NOT_FOUND', message, details);
  }
}

/**
 * 409 Conflict error
 */
export class ConflictError extends ApiError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(409, 'CONFLICT', message, details);
  }
}

/**
 * 500 Internal Server Error
 */
export class InternalServerError extends ApiError {
  constructor(message = 'Internal server error', details?: Record<string, unknown>) {
    super(500, 'INTERNAL_SERVER_ERROR', message, details);
  }
}

/**
 * 502 Bad Gateway Error
 * Used when a provider API returns an error
 */
export class ProviderApiError extends ApiError {
  constructor(message: string, details?: Record<string, unknown>) {
    super(502, 'PROVIDER_API_ERROR', message, details);
  }
}

/**
 * Handle errors in API routes
 */
export function handleError(error: unknown, res: NextApiResponse): void {
  // Log the error using the logger integrated in handler.ts if possible,
  // otherwise fallback to console.error
  // Assuming logger might not be easily accessible here, stick with console.error for now.
  // TODO: Consider passing logger instance or using a global logger if needed.
  console.error('API Error:', error);

  // Handle Zod validation errors
  if (error instanceof ZodError) {
    const validationError = new BadRequestError('Invalid request data', {
      issues: error.errors, // Include Zod issues in details
    });
    // Workaround: Manually construct the response object
    res.status(validationError.status).json({
      status: validationError.status,
      code: validationError.code,
      message: validationError.message,
      details: validationError.details,
    });
    return;
  }

  // Check if the error is an ApiError by looking for the status and code properties
  if (error && typeof error === 'object' && 'status' in error && 'code' in error) {
    const apiError = error as ApiError;
    res.status(apiError.status).json({
      status: apiError.status,
      code: apiError.code,
      message: apiError.message,
      details: 'details' in apiError ? apiError.details : undefined,
    });
    return;
  }

  // Handle generic errors
  const status = 500;
  const code = 'INTERNAL_SERVER_ERROR';
  const message = error instanceof Error ? error.message : 'Unknown error';

  res.status(status).json({
    status,
    code,
    message,
  });
}
