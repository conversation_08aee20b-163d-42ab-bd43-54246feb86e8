/**
 * Generic caching mechanism for API data
 * Provides reusable caching functionality across all services
 */

/**
 * Cache entry containing data, timestamp, and TTL
 */
interface CacheEntry<T> {
  data: T;
  timestamp: number; // timestamp in milliseconds
  ttl: number; // TTL in seconds
}

/**
 * Cache service interface defining cache operations
 */
export interface ICacheService<T> {
  /**
   * Get data from cache if it exists and is not expired
   * @param key Cache key
   * @returns Cached data or undefined if not found or expired
   */
  get(key: string): T | undefined;

  /**
   * Store data in cache with optional TTL override
   * @param key Cache key
   * @param data Data to cache
   * @param ttl Optional TTL override in seconds
   */
  set(key: string, data: T, ttl?: number): void;

  /**
   * Delete an entry from cache
   * @param key Cache key
   */
  delete(key: string): void;

  /**
   * Clear all entries from cache
   */
  clear(): void;

  /**
   * Get cache statistics for monitoring
   * @returns Object containing cache statistics
   */
  getStats(): Record<string, unknown>;
}

/**
 * In-memory cache implementation
 * Suitable for development and single-instance deployments
 */
export class InMemoryCache<T> implements ICacheService<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private defaultTtl: number;

  /**
   * Create a new in-memory cache
   * @param defaultTtl Default TTL in seconds (defaults to 1 hour)
   */
  constructor(defaultTtl = 3600) {
    this.defaultTtl = defaultTtl;
  }

  /**
   * Get data from cache if it exists and is not expired
   * @param key Cache key
   * @returns Cached data or undefined if not found or expired
   */
  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl * 1000) {
      this.delete(key);
      return undefined;
    }

    return entry.data;
  }

  /**
   * Store data in cache with optional TTL override
   * @param key Cache key
   * @param data Data to cache
   * @param ttl Optional TTL override in seconds
   */
  set(key: string, data: T, ttl?: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl ?? this.defaultTtl,
    });
  }

  /**
   * Delete an entry from cache
   * @param key Cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all entries from cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics for monitoring
   * @returns Object containing cache statistics
   */
  getStats(): Record<string, unknown> {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      defaultTtl: this.defaultTtl,
    };
  }
}

/**
 * Factory for creating and managing cache instances
 * Ensures singleton caches across the application
 */
export class CacheFactory {
  private static caches: Record<string, ICacheService<unknown>> = {};

  /**
   * Get or create a cache instance for a specific entity type
   * @param name Cache name/identifier
   * @param ttl Optional default TTL in seconds
   * @returns Cache instance
   */
  static getCache<T>(name: string, ttl?: number): ICacheService<T> {
    if (!this.caches[name]) {
      this.caches[name] = new InMemoryCache<T>(ttl);
    }
    return this.caches[name] as ICacheService<T>;
  }

  /**
   * Clear a specific cache instance
   * @param name Cache name/identifier
   */
  static clearCache(name: string): void {
    if (this.caches[name]) {
      this.caches[name].clear();
    }
  }

  /**
   * Clear all cache instances
   */
  static clearAllCaches(): void {
    Object.values(this.caches).forEach(cache => cache.clear());
  }
}

/**
 * Cache entity data with appropriate TTLs
 * Constants for entity-specific cache TTLs
 */
export const CACHE_TTL = {
  APPOINTMENT_TYPES: 3600, // 1 hour
  APPOINTMENT_PURPOSES: 86400, // 24 hours
  LOCATIONS: 86400, // 24 hours
  CLINICS: 86400, // 24 hours
  USERS: 21600, // 6 hours
};

/**
 * Helper function to generate a cache key from query parameters
 * @param baseKey Base key identifier
 * @param params Query parameters
 * @returns Generated cache key
 */
export function generateCacheKey(baseKey: string, params?: Record<string, unknown>): string {
  if (!params || Object.keys(params).length === 0) {
    return baseKey;
  }

  return `${baseKey}:${JSON.stringify(params)}`;
}

/**
 * Generic helper for fetching data with cache
 * @param cache Cache instance
 * @param key Cache key
 * @param fetchFn Function to fetch data if not in cache
 * @param args Arguments to pass to fetch function
 * @returns Promise with data from cache or fetch function
 */
export async function fetchWithCache<T, P extends Array<unknown>>(
  cache: ICacheService<T>,
  key: string,
  fetchFn: (...args: P) => Promise<T>,
  ...args: P
): Promise<T> {
  // Try to get from cache first
  const cachedData = cache.get(key);
  if (cachedData !== undefined) {
    return cachedData;
  }

  // If not in cache, fetch fresh data
  const data = await fetchFn(...args);

  // Cache the result
  cache.set(key, data);

  return data;
}
