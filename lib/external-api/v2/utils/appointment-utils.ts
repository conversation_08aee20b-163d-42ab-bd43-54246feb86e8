import { appointmentFactory } from '@/lib/factories/appointment-factory';

/**
 * Store an appointment reference in Firestore
 * @param provider The provider that created the appointment
 * @param appointment The appointment data
 * @returns Promise that resolves when the reference is stored
 */
export async function storeAppointmentReference(
  provider: { name: string },
  appointment: {
    id: string;
    patientId?: string;
    patientName?: string;
    providerId?: string;
    practitionerId?: string;
    practitionerName?: string;
    locationId?: string;
    locationName?: string;
    startTime?: string;
    endTime?: string;
    type?: string;
    status?: string;
    reason?: string;
    notes?: string;
  },
): Promise<void> {
  try {
    const appointmentReferenceService = appointmentFactory.getAppointmentReferenceService();

    await appointmentReferenceService.storeNewAppointment({
      provider: provider.name,
      externalId: appointment.id,
      providerId: appointment.id, // Keep for backward compatibility
      patientId: appointment.patientId || '',
      patientName: appointment.patientName,
      practitionerId: appointment.practitionerId || appointment.providerId || '',
      practitionerName: appointment.practitionerName,
      locationId: appointment.locationId || '',
      locationName: appointment.locationName,
      startTime: appointment.startTime || '',
      endTime: appointment.endTime || '',
      type: appointment.type || '',
      status: appointment.status || 'booked',
      reason: appointment.reason || '',
      notes: appointment.notes || '',
    });
  } catch (error) {
    // Log the error but don't fail the request if reference creation fails
    console.error('Error creating appointment reference:', error);
    // We don't rethrow the error to allow the calling function to continue
  }
}
