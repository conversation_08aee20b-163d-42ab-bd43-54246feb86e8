import { CallType } from '@/models/CallTypes';
import { callSessionsService, callsService } from '@/utils/firestore';
import logger from '../utils/logger';
import { shouldMarkAsDisconnected } from '@/utils/call-duration-utils';

// Map each CallType to a logical category.  Only one value from each category
// will be preserved in the callTypes history so the sequence remains meaningful.
const CALL_TYPE_CATEGORY: Record<CallType, string> = {
  [CallType.OTHER]: 'misc',
  [CallType.VOICEMAIL]: 'voicemail',
  [CallType.TRANSFER_TO_HUMAN]: 'transfer',
  [CallType.NEW_PATIENT_NEW_APPOINTMENT]: 'scheduling',
  [CallType.NEW_APPOINTMENT_EXISTING_PATIENT]: 'scheduling',
  [CallType.RESCHEDULE]: 'scheduling',
  [CallType.CANCELLATION]: 'scheduling',
  [CallType.VOICEMAIL_SYSTEM_ERROR]: 'voicemail',
  [CallType.GENERAL_INFO]: 'misc',
  [CallType.LOOKUP]: 'lookup',
  [CallType.TRANSFER_TO_CLINIC]: 'transfer',
  [CallType.AFTER_HOURS]: 'after_hours',
  [CallType.CONFIRM_APPOINTMENT]: 'scheduling',
  [CallType.DISCONNECTED]: 'disconnected',
  [CallType.IMMEDIATE_TRANSFER]: 'transfer',
  [CallType.TRANSFER_DUE_TO_SCHEDULING]: 'transfer',
  [CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST]: 'transfer',
  [CallType.IMMEDIATE_HANGUP]: 'hangup',
};

/**
 * Merges a new call type into an existing callTypes array following category rules
 * @param existing The existing callTypes array
 * @param newType The new call type to merge
 * @returns The updated callTypes array
 */
export function mergeCallTypes(existing: number[] | undefined, newType: CallType): number[] {
  const current: number[] = existing ? [...existing] : [];

  const newCategory = CALL_TYPE_CATEGORY[newType];

  // Remove any earlier entry that belongs to the same category
  const filtered = current.filter(t => CALL_TYPE_CATEGORY[t as CallType] !== newCategory);

  // If the last type is identical to the new one, keep as-is to avoid duplicates
  if (filtered.length > 0 && filtered[filtered.length - 1] === newType) {
    return filtered;
  }

  filtered.push(newType);
  return filtered;
}

/**
 * Updates the call type in a call session based on the appointment action
 * @param sessionId The session ID to update
 * @param callType The call type to set
 * @param context Additional context for logging
 */
export async function updateCallSessionType(
  sessionId: string,
  callType: CallType,
  context: string,
): Promise<void> {
  try {
    // Fetch existing session to build callTypes array
    const existingSession = await callSessionsService.getCallSessionBySessionId(sessionId);

    let currentArray: number[] | undefined;
    if (existingSession?.callTypes && Array.isArray(existingSession.callTypes)) {
      currentArray = existingSession.callTypes;
    } else if (existingSession?.callType !== undefined) {
      currentArray = [existingSession.callType];
    }

    const newCallTypes = mergeCallTypes(currentArray, callType);

    // Update the call session
    await callSessionsService.addOrUpdateCallSession(sessionId, {
      callType, // keep latest for backward compatibility
      callTypes: newCallTypes,
    });

    // Also update the actual call record if it exists
    if (existingSession?.callId) {
      try {
        await callsService.updateCall(existingSession.callId, {
          type: callType,
          callTypes: newCallTypes,
        });

        logger.info(
          { sessionId, callId: existingSession.callId, callType, callTypes: newCallTypes, context },
          `Updated both call session and call record ${existingSession.callId} with call type ${callType}`,
        );
      } catch (callUpdateError) {
        logger.warn(
          { sessionId, callId: existingSession.callId, callType, error: callUpdateError, context },
          `Failed to update call record ${existingSession.callId}, but session was updated successfully`,
        );
      }
    } else {
      logger.info(
        { sessionId, callType, callTypes: newCallTypes, context },
        `Updated call session ${sessionId} with call type ${callType} (no call record linked)`,
      );
    }
  } catch (error) {
    logger.error(
      { sessionId, callType, error, context },
      `Failed to update call session ${sessionId} with call type ${callType}`,
    );
    // Don't throw - this is a non-critical operation
  }
}

/**
 * Updates the call type for all call sessions associated with a patient
 * @param patientId The patient ID to find sessions for
 * @param callType The call type to set
 * @param context Additional context for logging
 */
export async function updateCallSessionTypeForPatient(
  patientId: string,
  callType: CallType,
  context: string,
): Promise<void> {
  try {
    // Find all call sessions for this patient
    const sessions = await callSessionsService.findCallSessionsByPatientId(patientId);

    if (sessions.length === 0) {
      logger.info({ patientId, context }, `No call sessions found for patient ${patientId}`);
      return;
    }

    // Update each session with the new call type
    const updatePromises = sessions.map(session =>
      updateCallSessionType(session.sessionId, callType, context),
    );

    await Promise.all(updatePromises);

    logger.info(
      { patientId, callType, sessionCount: sessions.length, context },
      `Updated ${sessions.length} call sessions for patient ${patientId} with call type ${callType}`,
    );
  } catch (error) {
    logger.error(
      { patientId, callType, error, context },
      `Failed to update call sessions for patient ${patientId}`,
    );
    // Don't throw - this is a non-critical operation
  }
}

/**
 * Updates the call type for all call sessions associated with an appointment
 * @param appointmentId The appointment ID to find sessions for
 * @param callType The call type to set
 * @param context Additional context for logging
 */
export async function updateCallSessionTypeForAppointment(
  appointmentId: string,
  callType: CallType,
  context: string,
): Promise<void> {
  try {
    // Find all call sessions for this appointment
    const sessions = await callSessionsService.findCallSessionsByAppointmentId(appointmentId);

    if (sessions.length === 0) {
      logger.info(
        { appointmentId, context },
        `No call sessions found for appointment ${appointmentId}`,
      );
      return;
    }

    // Update each session with the new call type
    const updatePromises = sessions.map(session =>
      updateCallSessionType(session.sessionId, callType, context),
    );

    await Promise.all(updatePromises);

    logger.info(
      { appointmentId, callType, sessionCount: sessions.length, context },
      `Updated ${sessions.length} call sessions for appointment ${appointmentId} with call type ${callType}`,
    );
  } catch (error) {
    logger.error(
      { appointmentId, callType, error, context },
      `Failed to update call sessions for appointment ${appointmentId}`,
    );
    // Don't throw - this is a non-critical operation
  }
}

// Duration parsing and display logic functions have been moved to @/utils/call-duration-utils

// shouldMarkAsDisconnected has been moved to @/utils/call-duration-utils

/**
 * Process call for auto-marking as disconnected if needed
 * @param call The call to process
 * @param context Additional context for logging
 * @returns Updated call type if changes were made
 */
export async function processCallForAutoDisconnected(
  call: {
    id: string;
    duration?: string | null;
    type?: CallType | null;
    callTypes?: CallType[] | null;
    sessionId?: string | null;
  },
  context: string = 'auto-disconnected-check',
): Promise<CallType | null> {
  if (!shouldMarkAsDisconnected(call)) {
    return null;
  }

  logger.info(
    {
      callId: call.id,
      duration: call.duration,
      type: call.type,
      callTypes: call.callTypes,
      context,
    },
    `Auto-marking call as DISCONNECTED - duration over 20s with no meaningful type`,
  );

  // Update the call session if sessionId is available
  if (call.sessionId) {
    await updateCallSessionType(call.sessionId, CallType.DISCONNECTED, context);
  }

  return CallType.DISCONNECTED;
}

/**
 * Diagnostic function to identify calls with incorrect type/callTypes state
 * Focuses on finding calls where the external API endpoints didn't properly set call types
 * @param call Call object to analyze
 * @returns Analysis results about the call type state
 */
export function analyzeCallTypeState(call: {
  id: string;
  sessionId?: string;
  type?: number;
  callTypes?: number[] | null;
  duration?: string;
  transcription?: string;
  summary?: string;
}): {
  hasValidState: boolean;
  issues: string[];
  currentType: CallType;
  currentCallTypes: number[] | null;
  possibleCause: string;
} {
  const currentType = call.type ?? CallType.OTHER;
  const currentCallTypes = call.callTypes ?? null;
  const issues: string[] = [];
  let possibleCause = 'Unknown';

  // Check for the main issue: type is OTHER but callTypes is null/undefined
  if (
    currentType === CallType.OTHER &&
    (currentCallTypes === null || currentCallTypes === undefined)
  ) {
    issues.push('Call has type=OTHER but callTypes is null/undefined');
    possibleCause = 'External API endpoints not called with sessionId parameter';
  }

  // Check for inconsistent state where type is set but callTypes is null
  if (
    currentType !== CallType.OTHER &&
    (currentCallTypes === null || currentCallTypes === undefined)
  ) {
    issues.push(`Call has type=${CallType[currentType]} but callTypes is null/undefined`);
    possibleCause = 'Call type was set manually but callTypes array not initialized';
  }

  // Check for empty callTypes array
  if (Array.isArray(currentCallTypes) && currentCallTypes.length === 0) {
    issues.push('Call has empty callTypes array');
    possibleCause = 'Call types were cleared or never properly set';
  }

  // Check for type not matching latest callType
  if (Array.isArray(currentCallTypes) && currentCallTypes.length > 0) {
    const latestCallType = currentCallTypes[currentCallTypes.length - 1];
    if (currentType !== latestCallType) {
      issues.push(
        `Call type (${CallType[currentType]}) doesn't match latest callType (${CallType[latestCallType]})`,
      );
      possibleCause = 'Type and callTypes got out of sync';
    }
  }

  // Check if sessionId is missing (common cause of issues)
  if (!call.sessionId || call.sessionId.trim() === '') {
    issues.push('Call has no sessionId');
    possibleCause = 'No sessionId means external API endpoints cannot update call types';
  }

  const hasValidState = issues.length === 0;

  return {
    hasValidState,
    issues,
    currentType,
    currentCallTypes,
    possibleCause,
  };
}
