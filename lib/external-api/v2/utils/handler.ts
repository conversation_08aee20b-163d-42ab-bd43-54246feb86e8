import { NextApiRequest, NextApiResponse } from 'next';
import { providerRegistry } from '../providers/index';
import { handleError } from './errors';
import { applyMiddleware } from '../middleware/index';
import type { MiddlewareHandler } from '../middleware/index';
import logger, { generateRequestId } from './logger'; // Import logger and ID generator
import { recordMetrics } from './monitoring'; // Import metrics recorder

/**
 * Options for creating an API handler
 */
export interface ApiHandlerOptions {
  middleware?: MiddlewareHandler[];
}

/**
 * Extended NextApiRequest with additional properties
 */
export interface ExtendedNextApiRequest extends NextApiRequest {
  requestId: string;
}

/**
 * Creates a handler function for API routes with error handling and middleware support
 * @param handler The handler function to wrap
 * @param options Options for the handler
 * @returns A NextJS API handler function
 */
export function createApiHandler(
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  options: ApiHandlerOptions = {},
) {
  const middleware = options.middleware || [];

  return async (req: NextApiRequest, res: NextApiResponse) => {
    const start = Date.now();
    const requestId = generateRequestId();

    // Extend NextApiRequest to include requestId (consider a global type augmentation if used widely)
    (req as ExtendedNextApiRequest).requestId = requestId;

    // Log request start
    logger.info(
      {
        requestId,
        method: req.method,
        url: req.url,
        headers: req.headers, // Pino will redact sensitive headers based on config
        body: req.body, // Pino will redact sensitive body fields based on config
      },
      `Request received: ${req.method} ${req.url}`,
    );

    let hasError = false;

    // Log response finish
    res.on('finish', () => {
      const duration = Date.now() - start;
      const statusCode = res.statusCode;
      const level = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';

      // Record metrics
      recordMetrics(req, duration, hasError || statusCode >= 400);

      logger[level](
        {
          requestId,
          statusCode,
          duration,
          // Optionally log response headers if needed (be mindful of sensitive data)
          // responseHeaders: res.getHeaders(),
        },
        `Request finished: ${req.method} ${req.url} - ${statusCode} (${duration}ms)`,
      );
    });

    try {
      // Apply middleware and the main handler
      await applyMiddleware(middleware)(req, res, handler);
    } catch (error: unknown) {
      // Mark that an error occurred
      hasError = true;

      // Create a properly typed error object
      const typedError = error instanceof Error ? error : new Error(String(error));

      // Extract additional properties if available
      const errorCode = 'code' in typedError ? (typedError as { code: string }).code : undefined;
      const statusCode =
        'statusCode' in typedError ? (typedError as { statusCode: number }).statusCode : undefined;

      // Log the error with context
      logger.error(
        {
          requestId,
          err: {
            message: typedError.message,
            stack: typedError.stack,
            // Include custom error properties if available
            ...(statusCode && { statusCode }),
            ...(errorCode && { code: errorCode }),
          },
        },
        `Error processing request: ${req.method} ${req.url}`,
      );
      // Use existing error handler to send response
      handleError(typedError, res);
    }
  };
}

/**
 * Get the provider to use for the request
 * @param req The NextJS API request
 * @returns The provider to use
 */
export function getProviderFromRequest(req: NextApiRequest) {
  // Check for provider in headers, query params, or use default
  const providerName = (req.headers['x-provider'] as string) || (req.query.provider as string);

  return providerRegistry.getProvider(providerName);
}
