import pino from 'pino';
import { v4 as uuidv4 } from 'uuid'; // Import uuid for request IDs
import pretty from 'pino-pretty';

// Determine if we are in a development environment
const isDevelopment = process.env.NODE_ENV === 'development';

// Define interface for context with requestId
interface LogContext {
  requestId?: string;
  [key: string]: unknown;
}

// Define options for Pino
const pinoOptions: pino.LoggerOptions = {
  level: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info'),
  // Redact sensitive headers and common PII fields
  redact: {
    paths: [
      'req.headers.authorization',
      'req.headers["x-api-key"]',
      'req.headers.cookie', // Also redact cookies
      'res.headers["set-cookie"]',
      '*.password',
      '*.apiKey',
      '*.secret',
      '*.token',
      '*.ssn', // Example PII redaction
      '*.dob', // Example PII redaction
      '*.email', // Example PII redaction (if needed, be careful)
      '*.phone', // Example PII redaction (if needed, be careful)
      'req.body.password', // Specific body fields
      'req.body.apiKey',
      'req.body.secret',
    ],
    censor: '[REDACTED]',
  },
  // Add request ID automatically to logs if available
  mixin(_context) {
    // Check if context has requestId property before accessing it
    const context = _context as LogContext;
    return context.requestId ? { requestId: context.requestId } : {};
  },
  // Base fields to include in every log record
  base: {
    // pid: process.pid, // Usually included by default, uncomment if needed
    // hostname: os.hostname(), // Usually included by default, uncomment if needed
  },
  timestamp: pino.stdTimeFunctions.isoTime, // Use ISO 8601 format timestamps
};

// Enable pretty printing only in development
const stream = pretty({
  levelFirst: true,
  colorize: true,
  ignore: 'time,hostname,pid',
});

const logger = isDevelopment
  ? pino(
      {
        name: 'MyLogger',
        level: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
      },
      stream,
    )
  : pino(pinoOptions);

// Function to generate a request ID (can be moved elsewhere if needed)
export const generateRequestId = (): string => uuidv4();

export default logger;
