import { NextApiRequest } from 'next';
import logger from './logger';

// Simple in-memory metrics store
// In a production environment, this would be replaced with a proper metrics system
// like Prometheus, Datadog, New Relic, etc.
interface MetricsStore {
  endpoints: Record<
    string,
    {
      totalCalls: number;
      errors: number;
      totalLatency: number;
      latencies: number[]; // Keep last 100 latencies for percentile calculation
    }
  >;
}

const metricsStore: MetricsStore = {
  endpoints: {},
};

// Maximum number of latency records to keep per endpoint
const MAX_LATENCY_RECORDS = 100;

/**
 * Calculate P95 latency from an array of latencies
 * @param latencies Array of latency values in milliseconds
 * @returns P95 latency or 0 if no latencies
 */
function calculateP95(latencies: number[]): number {
  if (latencies.length === 0) return 0;

  // Sort latencies in ascending order
  const sortedLatencies = [...latencies].sort((a, b) => a - b);

  // Calculate the index for P95
  const idx = Math.ceil(sortedLatencies.length * 0.95) - 1;

  // Return the P95 value
  return sortedLatencies[idx];
}

/**
 * Record API call metrics
 * @param req The NextApiRequest object
 * @param duration Duration of the request in milliseconds
 * @param isError Whether the request resulted in an error
 */
export function recordMetrics(req: NextApiRequest, duration: number, isError: boolean): void {
  const endpoint = getEndpointIdentifier(req);

  // Initialize endpoint metrics if not exist
  if (!metricsStore.endpoints[endpoint]) {
    metricsStore.endpoints[endpoint] = {
      totalCalls: 0,
      errors: 0,
      totalLatency: 0,
      latencies: [],
    };
  }

  const metrics = metricsStore.endpoints[endpoint];

  // Update metrics
  metrics.totalCalls += 1;
  metrics.totalLatency += duration;
  if (isError) {
    metrics.errors += 1;
  }

  // Add to latency array with limit
  metrics.latencies.push(duration);
  if (metrics.latencies.length > MAX_LATENCY_RECORDS) {
    metrics.latencies.shift(); // Remove oldest entry
  }
}

/**
 * Generate a standardized endpoint identifier from a request
 * @param req The NextApiRequest object
 * @returns A string identifying the endpoint
 */
function getEndpointIdentifier(req: NextApiRequest): string {
  // Extract route path - handles dynamic routes like [id]
  // For API routes we can extract from req.url or build from req.query
  let path = req.url || '';

  // Remove query parameters
  path = path.split('?')[0];

  // Standardize the identifier with method
  return `${req.method} ${path}`;
}

/**
 * Get metrics for all endpoints
 * @returns Object with aggregated metrics for all endpoints
 */
interface EndpointMetrics {
  totalCalls: number;
  errors: number;
  errorRate: number;
  avgLatencyMs: number;
  p95LatencyMs: number;
}

export function getMetrics() {
  const result: Record<string, EndpointMetrics> = {};

  Object.entries(metricsStore.endpoints).forEach(([endpoint, metrics]) => {
    const errorRate = metrics.totalCalls > 0 ? metrics.errors / metrics.totalCalls : 0;

    const avgLatency = metrics.totalCalls > 0 ? metrics.totalLatency / metrics.totalCalls : 0;

    const p95Latency = calculateP95(metrics.latencies);

    result[endpoint] = {
      totalCalls: metrics.totalCalls,
      errors: metrics.errors,
      errorRate: errorRate,
      avgLatencyMs: avgLatency,
      p95LatencyMs: p95Latency,
    };
  });

  return result;
}

/**
 * Log current metrics (can be called periodically or on-demand)
 */
export function logMetrics(): void {
  const metrics = getMetrics();
  logger.info({ metrics }, 'API Metrics');
}

/**
 * Reset all metrics (mainly for testing purposes)
 */
export function resetMetrics(): void {
  Object.keys(metricsStore.endpoints).forEach(key => {
    delete metricsStore.endpoints[key];
  });
}
