import { BadRequestError, ConflictError } from '@/lib/external-api/v2';
import { Patient } from '../models/types';
import { IPatientService } from '../providers/types';
import { patientFactory } from '@/lib/factories/patient-factory';

/**
 * Create a patient and store a reference in Firestore
 * @param provider The provider to use
 * @param patientData The patient data to create
 * @param createReference Whether to create a reference in Firestore (default: false)
 * @returns The created patient
 */
export async function createPatientWithReference(
  provider: {
    name: string;
    getPatientService: () => IPatientService;
  },
  patientData: Omit<Patient, 'id' | 'providerInfo'>,
  createReference: boolean = false,
): Promise<Patient> {
  try {
    const patientToCreate = {
      ...patientData,
    };

    // Validate patient date of birth is not in the future
    if (patientToCreate.dateOfBirth) {
      const dateOfBirth = new Date(patientToCreate.dateOfBirth);
      const now = new Date();
      if (dateOfBirth > now) {
        throw new BadRequestError('Patient date of birth cannot be in the future', {
          code: 'INVALID_PATIENT_DOB',
          message: 'The patient date of birth must be in the past',
          suggestion: 'Please use a past date of birth for the patient',
        });
      }
    }

    // Use createReference parameter to determine if we should create a reference

    // Create patient
    const patient = await provider.getPatientService().createPatient(patientToCreate);

    // Create a patient reference in Firestore if requested
    if (createReference) {
      try {
        const patientCoordinatorService = patientFactory.getPatientCoordinatorService();
        await patientCoordinatorService.storePatientReference({
          provider: provider.name,
          providerId: patient.providerInfo.externalId,
          phoneNumber: patient.phoneNumber,
        });
      } catch (refError) {
        // Log the error but don't fail the request if reference creation fails
        console.error('Error creating patient reference:', refError);
        // We still continue to return the patient data
      }
    }

    return patient;
  } catch (error) {
    // Handle conflict errors (patient already exists)
    if (error instanceof ConflictError) {
      throw error;
    }
    // Handle other errors
    throw error;
  }
}

/**
 * Find a patient by various criteria
 * @param provider The provider to use
 * @param criteria The search criteria
 * @returns The found patient or null if not found
 */
export async function findPatientByCriteria(
  provider: {
    getPatientService: () => IPatientService;
  },
  criteria: Record<string, unknown>,
): Promise<Patient | null> {
  try {
    // Try to find by phone number if provided
    if (criteria.phoneNumber && criteria.dateOfBirth) {
      const patient = await searchPatients(provider, {
        phoneNumber: criteria.phoneNumber,
        dateOfBirth: criteria.dateOfBirth,
      });
      if (patient) return patient;
    }

    // If not found by phone or phone not provided, try by name and DOB
    if (criteria.firstName && criteria.lastName && criteria.dateOfBirth) {
      const searchCriteria = {
        firstName: criteria.firstName,
        lastName: criteria.lastName,
        dateOfBirth: criteria.dateOfBirth,
      };

      return await searchPatients(provider, searchCriteria);
    }

    return null;
  } catch (error) {
    console.error('Error finding patient by criteria:', error);
    throw error;
  }
}

/**
 * Helper function to search for patients and normalize results
 * @param provider The provider to use
 * @param searchCriteria The search criteria
 * @returns The first matching patient or null if none found
 */
async function searchPatients(
  provider: {
    getPatientService: () => IPatientService;
  },
  searchCriteria: Record<string, unknown>,
): Promise<Patient | null> {
  const patientsResult = await provider.getPatientService().getPatients(searchCriteria);

  // Extract patients array from different response formats
  let patients: Patient[] = [];

  if (Array.isArray(patientsResult)) {
    patients = patientsResult;
  } else if (patientsResult) {
    // Handle PaginatedResult with items property
    if ('items' in patientsResult && Array.isArray(patientsResult.items)) {
      patients = patientsResult.items;
    }
    // Handle response with data property (for tests and some APIs)
    else if ('data' in patientsResult && Array.isArray(patientsResult.data)) {
      patients = patientsResult.data;
    }
  }

  return patients.length > 0 ? patients[0] : null;
}
