import { handleError } from './errors';
import { createApiHandler, getProviderFromRequest } from './handler';
import type { ApiHandlerOptions } from './handler';
import type { ErrorResponse } from './errors';
import {
  ApiError,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  InternalServerError,
  ProviderApiError,
} from './errors';
import {
  getCachedAppointmentTypes,
  getCachedAppointmentPurposes,
  getCachedLocations,
  getCachedLocationById,
  getCachedClinics,
  getCachedClinicById,
  getCachedUsers,
  getCachedUserById,
  clearCache,
  clearAllCaches,
  getCacheStats,
} from './cache';
import {
  isChicagoDST,
  ensureChicagoTimezone,
  ensureAppointmentTimezones,
  parseISODateString,
} from './timezone';

export {
  handleError,
  createApiHandler,
  getProviderFromRequest,
  ApiError,
  BadRequestError,
  UnauthorizedError,
  ForbiddenError,
  NotFoundError,
  ConflictError,
  InternalServerError,
  ProviderApiError,
  getCachedAppointmentTypes,
  getCachedAppointmentPurposes,
  getCachedLocations,
  getCachedLocationById,
  getCachedClinics,
  getCachedClinicById,
  getCachedUsers,
  getCachedUserById,
  clearCache,
  clearAllCaches,
  getCacheStats,
  isChicagoDST,
  ensureChicagoTimezone,
  ensureAppointmentTimezones,
  parseISODateString,
};

export type { ApiHandlerOptions, ErrorResponse };
