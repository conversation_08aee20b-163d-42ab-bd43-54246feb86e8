/**
 * The method groups the hours by day of the week and returns a compact format
 * @param hours
 * @returns
 */
export const compactFormatLocationHours = (
  hours?: Record<string, { start: string; end: string } | null>,
): string[] => {
  if (!hours) {
    return [];
  }

  // Map day numbers to their shorthand representation
  const dayShortMap: Record<string, string> = {
    '1': 'M',
    '2': 'Tu',
    '3': 'W',
    '4': 'Th',
    '5': 'F',
    '6': 'Sa',
    '7': 'Su',
  };

  // Convert 24-hour format to 12-hour format
  const formatTime = (time: string): string => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? 'pm' : 'am';
    const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
    return `${formattedHour}${minutes === '00' ? '' : `:${minutes}`}${period}`;
  };

  // Group consecutive days with the same hours
  const groups: Array<{
    days: string[];
    hours: { start: string; end: string } | null;
  }> = [];

  let currentGroup: {
    days: string[];
    hours: { start: string; end: string } | null;
  } | null = null;

  // Process each day
  for (let day = 1; day <= 7; day++) {
    const dayKey = day.toString();
    const dayHours = hours[dayKey];
    const hasDayHours = Boolean(dayHours);

    // Skip if current day's hours are the same as previous group
    if (
      currentGroup &&
      ((!hasDayHours && currentGroup.hours === null) ||
        (hasDayHours &&
          currentGroup.hours !== null &&
          dayHours!.start === currentGroup.hours.start &&
          dayHours!.end === currentGroup.hours.end))
    ) {
      currentGroup.days.push(dayShortMap[dayKey]);
    } else {
      // Start a new group
      if (currentGroup) {
        groups.push(currentGroup);
      }

      if (hasDayHours) {
        currentGroup = {
          days: [dayShortMap[dayKey]],
          hours: dayHours,
        };
      } else {
        currentGroup = {
          days: [dayShortMap[dayKey]],
          hours: null,
        };
      }
    }
  }

  // Add the last group
  if (currentGroup) {
    groups.push(currentGroup);
  }

  // Format each group into a string
  return groups
    .filter(group => group.hours !== null) // Filter out closed days
    .map(group => {
      const { days, hours } = group;

      // Format the days part
      let daysStr: string;
      if (days.length === 1) {
        daysStr = days[0];
      } else {
        daysStr = `${days[0]}-${days[days.length - 1]}`;
      }

      // Format the hours part
      if (hours === null) {
        return '';
      }

      const formattedStart = formatTime(hours.start);
      const formattedEnd = formatTime(hours.end);

      return `${daysStr} ${formattedStart}-${formattedEnd}`;
    });
};
