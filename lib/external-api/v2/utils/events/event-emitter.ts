import { EventEmitter } from 'events';
import type { EventPayload } from './event.types';
/**
 * Singleton EventEmitter service for application-wide event handling
 * Allows services to emit events and other parts of the application to subscribe to them
 */
export class EventEmitterService {
  private static instance: EventEmitterService;
  private emitter: EventEmitter;

  private constructor() {
    this.emitter = new EventEmitter();
  }

  /**
   * Get the singleton instance of EventEmitterService
   * @returns EventEmitterService instance
   */
  public static getInstance(): EventEmitterService {
    if (!EventEmitterService.instance) {
      EventEmitterService.instance = new EventEmitterService();
    }
    return EventEmitterService.instance;
  }

  /**
   * Emit an event with payload
   * @param eventName Name of the event to emit
   * @param payload Data to pass to event subscribers
   */
  public emit<K extends keyof EventPayload>(eventName: K, payload: EventPayload[K]): void {
    this.emitter.emit(eventName as string, payload);
  }

  /**
   * Subscribe to an event
   * @param eventName Name of the event to subscribe to
   * @param listener Function to call when event is emitted
   * @returns Function to call to unsubscribe
   */
  public on<K extends keyof EventPayload>(
    eventName: K,
    listener: (payload: EventPayload[K]) => void,
  ): () => void {
    this.emitter.on(eventName as string, listener);

    // Return unsubscribe function
    return () => {
      this.emitter.off(eventName as string, listener);
    };
  }

  /**
   * Subscribe to an event once
   * @param eventName Name of the event to subscribe to
   * @param listener Function to call when event is emitted
   */
  public once<K extends keyof EventPayload>(
    eventName: K,
    listener: (payload: EventPayload[K]) => void,
  ): void {
    this.emitter.once(eventName as string, listener);
  }
}

// Export singleton instance
export const eventEmitter = EventEmitterService.getInstance();
