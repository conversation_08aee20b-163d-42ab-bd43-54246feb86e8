import type { Appointment } from '../../models/types';
import type { Call } from '@/models/Call';

/**
 * Supported event payloads
 */
export type EventPayload = {
  'appointment:created': { appointment: Appointment; sessionId: string };
  'call:created': Call;
};

// Define event names as constants to prevent typos
export const EVENTS = {
  APPOINTMENT: {
    CREATED: 'appointment:created' as const,
  },
  CALL: {
    CREATED: 'call:created' as const,
  },
};
