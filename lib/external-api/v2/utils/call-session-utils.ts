/**
 * Checks if a session ID is an audio session ID
 * Audio sessions use a 25 character session ID (065tlJ8BiLaT4OJGJW8X5J6yA)
 * @param sessionId The session ID to check
 * @returns True if the session ID is an audio session ID, false otherwise
 */
export const isAudioSessionId = (sessionId: string | undefined): boolean =>
  typeof sessionId === 'string' && sessionId.length === 25;

/**
 * Checks if a session ID is a messenger session ID
 * Messenger sessions starts with "dfMessenger-" (dfMessenger-a19ec4e1-72a3-4618-aefc-c64009554411)
 * @param sessionId The session ID to check
 * @returns True if the session ID is a messenger session ID, false otherwise
 */
export const isMessengerSessionId = (sessionId: string | undefined): boolean =>
  typeof sessionId === 'string' && sessionId.startsWith('dfMessenger');
