import { IProvider } from './types';

/**
 * Configuration for a provider
 */
export interface ProviderConfig {
  // Common configuration properties
  name: string;
  [key: string]: unknown;
}

/**
 * Factory for creating provider instances
 */
export class ProviderFactory {
  private providerConstructors: Map<string, new (config: ProviderConfig) => IProvider> = new Map();

  /**
   * Registers a provider constructor with the factory
   * @param name Name of the provider
   * @param constructor Constructor function for the provider
   */
  registerProviderConstructor(
    name: string,
    constructor: new (config: ProviderConfig) => IProvider,
  ): void {
    this.providerConstructors.set(name, constructor);
  }

  /**
   * Creates a provider instance based on configuration
   * @param config Configuration for the provider
   * @returns A provider instance
   * @throws Error if the provider constructor is not registered
   */
  createProvider(config: ProviderConfig): IProvider {
    const Constructor = this.providerConstructors.get(config.name);

    if (!Constructor) {
      throw new Error(`Provider constructor for ${config.name} not found`);
    }

    return new Constructor(config);
  }
}
