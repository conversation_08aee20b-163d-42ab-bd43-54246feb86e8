import type { CreateAppointmentDto, UpdateAppointmentDto } from '../models/dto';
import type { PaginatedResult, PaginationParams } from '../models/pagination';
import type {
  Appointment,
  AppointmentPurpose,
  AppointmentStatus,
  AppointmentType,
  AvailableSlot,
  Clinic,
  Insurance,
  Location,
  Patient,
  PatientType,
  User,
} from '../models/types';

// Base provider interface
export interface IProvider {
  name: string;
  getClinicService(): IClinicService;
  getLocationService(): ILocationService;
  getPatientService(): IPatientService;
  getUserService(): IUserService;
  getAppointmentService(): IAppointmentService;
}

// Service interfaces
export interface IClinicService {
  getClinics(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Clinic>>;
  getClinicById(id: string): Promise<Clinic | null>;
  getClinicByPhone(phone: string): Promise<Clinic | null>;
}

export interface ILocationService {
  getLocations(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Location>>;
  getLocationById(id: string): Promise<Location | null>;
  getLocationByPhone(phone: string): Promise<Location | null>;
}

export interface IPatientService {
  getPatients(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Patient>>;
  getPatientById(id: string): Promise<Patient | null>;
  getPatientByIdWithoutInsurance(id: string): Promise<Patient | null>;
  getPatientByPhone(phone: string): Promise<Patient | null>;
  getPatientByEmail(email: string): Promise<Patient | null>;
  getPatientByFullNameAndDob(fullName: string, dob: string): Promise<Patient | null>;

  /**
   * Create a new patient
   * @param patientData Patient data to create
   * @returns Created patient data
   * @throws ConflictError if patient already exists
   * @throws BadRequestError if patient data is invalid
   */
  createPatient(patientData: Omit<Patient, 'id' | 'providerInfo'>): Promise<Patient>;

  /**
   * Update an existing patient
   * @param id Patient ID to update
   * @param patientData Patient data to update
   * @returns Updated patient data
   * @throws NotFoundError if patient not found
   * @throws BadRequestError if patient data is invalid
   */
  updatePatient(
    id: string,
    patientData: Partial<Omit<Patient, 'id' | 'providerInfo'>>,
  ): Promise<Patient>;

  // Patient type methods
  getPatientTypes(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<PatientType>>;
  getPatientTypeById(id: string): Promise<PatientType | null>;

  // Insurance methods
  getPatientInsurances(patientId: string): Promise<Insurance[]>;
  getInsuranceById(id: string): Promise<Insurance | null>;
  createInsurance(insurance: Omit<Insurance, 'id' | 'providerInfo'>): Promise<Insurance>;
  updateInsurance(id: string, insurance: Partial<Insurance>): Promise<Insurance>;
  deleteInsurance(id: string): Promise<boolean>;
}

export interface IUserService {
  getUsers(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<User>>;
  getUserById(id: string): Promise<User | null>;
  getUserByPhone(phone: string): Promise<User | null>;
  getUserByEmail(email: string): Promise<User | null>;
  getUserByFullName(fullName: string): Promise<User | null>;
}

export interface IAppointmentService {
  getAppointments(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentById(id: string): Promise<Appointment | null>;
  createAppointment(data: CreateAppointmentDto): Promise<Appointment>;
  updateAppointment(id: string, data: UpdateAppointmentDto): Promise<Appointment>;
  cancelAppointment(id: string, reason?: string): Promise<boolean>;
  confirmAppointment(id: string): Promise<boolean>;
  changeAppointment(id: string, data: UpdateAppointmentDto): Promise<Appointment>;
  getAppointmentByPatientId(
    patientId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getLastFulfilledAppointmentByPatientId(patientId: string): Promise<Appointment | null>;
  getAppointmentByClinicId(
    clinicId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByLocationId(
    locationId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDate(
    date: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDateRange(
    startDate: string,
    endDate: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDateRangeAndLocationId(
    startDate: string,
    endDate: string,
    locationId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentsByStatus(
    status: AppointmentStatus,
    params: {
      startDate?: string;
      endDate?: string;
      startLastUpdated?: string;
      endLastUpdated?: string;
      locationId?: string;
      pagination?: PaginationParams;
    },
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentsByStatuses(params: {
    statuses: AppointmentStatus[];
    startDate?: string;
    endDate?: string;
    startLastUpdated?: string;
    endLastUpdated?: string;
    locationId?: string;
    pagination?: PaginationParams;
  }): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDateRangeAndClinicId(
    startDate: string,
    endDate: string,
    clinicId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDateRangeAndProviderId(
    startDate: string,
    endDate: string,
    providerId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;
  getAppointmentByDateRangeAndPatientId(
    startDate: string,
    endDate: string,
    patientId: string,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Appointment>>;

  // Appointment types methods
  getAppointmentTypes(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AppointmentType>>;
  getAppointmentTypeById(id: string): Promise<AppointmentType | null>;

  // Appointment availability methods
  getAvailableSlots(
    filters: {
      appointmentTypeId: string;
      startDate: string;
      endDate: string;
      practitionerId?: string;
      locationId?: string;
    },
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AvailableSlot>>;

  // Appointment purpose methods
  getAppointmentPurposes(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<AppointmentPurpose>>;
  getAppointmentPurposeById(id: string): Promise<AppointmentPurpose | null>;

  /**
   * Get appointments for a patient with optional rescheduling logic
   * @param patientId Patient ID
   * @param isForRescheduling Whether to include no-show appointments for rescheduling
   * @returns Promise with paginated result of appointments
   */
  getAppointmentsForRescheduling(
    patientId: string,
    isForRescheduling?: boolean,
  ): Promise<PaginatedResult<Appointment>>;
}
