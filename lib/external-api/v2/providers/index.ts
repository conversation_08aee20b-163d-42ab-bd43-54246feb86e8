import { ProviderRegistry } from './registry';
import { ProviderFactory } from './factory';
import type { ProviderConfig } from './factory';
import type {
  IProvider,
  IClinicService,
  ILocationService,
  IPatientService,
  IUserService,
  IAppointmentService,
} from './types';
import { NextechProvider } from './nextech';
import type { NextechConfig } from './nextech';

// Singleton instances
const providerRegistry = new ProviderRegistry('nextech');
const providerFactory = new ProviderFactory();

export { ProviderRegistry, ProviderFactory, NextechProvider, providerRegistry, providerFactory };

export type {
  ProviderConfig,
  NextechConfig,
  IProvider,
  IClinicService,
  ILocationService,
  IPatientService,
  IUserService,
  IAppointmentService,
};
