import { IClinicService } from '@/lib/external-api/v2';
import { Clinic } from '../../../models/types';
import { NextechApiClient } from '../client';
import { NextechClinicResponse, mapNextechClinicToClinic } from './mappers';
import { PaginationParams, PaginatedResult, createPaginationLinks } from '@/lib/external-api/v2';
import logger from '../../../utils/logger';

// TODO VK: figure out how to get a clinic from Nextech API
/**
 * NextechClinicService
 * Implementation of IClinicService for Nextech API
 */
export class NextechClinicService implements IClinicService {
  constructor(private readonly client: NextechApiClient) {}
  private readonly defaultLimit = 10;
  private readonly maxLimit = 50;

  /**
   * Get all clinics from Nextech
   * @param filters Optional filters (supported: name, phone)
   * @param pagination Optional pagination parameters
   * @returns Paginated result of mapped Clinic objects
   */
  async getClinics(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Clinic>> {
    try {
      // Extract pagination parameters with defaults
      const limit = Math.min(pagination?.limit || this.defaultLimit, this.maxLimit);
      const offset = pagination?.offset || 0;

      // Build query parameters from filters
      const queryParams: Record<string, string> = {
        limit: limit.toString(),
        offset: offset.toString(),
      };

      if (filters?.name && typeof filters.name === 'string') {
        queryParams.name = filters.name;
      }

      if (filters?.phone && typeof filters.phone === 'string') {
        queryParams.phone = filters.phone;
      }

      // Call Nextech API to get practices (clinics)
      const response = await this.client.get<{
        items: NextechClinicResponse[];
        totalCount?: number;
      }>('/v1/practices', { params: queryParams });

      // Map Nextech response to our common Clinic model
      const clinics = response.items.filter(clinic => clinic.active).map(mapNextechClinicToClinic);

      // Get total count from response or use array length
      const totalCount = response.totalCount || clinics.length;

      // Create pagination links
      const links = createPaginationLinks('/v1/practices', totalCount, limit, offset);

      // Return paginated result
      return {
        items: clinics,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + clinics.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Clinic Service - Get Clinics',
          error,
        },
        'Error fetching clinics from Nextech API',
      );

      // Return empty paginated result
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get a specific clinic by ID
   * @param id The clinic ID
   * @returns The mapped Clinic object or null if not found
   */
  async getClinicById(id: string): Promise<Clinic | null> {
    try {
      // Call Nextech API to get specific practice (clinic)
      const response = await this.client.get<NextechClinicResponse>(`/v1/practices/${id}`);

      // Only return active clinics
      if (!response.active) {
        return null;
      }

      // Map Nextech response to our common Clinic model
      return mapNextechClinicToClinic(response);
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Clinic Service - Get Clinic By ID',
          clinicId: id,
          error,
        },
        'Error fetching clinic from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get a clinic by phone number
   * @param phone The phone number to search for
   * @returns The mapped Clinic object or null if not found
   */
  async getClinicByPhone(phone: string): Promise<Clinic | null> {
    try {
      // Call getClinics with phone filter
      const result = await this.getClinics({ phone });

      // Return first matching clinic or null
      return result.items.length > 0 ? result.items[0] : null;
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Clinic Service - Get Clinic By Phone',
          error,
        },
        'Error fetching clinic by phone from Nextech API',
      );

      return null;
    }
  }
}
