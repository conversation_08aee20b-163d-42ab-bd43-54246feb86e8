import { IUserService } from '../../../providers/types';
import { User } from '../../../models/types';
import { NextechApiClient } from '../client';
import { FHIRPractitioner, FHIRBundle, mapFHIRPractitionerToUser } from './mappers';
import { PaginationParams, PaginatedResult } from '../../../models/pagination';
import logger from '../../../utils/logger';

/**
 * Nextech User Service
 * Implements IUserService for Nextech API
 */
export class NextechUserService implements IUserService {
  private client: NextechApiClient;
  private readonly baseUrl = '/Practitioner';
  private readonly defaultLimit = 10;
  private readonly maxLimit = 50;
  // currently hardcoded to the practice's phone number
  private readonly defaultPhone = '************';

  constructor(client: NextechApiClient) {
    this.client = client;
  }

  /**
   * Get all users/practitioners with optional filters
   * @param filters Optional filters to apply
   * @param pagination Optional pagination parameters
   * @returns Paginated result of mapped User objects
   */
  async getUsers(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<User>> {
    try {
      // Extract pagination parameters with defaults
      const limit = Math.min(pagination?.limit || this.defaultLimit, this.maxLimit);
      const offset = pagination?.offset || 0;

      // Build query parameters from filters
      const queryParams = new URLSearchParams();

      // Apply filters if provided
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.append(key, String(value));
          }
        });
      }

      // Add pagination parameters - FHIR uses _count and _getpagesoffset
      queryParams.set('_count', String(limit));
      queryParams.set('_getpagesoffset', String(offset));

      // Only add active=true if not explicitly set in filters
      if (!filters || filters.active === undefined) {
        queryParams.set('active', 'true');
      }

      // Add phone filter
      queryParams.set('phone', this.defaultPhone);

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if response has entries
      const users =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => entry.resource)
              .map(entry => mapFHIRPractitionerToUser(entry.resource))
          : [];

      // Get total count from response or use array length
      const totalCount = response.total || users.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: users,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + users.length < totalCount,
          links,
        },
      };
    } catch (error) {
      // Log error and return empty paginated result
      logger.error(
        {
          provider: 'nextech',
          context: 'User Service - Get Users',
          error,
        },
        'Error fetching users from Nextech API',
      );

      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get user by ID
   * @param id User ID
   * @returns User or null if not found
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      // Make API request
      const response = await this.client.get<FHIRPractitioner>(`${this.baseUrl}/${id}`);

      // Map response to common model
      return mapFHIRPractitionerToUser(response);
    } catch (error) {
      // Log error and return null
      logger.error(
        {
          provider: 'nextech',
          context: 'User Service - Get User By ID',
          userId: id,
          error,
        },
        'Error fetching user from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get user by phone number
   * @param phone Phone number
   * @returns User or null if not found
   */
  async getUserByPhone(phone: string): Promise<User | null> {
    try {
      // Remove non-numeric characters for consistent search
      const cleanPhone = phone.replace(/\D/g, '');

      // Search using telecom parameter for FHIR
      const queryParams = new URLSearchParams({
        phone: cleanPhone,
        _count: '1', // Only need one result
      });

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any users were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return null;
      }

      // Map the first user to common model
      return mapFHIRPractitionerToUser(response.entry[0].resource);
    } catch (error) {
      // Log error and return null
      logger.error(
        {
          provider: 'nextech',
          context: 'User Service - Get User By Phone',
          error,
        },
        'Error searching user by phone from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get user by email address
   * @param email Email address
   * @returns User or null if not found
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      // Search using telecom parameter for FHIR
      const queryParams = new URLSearchParams({
        telecom: `email|${email.toLowerCase()}`, // Normalize email to lowercase
        _count: '1', // Only need one result
        _getpagesoffset: '0',
      });

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any users were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return null;
      }

      // Map the first user to common model
      return mapFHIRPractitionerToUser(response.entry[0].resource);
    } catch (error) {
      // Log error and return null
      logger.error(
        {
          provider: 'nextech',
          context: 'User Service - Get User By Email',
          error,
        },
        'Error searching user by email from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get user by full name
   * @param fullName Full name (format: "LastName, FirstName" or "FirstName LastName")
   * @returns User or null if not found
   */
  async getUserByFullName(fullName: string): Promise<User | null> {
    try {
      // Parse full name to extract first and last name
      let firstName = '';
      let lastName = '';

      // Check if the format is "LastName, FirstName"
      if (fullName.includes(',')) {
        const parts = fullName.split(',');
        lastName = parts[0].trim();
        firstName = parts[1].trim();
      } else {
        // Assume format is "FirstName LastName"
        const parts = fullName.split(' ');
        firstName = parts[0].trim();
        lastName = parts.slice(1).join(' ').trim();
      }

      // Search using name parameters
      const queryParams = new URLSearchParams({
        family: lastName,
        given: firstName,
        _count: '1', // Only need one result
        _getpagesoffset: '0',
      });

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any users were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return null;
      }

      // Map the first user to common model
      return mapFHIRPractitionerToUser(response.entry[0].resource);
    } catch (error) {
      // Log error and return null
      logger.error(
        {
          provider: 'nextech',
          context: 'User Service - Get User By Name',
          error,
        },
        'Error searching user by name from Nextech API',
      );

      return null;
    }
  }
}
