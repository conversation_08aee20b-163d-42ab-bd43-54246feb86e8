import {
  IPatientService,
  NotFoundError,
  PaginatedResult,
  PaginationParams,
  createPaginationLinks,
  extractPaginationFromQuery,
} from '@/lib/external-api/v2';
import { BadRequestError, ConflictError } from '@/lib/external-api/v2/utils/errors';
import { Insurance, Patient, PatientType } from '../../../models/types';
import logger from '../../../utils/logger';
import { NextechApiClient } from '../client';
import {
  FHIRBundle,
  FHIRCoverage,
  FHIRPatient,
  NextechPatientTypeResponse,
  mapFHIRCoverageToInsurance,
  mapFHIRPatientToPatient,
  mapInsuranceToFHIRCoverage,
  mapNextechPatientTypeToPatientType,
  mapPatientToFHIRPatient,
  mapPatientToFHIRPatientWithId,
} from './mappers';

/**
 * Nextech Patient Service
 * Implements IPatientService for Nextech API
 */
export class NextechPatientService implements IPatientService {
  private client: NextechApiClient;
  private readonly baseUrl = '/Patient';
  private readonly coverageUrl = '/Coverage';
  private readonly defaultLimit = 10; // Changed from 50 to match Nextech's default
  private readonly maxLimit = 50;

  constructor(client: NextechApiClient) {
    this.client = client;
  }

  /**
   * Get all patients with optional filters and pagination
   * @param filters Optional filters to apply
   * @param pagination Optional pagination parameters
   * @returns Paginated result of patients
   */
  async getPatients(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Patient>> {
    logger.info(
      {
        provider: 'nextech',
        context: 'Patient Service - Get Patients',
        request: {
          filters,
          pagination,
        },
      },
      'Patient Service - Get Patients - request',
    );

    try {
      // Extract pagination parameters
      const { limit, offset } = pagination || extractPaginationFromQuery(filters || {});
      const actualLimit = Math.min(limit || this.defaultLimit, this.maxLimit);

      // Build query parameters from filters
      const queryParams = new URLSearchParams();

      // Apply filters if provided
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (
            value !== undefined &&
            value !== null &&
            key !== 'limit' &&
            key !== 'offset' &&
            key !== 'fullName' &&
            key !== 'firstName' &&
            key !== 'lastName' &&
            key !== 'dateOfBirth' &&
            key !== 'phoneNumber'
          ) {
            queryParams.append(key, String(value));
          }
        });
      }

      // family	query	The family (last) name of the patient	No	string
      // given	query	The given (first) name of the patient	No	string
      // Add full name parameter
      if (filters?.fullName) {
        // If fullName is provided, use first 3 letters for name search
        const useFirstThreeLettersForNameSearch = true;

        const fullName = filters.fullName as string;
        const parts = fullName.trim().split(/\s+/); // Split by any whitespace

        if (parts.length > 0) {
          // First part is the given name
          const given = parts[0];
          if (useFirstThreeLettersForNameSearch && given.length > 2) {
            // If using first 3 letters, take first 3 letters of given name
            queryParams.append('given', given.substring(0, 3));
          } else {
            queryParams.append('given', given);
          }

          // If there are more parts, join them as the family name
          if (parts.length > 1) {
            const family = parts.slice(1).join(' ');
            if (useFirstThreeLettersForNameSearch && family.length > 2) {
              // If using first 3 letters, take first 3 letters of family name
              queryParams.append('family', family.substring(0, 3));
            } else {
              queryParams.append('family', family);
            }
          }
        }
      }

      if (filters?.firstName) {
        queryParams.append('given', filters.firstName as string);
      }

      if (filters?.lastName) {
        queryParams.append('family', filters.lastName as string);
      }

      // birthdate	query	The patient's date of birth formatted as YYYY-MM-DD	No	dateTime
      if (filters?.dateOfBirth) {
        queryParams.append('birthdate', filters.dateOfBirth as string);
      }

      // The patient's phone number which will be matched against any phone number (home, cell, etc.)
      if (filters?.phoneNumber) {
        // Clean the phone number to remove non-numeric characters
        const cleanPhone = (filters.phoneNumber as string).replace(/\D/g, '');

        // Create an array of possible phone formats to search
        const phoneFormats = [cleanPhone];

        // If 10 digits (US number without country code), add with country code
        if (cleanPhone.length === 10) {
          phoneFormats.push(`1${cleanPhone}`);
        }

        // If 11 digits with US country code, add without country code
        if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
          phoneFormats.push(cleanPhone.substring(1));
        }

        // Set first phone format for initial search
        queryParams.append('phone', phoneFormats[0]);

        // Store additional formats for potential secondary searches
        const additionalPhoneFormats = phoneFormats.slice(1);

        // If we have multiple formats, make multiple requests and combine results
        if (additionalPhoneFormats.length > 0) {
          return this.searchPatientsWithPhoneFormats(
            queryParams,
            phoneFormats,
            actualLimit,
            offset,
          );
        }
      }

      // Add pagination parameters - FHIR uses _count and _getpagesoffset
      queryParams.append('_count', String(actualLimit));
      queryParams.append('_getpagesoffset', String(offset));

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Map API response to common model with deduplication
      const allPatients =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => {
                // Check if it's a Patient resource
                return entry.resource && entry.resource.resourceType === 'Patient';
              })
              .map(entry => mapFHIRPatientToPatient(entry.resource as FHIRPatient))
          : [];

      // Deduplicate patients by phone and birthDate
      const patients = this.deduplicatePatients(allPatients);

      // Extract total count from response or use array length if not available
      const totalCount = response.total || patients.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: patients,
        pagination: {
          totalCount,
          limit: actualLimit,
          offset,
          hasMore: offset + patients.length < totalCount,
          links,
        },
      };
    } catch (error) {
      // Log error (without PHI) and return empty paginated result instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patients',
          error,
        },
        'Error fetching patients from Nextech API',
      );

      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Helper method to search patients with multiple phone formats
   * @param baseParams Base query parameters
   * @param phoneFormats Array of phone formats to try
   * @param limit Results limit
   * @param offset Results offset
   * @returns Paginated result of patients
   */
  private async searchPatientsWithPhoneFormats(
    baseParams: URLSearchParams,
    phoneFormats: string[],
    limit: number,
    offset: number,
  ): Promise<PaginatedResult<Patient>> {
    // Array to store patients found with any phone format
    const allPatients: Patient[] = [];
    let totalCount = 0;
    const responseLinks: Record<string, string> = {};

    // Try each phone format in sequence
    for (const phoneFormat of phoneFormats) {
      // Create a new query with this phone format
      const formatParams = new URLSearchParams(baseParams.toString());
      // Replace the phone parameter
      formatParams.delete('phone');
      formatParams.append('phone', phoneFormat);

      try {
        const url = `${this.baseUrl}?${formatParams.toString()}`;
        const response = await this.client.get<FHIRBundle>(url);

        // Extract patients from this response
        const responsePatients =
          response.entry && Array.isArray(response.entry)
            ? response.entry
                .filter(entry => entry.resource && entry.resource.resourceType === 'Patient')
                .map(entry => mapFHIRPatientToPatient(entry.resource as FHIRPatient))
            : [];

        // Deduplicate patients by phone and birthDate
        const patients = this.deduplicatePatients(responsePatients);

        if (patients.length > 0) {
          logger.info(
            {
              provider: 'nextech',
              context: 'Patient Service - Get Patients',
              phoneFormatUsed: phoneFormat,
              resultCount: patients.length,
            },
            'Found patients using phone format',
          );

          // Add these patients to our combined results
          allPatients.push(...patients);

          // Update total count (use the largest count)
          totalCount = Math.max(totalCount, response.total || patients.length);

          // Store links from the first successful response
          if (
            Object.keys(responseLinks).length === 0 &&
            response.link &&
            Array.isArray(response.link)
          ) {
            response.link.forEach(link => {
              if (link.relation === 'first') responseLinks.first = link.url;
              if (link.relation === 'next') responseLinks.next = link.url;
              if (link.relation === 'last') responseLinks.last = link.url;
              if (link.relation === 'prev') responseLinks.prev = link.url;
            });
          }
        }
      } catch (error) {
        // Log error but continue with next format
        logger.warn(
          {
            provider: 'nextech',
            context: 'Patient Service - Phone Format Search',
            phoneFormat,
            error,
          },
          'Error searching with alternate phone format',
        );
      }
    }

    // Remove duplicate patients (by ID)
    const uniquePatients = allPatients.filter(
      (patient, index, self) => index === self.findIndex(p => p.id === patient.id),
    );

    // Return paginated result with combined unique patients
    return {
      items: uniquePatients,
      pagination: {
        totalCount,
        limit,
        offset,
        hasMore: offset + uniquePatients.length < totalCount,
        links: responseLinks,
      },
    };
  }

  async getPatientByIdWithoutInsurance(id: string): Promise<Patient | null> {
    try {
      const response = await this.client.get<FHIRPatient>(`${this.baseUrl}/${id}`);
      return mapFHIRPatientToPatient(response);
    } catch (error) {
      // Handle 404 errors by returning null
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }

      // Log error (without PHI) and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient By ID',
          patientIdPrefix: id.substring(0, 3), // Only log prefix for privacy
          error,
        },
        'Error fetching patient by ID from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get patient by ID
   * @param id Patient ID
   * @returns Patient or null if not found
   */
  async getPatientById(id: string): Promise<Patient | null> {
    try {
      // Make API request
      const response = await this.client.get<FHIRPatient>(`${this.baseUrl}/${id}`);

      // Map response to common model
      const patient = mapFHIRPatientToPatient(response);

      // Flag to track if we need to verify insurance was saved in Nextech
      let insuranceFoundInNotes = false;

      // Check if there are insurances in the notes field (legacy approach)
      if (patient.notes) {
        try {
          const notesObj = JSON.parse(patient.notes);
          if (notesObj && typeof notesObj === 'object') {
            // Convert legacy insurance notes to standard format
            if (notesObj.insurance_company_name || notesObj.insurance_member_id) {
              const legacyInsurance: Insurance = {
                companyName: notesObj.insurance_company_name || 'Unknown',
                memberId: notesObj.insurance_member_id || '',
                groupNumber: notesObj.insurance_group_number || '',
                isPrimary: true,
                patientId: id,
                providerInfo: {
                  provider: 'nextech',
                  externalId: 'legacy',
                },
              };

              patient.insurances = [legacyInsurance];
              insuranceFoundInNotes = true;
            }
          }
        } catch (parseError) {
          // If notes can't be parsed as JSON, just ignore
          logger.warn(
            {
              provider: 'nextech',
              context: 'Patient Service - Parse Insurance Notes',
              error: parseError,
            },
            'Failed to parse patient notes as JSON',
          );
        }
      }

      // Always fetch from Coverage endpoint regardless of notes to verify insurance was saved properly
      try {
        const nexTechInsurances = await this.getPatientInsurances(id);

        if (nexTechInsurances.length > 0) {
          // If we found insurance in both places, use the nexTechInsurances as source of truth
          patient.insurances = nexTechInsurances;
        } else if (insuranceFoundInNotes) {
          // If we found insurance in notes but not in Nextech system, log warning
          logger.warn(
            {
              provider: 'nextech',
              context: 'Patient Service - Get Patient By ID',
              patientId: id,
            },
            'Insurance found in patient notes but not properly saved in Nextech system',
          );

          // Save the insurance to Nextech system
          try {
            if (patient.insurances && patient.insurances.length > 0) {
              const insuranceToCreate: Omit<Insurance, 'id' | 'providerInfo'> = {
                ...patient.insurances[0],
                patientId: id,
              };

              // Create the insurance in Nextech
              const createdInsurance = await this.createInsurance(insuranceToCreate);

              // Update the patient's insurance with the properly saved one
              patient.insurances = [createdInsurance];

              logger.info(
                {
                  provider: 'nextech',
                  context: 'Patient Service - Get Patient By ID',
                  patientIdPrefix: id.substring(0, 3),
                },
                'Successfully saved insurance from notes to Nextech system',
              );
            }
          } catch (insuranceCreateError) {
            // Log error but continue - we still want to return the patient
            logger.error(
              {
                provider: 'nextech',
                context: 'Patient Service - Get Patient By ID - Save Insurance',
                patientIdPrefix: id.substring(0, 3),
                error: insuranceCreateError,
              },
              'Failed to save insurance from notes to Nextech system',
            );
          }
        }
      } catch (coverageError) {
        // Log error but continue - the patient data is still valid without insurance
        logger.warn(
          {
            provider: 'nextech',
            context: 'Patient Service - Get Patient Insurances',
            error: coverageError,
          },
          'Failed to fetch patient insurances',
        );
      }

      return patient;
    } catch (error) {
      // Handle 404 errors by returning null
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }

      // Log error (without PHI) and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient By ID',
          patientIdPrefix: id.substring(0, 3), // Only log prefix for privacy
          error,
        },
        'Error fetching patient by ID from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get insurances for a patient
   * @param patientId Patient ID
   * @returns Array of Insurance objects
   */
  async getPatientInsurances(patientId: string): Promise<Insurance[]> {
    try {
      // Query parameters to find coverages for this patient
      const queryParams = new URLSearchParams({
        patient: patientId,
        _count: '100', // Get a high count to avoid pagination
      });

      // Make API request
      const url = `${this.coverageUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any coverages were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return [];
      }

      // Map each coverage to our Insurance model
      const insurances: Insurance[] = response.entry
        .filter(entry => entry.resource && entry.resource.resourceType === 'Coverage')
        .map((entry, index) => {
          const insurance = mapFHIRCoverageToInsurance(entry.resource as FHIRCoverage);

          // The first coverage is likely primary
          if (index === 0) {
            insurance.isPrimary = true;
          } else {
            insurance.isPrimary = false;
          }

          return insurance;
        });

      return insurances;
    } catch (error) {
      // Log error and return empty array instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient Insurances',
          patientIdPrefix: patientId.substring(0, 3), // Only log prefix for privacy
          error,
        },
        'Error fetching patient insurances from Nextech API',
      );

      return [];
    }
  }

  /**
   * Get insurance by ID
   * @param id Insurance ID
   * @returns Insurance or null if not found
   */
  async getInsuranceById(id: string): Promise<Insurance | null> {
    try {
      // Make API request
      const response = await this.client.get<FHIRCoverage>(`${this.coverageUrl}/${id}`);

      // Map response to common model
      return mapFHIRCoverageToInsurance(response);
    } catch (error) {
      // Handle 404 errors by returning null
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }

      // Log error and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Insurance By ID',
          insuranceIdPrefix: id.substring(0, 3), // Only log prefix for privacy
          error,
        },
        'Error fetching insurance by ID from Nextech API',
      );

      return null;
    }
  }

  /**
   * Create a new insurance record
   * @param insurance Insurance data to create
   * @returns Created insurance
   */
  async createInsurance(insurance: Omit<Insurance, 'id' | 'providerInfo'>): Promise<Insurance> {
    const maxRetries = 1;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Validate required fields
        if (!insurance.patientId) {
          throw new BadRequestError('Patient ID is required for insurance creation');
        }

        if (!insurance.companyName) {
          throw new BadRequestError('Insurance company name is required');
        }

        if (!insurance.memberId) {
          throw new BadRequestError('Member ID is required');
        }

        // Map to FHIR Coverage (for POST, do not include id)
        const fhirCoverage = mapInsuranceToFHIRCoverage(
          insurance as Insurance,
          insurance.patientId,
          true,
        );

        // Log the payload for debugging
        logger.info(
          {
            provider: 'nextech',
            context: 'Patient Service - Create Insurance',
            payload: JSON.stringify(fhirCoverage),
            attempt: attempt + 1,
            maxRetries,
          },
          `Sending Coverage payload to Nextech API (Attempt ${attempt + 1}/${maxRetries + 1})`,
        );

        // Create coverage in Nextech
        const response = await this.client.post<FHIRCoverage>(
          this.coverageUrl,
          fhirCoverage as unknown as { [key: string]: unknown },
        );

        // Map response back to our model
        return mapFHIRCoverageToInsurance(response);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Don't retry for these specific errors
        if (
          error instanceof BadRequestError ||
          (error instanceof Error && error.message.includes('409'))
        ) {
          throw error;
        }

        // For server errors (500, 502) or network errors, retry if attempts remain
        if (
          attempt < maxRetries &&
          error instanceof Error &&
          (error.message.includes('500') ||
            error.message.includes('502') ||
            error.message.includes('timeout') ||
            error.message.includes('network'))
        ) {
          // Log retry
          logger.warn(
            {
              provider: 'nextech',
              context: 'Patient Service - Create Insurance - Retry',
              attempt: attempt + 1,
              maxRetries,
              error: error instanceof Error ? error.message : String(error),
            },
            `Retrying insurance creation after error (Attempt ${attempt + 1}/${maxRetries + 1})`,
          );

          // Wait for a bit before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt)));
          continue;
        }

        // Handle specific error cases
        if (error instanceof Error) {
          // Check for conflict (insurance already exists)
          if (error.message.includes('409')) {
            throw new ConflictError('Insurance already exists', {
              fields: ['companyName', 'memberId', 'patientId'],
            });
          }

          // Enhanced validation error handling with more details
          if (error.message.includes('400')) {
            // Try to extract more detailed error information from the response
            let responseData: Record<string, unknown> = {};
            let errorDetails: Record<string, unknown> = {};

            try {
              // Check if there's a response property with data
              // Access error response data safely using unknown type
              const errorObj = error as unknown;
              if (
                typeof errorObj === 'object' &&
                errorObj !== null &&
                'response' in errorObj &&
                typeof errorObj.response === 'object' &&
                errorObj.response !== null &&
                'data' in errorObj.response
              ) {
                responseData = errorObj.response.data as Record<string, unknown>;

                // If the API returned specific error details, capture them
                if (
                  typeof errorObj.response.data === 'object' &&
                  errorObj.response.data !== null &&
                  'errors' in errorObj.response.data
                ) {
                  errorDetails = errorObj.response.data.errors as Record<string, unknown>;
                }
              }
            } catch (parseError) {
              // Ignore parse errors, just continue with what we have
              console.warn('Error parsing error response data:', parseError);
            }

            // Create a more detailed error message
            const details = {
              originalError: error.message,
              payload: JSON.stringify(insurance),
              fhirPayload: JSON.stringify(
                mapInsuranceToFHIRCoverage(insurance as Insurance, insurance.patientId, true),
              ),
              responseData,
              errorDetails,
              possibleIssues: [
                'Incorrect relationship format',
                'Invalid patient ID reference',
                'Required fields missing in the FHIR Coverage format',
                'Invalid member ID format',
                'Missing or invalid identifiers',
              ],
            };

            logger.error(
              {
                provider: 'nextech',
                context: 'Patient Service - Create Insurance - 400 Error',
                error: details,
              },
              'Nextech API rejected insurance data with 400 Bad Request',
            );

            throw new BadRequestError('Invalid insurance data', details);
          }

          // Add more detailed error information for server errors
          if (error.message.includes('500') || error.message.includes('502')) {
            throw new BadRequestError('Provider API Error', {
              originalError: error.message,
              payload: JSON.stringify(insurance),
              fhirPayload: JSON.stringify(
                mapInsuranceToFHIRCoverage(insurance as Insurance, insurance.patientId, true),
              ),
            });
          }

          // Log error and rethrow
          logger.error(
            {
              provider: 'nextech',
              context: 'Patient Service - Create Insurance',
              error,
              payload: JSON.stringify(insurance),
              fhirPayload: JSON.stringify(
                mapInsuranceToFHIRCoverage(insurance as Insurance, insurance.patientId, true),
              ),
            },
            'Error creating insurance with Nextech API',
          );
        }

        // Rethrow the error to be handled by the API handler
        throw error;
      }
    }

    // If we got here, all retries failed
    throw lastError || new Error('Failed to create insurance after retries');
  }

  /**
   * Update an existing insurance record
   * @param id Insurance ID
   * @param insurance Partial insurance data to update
   * @returns Updated insurance
   */
  async updateInsurance(id: string, insurance: Partial<Insurance>): Promise<Insurance> {
    try {
      // First, get the existing insurance
      const existingInsurance = await this.getInsuranceById(id);

      if (!existingInsurance) {
        throw new NotFoundError(`Insurance with ID ${id} not found`);
      }

      // Merge existing and new data
      const updatedInsurance: Insurance = {
        ...existingInsurance,
        ...insurance,
        id, // Ensure ID is preserved
        providerInfo: existingInsurance.providerInfo, // Preserve provider info
      };

      // Map to FHIR Coverage
      const fhirCoverage = mapInsuranceToFHIRCoverage(updatedInsurance, updatedInsurance.patientId);

      // Update coverage in Nextech
      const response = await this.client.put<FHIRCoverage>(
        `${this.coverageUrl}/${id}`,
        fhirCoverage as unknown as { [key: string]: unknown },
      );

      // Map response back to our model
      return mapFHIRCoverageToInsurance(response);
    } catch (error) {
      // Handle specific error cases
      if (error instanceof NotFoundError) {
        throw error; // Rethrow NotFoundError
      }

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('400')) {
          throw new BadRequestError('Invalid insurance data', {
            originalError: error.message,
          });
        }

        // Log error and rethrow
        logger.error(
          {
            provider: 'nextech',
            context: 'Patient Service - Update Insurance',
            insuranceIdPrefix: id.substring(0, 3),
            error,
          },
          'Error updating insurance with Nextech API',
        );
      }

      // Rethrow the error to be handled by the API handler
      throw error;
    }
  }

  /**
   * Delete an insurance record
   * @param id Insurance ID
   * @returns True if deleted successfully
   */
  async deleteInsurance(id: string): Promise<boolean> {
    try {
      // Delete coverage in Nextech
      await this.client.delete(`${this.coverageUrl}/${id}`);
      return true;
    } catch (error) {
      // Handle 404 errors
      if (error instanceof Error && error.message.includes('404')) {
        throw new NotFoundError(`Insurance with ID ${id} not found`);
      }

      // Log error and rethrow
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Delete Insurance',
          insuranceIdPrefix: id.substring(0, 3),
          error,
        },
        'Error deleting insurance with Nextech API',
      );

      // Rethrow the error to be handled by the API handler
      throw error;
    }
  }

  /**
   * Get patient by phone number
   * @param phone Phone number
   * @returns Patient or null if not found
   */
  async getPatientByPhone(phone: string): Promise<Patient | null> {
    try {
      // Remove non-numeric characters for consistent search
      const cleanPhone = phone.replace(/\D/g, '');

      // Create an array of possible phone formats to search
      const phoneFormats = [cleanPhone];

      // If 10 digits (US number without country code), add with country code
      if (cleanPhone.length === 10) {
        phoneFormats.push(`1${cleanPhone}`);
      }

      // If 11 digits with US country code, add without country code
      if (cleanPhone.length === 11 && cleanPhone.startsWith('1')) {
        phoneFormats.push(cleanPhone.substring(1));
      }

      // Set up the base query parameters
      const baseParams = new URLSearchParams({
        _count: '1', // Only need one result
      });

      // Use the same helper method for multiple phone formats
      const searchResult = await this.searchPatientsWithPhoneFormats(
        baseParams,
        phoneFormats,
        1, // Limit to 1 result
        0, // No offset
      );

      // Return the first patient or null
      return searchResult.items.length > 0 ? searchResult.items[0] : null;
    } catch (error) {
      // Log error (without PHI) and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient By Phone',
          error,
        },
        'Error searching patient by phone from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get patient by email address
   * @param email Email address
   * @returns Patient or null if not found
   */
  async getPatientByEmail(email: string): Promise<Patient | null> {
    try {
      // Search using telecom parameter for FHIR
      const queryParams = new URLSearchParams({
        telecom: `email|${email.toLowerCase()}`, // Normalize email to lowercase
        _count: '1', // Only need one result
        _getpagesoffset: '0',
      });

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any patients were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return null;
      }

      // Map the first patient to common model
      return mapFHIRPatientToPatient(response.entry[0].resource as FHIRPatient);
    } catch (error) {
      // Log error (without PHI) and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient By Email',
          error,
        },
        'Error searching patient by email from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get patient by full name and date of birth
   * @param fullName Full name (format: "LastName, FirstName")
   * @param dob Date of birth (format: YYYY-MM-DD)
   * @returns Patient or null if not found
   */
  async getPatientByFullNameAndDob(fullName: string, dob: string): Promise<Patient | null> {
    try {
      // Parse full name to extract first and last name
      let firstName = '';
      let lastName = '';

      // Check if the format is "LastName, FirstName"
      if (fullName.includes(',')) {
        const parts = fullName.split(',');
        lastName = parts[0].trim();
        firstName = parts[1].trim();
      } else {
        // Assume format is "FirstName LastName"
        const parts = fullName.split(' ');
        firstName = parts[0].trim();
        lastName = parts.slice(1).join(' ').trim();
      }

      // Search using name and birthdate parameters
      const queryParams = new URLSearchParams({
        family: lastName,
        given: firstName,
        birthdate: dob,
        _count: '1', // Only need one result
        _getpagesoffset: '0',
      });

      // Make API request
      const url = `${this.baseUrl}?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Check if any patients were found
      if (!response.entry || !Array.isArray(response.entry) || response.entry.length === 0) {
        return null;
      }

      // Map the first patient to common model
      return mapFHIRPatientToPatient(response.entry[0].resource as FHIRPatient);
    } catch (error) {
      // Log error (without PHI) and return null instead of rethrowing
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient By Name and DOB',
          error,
        },
        'Error searching patient by name and DOB from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get all patient types with optional filtering and pagination
   * @param filters Optional filters for patient types
   * @param pagination Optional pagination parameters
   * @returns Paginated result of patient types
   */
  async getPatientTypes(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<PatientType>> {
    try {
      // Extract pagination parameters
      const { limit, offset } = pagination || extractPaginationFromQuery(filters || {});
      const actualLimit = Math.min(limit || this.defaultLimit, this.maxLimit);

      // Build query parameters
      const queryParams = this.buildPatientTypeQueryParams(filters);

      // Add pagination parameters
      queryParams._count = String(actualLimit);
      if (offset > 0) {
        queryParams._offset = String(offset);
      }

      // Make API request for patient types
      const url = `/PatientType?${new URLSearchParams(queryParams).toString()}`;
      const response = await this.client.get<{
        entry?: { resource: NextechPatientTypeResponse }[];
        total?: number;
      }>(url);

      // Map API response to common model
      const patientTypes =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => entry.resource)
              .map(entry => mapNextechPatientTypeToPatientType(entry.resource))
          : [];

      // Extract total count from response or use array length if not available
      const totalCount = response.total || patientTypes.length;

      // Create pagination links
      const links = createPaginationLinks(
        '/PatientType',
        totalCount,
        actualLimit,
        offset,
        queryParams,
      );

      // Return paginated result
      return {
        items: patientTypes,
        pagination: {
          totalCount,
          limit: actualLimit,
          offset,
          hasMore: offset + patientTypes.length < totalCount,
          links,
        },
      };
    } catch (error) {
      // Log error and return empty paginated result
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient Types',
          error,
        },
        'Error fetching patient types from Nextech API',
      );

      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get patient type by ID
   * @param id Patient type ID
   * @returns Promise with PatientType object or null if not found
   */
  async getPatientTypeById(id: string): Promise<PatientType | null> {
    try {
      const patientType = await this.client.get<NextechPatientTypeResponse>(`/PatientType/${id}`);
      return mapNextechPatientTypeToPatientType(patientType);
    } catch (error) {
      // Return null if patient type not found (404)
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      // Log other errors and return null
      logger.error(
        {
          provider: 'nextech',
          context: 'Patient Service - Get Patient Type By ID',
          patientTypeId: id,
          error,
        },
        'Error fetching patient type from Nextech API',
      );

      return null;
    }
  }

  /**
   * Deduplicate patients by phone and birthDate, keeping the most recently updated record
   * @param patients Array of patients to deduplicate
   * @returns Array of deduplicated patients
   */
  private deduplicatePatients(patients: Patient[]): Patient[] {
    // Create a map to group patients by phone + birthDate
    const groupsMap = new Map<string, Patient[]>();

    for (const patient of patients) {
      // Create a key combining phone and birthDate
      const key = `${patient.phoneNumber || 'no-phone'}_${patient.dateOfBirth || 'no-dob'}`;

      if (!groupsMap.has(key)) {
        groupsMap.set(key, []);
      }
      groupsMap.get(key)!.push(patient);
    }

    // For each group, sort by meta.lastUpdated descending and take the first one
    const deduplicatedPatients: Patient[] = [];

    Array.from(groupsMap.values()).forEach(group => {
      // Sort by meta.lastUpdated descending (most recent first)
      // If lastUpdated is undefined, use epoch start time (0)
      group.sort((a: Patient, b: Patient) => {
        const aTime = a.lastUpdated ? new Date(a.lastUpdated).getTime() : 0;
        const bTime = b.lastUpdated ? new Date(b.lastUpdated).getTime() : 0;
        return bTime - aTime; // Descending order
      });

      // Take the first (most recent) patient
      const mostRecent = group[0];
      deduplicatedPatients.push(mostRecent);
    });

    return deduplicatedPatients;
  }

  /**
   * Build query parameters for patient type filtering
   * @param filters Optional filters
   * @returns Formatted query parameters
   */
  private buildPatientTypeQueryParams(filters?: Record<string, unknown>): Record<string, string> {
    const params: Record<string, string> = {};

    if (!filters) {
      return params;
    }

    // Map our filter keys to Nextech API parameters
    if (filters.name) {
      params.name = String(filters.name);
    }

    if (filters.isActive !== undefined) {
      params.active = String(filters.isActive);
    }

    return params;
  }

  /**
   * Create a new patient
   * @param patientData Patient data to create
   * @returns Created patient data
   * @throws ConflictError if patient already exists
   * @throws BadRequestError if patient data is invalid
   */
  async createPatient(patientData: Omit<Patient, 'id' | 'providerInfo'>): Promise<Patient> {
    try {
      // Extract optional fields from patientData
      type ExtendedPatientData = Omit<Patient, 'id' | 'providerInfo'> & {
        patientTypeId?: string;
        referralSourceId?: string;
        notes?: string;
        primaryProviderId?: string;
      };

      const extendedData = patientData as ExtendedPatientData;
      const patientTypeId = extendedData.patientTypeId;
      const referralSourceId = extendedData.referralSourceId || '1'; // Default to referral source ID 1
      const primaryProviderId = extendedData.primaryProviderId || '1'; // Default to provider ID 1

      // Extract notes
      const notes = extendedData.notes;
      let insuranceFromNotes: Insurance | null = null;

      // Check if notes contain insurance data
      if (notes) {
        try {
          const notesObj = JSON.parse(notes);
          if (notesObj && typeof notesObj === 'object') {
            if (notesObj.insurance_company_name || notesObj.insurance_member_id) {
              insuranceFromNotes = {
                companyName: notesObj.insurance_company_name || 'Unknown',
                memberId: notesObj.insurance_member_id || '',
                groupNumber: notesObj.insurance_group_number || '',
                isPrimary: true,
                patientId: '', // Will be set after patient creation
                providerInfo: {
                  provider: 'nextech',
                  externalId: 'legacy',
                },
              };
            }
          }
        } catch (parseError) {
          // If notes can't be parsed as JSON, just ignore
          logger.warn(
            {
              provider: 'nextech',
              context: 'Patient Service - Create Patient - Parse Insurance Notes',
              error: parseError,
            },
            'Failed to parse patient notes as JSON during creation',
          );
        }
      }

      // Convert patient data to FHIR format
      const fhirPatient = mapPatientToFHIRPatient(
        patientData,
        patientTypeId,
        referralSourceId,
        notes,
        primaryProviderId,
      );

      console.log(`FHIR Patient: ${JSON.stringify(fhirPatient)}`);

      // Make API request to create patient
      // Convert to unknown first, then to RequestData to satisfy TypeScript
      const response = await this.client.post<FHIRPatient>(
        this.baseUrl,
        fhirPatient as unknown as { [key: string]: unknown },
      );

      // Map the response to our common model
      const createdPatient = mapFHIRPatientToPatient(response);

      // If we extracted insurance from notes, create it in Nextech
      if (insuranceFromNotes && createdPatient.id) {
        try {
          // Update the patientId with the newly created patient ID
          insuranceFromNotes.patientId = createdPatient.id;

          // Create insurance record in Nextech
          const insuranceToCreate: Omit<Insurance, 'id' | 'providerInfo'> = {
            ...insuranceFromNotes,
            patientId: createdPatient.id,
          };

          const createdInsurance = await this.createInsurance(insuranceToCreate);
          createdPatient.insurances = [createdInsurance];

          logger.info(
            {
              provider: 'nextech',
              context: 'Patient Service - Create Patient',
              patientId: createdPatient.id,
            },
            'Successfully created insurance from notes in Nextech system',
          );
        } catch (error) {
          logger.error(
            {
              provider: 'nextech',
              context: 'Patient Service - Create Patient - Insurance from Notes',
              patientId: createdPatient.id,
              error,
            },
            'Error creating insurance from notes during patient creation',
          );
        }
      }

      return createdPatient;
    } catch (error) {
      // Handle specific error cases
      if (error instanceof Error) {
        // Check for conflict (patient already exists)
        if (error.message.includes('409')) {
          throw new ConflictError('Patient already exists', {
            fields: ['firstName', 'lastName', 'dateOfBirth', 'email'],
          });
        }

        // Handle validation errors
        if (error.message.includes('400')) {
          throw new BadRequestError('Invalid patient data', {
            originalError: error.message,
          });
        }

        // Log error (without PHI) and rethrow
        logger.error(
          {
            provider: 'nextech',
            context: 'Patient Service - Create Patient',
            error,
          },
          'Error creating patient with Nextech API',
        );
      }

      // Rethrow the error to be handled by the API handler
      throw error;
    }
  }

  /**
   * Update a patient
   * @param id Patient ID
   * @param patientData Partial patient data to update
   * @returns Updated patient
   */
  async updatePatient(id: string, patientData: Patient): Promise<Patient> {
    try {
      // First, get the existing patient
      const existingPatient = await this.getPatientById(id);
      if (!existingPatient) {
        throw new NotFoundError(`Patient with ID ${id} not found`);
      }

      // Only update fields that were provided in patientData
      const updatedPatient = {
        ...existingPatient,
        ...Object.fromEntries(
          Object.entries(patientData).filter(([, value]) => value !== undefined),
        ),
        id, // Ensure ID is preserved
        providerInfo: existingPatient.providerInfo, // Preserve provider info
      } as Patient & {
        patientTypeId?: string;
        referralSourceId?: string;
        primaryProviderId?: string;
      };

      // Convert to FHIR format
      const fhirPatient = mapPatientToFHIRPatientWithId(
        updatedPatient,
        updatedPatient.patientTypeId,
        updatedPatient.referralSourceId,
        updatedPatient.notes,
        updatedPatient.primaryProviderId,
      );
      fhirPatient.id = id;
      console.log(`FHIR Patient: ${JSON.stringify(fhirPatient)}`);

      // Update patient in Nextech
      const response = await this.client.put<FHIRPatient>(
        `${this.baseUrl}/${id}`,
        fhirPatient as unknown as { [key: string]: unknown },
      );

      // Map response back to our model
      return mapFHIRPatientToPatient(response);
    } catch (error) {
      // Handle specific error cases
      if (error instanceof NotFoundError) {
        throw error; // Rethrow NotFoundError
      }

      if (error instanceof Error) {
        // Handle validation errors
        if (error.message.includes('400')) {
          throw new BadRequestError('Invalid patient data', {
            originalError: error.message,
          });
        }

        // Log error and rethrow
        logger.error(
          {
            provider: 'nextech',
            context: 'Patient Service - Update Patient',
            patientIdPrefix: id.substring(0, 3), // Only log prefix for privacy
            error,
          },
          'Error updating patient with Nextech API',
        );
      }

      // Rethrow the error to be handled by the API handler
      throw error;
    }
  }
}
