import { v4 as uuidv4 } from 'uuid';
import {
  Address,
  Appointment,
  AppointmentPurpose,
  AppointmentStatus,
  AppointmentType,
  AvailableSlot,
  Clinic,
  Identifier,
  Insurance,
  Location,
  Patient,
  PatientType,
  ProviderInfo,
  User,
} from '../../../models/types';

// FHIR System URL Constants
const FHIR_SYSTEM_URLS = {
  US_NPI: 'http://hl7.org/fhir/sid/us-npi',
  COVERAGE_CLASS: 'http://terminology.hl7.org/CodeSystem/coverage-class',
  SUBSCRIBER_ID: 'http://hl7.org/fhir/sid/member-id',
  GROUP_ID: 'http://hl7.org/fhir/sid/group-id',
  PATIENT_RELATIONSHIP: 'http://terminology.hl7.org/CodeSystem/v3-RoleCode', // Example, verify correct system for relationship
  HL7_IDENTIFIER_USE: 'http://hl7.org/fhir/identifier-use',
  FHIR_IDENTIFIER_TYPE: 'http://terminology.hl7.org/CodeSystem/v2-0203',
};

/**
 * NextechClinicResponse
 * Nextech API response for a Practice (Clinic)
 */
export interface NextechClinicResponse {
  practiceId: string;
  name: string;
  website?: string;
  email?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phoneNumber: string;
  faxNumber?: string;
  active: boolean;
}

/**
 * NextechLocationResponse
 * Nextech API response for a Location
 */
export interface NextechLocationResponse {
  locationId: string;
  practiceId: string;
  name: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phoneNumber?: string;
  faxNumber?: string;
  active: boolean;
}

/**
 * NextechPatientResponse
 * Nextech API response for a Patient
 */
export interface NextechPatientResponse {
  patientId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  dateOfBirth: string;
  gender?: string;
  email?: string;
  phoneHome?: string;
  phoneMobile?: string;
  phoneWork?: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  active: boolean;
}

/**
 * NextechUserResponse
 * Nextech API response for a User/Practitioner
 */
export interface NextechUserResponse {
  userId: string;
  providerId: string;
  firstName: string;
  lastName: string;
  middleName?: string;
  email?: string;
  phone?: string;
  role?: string;
  specialty?: string;
  practiceId?: string;
  locationId?: string;
  active: boolean;
}

/**
 * FHIRPractitioner
 * FHIR Practitioner resource structure from Nextech API
 */
export interface FHIRPractitioner extends FHIRResource {
  identifier?: Array<{
    use?: string;
    system?: string;
    value?: string;
  }>;
  active?: boolean;
  name?: Array<{
    use?: string;
    text?: string;
    family?: string;
    given?: string[];
  }>;
  telecom?: Array<{
    system?: string;
    value?: string;
    use?: string;
  }>;
  address?: Array<{
    use?: string;
    type?: string;
    country?: string;
    line?: string[];
    city?: string;
    state?: string;
    postalCode?: string;
  }>;
}

/**
 * FHIRLocation
 * FHIR Location resource structure from Nextech API
 */
export interface FHIRLocation extends FHIRResource {
  identifier?: Array<{
    use?: string;
    system?: string;
    value?: string;
  }>;
  status?: string; // 'active' or 'inactive'
  name?: string;
  telecom?: Array<{
    system?: string;
    value?: string;
    use?: string;
  }>;
  address?: {
    line?: string[];
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  };
  managingOrganization?: {
    reference?: string; // Format: "Practice/{id}"
    display?: string;
  };
}

/**
 * FHIRPatient
 * FHIR Patient resource structure from Nextech API
 */
export interface FHIRPatient extends FHIRResource {
  meta?: {
    lastUpdated?: string;
  };
  identifier?: Array<{
    use?: string;
    system?: string;
    value?: string;
  }>;
  active?: boolean;
  name?: Array<{
    use?: string;
    text?: string;
    family?: string;
    given?: string[];
    prefix?: string[];
    suffix?: string[];
  }>;
  telecom?: Array<{
    system?: string;
    value?: string;
    use?: string;
    rank?: number;
  }>;
  gender?: string;
  birthDate?: string;
  address?: Array<{
    use?: string;
    type?: string;
    line?: string[];
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  }>;
  maritalStatus?: {
    coding?: Array<{
      system?: string;
      code?: string;
    }>;
    text?: string;
  };
  communication?: Array<{
    language?: {
      coding?: Array<{
        system?: string;
        code?: string;
      }>;
      text?: string;
    };
    preferred?: boolean;
  }>;
  generalPractitioner?: Array<{
    reference?: string;
    display?: string;
  }>;
  extension?: Array<{
    url?: string;
    valueString?: string;
    valueBoolean?: boolean;
    valueDate?: string;
    valueDecimal?: number;
    valueReference?: {
      reference?: string;
      display?: string;
    };
    valueCodeableConcept?: {
      coding?: Array<{
        system?: string;
        code?: string;
      }>;
      text?: string;
    };
    extension?: Array<{
      url?: string;
      valueHumanName?: {
        use?: string;
        text?: string;
        family?: string;
        given?: string[];
        suffix?: string[];
      };
    }>;
  }>;
  contact?: Array<{
    relationship?: Array<{
      coding?: Array<{
        system?: string;
        code?: string;
      }>;
    }>;
    name?: {
      use?: string;
      text?: string;
      family?: string;
      given?: string[];
    };
    telecom?: Array<{
      system?: string;
      value?: string;
      use?: string;
      rank?: number;
    }>;
    address?: {
      use?: string;
      type?: string;
      line?: string[];
      city?: string;
      state?: string;
      postalCode?: string;
      country?: string;
    };
    organization?: {
      identifier?: {
        use?: string;
        value?: string;
      };
    };
    extension?: Array<{
      url?: string;
      valueString?: string;
    }>;
  }>;
}

/**
 * FHIRAppointment
 * FHIR Appointment resource structure from Nextech API
 */
export interface FHIRAppointment extends FHIRResource {
  meta?: {
    lastUpdated?: string;
  };
  status?: string;
  serviceType?: Array<{
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
    text?: string;
  }>;
  reason?: Array<{
    text?: string;
  }>;
  description?: string;
  start?: string;
  end?: string;
  created?: string;
  comment?: string;
  participant?: Array<{
    actor?: {
      reference?: string;
      display?: string;
    };
    status?: string;
    type?: Array<{
      coding?: Array<{
        system?: string;
        code?: string;
      }>;
    }>;
  }>;
  extension?: Array<{
    url?: string;
    valueReference?: {
      reference?: string;
      display?: string;
    };
    valueString?: string;
    valueDateTime?: string;
  }>;
}

/**
 * FHIRSlot
 * FHIR Slot resource structure from Nextech API
 */
export interface FHIRSlot extends FHIRResource {
  identifier?: Array<{
    use?: string;
    system?: string;
    value?: string;
  }>;
  serviceCategory?: Array<{
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
    text?: string;
  }>;
  serviceType?: Array<{
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
    text?: string;
  }>;
  specialty?: Array<{
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
    text?: string;
  }>;
  appointmentType?: Array<{
    coding?: Array<{
      system?: string;
      code?: string;
      display?: string;
    }>;
    text?: string;
  }>;
  schedule?: {
    reference?: string; // Format: "Schedule/{id}"
    display?: string;
  };
  status?: string; // 'free', 'busy', 'busy-unavailable', 'busy-tentative', 'entered-in-error'
  start?: string; // ISO 8601 format
  end?: string; // ISO 8601 format
  overbooked?: boolean;
  comment?: string;
  extension?: Array<{
    url?: string;
    valueReference?: {
      reference?: string;
      display?: string;
    };
    valueString?: string;
    valueDateTime?: string;
  }>;
  contained?: Array<FHIRResource>; // Contained resources like Location and Practitioner
}

/**
 * FHIRResource
 * Base interface for FHIR resources
 */
export interface FHIRResource {
  resourceType: string;
  id: string;
}

/**
 * FHIRBundle
 * FHIR Bundle resource structure from Nextech API
 */
export interface FHIRBundle {
  resourceType: string;
  type: string;
  total?: number;
  link?: Array<{
    relation: string;
    url: string;
  }>;
  entry?: Array<{
    resource: FHIRResource;
  }>;
}

/**
 * NextechAppointmentResponse
 * Nextech API response for an Appointment
 */
export interface NextechAppointmentResponse {
  appointmentId: string;
  patientId: string;
  providerId: string;
  locationId: string;
  practiceId: string;
  startDateTime: string;
  endDateTime: string;
  status: string;
  appointmentType?: string;
  reason?: string;
  notes?: string;
  createdDate: string;
  modifiedDate: string;
}

/**
 * NextechAppointmentTypeResponse
 * Nextech API response for an Appointment Type
 */
export interface NextechAppointmentTypeResponse {
  appointmentTypeId: string;
  name: string;
  description: string;
  duration: number;
  color?: string;
  active: boolean;
}

/**
 * NextechAvailableSlotResponse
 * Nextech API response for an Available Appointment Slot
 */
export interface NextechAvailableSlotResponse {
  slotId: string;
  providerId: string;
  locationId: string;
  appointmentTypeId: string;
  startDateTime: string;
  endDateTime: string;
  available: boolean;
}

/**
 * NextechAppointmentPurposeResponse
 * Nextech API response for an Appointment Purpose
 */
export interface NextechAppointmentPurposeResponse {
  appointmentPurposeId: string;
  name: string;
  description?: string;
  active: boolean;
}

/**
 * NextechPatientTypeResponse
 * Nextech API response for a Patient Type
 */
export interface NextechPatientTypeResponse {
  patientTypeId: string;
  name: string;
  description?: string;
  active: boolean;
}

/**
 * FHIRCoverage
 * FHIR Coverage resource structure from Nextech API
 */
export interface FHIRCoverage {
  resourceType: 'Coverage';
  id: string;
  status: string;
  type: {
    coding: Array<{
      system: string;
      code: string;
      display: string;
    }>;
  };
  subscriber: {
    reference: string;
    display?: string;
  };
  relationship?: {
    coding: {
      system: string;
      code: string;
      display: string;
    }[];
  };
  beneficiary: {
    reference: string;
  };
  payor: {
    reference?: string;
    display?: string;
  }[];
  grouping?: {
    group?: string;
    groupDisplay?: string;
    plan?: string;
    planDisplay?: string;
  };
  identifier?: {
    use?: string;
    type?: {
      coding: {
        system?: string;
        code?: string;
        display?: string;
      }[];
    };
    system?: string;
    value: string;
    period?: {
      start?: string;
      end?: string;
    };
    assigner?: {
      reference?: string;
      display?: string;
    };
  }[];
  extension?: {
    url: string;
    valuePositiveInt?: number;
    valueString?: string;
    valueReference?: {
      reference: string;
      display?: string;
    };
  }[];
  period?: {
    start?: string;
    end?: string;
  };
  // Nextech specific fields
  subscriberId?: string;
  class?: Array<{
    type: {
      coding: Array<{
        system: string;
        code: string;
        display?: string;
      }>;
    };
    value: string;
  }>;
}

/**
 * Map Nextech clinic response to common Clinic model
 * @param clinicData Nextech API clinic/practice data
 * @returns Mapped Clinic object
 */
export function mapNextechClinicToClinic(clinicData: NextechClinicResponse): Clinic {
  const address: Address = {
    line1: clinicData.address1,
    line2: clinicData.address2,
    city: clinicData.city,
    state: clinicData.state,
    postalCode: clinicData.postalCode,
    country: clinicData.country || 'US', // Default to US if not provided
  };

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: clinicData.practiceId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: clinicData.name,
    address,
    phoneNumber: clinicData.phoneNumber,
    emailAddress: clinicData.email,
    website: clinicData.website,
    providerInfo,
  };
}

/**
 * Map Nextech location response to common Location model
 * @param locationData Nextech API location data
 * @returns Mapped Location object
 */
export function mapNextechLocationToLocation(locationData: NextechLocationResponse): Location {
  const address: Address = {
    line1: locationData.address1,
    line2: locationData.address2,
    city: locationData.city,
    state: locationData.state,
    postalCode: locationData.postalCode,
    country: locationData.country || 'US', // Default to US if not provided
  };

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: locationData.locationId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: locationData.name,
    address,
    clinicId: locationData.practiceId,
    phoneNumber: locationData.phoneNumber,
    emailAddress: undefined, // Nextech API doesn't provide location email
    providerInfo,
  };
}

/**
 * Map FHIR Location resource to common Location model
 * @param location FHIR Location resource from Nextech API
 * @returns Mapped Location object
 */
export function mapFHIRLocationToLocation(location: FHIRLocation): Location {
  // Extract clinic ID from managingOrganization reference
  // Format is typically "Practice/{id}"
  let clinicId = '';
  let clinicDisplayName = '';
  if (location.managingOrganization?.reference) {
    const parts = location.managingOrganization.reference.split('/');
    if (parts.length > 1) {
      clinicId = parts[1];
    }
    clinicDisplayName = location.managingOrganization.display || '';
  }

  // Extract phone number from telecom array
  const phoneTelecom = location.telecom?.find(
    t => (t.system === 'phone' && t.use === 'work') || t.use === 'work',
  );
  const phoneNumber = phoneTelecom?.value;

  // Create address object
  const address: Address = {
    line1:
      location.address?.line && location.address.line.length > 0 ? location.address.line[0] : '',
    line2:
      location.address?.line && location.address.line.length > 1
        ? location.address.line[1]
        : undefined,
    city: location.address?.city || '',
    state: location.address?.state || '',
    postalCode: location.address?.postalCode || '',
    country: location.address?.country || 'US', // Default to US if not provided
  };

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: location.id,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: clinicDisplayName || location.name || '',
    address,
    clinicId,
    phoneNumber,
    emailAddress: undefined, // FHIR Location doesn't typically include email
    providerInfo,
  };
}

/**
 * Map Nextech patient response to common Patient model
 * @param patientData Nextech API patient data
 * @returns Mapped Patient object
 */
export function mapNextechPatientToPatient(patientData: NextechPatientResponse): Patient {
  // Only create address if we have at least the line1 field
  const address = patientData.address1
    ? {
        line1: patientData.address1,
        line2: patientData.address2,
        city: patientData.city || '',
        state: patientData.state || '',
        postalCode: patientData.postalCode || '',
        country: patientData.country || 'US', // Default to US if not provided
      }
    : undefined;

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: patientData.patientId,
  };

  // Use the most available phone number
  const phoneNumber = patientData.phoneMobile || patientData.phoneHome || patientData.phoneWork;

  return {
    id: uuidv4(), // Generate unique ID for our system
    firstName: patientData.firstName,
    lastName: patientData.lastName,
    dateOfBirth: patientData.dateOfBirth,
    gender: patientData.gender,
    email: patientData.email,
    phoneNumber,
    address,
    providerInfo,
  };
}

/**
 * Map FHIR Patient resource to common Patient model
 * @param patient FHIR Patient resource from Nextech API
 * @returns Mapped Patient object
 */
export function mapFHIRPatientToPatient(patient: FHIRPatient): Patient {
  // Extract name
  const nameObj = patient.name && patient.name.length > 0 ? patient.name[0] : null;
  const firstName = nameObj?.given && nameObj.given.length > 0 ? nameObj.given[0] : '';
  const lastName = nameObj?.family || '';

  // Extract date of birth
  const dateOfBirth = patient.birthDate || '';

  // Extract gender
  const gender = patient.gender || '';

  // Extract email
  const emailTelecom = patient.telecom?.find(t => t.system === 'email');
  const email = emailTelecom?.value;

  // Extract phone number - prefer mobile, then home, then work
  const mobileTelecom = patient.telecom?.find(t => t.system === 'phone' && t.use === 'mobile');
  const homeTelecom = patient.telecom?.find(t => t.system === 'phone' && t.use === 'home');
  const workTelecom = patient.telecom?.find(t => t.system === 'phone' && t.use === 'work');
  const phoneNumber = mobileTelecom?.value || homeTelecom?.value || workTelecom?.value;
  const notes = patient.extension?.find(e => e.url?.includes('patient-note'))?.valueString;

  // Extract address
  const addressObj = patient.address && patient.address.length > 0 ? patient.address[0] : null;
  const address = addressObj
    ? {
        line1: addressObj.line && addressObj.line.length > 0 ? addressObj.line[0] : '',
        line2: addressObj.line && addressObj.line.length > 1 ? addressObj.line[1] : undefined,
        city: addressObj.city || '',
        state: addressObj.state || '',
        postalCode: addressObj.postalCode || '',
        country: addressObj.country || 'US', // Default to US if not provided
      }
    : undefined;

  // Extract patient ID from identifier
  const officialIdentifier = patient.identifier?.find(i => i.use === 'official');
  const patientId = officialIdentifier?.value || patient.id;

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: patientId,
  };

  // Extract identifiers
  const identifiers: Identifier[] =
    patient.identifier?.map(i => ({
      use: i.use || 'usual',
      system: i.system || '',
      value: i.value || '',
    })) || [];

  return {
    id: patientId,
    firstName,
    lastName,
    dateOfBirth,
    gender,
    email,
    phoneNumber,
    address,
    providerInfo,
    notes,
    identifiers,
    lastUpdated: patient.meta?.lastUpdated,
  };
}

/**
 * Map Nextech user response to common User model
 * @param userData Nextech API user/practitioner data
 * @returns Mapped User object
 */
export function mapNextechUserToUser(userData: NextechUserResponse): User {
  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: userData.providerId || userData.userId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    firstName: userData.firstName,
    lastName: userData.lastName,
    email: userData.email,
    phoneNumber: userData.phone,
    role: userData.role,
    speciality: userData.specialty,
    clinicId: userData.practiceId,
    locationId: userData.locationId,
    providerInfo,
  };
}

/**
 * Map FHIR Practitioner resource to common User model
 * @param practitioner FHIR Practitioner resource from Nextech API
 * @returns Mapped User object
 */
export function mapFHIRPractitionerToUser(practitioner: FHIRPractitioner): User {
  // Extract name
  const nameObj = practitioner.name && practitioner.name.length > 0 ? practitioner.name[0] : null;
  const firstName = nameObj?.given && nameObj.given.length > 0 ? nameObj.given[0] : '';
  const lastName = nameObj?.family || '';

  // Extract email
  const emailTelecom = practitioner.telecom?.find(t => t.system === 'email');
  const email = emailTelecom?.value;

  // Extract phone
  const phoneTelecom = practitioner.telecom?.find(t => t.system === 'phone' && t.use === 'work');
  const phoneNumber = phoneTelecom?.value;

  // Extract NPI (not used currently, but could be used in the future)
  // const npiIdentifier = practitioner.identifier?.find(i => i.system === 'http://hl7.org/fhir/sid/us-npi');
  // const npi = npiIdentifier?.value;

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: practitioner.id,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    firstName,
    lastName,
    email,
    phoneNumber,
    // No direct mapping for these fields in the FHIR resource
    role: undefined,
    speciality: undefined,
    clinicId: undefined,
    locationId: undefined,
    providerInfo,
  };
}

/**
 * Map Nextech appointment response to common Appointment model
 * @param appointmentData Nextech API appointment data
 * @returns Mapped Appointment object
 */
export function mapNextechAppointmentToAppointment(
  appointmentData: NextechAppointmentResponse,
): Appointment {
  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: appointmentData.appointmentId,
  };

  // Note: NextechAppointmentResponse doesn't include names, so we leave those fields undefined
  // They would need to be populated by additional API calls if needed
  return {
    id: uuidv4(), // Generate unique ID for our system
    patientId: appointmentData.patientId,
    providerId: appointmentData.providerId,
    practitionerId: appointmentData.providerId, // Set practitionerId to be the same as providerId
    locationId: appointmentData.locationId,
    clinicId: appointmentData.practiceId,
    startTime: appointmentData.startDateTime,
    endTime: appointmentData.endDateTime,
    status: mapNextechAppointmentStatus(appointmentData.status),
    type: appointmentData.appointmentType,
    reason: appointmentData.reason,
    notes: appointmentData.notes,
    createdAt: appointmentData.createdDate,
    updatedAt: appointmentData.modifiedDate,
    providerInfo,
  };
}

/**
 * Map FHIR Appointment resource to common Appointment model
 * @param appointment FHIR Appointment resource from Nextech API
 * @returns Mapped Appointment object
 */
export function mapFHIRAppointmentToAppointment(appointment: FHIRAppointment): Appointment {
  // Extract appointment ID from identifier or use resource ID
  const appointmentId = appointment.id;

  // Extract patient, provider, location, and clinic IDs from participants and extensions
  let patientId = '';
  let patientName = '';
  let providerId = '';
  let practitionerId = '';
  let practitionerName = '';
  let locationId = '';
  let locationName = '';
  let clinicId = '';

  // Extract participant references
  if (appointment.participant) {
    for (const participant of appointment.participant) {
      if (participant.actor?.reference) {
        // Check participant type
        const isPatient = participant.actor.reference.startsWith('Patient/');
        const isPractitioner = participant.actor.reference.startsWith('Practitioner/');
        const isLocation = participant.actor.reference.startsWith('Location/');

        if (isPatient) {
          patientId = participant.actor.reference.split('/')[1];
          patientName = participant.actor.display || '';
        } else if (isPractitioner) {
          providerId = participant.actor.reference.split('/')[1];
          practitionerId = providerId;
          practitionerName = participant.actor.display || '';
        } else if (isLocation) {
          locationId = participant.actor.reference.split('/')[1];
          locationName = participant.actor.display || '';
        }
      }
    }
  }

  // Extract clinic ID from extension
  if (appointment.extension) {
    const practiceExtension = appointment.extension.find(
      ext =>
        ext.url === 'https://api.pm.nextech.com/api/structuredefinition/practice' &&
        ext.valueReference?.reference,
    );

    if (practiceExtension?.valueReference?.reference) {
      clinicId = practiceExtension.valueReference.reference.split('/')[1];
    }
  }

  // Extract start and end times
  const startTime = appointment.start || '';
  const endTime = appointment.end || '';

  // Extract status
  const status = mapFHIRAppointmentStatus(appointment.status || '');

  // Extract type from appointment-type extension
  let type;
  if (appointment.extension) {
    const appointmentTypeExtension = appointment.extension.find(
      ext => ext.url === 'appointment-type' && ext.valueReference?.reference,
    );

    if (appointmentTypeExtension?.valueReference?.reference) {
      // Extract the ID from the reference (format: "appointment-type/{id}")
      const parts = appointmentTypeExtension.valueReference.reference.split('/');
      if (parts.length > 1) {
        // Use the ID as the type
        type = parts[1];
      }
    }
  }

  // Extract reason
  const reason =
    appointment.reason && appointment.reason.length > 0 ? appointment.reason[0].text : undefined;

  // Extract notes from comment
  const notes = appointment.comment;

  // Extract created date
  const createdAt = appointment.created || '';

  // Use last updated from meta if available, otherwise use created date
  const updatedAt = appointment.meta?.lastUpdated || createdAt;

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: appointmentId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    patientId,
    patientName,
    providerId,
    practitionerId,
    practitionerName,
    locationId,
    locationName,
    clinicId,
    startTime,
    endTime,
    status,
    type,
    reason,
    notes,
    createdAt,
    updatedAt,
    providerInfo,
  };
}

/**
 * Map Nextech appointment type response to common AppointmentType model
 * @param appointmentTypeData Nextech API appointment type data
 * @returns Mapped AppointmentType object
 */
export function mapNextechAppointmentTypeToAppointmentType(
  appointmentTypeData: NextechAppointmentTypeResponse,
): AppointmentType {
  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: appointmentTypeData.appointmentTypeId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: appointmentTypeData.name,
    description: appointmentTypeData.description,
    duration: appointmentTypeData.duration,
    color: appointmentTypeData.color,
    isActive: appointmentTypeData.active,
    providerInfo,
  };
}

/**
 * Map FHIR appointment type resource to common AppointmentType model
 * @param resource FHIR appointment-type resource from Nextech API
 * @returns Mapped AppointmentType object
 */
export function mapFHIRAppointmentTypeToAppointmentType(resource: {
  resourceType: string;
  id: string;
  extension?: Array<{
    url: string;
    valueString?: string;
  }>;
}): AppointmentType {
  // Extract the appointment type name from the extension
  let name = '';
  if (resource.extension) {
    const nameExtension = resource.extension.find(
      ext => ext.url === 'appointment-type' && ext.valueString,
    );
    if (nameExtension?.valueString) {
      name = nameExtension.valueString;
    }
  }

  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: resource.id,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: name,
    description: '', // Not available in FHIR response
    duration: 0, // Not available in FHIR response
    color: undefined, // Not available in FHIR response
    isActive: true, // Assume active since it's returned in the list
    providerInfo,
  };
}

/**
 * Map Nextech available slot response to common AvailableSlot model
 * @param slotData Nextech API available slot data
 * @returns Mapped AvailableSlot object
 */
export function mapNextechAvailableSlotToAvailableSlot(
  slotData: NextechAvailableSlotResponse,
): AvailableSlot {
  return {
    startDateTime: slotData.startDateTime,
    endDateTime: slotData.endDateTime,
    practitionerId: slotData.providerId, // Map providerId to practitionerId
    locationId: slotData.locationId,
    appointmentTypeId: slotData.appointmentTypeId,
    practitionerName: '', // Not available in NextechAvailableSlotResponse
    locationName: '', // Not available in NextechAvailableSlotResponse
  };
}

/**
 * Map FHIR Slot resource to common AvailableSlot model
 * @param slot FHIR Slot resource from Nextech API
 * @returns Mapped AvailableSlot object
 */
export function mapFHIRSlotToAvailableSlot(slot: FHIRSlot): AvailableSlot {
  // Extract practitioner and location from contained resources
  const practitioner = slot.contained?.find(
    (resource: FHIRResource) => resource.resourceType === 'Practitioner',
  ) as FHIRPractitioner | undefined;
  const location = slot.contained?.find(
    (resource: FHIRResource) => resource.resourceType === 'Location',
  ) as FHIRLocation | undefined;

  // Extract practitioner ID and name
  const practitionerId = practitioner?.id || '';
  const practitionerName = practitioner?.name?.[0]?.text || '';

  // Extract location ID and name
  const locationId = location?.id || '';
  const locationName = location?.name || '';

  // Extract appointment type ID from extension
  let appointmentTypeId = '';
  if (slot.extension) {
    const appointmentTypeExtension = slot.extension.find(
      ext => ext.url === 'appointment-type' && ext.valueReference?.reference,
    );

    if (appointmentTypeExtension?.valueReference?.reference) {
      appointmentTypeId = appointmentTypeExtension.valueReference.reference.split('/')[1];
    }
  }

  return {
    startDateTime: slot.start || '',
    endDateTime: slot.end || '',
    practitionerId,
    locationId,
    appointmentTypeId,
    practitionerName,
    locationName,
  };
}

/**
 * Map Nextech appointment status to common AppointmentStatus enum
 * @param status Nextech appointment status
 * @returns Mapped AppointmentStatus enum value
 */
function mapNextechAppointmentStatus(status: string): AppointmentStatus {
  // Map Nextech status values to our enum
  switch (status.toLowerCase()) {
    case 'scheduled':
      return AppointmentStatus.BOOKED;
    case 'confirmed':
      return AppointmentStatus.BOOKED;
    case 'arrived':
      return AppointmentStatus.ARRIVED;
    case 'completed':
      return AppointmentStatus.FULFILLED;
    case 'cancelled':
      return AppointmentStatus.CANCELLED;
    case 'no show':
      return AppointmentStatus.NOSHOW;
    case 'pending':
      return AppointmentStatus.PENDING;
    default:
      return AppointmentStatus.PROPOSED;
  }
}

/**
 * Map FHIR appointment status to common AppointmentStatus enum
 * @param status FHIR appointment status
 * @returns Mapped AppointmentStatus enum value
 */
function mapFHIRAppointmentStatus(status: string): AppointmentStatus {
  // Map FHIR status values to our enum
  switch (status.toLowerCase()) {
    case 'booked':
      return AppointmentStatus.BOOKED;
    case 'arrived':
      return AppointmentStatus.ARRIVED;
    case 'fulfilled':
      return AppointmentStatus.FULFILLED;
    case 'cancelled':
      return AppointmentStatus.CANCELLED;
    case 'noshow':
      return AppointmentStatus.NOSHOW;
    case 'pending':
      return AppointmentStatus.PENDING;
    case 'proposed':
      return AppointmentStatus.PROPOSED;
    default:
      return AppointmentStatus.PROPOSED;
  }
}

/**
 * Map Nextech appointment purpose response to common AppointmentPurpose model
 * @param purposeData Nextech API appointment purpose data
 * @returns Mapped AppointmentPurpose object
 */
export function mapNextechAppointmentPurposeToAppointmentPurpose(
  purposeData: NextechAppointmentPurposeResponse,
): AppointmentPurpose {
  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: purposeData.appointmentPurposeId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: purposeData.name,
    description: purposeData.description,
    isActive: purposeData.active,
    providerInfo,
  };
}

/**
 * Map Nextech patient type response to common PatientType model
 * @param patientTypeData Nextech API patient type data
 * @returns Mapped PatientType object
 */
export function mapNextechPatientTypeToPatientType(
  patientTypeData: NextechPatientTypeResponse,
): PatientType {
  const providerInfo: ProviderInfo = {
    provider: 'nextech',
    externalId: patientTypeData.patientTypeId,
  };

  return {
    id: uuidv4(), // Generate unique ID for our system
    name: patientTypeData.name,
    description: patientTypeData.description,
    isActive: patientTypeData.active,
    providerInfo,
  };
}

/**
 * Map our Patient model to FHIR Patient resource for Nextech API
 * @param patientData Patient data to convert to FHIR format
 * @param patientTypeId Optional patient type ID
 * @param referralSourceId Optional referral source ID
 * @param notes Optional patient notes
 * @returns FHIR Patient object ready for Nextech API
 */
export function mapPatientToFHIRPatient(
  patientData: Omit<Patient, 'id' | 'providerInfo'>,
  patientTypeId?: string,
  referralSourceId: string = '1', // Default referral source ID
  notes?: string,
  primaryProviderId: string = '1', // Default primary provider ID
): Omit<FHIRPatient, 'id'> {
  // Create the extensions array
  const extensions: FHIRPatient['extension'] = [
    {
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-status',
      valueString: 'Active',
    },
  ];

  // Add patient type if provided
  if (patientTypeId) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-type',
      valueReference: {
        reference: `patient-type/${patientTypeId}`,
      },
    });
  }

  // Add referral source if provided
  if (referralSourceId) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/referral-source',
      valueReference: {
        reference: `referral-source/${referralSourceId}`,
      },
    });
  }

  // Add notes if provided
  if (notes) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-note',
      valueString: notes,
    });
  }

  // Build the FHIR Patient object
  // Note: We're creating a partial object and will add optional fields conditionally
  const fhirPatient: Partial<FHIRPatient> & { resourceType: string } = {
    resourceType: 'Patient',
    extension: extensions,
    generalPractitioner: [
      {
        reference: `practitioner/${primaryProviderId}`,
      },
    ],
    name: [
      {
        use: 'official',
        family: patientData.lastName,
        given: [patientData.firstName],
      },
    ],
    telecom: [
      // Add phone number
      {
        system: 'phone',
        value: patientData.phoneNumber,
        use: 'home',
        rank: 1,
      },
    ],
    birthDate: patientData.dateOfBirth,
  };

  // Add identifiers if provided
  if (patientData.identifiers) {
    fhirPatient.identifier = patientData.identifiers.map(i => ({
      use: i.use,
      system: i.system,
      value: i.value,
    }));
  }

  // Add gender if provided
  if (patientData.gender) {
    fhirPatient.gender = patientData.gender;
  }

  // Add email if provided
  if (patientData.email) {
    fhirPatient.telecom?.push({
      system: 'email',
      value: patientData.email,
    });
  }

  // Add address if provided
  if (patientData.address) {
    // Convert state name to two-letter abbreviation if needed
    const stateAbbreviation = convertStateNameToAbbreviation(patientData.address.state);

    fhirPatient.address = [
      {
        use: 'home',
        type: 'both',
        line: [
          patientData.address.line1,
          ...(patientData.address.line2 ? [patientData.address.line2] : []),
        ],
        city: patientData.address.city,
        state: stateAbbreviation,
        postalCode: patientData.address.postalCode,
        country: patientData.address.country,
      },
    ];
  }

  return fhirPatient as FHIRPatient;
}

export function mapPatientToFHIRPatientWithId(
  patientData: Patient,
  patientTypeId?: string,
  referralSourceId: string = '1', // Default referral source ID
  notes?: string,
  primaryProviderId: string = '1', // Default primary provider ID
): FHIRPatient {
  // Create the extensions array
  const extensions: FHIRPatient['extension'] = [
    {
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-status',
      valueString: 'Active',
    },
  ];

  // Add patient type if provided
  if (patientTypeId) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-type',
      valueReference: {
        reference: `patient-type/${patientTypeId}`,
      },
    });
  }

  // Add referral source if provided
  if (referralSourceId) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/referral-source',
      valueReference: {
        reference: `referral-source/${referralSourceId}`,
      },
    });
  }

  // Add notes if provided
  if (notes) {
    extensions.push({
      url: 'https://api.pm.nextech.com/api/structuredefinition/patient-note',
      valueString: notes,
    });
  }

  // Build the FHIR Patient object
  // Note: We're creating a partial object and will add optional fields conditionally
  const fhirPatient: Partial<FHIRPatient> & { resourceType: string } = {
    resourceType: 'Patient',
    extension: extensions,
    generalPractitioner: [
      {
        reference: `practitioner/${primaryProviderId}`,
      },
    ],
    name: [
      {
        use: 'official',
        family: patientData.lastName,
        given: [patientData.firstName],
      },
    ],
    telecom: [
      // Add phone number
      {
        system: 'phone',
        value: patientData.phoneNumber,
        use: 'home',
        rank: 1,
      },
    ],
    birthDate: patientData.dateOfBirth,
  };

  // Add identifiers if provided
  if (patientData.identifiers) {
    fhirPatient.identifier = patientData.identifiers.map(i => ({
      use: i.use,
      system: i.system,
      value: i.value,
    }));
  }

  // Add gender if provided
  if (patientData.gender) {
    fhirPatient.gender = patientData.gender;
  }

  // Add email if provided
  if (patientData.email) {
    fhirPatient.telecom?.push({
      system: 'email',
      value: patientData.email,
    });
  }

  // Add address if provided
  if (patientData.address) {
    // Convert state name to two-letter abbreviation if needed
    const stateAbbreviation = convertStateNameToAbbreviation(patientData.address.state);

    fhirPatient.address = [
      {
        use: 'home',
        type: 'both',
        line: [
          patientData.address.line1,
          ...(patientData.address.line2 ? [patientData.address.line2] : []),
        ],
        city: patientData.address.city,
        state: stateAbbreviation,
        postalCode: patientData.address.postalCode,
        country: patientData.address.country,
      },
    ];
  }

  return fhirPatient as FHIRPatient;
}

/**
 * Convert a state name to its two-letter abbreviation
 * @param state The state name or abbreviation
 * @returns The two-letter state abbreviation
 */
function convertStateNameToAbbreviation(state: string): string {
  // If the state is already a two-letter code, return it
  if (state.length === 2 && state === state.toUpperCase()) {
    return state;
  }

  // Map of state names to abbreviations
  const stateMap: { [key: string]: string } = {
    alabama: 'AL',
    alaska: 'AK',
    arizona: 'AZ',
    arkansas: 'AR',
    california: 'CA',
    colorado: 'CO',
    connecticut: 'CT',
    delaware: 'DE',
    florida: 'FL',
    georgia: 'GA',
    hawaii: 'HI',
    idaho: 'ID',
    illinois: 'IL',
    indiana: 'IN',
    iowa: 'IA',
    kansas: 'KS',
    kentucky: 'KY',
    louisiana: 'LA',
    maine: 'ME',
    maryland: 'MD',
    massachusetts: 'MA',
    michigan: 'MI',
    minnesota: 'MN',
    mississippi: 'MS',
    missouri: 'MO',
    montana: 'MT',
    nebraska: 'NE',
    nevada: 'NV',
    'new hampshire': 'NH',
    'new jersey': 'NJ',
    'new mexico': 'NM',
    'new york': 'NY',
    'north carolina': 'NC',
    'north dakota': 'ND',
    ohio: 'OH',
    oklahoma: 'OK',
    oregon: 'OR',
    pennsylvania: 'PA',
    'rhode island': 'RI',
    'south carolina': 'SC',
    'south dakota': 'SD',
    tennessee: 'TN',
    texas: 'TX',
    utah: 'UT',
    vermont: 'VT',
    virginia: 'VA',
    washington: 'WA',
    'west virginia': 'WV',
    wisconsin: 'WI',
    wyoming: 'WY',
    'district of columbia': 'DC',
    'american samoa': 'AS',
    guam: 'GU',
    'northern mariana islands': 'MP',
    'puerto rico': 'PR',
    'united states minor outlying islands': 'UM',
    'u.s. virgin islands': 'VI',
  };

  // Normalize the state name and look up the abbreviation
  const normalizedState = state.toLowerCase().trim();
  return stateMap[normalizedState] || state; // Return the original if not found
}

/**
 * Map Insurance model to FHIR Coverage
 * @param insurance Insurance model
 * @param patientId Patient ID
 * @returns FHIR Coverage
 */
export function mapInsuranceToFHIRCoverage(
  insurance: Insurance,
  patientId: string,
  isCreate: boolean = false,
): FHIRCoverage {
  if (!insurance) {
    throw new Error('Invalid Insurance object: insurance itself is null/undefined.');
  }
  if (!patientId) {
    throw new Error('Patient ID is required to map Insurance to FHIRCoverage.');
  }

  // Relationship mapping
  let relationshipCode = 'self';
  let relationshipDisplay = 'Self';
  if (insurance.subscriberRelationship) {
    const rel = insurance.subscriberRelationship.toLowerCase();
    if (rel === 'spouse') {
      relationshipCode = 'spouse';
      relationshipDisplay = 'Spouse';
    } else if (rel === 'child') {
      relationshipCode = 'child';
      relationshipDisplay = 'Child';
    } else if (rel === 'other') {
      relationshipCode = 'other';
      relationshipDisplay = 'Other';
    }
  }

  // Build a minimal coverage object with only essential fields
  const coverage: {
    resourceType: string;
    id?: string;
    status: string;
    type: {
      coding: Array<{
        system: string;
        code: string;
        display: string;
      }>;
    };
    subscriber: {
      reference: string;
    };
    beneficiary: {
      reference: string;
    };
    relationship: {
      coding: Array<{
        system: string;
        code: string;
        display: string;
      }>;
    };
    payor: Array<{
      display: string;
    }>;
    period?: {
      start?: string;
      end?: string;
    };
  } = {
    resourceType: 'Coverage' as const,
    status: 'active',
    type: {
      coding: [
        {
          system: 'http://terminology.hl7.org/CodeSystem/v3-ActCode',
          code: 'EHCPOL',
          display: 'extended healthcare',
        },
      ],
    },
    subscriber: {
      reference: `Patient/${patientId}`,
    },
    beneficiary: {
      reference: `Patient/${patientId}`,
    },
    relationship: {
      coding: [
        {
          system: 'http://hl7.org/fhir/policyholder-relationship',
          code: relationshipCode,
          display: relationshipDisplay,
        },
      ],
    },
    payor: [
      {
        display: insurance.companyName || 'Unknown Payor',
      },
    ],
  };

  // Only include id if not creating (i.e., for PUT/GET)
  if (!isCreate && (insurance.providerInfo?.externalId || insurance.id)) {
    coverage.id = insurance.providerInfo?.externalId || insurance.id;
  }

  // Optionally add period if available
  if (insurance.effectiveDate || insurance.expirationDate) {
    coverage.period = {
      start: insurance.effectiveDate,
      end: insurance.expirationDate,
    };
  }

  // Add very basic versions of the required fields directly in the Coverage object
  // instead of using complex structures
  (coverage as FHIRCoverage).subscriberId = insurance.memberId;

  // Add group number directly (only if provided)
  if (insurance.groupNumber) {
    (coverage as FHIRCoverage).class = [
      {
        type: {
          coding: [
            {
              system: 'http://terminology.hl7.org/CodeSystem/coverage-class',
              code: 'group',
            },
          ],
        },
        value: insurance.groupNumber,
      },
    ];
  }

  // Use explicit type assertion since we're building the coverage object manually
  // and TypeScript can't infer the literal 'Coverage' type for resourceType
  return coverage as FHIRCoverage;
}

/**
 * Map FHIR Coverage to Insurance model
 * @param coverage FHIR Coverage
 * @returns Insurance model
 */
export function mapFHIRCoverageToInsurance(coverage: FHIRCoverage): Insurance {
  if (!coverage || !coverage.id) {
    throw new Error(
      'Invalid FHIRCoverage object: Missing id or coverage itself is null/undefined.',
    );
  }

  const primaryPayor = coverage.payor && coverage.payor.length > 0 ? coverage.payor[0] : undefined;

  // Try to get memberId from various places
  let memberId: string | undefined;

  // First, try the direct subscriberId field (our custom simplified approach)
  if (coverage.subscriberId) {
    memberId = coverage.subscriberId;
  }
  // Next try identifier array if it exists
  else if (coverage.identifier?.length) {
    const memberIdIdentifier = coverage.identifier.find(
      id =>
        id.use === 'official' ||
        id.use === 'usual' ||
        id.type?.coding?.some(
          c => c.code === 'MB' && c.system === FHIR_SYSTEM_URLS.FHIR_IDENTIFIER_TYPE,
        ) ||
        id.system === FHIR_SYSTEM_URLS.SUBSCRIBER_ID,
    );
    memberId = memberIdIdentifier?.value;
  }

  // Try to get group number from various places
  let groupNumber: string | undefined;

  // First check class array (our custom simplified approach)
  if (coverage.class && Array.isArray(coverage.class)) {
    const groupClass = coverage.class.find(cls => cls.type?.coding?.some(c => c.code === 'group'));
    if (groupClass) {
      groupNumber = groupClass.value;
    }
  }
  // Next check identifier array if it exists
  else if (coverage.identifier?.length) {
    const groupIdentifier = coverage.identifier.find(
      id =>
        id.type?.coding?.some(
          c => c.code === 'GROUP' && c.system === FHIR_SYSTEM_URLS.COVERAGE_CLASS,
        ) ||
        id.type?.coding?.some(
          c => c.code === 'GRP' && c.system === FHIR_SYSTEM_URLS.FHIR_IDENTIFIER_TYPE,
        ) ||
        id.system === FHIR_SYSTEM_URLS.GROUP_ID,
    );
    groupNumber = groupIdentifier?.value;
  }
  // Lastly check grouping if it exists
  else if (coverage.grouping?.group) {
    groupNumber = coverage.grouping.group;
  }

  const isPrimary = coverage.payor?.length === 1;

  let subscriberRelationshipDisplay = 'Self';
  if (coverage.relationship?.coding && coverage.relationship.coding.length > 0) {
    subscriberRelationshipDisplay =
      coverage.relationship.coding[0].display || coverage.relationship.coding[0].code || 'Self';
  }

  if (!memberId) {
    console.warn(`FHIRCoverage ${coverage.id} is missing a clear memberId.`);
  }

  const insuranceId = coverage.id;

  return {
    id: insuranceId,
    companyName: primaryPayor?.display || 'Unknown Payor',
    memberId: memberId || 'N/A',
    groupNumber: groupNumber || 'N/A',
    planName: coverage.grouping?.planDisplay || coverage.grouping?.plan || 'N/A',
    isPrimary: isPrimary,
    subscriberName: coverage.subscriber?.display || 'N/A',
    subscriberRelationship: subscriberRelationshipDisplay,
    effectiveDate: coverage.period?.start,
    expirationDate: coverage.period?.end,
    patientId: coverage.beneficiary?.reference?.split('/')[1] || 'UnknownPatient',
    providerInfo: {
      provider: 'nextech-fhir',
      externalId: coverage.id,
    },
  };
}
