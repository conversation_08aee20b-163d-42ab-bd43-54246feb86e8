import { ILocationService } from '@/lib/external-api/v2';
import { Location } from '../../../models/types';
import { NextechApiClient } from '../client';
import { FHIRLocation, FHIRBundle, mapFHIRLocationToLocation } from './mappers';
import { PaginationParams, PaginatedResult } from '@/lib/external-api/v2';
import logger from '../../../utils/logger';

/**
 * NextechLocationService
 * Implementation of ILocationService for Nextech API
 */
export class NextechLocationService implements ILocationService {
  constructor(private readonly client: NextechApiClient) {}
  private readonly defaultLimit = 10;
  private readonly maxLimit = 50;

  // currently getting only one location by phone // todo remove when has more locations
  private readonly defaultPhone = '************';

  /**
   * Get all locations from Nextech
   * @param filters Optional filters (supported: name, phone, clinicId/practiceId)
   * @param pagination Optional pagination parameters
   * @returns Paginated result of mapped Location objects
   */
  async getLocations(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<Location>> {
    try {
      // Extract pagination parameters with defaults
      const limit = Math.min(pagination?.limit || this.defaultLimit, this.maxLimit);
      const offset = pagination?.offset || 0;

      // Build query parameters from filters
      const queryParams = new URLSearchParams();

      // Add pagination parameters - FHIR uses _count and _getpagesoffset
      queryParams.set('_count', String(limit));
      queryParams.set('_getpagesoffset', String(offset));

      // Add filters if provided
      if (filters?.name && typeof filters.name === 'string') {
        queryParams.set('name', filters.name);
      }

      // // Handle phone filter
      // if (filters?.phoneNumber && typeof filters.phoneNumber === "string") {
      //   queryParams.set("phone", `${filters.phoneNumber}`);
      // }
      queryParams.set('phone', this.defaultPhone);

      // Add active=true to only get active locations
      queryParams.set('status', 'active');

      // Call Nextech API to get locations
      const url = `/location?${queryParams.toString()}`;
      const response = await this.client.get<FHIRBundle>(url);

      // Map FHIR response to our common Location model
      const locations =
        response.entry && Array.isArray(response.entry)
          ? response.entry
              .filter(entry => {
                // Check if it's a Location resource and is active
                return (
                  entry.resource &&
                  entry.resource.resourceType === 'Location' &&
                  (entry.resource as FHIRLocation).status === 'active'
                );
              })
              .map(entry => mapFHIRLocationToLocation(entry.resource as FHIRLocation))
          : [];

      // Get total count from response or use array length
      const totalCount = response.total || locations.length;

      // Extract pagination links from response
      const links: Record<string, string> = {};
      if (response.link && Array.isArray(response.link)) {
        response.link.forEach(link => {
          if (link.relation === 'first') links.first = link.url;
          if (link.relation === 'next') links.next = link.url;
          if (link.relation === 'last') links.last = link.url;
          if (link.relation === 'prev') links.prev = link.url;
        });
      }

      // Return paginated result
      return {
        items: locations,
        pagination: {
          totalCount,
          limit,
          offset,
          hasMore: offset + locations.length < totalCount,
          links,
        },
      };
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Location Service - Get Locations',
          error,
        },
        'Error fetching locations from Nextech API',
      );

      // Return empty paginated result
      return {
        items: [],
        pagination: {
          totalCount: 0,
          limit: Math.min(pagination?.limit || this.defaultLimit, this.maxLimit),
          offset: pagination?.offset || 0,
          hasMore: false,
          links: {},
        },
      };
    }
  }

  /**
   * Get a specific location by ID
   * @param id The location ID
   * @returns The mapped Location object or null if not found
   */
  async getLocationById(id: string): Promise<Location | null> {
    try {
      // Call Nextech API to get specific location
      const response = await this.client.get<FHIRLocation>(`/location/${id}`);

      // Only return active locations
      if (response.status !== 'active') {
        return null;
      }

      // Map FHIR response to our common Location model
      return mapFHIRLocationToLocation(response);
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Location Service - Get Location By ID',
          locationId: id,
          error,
        },
        'Error fetching location from Nextech API',
      );

      return null;
    }
  }

  /**
   * Get a location by phone number
   * @param phone The phone number to search for
   * @returns The mapped Location object or null if not found
   */
  async getLocationByPhone(phone: string): Promise<Location | null> {
    try {
      // Clean phone number by removing non-numeric characters
      const cleanPhone = phone.replace(/\D/g, '');

      // Call getLocations with phone filter
      const locations = await this.getLocations({ phone: cleanPhone });

      // Return first matching location or null
      return locations.items.length > 0 ? locations.items[0] : null;
    } catch (error) {
      logger.error(
        {
          provider: 'nextech',
          context: 'Location Service - Get Location By Phone',
          error,
        },
        'Error fetching location by phone from Nextech API',
      );

      return null;
    }
  }
}
