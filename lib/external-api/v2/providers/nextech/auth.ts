import { AuthFactory } from '../../auth/auth-factory';
import logger from '../../utils/logger';

/**
 * Nextech authentication helper
 * Provides authenticated headers for Nextech API requests
 */
export class NextechAuth {
  private authService = AuthFactory.createAuthService('nextech');

  /**
   * Get authorization header with valid token
   * @returns Authorization header object
   * @throws AuthenticationError or RateLimitError if auth fails
   */
  async getAuthorizationHeader(): Promise<{ Authorization: string; [key: string]: string }> {
    try {
      const token = await this.authService.getAccessToken();
      return {
        Authorization: `Bearer ${token}`,
        'nx-practice-id': process.env.NEXTECH_PRACTICE_ID || '',
      };
    } catch (error) {
      // Log error safely (without credentials)
      logger.error(
        {
          provider: 'nextech',
          context: 'Authentication',
          error: this.sanitizeError(error as Error),
        },
        'Nextech authentication error',
      );
      throw error;
    }
  }

  /**
   * Clear token cache
   * Call when a token becomes invalid
   */
  clearTokenCache(): void {
    this.authService.clearTokenCache();
  }

  /**
   * Sanitize error message to ensure no credentials are logged
   * @param error The error to sanitize
   * @returns Sanitized error message
   */
  private sanitizeError(error: Error): string {
    // For complete security, replace the entire error message with a generic one
    // if it contains sensitive information
    let sanitized = error.message;

    // List of sensitive keywords to completely replace
    const sensitiveKeywords = [
      'client_id',
      'client_secret',
      'token',
      'apiKey',
      'password',
      'secret',
      'credential',
      'auth',
    ];

    // Check if any sensitive keyword is present
    if (sensitiveKeywords.some(keyword => sanitized.includes(keyword))) {
      // Replace with completely sanitized message
      sanitized = 'Authentication credentials error [SENSITIVE DETAILS REDACTED]';
    }

    return `${error.name}: ${sanitized}`;
  }
}
