import logger from '../../utils/logger'; // Import the logger

/**
 * NextechRateLimiter
 * Ensures compliance with Nextech's rate limit of 20 requests per second per endpoint
 * Implements token bucket algorithm to enforce the rate limit
 */
export class NextechRateLimiter {
  private requestCounts: Map<string, number[]> = new Map();
  private readonly maxRequests = 20; // 20 requests per second per endpoint
  private readonly timeWindow = 1000; // 1 second in milliseconds
  private timeoutRefs: Set<NodeJS.Timeout> = new Set(); // Track active timeouts

  /**
   * Acquire a token before making a request to ensure rate limit compliance
   * If the rate limit would be exceeded, this method waits until it's safe to proceed
   * @param endpoint The endpoint being requested (used to track per-endpoint limits)
   * @returns Promise that resolves when it's safe to make the request
   */
  async acquireToken(endpoint: string): Promise<void> {
    // Get the current time
    const now = Date.now();

    // Get or initialize the request timestamps for this endpoint
    let requests = this.requestCounts.get(endpoint) || [];

    // Filter out requests older than the time window
    requests = requests.filter(timestamp => now - timestamp < this.timeWindow);

    // Check if we've hit the rate limit
    if (requests.length >= this.maxRequests) {
      // Calculate time to wait before next request
      const oldestRequest = requests[0];
      const timeToWait = this.timeWindow - (now - oldestRequest);

      // Log rate limiting action
      logger.warn(
        {
          provider: 'nextech',
          context: 'Rate Limiter',
          endpoint,
          requestCount: requests.length,
          timeToWait,
        },
        'Nextech rate limit reached, throttling requests',
      );

      // Wait for the required time with tracked timeout
      await new Promise<void>(resolve => {
        const timeoutRef = setTimeout(() => {
          this.timeoutRefs.delete(timeoutRef);
          resolve();
        }, timeToWait);
        this.timeoutRefs.add(timeoutRef);
      });

      // Try again after waiting
      return this.acquireToken(endpoint);
    }

    // Add this request to the list
    requests.push(now);
    this.requestCounts.set(endpoint, requests);
  }

  /**
   * Clear all rate limiting data
   * Useful for testing or when rate limiting state needs to be reset
   */
  clear(): void {
    this.requestCounts.clear();
    logger.debug(
      {
        provider: 'nextech',
        context: 'Rate Limiter',
      },
      'Nextech rate limiter state cleared',
    );
  }

  /**
   * Clean up all active timeouts
   * Should be called when shutting down or in test cleanup
   */
  cleanup(): void {
    // Clear all active timeouts
    this.timeoutRefs.forEach(timeoutRef => clearTimeout(timeoutRef));
    this.timeoutRefs.clear();

    // Reset request counts to allow immediate requests after cleanup
    this.requestCounts.clear();

    logger.debug(
      {
        provider: 'nextech',
        context: 'Rate Limiter',
      },
      'Nextech rate limiter resources cleaned up',
    );
  }
}
