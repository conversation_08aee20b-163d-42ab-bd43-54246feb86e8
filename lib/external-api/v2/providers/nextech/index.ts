import {
  IProvider,
  IClinicService,
  ILocationService,
  IPatientService,
  IUserService,
  IAppointmentService,
} from '../types';
import type { ProviderConfig } from '../factory';
import { NextechApiClient } from './client';
import {
  NextechClinicService,
  NextechLocationService,
  NextechPatientService,
  NextechUserService,
  NextechAppointmentService,
} from './services';

/**
 * Nextech-specific configuration
 */
export interface NextechConfig extends ProviderConfig {
  name: 'nextech';
  apiUrl: string;
  clientId: string;
  clientSecret: string;
}

/**
 * Nextech provider implementation
 * Integrates with Nextech Practice+ API
 */
export class NextechProvider implements IProvider {
  name = 'nextech';
  private config: NextechConfig;
  private client: NextechApiClient;
  private clinicService: NextechClinicService | null = null;
  private locationService: NextechLocationService | null = null;
  private patientService: NextechPatientService | null = null;
  private userService: NextechUserService | null = null;
  private appointmentService: NextechAppointmentService | null = null;

  constructor(config: ProviderConfig) {
    this.config = {
      name: 'nextech',
      apiUrl: (config.apiUrl as string) || process.env.NEXTECH_BASE_URL,
      clientId: (config.clientId as string) || process.env.NEXTECH_CLIENT_ID,
      clientSecret: (config.clientSecret as string) || process.env.NEXTECH_CLIENT_SECRET,
    } as NextechConfig;

    // Validate required config
    if (!this.config.apiUrl) {
      throw new Error('Nextech provider requires apiUrl configuration');
    }
    if (!this.config.clientId) {
      throw new Error('Nextech provider requires clientId configuration');
    }
    if (!this.config.clientSecret) {
      throw new Error('Nextech provider requires clientSecret configuration');
    }

    // Create API client
    this.client = new NextechApiClient(this.config.apiUrl);
  }

  getClinicService(): IClinicService {
    if (!this.clinicService) {
      this.clinicService = new NextechClinicService(this.client);
    }
    return this.clinicService;
  }

  getLocationService(): ILocationService {
    if (!this.locationService) {
      this.locationService = new NextechLocationService(this.client);
    }
    return this.locationService;
  }

  getPatientService(): IPatientService {
    if (!this.patientService) {
      this.patientService = new NextechPatientService(this.client);
    }
    return this.patientService;
  }

  getUserService(): IUserService {
    if (!this.userService) {
      this.userService = new NextechUserService(this.client);
    }
    return this.userService;
  }

  getAppointmentService(): IAppointmentService {
    if (!this.appointmentService) {
      this.appointmentService = new NextechAppointmentService(this.client);
    }
    return this.appointmentService;
  }
}
