import axios, { AxiosError, AxiosInstance, AxiosRequestConfig } from 'axios';
import { NextechAuth } from './auth';
import { AuthenticationError, RateLimitError } from '@/lib/external-api/v2/auth';
import {
  BadRequestError,
  ForbiddenError,
  NotFoundError,
  ProviderApiError,
} from '../../utils/errors';
import { NextechRateLimiter } from './rate-limiter';
import logger from '../../utils/logger';

// Define types for request data to avoid using any
interface RequestData {
  [key: string]: unknown;
}

/**
 * NextechApiClient
 * Handles HTTP requests to Nextech API with authentication, error handling, and retry logic
 */
export class NextechApiClient {
  private client: AxiosInstance;
  private auth: NextechAuth;
  private rateLimiter: NextechRateLimiter;
  private maxRetries = 3;
  private initialRetryDelayMs = 1000;
  private timeoutRefs: Set<NodeJS.Timeout> = new Set(); // Track timeouts
  private isTestMode: boolean;

  constructor(
    private readonly baseURL: string,
    options: {
      isTestMode?: boolean;
      auth?: NextechAuth;
      rateLimiter?: NextechRateLimiter;
    } = {},
  ) {
    this.isTestMode = options.isTestMode || false;
    this.auth = options.auth || new NextechAuth();
    this.rateLimiter = options.rateLimiter || new NextechRateLimiter();
    this.client = axios.create({
      baseURL,
      timeout: this.isTestMode ? 5000 : 30000, // Shorter timeout for tests
    });
  }

  /**
   * Make authenticated GET request to Nextech API
   * @param url The endpoint URL (relative to baseURL)
   * @param config Optional axios config
   * @returns Promise with response data
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('GET', url, undefined, config);
  }

  /**
   * Make authenticated POST request to Nextech API
   * @param url The endpoint URL (relative to baseURL)
   * @param data The data to send
   * @param config Optional axios config
   * @returns Promise with response data
   */
  async post<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('POST', url, data, config);
  }

  /**
   * Make authenticated PUT request to Nextech API
   * @param url The endpoint URL (relative to baseURL)
   * @param data The data to send
   * @param config Optional axios config
   * @returns Promise with response data
   */
  async put<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PUT', url, data, config);
  }

  /**
   * Make authenticated PATCH request to Nextech API
   * @param url The endpoint URL (relative to baseURL)
   * @param data The data to send
   * @param config Optional axios config
   * @returns Promise with response data
   */
  async patch<T>(url: string, data?: RequestData, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('PATCH', url, data, config);
  }

  /**
   * Make authenticated DELETE request to Nextech API
   * @param url The endpoint URL (relative to baseURL)
   * @param config Optional axios config
   * @returns Promise with response data
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>('DELETE', url, undefined, config);
  }

  /**
   * Make authenticated request to Nextech API with retry logic
   * @param method HTTP method
   * @param url The endpoint URL
   * @param data Optional request data
   * @param config Optional axios config
   * @returns Promise with response data
   */
  private async request<T>(
    method: string,
    url: string,
    data?: RequestData,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    // In test mode, don't retry requests to speed up tests
    if (this.isTestMode) {
      return this.makeRequest<T>(method, url, data, config);
    }

    let attempt = 0;
    let lastError: Error | null = null;

    while (attempt < this.maxRetries) {
      try {
        return await this.makeRequest<T>(method, url, data, config);
      } catch (error) {
        lastError = error as Error;

        // Don't retry rate limiting errors
        if (error instanceof RateLimitError) {
          throw error;
        }

        // Don't retry auth errors on subsequent attempts
        if (attempt > 0 && error instanceof AuthenticationError) {
          throw error;
        }

        // If auth error on first attempt, clear token cache and retry once
        if (attempt === 0 && error instanceof AuthenticationError) {
          this.auth.clearTokenCache();
        }

        // Exponential backoff for retries
        const delay = this.initialRetryDelayMs * Math.pow(2, attempt);
        await new Promise<void>(resolve => {
          const timeoutRef = setTimeout(() => {
            this.timeoutRefs.delete(timeoutRef);
            resolve();
          }, delay);
          this.timeoutRefs.add(timeoutRef);
        });
        attempt++;
      }
    }

    // If we've exhausted all retries, throw the last error
    throw lastError || new Error('Unknown error during API request');
  }

  /**
   * Make a single request to Nextech API
   * @param method HTTP method
   * @param url The endpoint URL
   * @param data Optional request data
   * @param config Optional axios config
   * @returns Promise with response data
   */
  private async makeRequest<T>(
    method: string,
    url: string,
    data?: RequestData,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      // Apply rate limiting before making request (skip in test mode)
      if (!this.isTestMode) {
        await this.rateLimiter.acquireToken(url);
      }

      // Get auth header
      const authHeader = await this.auth.getAuthorizationHeader();

      // Make request with auth header
      const response = await this.client.request<T>({
        method,
        url,
        data,
        ...config,
        headers: {
          ...config?.headers,
          ...authHeader,
        },
      });

      return response.data;
    } catch (error) {
      this.handleApiError(error);
      // This line is never reached as handleApiError always throws
      throw error;
    }
  }

  /**
   * Handle API errors
   * Transforms various error types into standardized errors
   */
  private handleApiError(error: unknown): never {
    // Log the error (sanitized to remove sensitive info)
    logger.error(
      {
        provider: 'nextech',
        error: this.sanitizeErrorForLogging(error),
        context: 'API Request',
      },
      'Nextech API error occurred',
    );

    // Handle axios errors
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      const status = axiosError.response?.status || 500;
      const data = axiosError.response?.data;

      // Map Nextech error codes to appropriate HTTP status and messages
      switch (status) {
        case 400:
          throw new BadRequestError('Invalid request to Nextech API', {
            originalError: axiosError.message,
            responseData: data,
          });

        case 401:
          throw new AuthenticationError(
            'Authentication failed with Nextech API',
            'UNAUTHORIZED',
            axiosError,
          );

        case 403:
          throw new ForbiddenError('Not authorized to access this Nextech resource', {
            originalError: axiosError.message,
            responseData: data,
          });

        case 404:
          throw new NotFoundError('Resource not found in Nextech API', {
            originalError: axiosError.message,
            resource: axiosError.config?.url,
          });

        case 429:
          const retryAfter = parseInt(
            (axiosError.response?.headers['retry-after'] as string) || '60',
            10,
          );
          throw new RateLimitError('Rate limit exceeded on Nextech API', retryAfter, axiosError);

        default:
          // For all other errors, use ProviderApiError with detailed information
          throw new ProviderApiError(`Nextech API error: ${status} ${axiosError.message}`, {
            originalError: axiosError.message,
            responseData: data,
            status,
            resource: axiosError.config?.url,
            method: axiosError.config?.method,
          });
      }
    }

    // Handle other errors (non-axios)
    if (error instanceof Error) {
      throw new ProviderApiError(`Unexpected error during Nextech API request: ${error.message}`, {
        originalError: error.message,
        stack: error.stack,
      });
    }

    // As a final fallback
    throw new ProviderApiError('Unknown error occurred during Nextech API request', {
      originalError: String(error),
    });
  }

  /**
   * Sanitize error for logging to remove sensitive information
   */
  private sanitizeErrorForLogging(error: unknown): unknown {
    if (axios.isAxiosError(error)) {
      // Create a deep copy without methods to avoid mutation issues
      const sanitizedError: Record<string, unknown> = {
        ...JSON.parse(JSON.stringify(error, Object.getOwnPropertyNames(error))),
      };

      // Ensure config exists
      if (sanitizedError.config && typeof sanitizedError.config === 'object') {
        const config = sanitizedError.config as Record<string, unknown>;

        // Handle headers safely
        if (config.headers && typeof config.headers === 'object') {
          const sanitizedHeaders: Record<string, unknown> = {};
          const headers = config.headers as Record<string, unknown>;

          // Copy headers except sensitive ones
          Object.keys(headers).forEach(key => {
            if (key.toLowerCase() === 'authorization') {
              sanitizedHeaders[key] = '[REDACTED]';
            } else if (
              ['x-api-key', 'api-key', 'nx-practice-id', 'cookie', 'set-cookie'].includes(
                key.toLowerCase(),
              )
            ) {
              sanitizedHeaders[key] = '[REDACTED]';
            } else {
              sanitizedHeaders[key] = headers[key];
            }
          });

          config.headers = sanitizedHeaders;
        }

        // Sanitize request URL if it contains token or auth info
        if (
          typeof config.url === 'string' &&
          (config.url.includes('token') || config.url.includes('auth'))
        ) {
          config.url = config.url.split('?')[0] + '?[QUERY_PARAMS_REDACTED]';
        }

        sanitizedError.config = config;
      }

      return sanitizedError;
    }

    return error;
  }

  /**
   * Clean up all resources used by this client
   * Should be called when the client is no longer needed or during test cleanup
   */
  cleanup(): void {
    // Clean up rate limiter
    this.rateLimiter.cleanup();

    // Clean up any pending timeouts
    this.timeoutRefs.forEach(timeoutRef => clearTimeout(timeoutRef));
    this.timeoutRefs.clear();

    logger.debug(
      {
        provider: 'nextech',
        context: 'API Client',
      },
      'Nextech API client resources cleaned up',
    );
  }
}
