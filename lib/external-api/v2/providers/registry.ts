import { IProvider } from './types';

/**
 * ProviderRegistry manages the available API providers
 * and selects the appropriate provider based on configuration.
 */
export class ProviderRegistry {
  private providers: Map<string, IProvider> = new Map();
  private defaultProvider: string;

  /**
   * Creates a new ProviderRegistry
   * @param defaultProvider The name of the default provider to use when none is specified
   */
  constructor(defaultProvider: string) {
    this.defaultProvider = defaultProvider;
  }

  /**
   * Registers a provider with the registry
   * @param provider The provider implementation to register
   */
  registerProvider(provider: IProvider): void {
    this.providers.set(provider.name, provider);
  }

  /**
   * Gets a provider by name, or the default provider if none is specified
   * @param name Optional name of the provider to get
   * @returns The requested provider
   * @throws Error if the provider is not found
   */
  getProvider(name?: string): IProvider {
    const providerName = name || this.defaultProvider;
    const provider = this.providers.get(providerName);

    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }

    return provider;
  }

  /**
   * Gets the names of all available providers
   * @returns Array of provider names
   */
  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }
}
