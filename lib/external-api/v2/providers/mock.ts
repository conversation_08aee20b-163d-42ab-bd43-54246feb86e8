import { jest } from '@jest/globals';
import {
  IAppointmentService,
  IClinicService,
  ILocationService,
  IPatientService,
  IProvider,
  IUserService,
} from './types';
import {
  Appointment,
  AppointmentPurpose,
  AppointmentType,
  AvailableSlot,
  Clinic,
  Location,
  Patient,
  PatientType,
  User,
  Insurance,
  AppointmentStatus,
} from '../models/types';
import { CreateAppointmentDto, UpdateAppointmentDto } from '../models/dto';
import { PaginatedResult, PaginationParams } from '../models/pagination';

// Default pagination data
const defaultPagination = {
  totalCount: 0,
  limit: 10,
  offset: 0,
  hasMore: false,
  links: {},
};

// Service class implementations with explicit mock functions
export class MockClinicService implements IClinicService {
  getClinics = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Clinic>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getClinicById = jest.fn<(id: string) => Promise<Clinic | null>>().mockResolvedValue(null);
  getClinicByPhone = jest.fn<(phone: string) => Promise<Clinic | null>>().mockResolvedValue(null);
}

export class MockLocationService implements ILocationService {
  getLocations = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Location>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getLocationById = jest.fn<(id: string) => Promise<Location | null>>().mockResolvedValue(null);
  getLocationByPhone = jest
    .fn<(phone: string) => Promise<Location | null>>()
    .mockResolvedValue(null);
}

export class MockPatientService implements IPatientService {
  updatePatient = jest
    .fn<
      (id: string, patientData: Partial<Omit<Patient, 'id' | 'providerInfo'>>) => Promise<Patient>
    >()
    .mockResolvedValue({} as Patient);
  getPatients = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Patient>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getPatientById = jest.fn<(id: string) => Promise<Patient | null>>().mockResolvedValue(null);
  getPatientByIdWithoutInsurance = jest
    .fn<(id: string) => Promise<Patient | null>>()
    .mockResolvedValue(null);
  getPatientByPhone = jest.fn<(phone: string) => Promise<Patient | null>>().mockResolvedValue(null);
  getPatientByEmail = jest.fn<(email: string) => Promise<Patient | null>>().mockResolvedValue(null);
  getPatientByFullNameAndDob = jest
    .fn<(fullName: string, dob: string) => Promise<Patient | null>>()
    .mockResolvedValue(null);
  getPatientTypes = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<PatientType>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getPatientTypeById = jest
    .fn<(id: string) => Promise<PatientType | null>>()
    .mockResolvedValue(null);
  createPatient = jest
    .fn<(patientData: Omit<Patient, 'id' | 'providerInfo'>) => Promise<Patient>>()
    .mockResolvedValue({} as Patient);

  // Add the missing insurance-related methods
  getPatientInsurances = jest
    .fn<(patientId: string) => Promise<Insurance[]>>()
    .mockResolvedValue([]);

  getInsuranceById = jest.fn<(id: string) => Promise<Insurance | null>>().mockResolvedValue(null);

  createInsurance = jest
    .fn<(insurance: Omit<Insurance, 'id' | 'providerInfo'>) => Promise<Insurance>>()
    .mockResolvedValue({} as Insurance);

  updateInsurance = jest
    .fn<(id: string, insurance: Partial<Insurance>) => Promise<Insurance>>()
    .mockResolvedValue({} as Insurance);

  deleteInsurance = jest.fn<(id: string) => Promise<boolean>>().mockResolvedValue(true);
}

export class MockUserService implements IUserService {
  getUsers = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<User>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getUserById = jest.fn<(id: string) => Promise<User | null>>().mockResolvedValue(null);
  getUserByPhone = jest.fn<(phone: string) => Promise<User | null>>().mockResolvedValue(null);
  getUserByEmail = jest.fn<(email: string) => Promise<User | null>>().mockResolvedValue(null);
  getUserByFullName = jest.fn<(fullName: string) => Promise<User | null>>().mockResolvedValue(null);
}

export class MockAppointmentService implements IAppointmentService {
  getAppointments = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentById = jest
    .fn<(id: string) => Promise<Appointment | null>>()
    .mockResolvedValue(null);
  createAppointment = jest
    .fn<(data: CreateAppointmentDto) => Promise<Appointment>>()
    .mockResolvedValue({} as Appointment);
  updateAppointment = jest
    .fn<(id: string, data: UpdateAppointmentDto) => Promise<Appointment>>()
    .mockResolvedValue({} as Appointment);
  cancelAppointment = jest
    .fn<(id: string, reason?: string) => Promise<boolean>>()
    .mockResolvedValue(true);
  confirmAppointment = jest.fn<(id: string) => Promise<boolean>>().mockResolvedValue(true);
  changeAppointment = jest
    .fn<(id: string, data: UpdateAppointmentDto) => Promise<Appointment>>()
    .mockResolvedValue({} as Appointment);
  getAppointmentByPatientId = jest
    .fn<
      (patientId: string, pagination?: PaginationParams) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getLastFulfilledAppointmentByPatientId = jest
    .fn<(patientId: string) => Promise<Appointment | null>>()
    .mockResolvedValue(null);
  getAppointmentByClinicId = jest
    .fn<
      (clinicId: string, pagination?: PaginationParams) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByLocationId = jest
    .fn<
      (locationId: string, pagination?: PaginationParams) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDate = jest
    .fn<(date: string, pagination?: PaginationParams) => Promise<PaginatedResult<Appointment>>>()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDateRange = jest
    .fn<
      (
        startDate: string,
        endDate: string,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDateRangeAndLocationId = jest
    .fn<
      (
        startDate: string,
        endDate: string,
        locationId: string,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDateRangeAndClinicId = jest
    .fn<
      (
        startDate: string,
        endDate: string,
        clinicId: string,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDateRangeAndProviderId = jest
    .fn<
      (
        startDate: string,
        endDate: string,
        providerId: string,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentByDateRangeAndPatientId = jest
    .fn<
      (
        startDate: string,
        endDate: string,
        patientId: string,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentTypes = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<AppointmentType>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentTypeById = jest
    .fn<(id: string) => Promise<AppointmentType | null>>()
    .mockResolvedValue(null);
  getAvailableSlots = jest
    .fn<
      (
        filters: {
          appointmentTypeId: string;
          startDate: string;
          endDate: string;
          practitionerId?: string;
          locationId?: string;
        },
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<AvailableSlot>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentPurposes = jest
    .fn<
      (
        filters?: Record<string, unknown>,
        pagination?: PaginationParams,
      ) => Promise<PaginatedResult<AppointmentPurpose>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentPurposeById = jest
    .fn<(id: string) => Promise<AppointmentPurpose | null>>()
    .mockResolvedValue(null);

  getAppointmentsForRescheduling = jest
    .fn<(patientId: string, isForRescheduling?: boolean) => Promise<PaginatedResult<Appointment>>>()
    .mockResolvedValue({ items: [], pagination: defaultPagination });

  getAppointmentsByStatus = jest
    .fn<
      (
        status: AppointmentStatus,
        params: {
          startDate?: string;
          endDate?: string;
          locationId?: string;
          pagination?: PaginationParams;
        },
      ) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
  getAppointmentsByStatuses = jest
    .fn<
      (params: {
        statuses: AppointmentStatus[];
        startDate?: string;
        endDate?: string;
        startLastUpdated?: string;
        endLastUpdated?: string;
        locationId?: string;
        pagination?: PaginationParams;
      }) => Promise<PaginatedResult<Appointment>>
    >()
    .mockResolvedValue({ items: [], pagination: defaultPagination });
}

/**
 * Mock Provider for testing purposes
 * Implements the IProvider interface with mocked services.
 */
export class MockProvider implements IProvider {
  name = 'mock';

  // Services
  clinics = new MockClinicService();
  locations = new MockLocationService();
  patients = new MockPatientService();
  users = new MockUserService();
  appointments = new MockAppointmentService();

  // Direct method access properties for convenience
  getClinics = this.clinics.getClinics;
  getClinicById = this.clinics.getClinicById;
  getLocations = this.locations.getLocations;
  getLocationById = this.locations.getLocationById;
  getPatients = this.patients.getPatients;
  getPatientById = this.patients.getPatientById;
  getPatientTypes = this.patients.getPatientTypes;
  getUsers = this.users.getUsers;
  getUserById = this.users.getUserById;
  getAppointments = this.appointments.getAppointments;
  getAppointmentById = this.appointments.getAppointmentById;
  createAppointment = this.appointments.createAppointment;
  updateAppointment = this.appointments.updateAppointment;
  cancelAppointment = this.appointments.cancelAppointment;
  confirmAppointment = this.appointments.confirmAppointment;
  getAppointmentTypes = this.appointments.getAppointmentTypes;
  getAvailability = this.appointments.getAvailableSlots;
  getAppointmentPurposes = this.appointments.getAppointmentPurposes;
  bookAppointment = this.appointments.createAppointment;

  // IProvider interface implementation methods
  getClinicService(): IClinicService {
    return this.clinics;
  }

  getLocationService(): ILocationService {
    return this.locations;
  }

  getPatientService(): IPatientService {
    return this.patients;
  }

  getUserService(): IUserService {
    return this.users;
  }

  getAppointmentService(): IAppointmentService {
    return this.appointments;
  }
}
