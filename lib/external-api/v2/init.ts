import { providerRegistry, providerFactory, NextechProvider } from './providers';
import logger from './utils/logger';
import { NextApiRequest, NextApiResponse } from 'next/types';
import { eventEmitter } from './utils/events/event-emitter';
import { setupAllNotifications } from './notifications';

// Track initialization state
let initialized = false;

// Store unsubscribe function for cleanup
let unsubscribeNotifications: (() => void) | null = null;

/**
 * Initialize the External API v2 providers
 * This ensures all providers are properly registered with the registry
 */
export function initializeExternalApi(): void {
  // Skip if already initialized
  if (initialized) {
    return;
  }

  try {
    // Register provider constructors with the factory
    providerFactory.registerProviderConstructor('nextech', NextechProvider);

    // Create and register provider instances
    const nextechProvider = new NextechProvider({
      name: 'nextech',
      apiUrl: process.env.NEXTECH_BASE_URL,
      clientId: process.env.NEXTECH_CLIENT_ID,
      clientSecret: process.env.NEXTECH_CLIENT_SECRET,
    });

    // Register the provider with the registry
    providerRegistry.registerProvider(nextechProvider);

    // Make sure EventEmitter is initialized
    // This is a no-op since the eventEmitter is a singleton, but we import it here
    // to ensure it's required early in the initialization process
    if (eventEmitter) {
      logger.debug('EventEmitter initialized successfully');
    }

    // Set up all notification handlers
    unsubscribeNotifications = setupAllNotifications();
    logger.debug('All notification handlers set up successfully');

    // Mark as initialized
    initialized = true;

    logger.info(
      {
        providers: providerRegistry.getAvailableProviders(),
      },
      'External API v2 initialized successfully',
    );
  } catch (error) {
    logger.error(
      {
        error,
      },
      'Failed to initialize External API v2',
    );
    throw error;
  }
}

/**
 * Cleans up resources used by the External API v2
 * This should be called when shutting down the application
 */
export function cleanupExternalApi(): void {
  if (unsubscribeNotifications) {
    unsubscribeNotifications();
    unsubscribeNotifications = null;
  }

  initialized = false;
  logger.info('External API v2 cleaned up successfully');
}

/**
 * Middleware to ensure providers are initialized
 * This can be added to API handlers to guarantee initialization
 */
export function ensureProvidersInitialized(
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>,
): Promise<void> {
  if (!initialized) {
    initializeExternalApi();
  }
  return next();
}
