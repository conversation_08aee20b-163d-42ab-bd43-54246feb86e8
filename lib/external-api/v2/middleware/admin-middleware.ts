import { NextApiRequest, NextApiResponse } from 'next';
import { MiddlewareHandler } from './index';

/**
 * Middleware to protect admin endpoints
 * Checks for a valid admin token in the x-admin-token header
 *
 * @param req The request object
 * @param res The response object
 * @param next The next middleware function
 */
export const adminMiddleware: MiddlewareHandler = async (
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>,
): Promise<void> => {
  // For now, just check for a special header
  // In production, this would validate admin credentials properly
  const adminToken = req.headers['x-admin-token'];
  const validAdminKey = process.env.ADMIN_API_KEY;

  if (!adminToken || adminToken !== validAdminKey) {
    res.status(401).json({
      status: 401,
      code: 'UNAUTHORIZED',
      message: 'Admin authentication required',
    });
    return;
  }

  await next();
};
