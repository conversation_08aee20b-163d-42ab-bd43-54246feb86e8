import { validate<PERSON><PERSON><PERSON><PERSON>, applyMiddleware } from './auth';
import { requirePatientAccess, requireUserAccess } from './permissions';
import { withPagination, addPaginationHeaders } from './pagination';
import type { MiddlewareHandler } from './auth';
import type { NextApiRequestWithPagination } from './pagination';

export {
  validateApiKey,
  applyMiddleware,
  requirePatientAccess,
  requireUserAccess,
  withPagination,
  addPaginationHeaders,
};

export type { MiddlewareHandler, NextApiRequestWithPagination };
