import { NextApiRequest, NextApiResponse } from 'next';
import { UnauthorizedError } from '../utils/errors';

// NextJS middleware handler type
export type MiddlewareHandler = (
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>,
) => Promise<void>;

/**
 * Middleware for validating API keys
 * @param req NextJS API request
 * @param res NextJS API response
 * @param next Function to call next middleware
 */
export const validateApiKey: MiddlewareHandler = async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    throw new UnauthorizedError('API key is required');
  }

  // Use EXTERNAL_SERVICE_API_KEY like v1 API does
  const VALID_API_KEYS = [process.env.EXTERNAL_SERVICE_API_KEY].filter(Boolean);

  if (!VALID_API_KEYS.includes(apiKey as string)) {
    throw new UnauthorizedError('Invalid API key');
  }

  // If we get here, the API key is valid
  await next();
};

/**
 * Helper function to apply multiple middleware handlers to a request
 * @param handlers Array of middleware handlers to apply
 * @returns A handler function that applies all middleware
 */
export const applyMiddleware = (handlers: MiddlewareHandler[]) => {
  return async (
    req: NextApiRequest,
    res: NextApiResponse,
    handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>,
  ) => {
    // Create a linked list of middleware functions
    const execMiddleware = async (index: number): Promise<void> => {
      if (index < handlers.length) {
        await handlers[index](req, res, () => execMiddleware(index + 1));
      } else {
        await handler(req, res);
      }
    };

    await execMiddleware(0);
  };
};
