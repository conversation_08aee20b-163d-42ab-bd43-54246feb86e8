import { NextApiRequest, NextApiResponse } from 'next';
import { extractPaginationFromQuery } from '../models/pagination';

/**
 * Type definition for Next.js API request with pagination
 * Extends NextApiRequest to include typed pagination parameters
 */
export interface NextApiRequestWithPagination extends NextApiRequest {
  pagination: {
    limit: number;
    offset: number;
  };
}

/**
 * Middleware to handle pagination parameters
 * Extracts and validates pagination parameters from the request
 * Attaches them to the request object for use in handlers
 *
 * @param handler Next.js API handler function
 * @returns Handler function with pagination support
 */
export function withPagination(
  handler: (req: NextApiRequestWithPagination, res: NextApiResponse) => Promise<void>,
) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // Extract and validate pagination parameters
    const pagination = extractPaginationFromQuery(req.query);

    // Attach validated pagination to request
    (req as NextApiRequestWithPagination).pagination = pagination;

    // Call the original handler
    return handler(req as NextApiRequestWithPagination, res);
  };
}

/**
 * Helper function to add pagination link headers to the response
 * Formats and sets the Link header according to RFC 5988
 *
 * @param res Next.js API response object
 * @param links Pagination links object
 */
export function addPaginationHeaders(
  res: NextApiResponse,
  links: {
    first?: string;
    prev?: string;
    next?: string;
    last?: string;
  },
): void {
  const linkHeaders = [];

  if (links.first) {
    linkHeaders.push(`<${links.first}>; rel="first"`);
  }

  if (links.prev) {
    linkHeaders.push(`<${links.prev}>; rel="prev"`);
  }

  if (links.next) {
    linkHeaders.push(`<${links.next}>; rel="next"`);
  }

  if (links.last) {
    linkHeaders.push(`<${links.last}>; rel="last"`);
  }

  if (linkHeaders.length > 0) {
    res.setHeader('Link', linkHeaders.join(', '));
  }
}
