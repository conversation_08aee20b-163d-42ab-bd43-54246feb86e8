import logger from '../utils/logger';
import { setupAppointmentCreatedEmailNotifications } from './appointment-created-email-notifications';
import { setupCallCreatedEmailNotifications } from './call-created-email-notifications';

/**
 * Container for storing unsubscribe functions
 */
const unsubscribeFunctions: Array<() => void> = [];

/**
 * Initializes all notification handlers
 * @returns Function to unsubscribe all handlers
 */
export function setupAllNotifications(): () => void {
  logger.info('Setting up all notification handlers');

  // Setup all notification handlers and collect their unsubscribe functions
  unsubscribeFunctions.push(setupAppointmentCreatedEmailNotifications());
  unsubscribeFunctions.push(setupCallCreatedEmailNotifications());

  // Return a function that will unsubscribe all handlers
  return function unsubscribeAll(): void {
    logger.info('Unsubscribing all notification handlers');
    unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    unsubscribeFunctions.length = 0; // Clear the array
  };
}
