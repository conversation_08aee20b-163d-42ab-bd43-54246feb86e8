import logger from '../utils/logger';
import { eventEmitter, EVENTS } from '../utils/events';
import { Appointment } from '../models/types';
import { callSessionsService, mailService, userService } from '@/utils/firestore';
import { providerRegistry } from '../providers';
import { formatSimpleDateTime } from '../utils/timezone';
import { isAudioSessionId, isMessengerSessionId } from '../utils/call-session-utils';

const { GCP_AGENT_ID } = process.env;

/**
 * Handles sending email notifications when appointments are created
 */
export function setupAppointmentCreatedEmailNotifications(): () => void {
  logger.info('Setting up appointment created email notifications');

  // Subscribe to appointment created events
  const unsubscribe = eventEmitter.on(EVENTS.APPOINTMENT.CREATED, handleAppointmentCreated);

  return unsubscribe;
}

/**
 * Handles appointment created events
 * @param appointment The newly created appointment
 */
async function handleAppointmentCreated({
  appointment,
  sessionId,
}: {
  appointment: Appointment;
  sessionId: string;
}): Promise<void> {
  if (!sessionId) {
    logger.error(
      { context: 'Email Notification Service', appointmentId: appointment.id },
      'No session ID found for appointment',
    );
    return;
  }

  if (!isAudioSessionId(sessionId) && !isMessengerSessionId(sessionId)) {
    logger.info(
      { context: 'Email Notification Service', appointmentId: appointment.id, sessionId },
      'Skipping email notification for non-audio or non-messenger session',
    );
    return;
  }

  const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);
  if (!callSession) {
    logger.error(
      { context: 'Email Notification Service', appointmentId: appointment.id, sessionId },
      'No call session found for appointment',
    );
    return;
  }

  // Enable notifications only for production agent.
  const isNotificationEnabled = callSession.agentId === GCP_AGENT_ID;
  logger.info(
    {
      context: 'Email Notification Service',
      appointmentId: appointment.id,
      patientId: appointment.patientId,
      provider: appointment.providerInfo.provider,
      isNotificationEnabled,
    },
    'Sending email notification for new appointment',
  );

  if (isNotificationEnabled) {
    await sendAppointmentConfirmationEmail(appointment);
  }
}

/**
 * Sends an appointment confirmation email to the patient
 * @param appointment The appointment to send a confirmation for
 */
async function sendAppointmentConfirmationEmail(appointment: Appointment): Promise<void> {
  // This is a placeholder for your actual email sending implementation
  logger.info(
    {
      context: 'Email Notification Service',
      appointmentId: appointment.id,
      patientId: appointment.patientId,
      startTime: appointment.startTime,
      locationId: appointment.locationId,
    },
    'Appointment confirmation email sent',
  );

  // Get the staff member ids for the location
  const staffMemberIds = await userService.getLocationNotifiableStaffIdsForNewAppointment(
    appointment.locationId,
  );
  if (!staffMemberIds.length) {
    logger.info(
      {
        context: 'Email Notification Service',
      },
      'No staff members found for location',
    );
    return;
  }

  const contactNumber = await getPatientPhoneNumber(
    appointment.providerInfo.provider,
    appointment.patientId,
  );
  const formattedStartTime = formatSimpleDateTime(appointment.startTime);

  // Send the email to the staff members
  await mailService.sendNewAppointmentEmail(staffMemberIds, {
    patientName: appointment.patientName,
    doctorName: appointment.practitionerName,
    startTime: formattedStartTime,
    contactNumber,
  });
}

async function getPatientPhoneNumber(
  providerName: string | undefined,
  patientId: string,
): Promise<string> {
  if (!providerName) {
    return '';
  }

  const provider = providerRegistry.getProvider(providerName);
  const patientService = provider.getPatientService();
  const patient = await patientService.getPatientById(patientId);
  return patient?.phoneNumber || '';
}
