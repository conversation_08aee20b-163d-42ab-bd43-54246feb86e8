import logger from '../utils/logger';
import { eventEmitter, EVENTS } from '../utils/events';
import { Call } from '@/models/Call';
import { callSessionsService, mailService, userService } from '@/utils/firestore';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { isAudioSessionId, isMessengerSessionId } from '../utils/call-session-utils';

const { GCP_AGENT_ID } = process.env;

/**
 * <PERSON>les sending email notifications when appointments are created
 */
export function setupCallCreatedEmailNotifications(): () => void {
  logger.info('Setting up call created email notifications');

  // Subscribe to appointment created events
  const unsubscribe = eventEmitter.on(EVENTS.CALL.CREATED, handleCallCreated);

  return unsubscribe;
}

/**
 * Handles call created events
 * @param call The newly created call
 */
async function handleCallCreated(call: Call): Promise<void> {
  const { sessionId } = call;
  if (!sessionId) {
    logger.error(
      { context: 'Email Notification Service', callId: call.id },
      'No session ID found for call',
    );
    return;
  }

  if (!isAudioSessionId(sessionId) && !isMessengerSessionId(sessionId)) {
    logger.info(
      { context: 'Email Notification Service', callId: call.id, sessionId },
      'Skipping email notification for non-audio or non-messenger session',
    );
    return;
  }

  const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);
  if (!callSession) {
    logger.error(
      { context: 'Email Notification Service', callId: call.id, sessionId },
      'No call session found for call',
    );
    return;
  }

  // Enable notifications only for production agent.
  const isNotificationEnabled = callSession.agentId === GCP_AGENT_ID;
  logger.info(
    {
      context: 'Email Notification Service',
      callId: call.id,
      sessionId,
      isNotificationEnabled,
    },
    'Sending email notification for new call',
  );

  if (isNotificationEnabled) {
    await Promise.all([sendCallNotificationEmail(call), sendVoiceMailNotificationEmail(call)]);
  }
}

/**
 * Sends an appointment confirmation email to the patient
 * @param appointment The appointment to send a confirmation for
 */
async function sendCallNotificationEmail(call: Call): Promise<void> {
  logger.info(
    {
      context: 'Email Notification Service',
      callId: call.id,
      locationId: call.locationId?.toString(),
      clinicId: call.clinicId,
    },
    'Call notification email sent',
  );

  // Get the staff member ids for the location or clinic
  const staffMemberIds = await userService.getNotifiableStaffIdsForNewCall({
    locationId: call.locationId?.toString(),
    clinicId: call.clinicId,
  });
  if (!staffMemberIds.length) {
    logger.info(
      {
        context: 'Email Notification Service',
      },
      'No staff members found for new call notification',
    );
    return;
  }

  const appLinkBuilder = AppLinkBuilder.getInstance();
  const callDetailsLink = appLinkBuilder.getCallDetailsLink(call.id);

  // Send the email to the staff members
  await mailService.sendNewCallEmail(staffMemberIds, {
    ctaLink: callDetailsLink,
  });
}

/**
 * Sends a voice mail notification email to the staff members
 * @param call The call to send a voice mail notification for
 */
async function sendVoiceMailNotificationEmail(call: Call): Promise<void> {
  if (!call.hasVoiceMail) {
    logger.debug(
      {
        context: 'Email Notification Service',
        callId: call.id,
      },
      'No voice mail notification email sent because call does not have a voice mail',
    );
    return;
  }

  logger.info(
    {
      context: 'Email Notification Service',
      callId: call.id,
    },
    'Voice mail notification email sent',
  );

  // Get the staff member ids for the location or clinic
  const staffMemberIds = await userService.getNotifiableStaffIdsForNewVoiceMail({
    locationId: call.locationId?.toString(),
    clinicId: call.clinicId,
  });
  if (!staffMemberIds.length) {
    logger.info(
      {
        context: 'Email Notification Service',
      },
      'No staff members found for voice mail notification',
    );
    return;
  }

  const appLinkBuilder = AppLinkBuilder.getInstance();
  const callDetailsLink = appLinkBuilder.getCallDetailsLink(call.id);

  // Send the email to the staff members
  await mailService.sendNewVoiceMailEmail(staffMemberIds, {
    ctaLink: callDetailsLink,
  });
}
