# External API v2

This module provides an adapter-based architecture for integrating with external practice management APIs.

## Architecture

The architecture follows the adapter pattern to support multiple providers:

- Provider interfaces define the contract that all providers must implement
- Provider registry manages available providers
- Provider factory creates provider instances based on configuration
- Common data models ensure consistent structure regardless of provider

## Directory Structure

```
lib/external-api/v2/
├── auth/                # Authentication services
│   ├── interfaces/      # Auth interfaces
│   ├── errors/          # Auth-specific error types
│   ├── providers/       # Credential providers
│   ├── cache/           # Token caching
│   ├── services/        # Provider-specific auth implementations
│   └── auth-factory.ts  # Factory for creating auth services
├── models/              # Common data models
├── providers/           # Provider implementations
│   ├── nextech/         # Nextech provider
│   │   ├── auth.ts      # Nextech auth helper
│   │   ├── client.ts    # Nextech API client with auth integration
│   │   ├── services/    # Service implementations
│   │   └── index.ts     # Nextech provider entry point
│   ├── factory.ts       # Provider factory
│   ├── registry.ts      # Provider registry
│   ├── types.ts         # Provider interfaces
│   └── index.ts         # Provider exports
├── middleware/          # Middleware for API routes
├── utils/               # Utility functions
└── index.ts             # Main exports
```

## Authentication

The authentication system is designed to support multiple providers with different authentication mechanisms:

- `IAuthService` interface defines the contract for all auth services
- `ICredentialProvider` abstracts credential storage (environment variables, vault, etc.)
- `ITokenCache` provides token caching to minimize authentication requests
- Provider-specific auth services handle the authentication logic
- Token management includes automatic refresh, rate limiting awareness, and retry logic

### Nextech Authentication

Nextech uses OAuth 2.0 client credentials flow. To authenticate:

1. Set the required environment variables:

   - `NEXTECH_CLIENT_ID` - Your Nextech client ID
   - `NEXTECH_CLIENT_SECRET` - Your Nextech client secret
   - `NEXTECH_RESOURCE` - The resource URL (usually 'https://api.pm.nextech.com/')
   - `NEXTECH_BASE_URL` - The base URL for API requests (usually 'https://api.pm.nextech.com/api')
   - `NEXTECH_PRACTICE_ID` - Your Nextech practice ID

2. Use the `NextechAuth` helper for authenticated requests:

```typescript
import { NextechAuth } from 'lib/external-api/v2/providers/nextech/auth';

const auth = new NextechAuth();
const headers = await auth.getAuthorizationHeader();
// headers = { Authorization: 'Bearer token123' }
```

3. Or use the `NextechApiClient` for simplified API requests:

```typescript
import { NextechApiClient } from 'lib/external-api/v2/providers/nextech/client';

const client = new NextechApiClient('https://api.pm.nextech.com/api');
const clinics = await client.get('/clinics');
```

## Error Handling

Custom error types are provided for standardized error handling:

- `AuthenticationError` - Thrown when authentication fails
- `RateLimitError` - Thrown when rate limits are exceeded

## Pagination

The API implements standardized pagination across all list endpoints based on Nextech's model:

- **Default limit**: 10 items per page
- **Maximum limit**: 50 items per page
- **Offset-based**: Uses offset for pagination rather than page numbers

### Pagination Parameters

- `limit`: Number of items to return per page (default: 10, max: 50)
- `offset`: Starting position for results (default: 0)

Example request:

```
GET /api/external-api/v2/patients?limit=20&offset=40
```

### Response Format

All paginated responses follow this structure:

```json
{
  "items": [...],  // Array of items for the current page
  "pagination": {
    "totalCount": 100,  // Total number of items
    "limit": 20,        // Current limit
    "offset": 40,       // Current offset
    "hasMore": true,    // Whether more items exist
    "links": {
      "first": "/api/external-api/v2/patients?limit=20&offset=0",
      "prev": "/api/external-api/v2/patients?limit=20&offset=20",
      "next": "/api/external-api/v2/patients?limit=20&offset=60",
      "last": "/api/external-api/v2/patients?limit=20&offset=80"
    }
  }
}
```

### Pagination Utilities

The following utilities are provided for implementing pagination:

- `extractPaginationFromQuery`: Extracts and validates pagination parameters from request query
- `createPaginationLinks`: Generates pagination links for navigation
- `convertNextechPaginationParams`: Converts Nextech-specific pagination parameters to our standard format

## Overview

External API v2 is a provider-agnostic API for accessing clinic, patient, and appointment data. It's designed to support multiple providers, starting with Nextech Practice+ API, and can be extended to support additional providers in the future.

### Key Components

- **Provider Interfaces**: Common interfaces that all providers must implement
- **Provider Registry**: A central registry that manages available provider implementations
- **Provider Factory**: Creates provider instances based on configuration
- **Models**: Common data models that provide a consistent interface regardless of provider
- **Middleware**: Cross-cutting concerns like authentication, validation, and error handling

### Project Structure

```
lib/external-api/v2/           # Core implementation
├── index.ts                   # Main exports
├── providers/                 # Provider implementations
│   ├── index.ts               # Provider registry and factory exports
│   ├── registry.ts            # Provider registry implementation
│   ├── factory.ts             # Provider factory implementation
│   ├── types.ts               # Common provider interfaces
│   └── nextech/               # Nextech provider implementation
│       └── index.ts           # Nextech provider class
├── models/                    # Common data models
│   ├── index.ts               # Models exports
│   ├── types.ts               # Common type definitions
│   └── dto.ts                 # Data transfer objects
├── middleware/                # Middleware functions
│   ├── index.ts               # Middleware exports
│   └── auth.ts                # Authentication middleware
└── utils/                     # Utility functions
    ├── index.ts               # Utility exports
    ├── errors.ts              # Error handling utilities
    └── handler.ts             # API handler utilities

pages/api/external-api/v2/     # API route handlers
├── index.ts                   # Main API entry point
└── README.md                  # Documentation
```

## Providers

### Available Providers

- **Nextech**: Implementation for the Nextech Practice+ API

### Adding a New Provider

To add a new provider:

1. Create a new directory under `providers/` for your provider (e.g., `providers/newprovider/`)
2. Implement the provider interfaces defined in `providers/types.ts`
3. Register the provider with the registry in your application startup code:

```typescript
import { providerRegistry } from './providers';
import { NewProvider } from './providers/newprovider';

// Create provider instance
const newProvider = new NewProvider({
  name: 'newprovider',
  // Provider-specific configuration
});

// Register with the registry
providerRegistry.registerProvider(newProvider);
```

## Authentication

The API uses two levels of authentication:

1. **API Key Authentication**: Client applications must provide a valid API key in the `x-api-key` header
2. **Provider Authentication**: Each provider implementation handles its own authentication with the external API (e.g., OAuth for Nextech)

## Error Handling

Errors are standardized across all providers. The API returns consistent error responses with the following format:

```json
{
  "status": 400,
  "code": "BAD_REQUEST",
  "message": "Invalid input",
  "details": {
    // Optional additional details about the error
  }
}
```

## Usage

### Selecting a Provider

Clients can select a specific provider by including the `x-provider` header in their request. If not specified, the default provider (Nextech) will be used.

```
GET /api/external-api/v2/clinics
x-api-key: your-api-key
x-provider: nextech
```

### API Endpoints

#### Clinics and Locations

- `GET /api/external-api/v2/clinics` - Get all clinics (paginated)
- `GET /api/external-api/v2/locations` - Get all locations (paginated)

#### Patients

- `GET /api/external-api/v2/patients` - Get all patients (paginated)
- `GET /api/external-api/v2/patients/:id` - Get a specific patient by ID

#### Users/Practitioners

- `GET /api/external-api/v2/users` - Get all users/practitioners (paginated)
- `GET /api/external-api/v2/users/:id` - Get a specific user by ID

#### Appointments

- `GET /api/external-api/v2/appointments` - Get all appointments (paginated)
- `GET /api/external-api/v2/appointments/:id` - Get a specific appointment by ID
- `POST /api/external-api/v2/appointments` - Create a new appointment
- `PATCH /api/external-api/v2/appointments/:id` - Update an appointment
- `DELETE /api/external-api/v2/appointments/:id` - Cancel an appointment

#### Appointment Types

- `GET /api/external-api/v2/appointment-types` - Get all appointment types (paginated)

#### Appointment Availability

- `GET /api/external-api/v2/appointment-availability` - Get available appointment slots (paginated)
  - Required parameters:
    - `startDate`: Start date for availability search (YYYY-MM-DD)
    - `endDate`: End date for availability search (YYYY-MM-DD)
  - Optional parameters:
    - `locationId`: Filter by location
    - `practitionerId`: Filter by practitioner
    - `appointmentTypeId`: Filter by appointment type

### Query Parameters

In addition to pagination parameters, most endpoints support filtering:

- `GET /api/external-api/v2/patients?firstName=John&lastName=Doe`
- `GET /api/external-api/v2/appointments?startDate=2023-01-01&endDate=2023-01-31`

See specific endpoint documentation for available filters.
