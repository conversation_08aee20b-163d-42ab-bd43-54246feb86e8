/**
 * Pagination parameters based on Nextech's model
 * Used for requests to paginate results
 */
export interface PaginationParams {
  limit: number; // Default 10, max 50
  offset: number; // For handling pages beyond the first
}

/**
 * Pagination link information
 * Used to navigate between pages of results
 */
export interface PaginationLinks {
  first?: string;
  prev?: string;
  next?: string;
  last?: string;
}

/**
 * Standardized result format for paginated data
 * Provides consistent interface across all paginated endpoints
 */
export interface PaginatedResult<T> {
  items: T[];
  pagination: {
    totalCount: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    links: PaginationLinks;
  };
}

/**
 * Base service interface with standardized pagination support
 */
export interface IBaseService<T> {
  getAll(
    filters?: Record<string, unknown>,
    pagination?: PaginationParams,
  ): Promise<PaginatedResult<T>>;
}

/**
 * Utility function to convert Nextech pagination parameters to our standard format
 * @param nextechParams Nextech pagination parameters (_count, etc.)
 * @returns Standardized pagination parameters
 */
export function convertNextechPaginationParams(
  nextechParams: Record<string, unknown>,
): PaginationParams {
  // Default values
  const defaultLimit = 10;
  const maxLimit = 50;

  // Extract and validate limit
  let limit = defaultLimit;
  if (nextechParams._count && typeof nextechParams._count === 'string') {
    const parsedCount = parseInt(nextechParams._count, 10);
    if (!isNaN(parsedCount) && parsedCount > 0) {
      limit = Math.min(parsedCount, maxLimit);
    }
  }

  // Extract and validate offset
  let offset = 0;
  if (nextechParams._offset && typeof nextechParams._offset === 'string') {
    const parsedOffset = parseInt(nextechParams._offset, 10);
    if (!isNaN(parsedOffset) && parsedOffset >= 0) {
      offset = parsedOffset;
    }
  }

  return { limit, offset };
}

/**
 * Create pagination links for the response
 * @param baseUrl Base URL for the endpoint
 * @param totalCount Total number of items
 * @param limit Number of items per page
 * @param offset Current offset
 * @param queryParams Additional query parameters to include in links
 * @returns PaginationLinks object with URLs for navigation
 */
export function createPaginationLinks(
  baseUrl: string,
  totalCount: number,
  limit: number,
  offset: number,
  queryParams: Record<string, string> = {},
): PaginationLinks {
  // Create a copy of the query params and remove pagination params
  const filteredParams = { ...queryParams };
  delete filteredParams.limit;
  delete filteredParams.offset;
  delete filteredParams._count;
  delete filteredParams._offset;

  // Build the query string for non-pagination parameters
  const queryString = Object.entries(filteredParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');

  const baseQueryString = queryString ? `${queryString}&` : '';
  const links: PaginationLinks = {};

  // First page link
  links.first = `${baseUrl}?${baseQueryString}limit=${limit}&offset=0`;

  // Previous page link (if not on first page)
  if (offset > 0) {
    const prevOffset = Math.max(0, offset - limit);
    links.prev = `${baseUrl}?${baseQueryString}limit=${limit}&offset=${prevOffset}`;
  }

  // Next page link (if more results exist)
  if (offset + limit < totalCount) {
    const nextOffset = offset + limit;
    links.next = `${baseUrl}?${baseQueryString}limit=${limit}&offset=${nextOffset}`;
  }

  // Last page link
  const lastOffset = Math.floor((totalCount - 1) / limit) * limit;
  if (lastOffset >= 0 && lastOffset !== offset) {
    links.last = `${baseUrl}?${baseQueryString}limit=${limit}&offset=${lastOffset}`;
  }

  return links;
}

/**
 * Helper function to extract default pagination parameters from a request
 * @param query Request query parameters
 * @returns Standardized pagination parameters
 */
export function extractPaginationFromQuery(query: Record<string, unknown>): PaginationParams {
  // Default values
  const defaultLimit = 10;
  const maxLimit = 50;

  // Extract and validate limit
  let limit = defaultLimit;
  if (query.limit) {
    if (typeof query.limit === 'string') {
      const parsedLimit = parseInt(query.limit, 10);
      if (!isNaN(parsedLimit) && parsedLimit > 0) {
        limit = Math.min(parsedLimit, maxLimit);
      }
    } else if (typeof query.limit === 'number') {
      limit = Math.min(query.limit, maxLimit);
    }
  }

  // Extract and validate offset
  let offset = 0;
  if (query.offset) {
    if (typeof query.offset === 'string') {
      const parsedOffset = parseInt(query.offset, 10);
      if (!isNaN(parsedOffset) && parsedOffset >= 0) {
        offset = parsedOffset;
      }
    } else if (typeof query.offset === 'number') {
      offset = query.offset;
    }
  }

  return { limit, offset };
}
