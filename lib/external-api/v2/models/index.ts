import { AppointmentStatus } from './types';
import type {
  Address,
  ProviderInfo,
  Clinic,
  Location,
  Patient,
  User,
  Appointment,
  AppointmentType,
  AppointmentPurpose,
  PatientType,
  AvailableSlot,
} from './types';
import type { CreateAppointmentDto, UpdateAppointmentDto } from './dto';

export { AppointmentStatus };
export type {
  Address,
  ProviderInfo,
  Clinic,
  Location,
  Patient,
  User,
  Appointment,
  AppointmentType,
  AppointmentPurpose,
  PatientType,
  AvailableSlot,
  CreateAppointmentDto,
  UpdateAppointmentDto,
};

export * from './types';
export * from './dto';
export * from './pagination';
