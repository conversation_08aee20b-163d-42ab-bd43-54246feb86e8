import { AppointmentStatus } from './types';

// DTO for creating a new appointment
export interface CreateAppointmentDto {
  patientId: string;
  practitionerId?: string;
  locationId?: string;
  clinicId?: string;
  startTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  endTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  type: string;
  reason?: string;
  notes?: string;
}

// DTO for updating an existing appointment
export interface UpdateAppointmentDto {
  patientId?: string;
  practitionerId?: string;
  locationId?: string;
  clinicId?: string;
  startTime?: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  endTime?: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  status?: AppointmentStatus;
  type?: string;
  reason?: string;
  notes?: string;
}
