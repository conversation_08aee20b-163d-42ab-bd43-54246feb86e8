// Common address model
export interface Address {
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Provider information model
export interface ProviderInfo {
  provider: string; // e.g., "nextech", "otherProvider"
  externalId: string; // ID in the provider's system
}

// Clinic model
export interface Clinic {
  id: string;
  name: string;
  address: Address;
  phoneNumber: string;
  emailAddress?: string;
  website?: string;
  providerInfo: ProviderInfo;
}

// Location model
export interface Location {
  id: string;
  name: string;
  address: Address;
  clinicId: string;
  phoneNumber?: string;
  emailAddress?: string;
  providerInfo: ProviderInfo;
}

/**
 * Insurance information for a patient
 */
export interface Insurance {
  id?: string;
  companyName: string;
  memberId: string;
  groupNumber?: string;
  planName?: string;
  isPrimary: boolean;
  subscriberName?: string;
  subscriberRelationship?: string;
  effectiveDate?: string;
  expirationDate?: string;
  copay?: number;
  patientId: string;
  providerInfo?: {
    provider: string;
    externalId: string;
  };
}

export interface Identifier {
  use: string;
  system: string;
  value: string;
}

// Patient model
export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string; // ISO format: YYYY-MM-DD
  gender?: string;
  email?: string;
  phoneNumber?: string;
  address?: Address;
  providerInfo: ProviderInfo;
  notes?: string;
  insurances?: Insurance[];
  identifiers?: Identifier[];
  lastUpdated?: string; // ISO format: 2024-12-20T20:56:03.71-05:00
}

// User/Practitioner model
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phoneNumber?: string;
  role?: string;
  speciality?: string;
  clinicId?: string;
  locationId?: string;
  providerInfo: ProviderInfo;
}

// Appointment model
export interface Appointment {
  id: string;
  patientId: string;
  patientName?: string; // Patient's full name
  providerId: string;
  practitionerId?: string; // Same as providerId, for consistency with FHIR terminology
  practitionerName?: string; // Practitioner's full name
  locationId: string;
  locationName?: string; // Location's name
  clinicId: string;
  startTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  endTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  status: AppointmentStatus;
  type?: string;
  reason?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  providerInfo: ProviderInfo;
}

// Appointment status enum
export enum AppointmentStatus {
  PROPOSED = 'proposed',
  PENDING = 'pending',
  BOOKED = 'booked',
  ARRIVED = 'arrived',
  FULFILLED = 'fulfilled',
  CANCELLED = 'cancelled',
  NOSHOW = 'noshow',
}

// Appointment type model
export interface AppointmentType {
  id: string;
  name: string;
  description: string;
  duration: number; // in minutes
  color?: string;
  isActive: boolean;
  providerInfo: ProviderInfo;
}

// Available slot model
export interface AvailableSlot {
  startDateTime: string; // ISO 8601 format
  endDateTime: string; // ISO 8601 format
  practitionerId: string; // Changed from providerId
  locationId: string;
  appointmentTypeId: string;
  practitionerName: string; // Practitioner's name
  locationName: string; // Location's name
}

// AppointmentPurpose model
export interface AppointmentPurpose {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  providerInfo: ProviderInfo;
}

// PatientType model
export interface PatientType {
  id: string;
  name: string;
  description?: string;
  isActive: boolean;
  providerInfo: ProviderInfo;
}
