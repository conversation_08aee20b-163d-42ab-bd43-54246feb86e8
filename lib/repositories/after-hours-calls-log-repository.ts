import { BaseRepository } from '../database/base-repository';
import { AfterHoursCallLog } from '../../models/AfterHoursCallLog';
import { PaginatedResult } from '../database/unit-of-work';
import { mysqlService } from '../database/mysql-service';

/**
 * After Hours Calls Log Repository - handles dual-database operations for AfterHoursCallLog entities
 */
export class AfterHoursCallsLogRepository extends BaseRepository<AfterHoursCallLog> {
  protected tableName = 'after_hours_calls_log';
  protected collectionName = 'after_hours_calls_log';

  constructor() {
    super('after_hours_calls_log', 'after_hours_calls_log', {
      id: 'id',
      afterHoursCallId: 'after_hours_call_id',
      viewedBy: 'viewed_by',
      contactedBy: 'contacted_by',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Schema mapping for MySQL columns
   */
  protected getSchemaMapping(): Record<string, string> {
    return {
      id: 'id',
      afterHoursCallId: 'after_hours_call_id',
      viewedBy: 'viewed_by',
      contactedBy: 'contacted_by',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };
  }

  /**
   * Entity validation
   */
  public validateEntity(entity: Partial<AfterHoursCallLog>): boolean {
    const errors: string[] = [];

    if (
      entity.afterHoursCallId !== undefined &&
      (!entity.afterHoursCallId || entity.afterHoursCallId.trim().length === 0)
    ) {
      errors.push('After hours call ID is required');
    }

    if (errors.length > 0) {
      throw new Error(`AfterHoursCallLog validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Convert MySQL row to AfterHoursCallLog entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): AfterHoursCallLog {
    let createdAt = new Date();
    if (row.created_at) {
      if (row.created_at instanceof Date) {
        createdAt = row.created_at;
      } else if (typeof row.created_at === 'string') {
        createdAt = new Date(row.created_at);
      }
    }

    let updatedAt = new Date();
    if (row.updated_at) {
      if (row.updated_at instanceof Date) {
        updatedAt = row.updated_at;
      } else if (typeof row.updated_at === 'string') {
        updatedAt = new Date(row.updated_at);
      }
    }

    return {
      id: row.id as string,
      afterHoursCallId: row.after_hours_call_id as string,
      viewedBy: (row.viewed_by as string) || undefined,
      contactedBy: (row.contacted_by as string) || undefined,
      createdAt,
      updatedAt,
    };
  }

  /**
   * Convert AfterHoursCallLog entity to MySQL data
   */
  protected entityToMysqlData(entity: AfterHoursCallLog): Record<string, unknown> {
    return {
      id: entity.id,
      after_hours_call_id: entity.afterHoursCallId,
      viewed_by: entity.viewedBy || null,
      contacted_by: entity.contactedBy || null,
      created_at: entity.createdAt || new Date(),
      updated_at: entity.updatedAt || new Date(),
    };
  }

  /**
   * After Hours Call Log-specific query methods
   */

  /**
   * Find logs by after hours call ID
   */
  async findByAfterHoursCallId(
    afterHoursCallId: string,
  ): Promise<PaginatedResult<AfterHoursCallLog>> {
    return this.findMany({
      where: { afterHoursCallId },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find logs by user who viewed
   */
  async findByViewedBy(viewedBy: string): Promise<PaginatedResult<AfterHoursCallLog>> {
    return this.findMany({
      where: { viewedBy },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find logs by user who contacted
   */
  async findByContactedBy(contactedBy: string): Promise<PaginatedResult<AfterHoursCallLog>> {
    return this.findMany({
      where: { contactedBy },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find logs that have been viewed but not contacted
   */
  async findViewedNotContacted(): Promise<PaginatedResult<AfterHoursCallLog>> {
    const result = await this.findMany({
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });

    const filteredItems = result.items.filter(log => log.viewedBy && !log.contactedBy);

    return {
      items: filteredItems,
      total: filteredItems.length,
      hasMore: false,
    };
  }

  /**
   * Find logs that have been contacted
   */
  async findContacted(): Promise<PaginatedResult<AfterHoursCallLog>> {
    const result = await this.findMany({
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });

    const filteredItems = result.items.filter(log => log.contactedBy);

    return {
      items: filteredItems,
      total: filteredItems.length,
      hasMore: false,
    };
  }

  /**
   * Find after-hours call logs by call_id with user names
   * Returns logs with doctor names for UI display
   */
  async findByCallIdWithUserNames(callId: string): Promise<
    Array<{
      id: string;
      afterHoursCallId: string;
      viewedBy: string | undefined;
      contactedBy: string | undefined;
      createdAt: Date;
      updatedAt: Date;
      viewedByUserName: string | undefined;
      contactedByUserName: string | undefined;
    }>
  > {
    try {
      // Custom SQL query to join after_hours_calls_log with after_hours_calls and users
      const sql = `
        SELECT 
          ahl.id,
          ahl.after_hours_call_id,
          ahl.viewed_by,
          ahl.contacted_by,
          ahl.created_at,
          ahl.updated_at,
          viewed_user.name as viewed_by_user_name,
          contacted_user.name as contacted_by_user_name
        FROM after_hours_calls_log ahl
        INNER JOIN after_hours_calls ahc ON ahl.after_hours_call_id = ahc.id
        LEFT JOIN users viewed_user ON ahl.viewed_by = viewed_user.id
        LEFT JOIN users contacted_user ON ahl.contacted_by = contacted_user.id
        WHERE ahc.call_id = ?
        ORDER BY ahl.created_at ASC
      `;

      const results = await mysqlService.query<Record<string, unknown>>(sql, [callId]);

      return results.map(row => {
        let createdAt = new Date();
        if (row.created_at) {
          if (row.created_at instanceof Date) {
            createdAt = row.created_at;
          } else if (typeof row.created_at === 'string') {
            createdAt = new Date(row.created_at);
          }
        }

        let updatedAt = new Date();
        if (row.updated_at) {
          if (row.updated_at instanceof Date) {
            updatedAt = row.updated_at;
          } else if (typeof row.updated_at === 'string') {
            updatedAt = new Date(row.updated_at);
          }
        }

        return {
          id: row.id as string,
          afterHoursCallId: row.after_hours_call_id as string,
          viewedBy: (row.viewed_by as string) || undefined,
          contactedBy: (row.contacted_by as string) || undefined,
          createdAt,
          updatedAt,
          viewedByUserName: (row.viewed_by_user_name as string) || undefined,
          contactedByUserName: (row.contacted_by_user_name as string) || undefined,
        };
      });
    } catch (error) {
      throw new Error(
        `Failed to find after-hours call logs by call ID: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get after-hours call log statistics
   */
  async getAfterHoursCallLogStats(): Promise<{
    totalLogs: number;
    viewedLogs: number;
    contactedLogs: number;
    logsByUser: Record<string, { viewed: number; contacted: number }>;
  }> {
    const result = await this.findMany({});

    const stats = {
      totalLogs: result.items.length,
      viewedLogs: 0,
      contactedLogs: 0,
      logsByUser: {} as Record<string, { viewed: number; contacted: number }>,
    };

    result.items.forEach(log => {
      // Count viewed/contacted logs
      if (log.viewedBy) {
        stats.viewedLogs++;
      }
      if (log.contactedBy) {
        stats.contactedLogs++;
      }

      // Count by user
      if (log.viewedBy) {
        if (!stats.logsByUser[log.viewedBy]) {
          stats.logsByUser[log.viewedBy] = { viewed: 0, contacted: 0 };
        }
        stats.logsByUser[log.viewedBy].viewed++;
      }

      if (log.contactedBy) {
        if (!stats.logsByUser[log.contactedBy]) {
          stats.logsByUser[log.contactedBy] = { viewed: 0, contacted: 0 };
        }
        stats.logsByUser[log.contactedBy].contacted++;
      }
    });

    return stats;
  }
}
