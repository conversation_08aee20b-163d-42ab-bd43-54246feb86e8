import { BaseRepository } from '../database/base-repository';
import { Appointment } from '../../models/Appointment';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Appointment entity repository with dual-database support
 * Handles MySQL-first reads with Firestore fallback
 */
export class AppointmentsRepository extends BaseRepository<Appointment> {
  constructor() {
    super('appointments', 'appointments', {
      id: 'id',
      userId: 'user_id',
      clientId: 'client_id',
      clientName: 'client_name',
      slotId: 'slot_id',
      callId: 'call_id',
      date: 'appointment_date',
      time: 'appointment_time',
      status: 'status',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: Appointment): Record<string, unknown> {
    return {
      userId: entity.userId,
      clientId: entity.clientId,
      clientName: entity.clientName,
      slotId: entity.slotId,
      callId: entity.callId,
      date: entity.date, // Keep as ISO string for Firestore
      time: entity.time,
      status: entity.status,
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): Appointment {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      userId: data.userId as string,
      clientId: data.clientId as string,
      clientName: data.clientName as string,
      slotId: data.slotId as string,
      callId: data.callId as string,
      date: data.date as string, // ISO format date string
      time: data.time as string,
      status: data.status as 'active' | 'completed' | 'cancelled',
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): Appointment {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    // Support both legacy `id` column or `uuid` column used in some schemas
    const rowId = (row.id || (row as Record<string, unknown>).uuid) as string;

    return {
      id: rowId,
      userId: row.user_id as string,
      clientId: row.client_id as string,
      clientName: row.client_name as string,
      slotId: row.slot_id as string,
      callId: row.call_id as string,
      date: row.appointment_date as string, // Keep as ISO string
      time: row.appointment_time as string,
      status: row.status as 'active' | 'completed' | 'cancelled',
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Custom validation for Appointment entities
   */
  protected customValidateEntity(entity: Partial<Appointment>): boolean {
    const errors: string[] = [];

    if (!entity.userId?.trim()) {
      errors.push('User ID is required');
    }

    if (!entity.clientId?.trim()) {
      errors.push('Client ID is required');
    }

    if (!entity.clientName?.trim()) {
      errors.push('Client name is required');
    }

    if (!entity.slotId?.trim()) {
      errors.push('Slot ID is required');
    }

    if (!entity.callId?.trim()) {
      errors.push('Call ID is required');
    }

    if (!entity.date?.trim()) {
      errors.push('Appointment date is required');
    }

    if (!entity.time?.trim()) {
      errors.push('Appointment time is required');
    }

    if (!entity.status || !['active', 'completed', 'cancelled'].includes(entity.status)) {
      errors.push('Valid status is required (active, completed, or cancelled)');
    }

    // Validate date format (ISO: YYYY-MM-DD)
    if (entity.date && !entity.date.match(/^\d{4}-\d{2}-\d{2}$/)) {
      errors.push('Date must be in ISO format (YYYY-MM-DD)');
    }

    // Validate time format (HH:MM)
    if (entity.time && !entity.time.match(/^\d{2}:\d{2}$/)) {
      errors.push('Time must be in format HH:MM');
    }

    if (errors.length > 0) {
      throw new Error(`Appointment validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Find appointments by user ID (staff member)
   */
  async findByUserId(userId: string, limit = 50): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { userId },
      limit,
      orderBy: [
        { field: 'date', direction: 'desc' },
        { field: 'time', direction: 'desc' },
      ],
    });
    return result.items;
  }

  /**
   * Find appointments by client ID
   */
  async findByClientId(clientId: string, limit = 50): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { clientId },
      limit,
      orderBy: [
        { field: 'date', direction: 'desc' },
        { field: 'time', direction: 'desc' },
      ],
    });
    return result.items;
  }

  /**
   * Find appointments by call ID
   */
  async findByCallId(callId: string): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { callId },
      limit: 10,
      orderBy: [{ field: 'date', direction: 'desc' }],
    });
    return result.items;
  }

  /**
   * Find appointments by status
   */
  async findByStatus(
    status: 'active' | 'completed' | 'cancelled',
    limit = 100,
  ): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { status },
      limit,
      orderBy: [
        { field: 'date', direction: 'desc' },
        { field: 'time', direction: 'desc' },
      ],
    });
    return result.items;
  }

  /**
   * Find appointments for a specific date
   */
  async findByDate(date: string, limit = 100): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { date },
      limit,
      orderBy: [{ field: 'time', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find active appointments for a user on a specific date
   */
  async findActiveByUserAndDate(userId: string, date: string): Promise<Appointment[]> {
    const result = await this.findMany({
      where: { userId, date, status: 'active' },
      limit: 50,
      orderBy: [{ field: 'time', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find upcoming appointments (active appointments for today and future)
   */
  async findUpcoming(limit = 100): Promise<Appointment[]> {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    // For now, get all active appointments and filter in memory
    // TODO: Implement proper date comparison queries when MySQL support is available
    const result = await this.findMany({
      where: { status: 'active' },
      limit: limit * 2, // Get more to filter
      orderBy: [
        { field: 'date', direction: 'asc' },
        { field: 'time', direction: 'asc' },
      ],
    });

    return result.items.filter(appointment => appointment.date >= today).slice(0, limit);
  }

  /**
   * Update appointment status
   */
  async updateStatus(
    appointmentId: string,
    status: 'active' | 'completed' | 'cancelled',
  ): Promise<Appointment> {
    return this.update(appointmentId, { status });
  }

  /**
   * Get appointment statistics
   */
  async getAppointmentStats(): Promise<{
    totalActive: number;
    totalCompleted: number;
    totalCancelled: number;
    upcomingToday: number;
  }> {
    const today = new Date().toISOString().split('T')[0];

    // Get all appointments to calculate stats
    const [active, completed, cancelled] = await Promise.all([
      this.findByStatus('active', 1000),
      this.findByStatus('completed', 1000),
      this.findByStatus('cancelled', 1000),
    ]);

    const upcomingToday = active.filter(app => app.date === today).length;

    return {
      totalActive: active.length,
      totalCompleted: completed.length,
      totalCancelled: cancelled.length,
      upcomingToday,
    };
  }

  /**
   * Find appointments that need follow-up (completed appointments without follow-up calls)
   */
  async findNeedingFollowUp(limit = 50): Promise<Appointment[]> {
    // For now, just return recent completed appointments
    // TODO: Implement proper logic to check for follow-up calls when call repository is integrated
    const result = await this.findMany({
      where: { status: 'completed' },
      limit,
      orderBy: [{ field: 'date', direction: 'desc' }],
    });
    return result.items;
  }
}
