import { BaseRepository } from '../database/base-repository';
import { AfterHoursCall } from '../../models/AfterHoursCall';
import { PaginatedResult } from '../database/unit-of-work';

/**
 * After Hours Calls Repository - handles dual-database operations for AfterHoursCall entities
 */
export class AfterHoursCallsRepository extends BaseRepository<AfterHoursCall> {
  protected tableName = 'after_hours_calls';
  protected collectionName = 'after_hours_calls';

  constructor() {
    super('after_hours_calls', 'after_hours_calls', {
      id: 'id',
      patientBirthday: 'patient_birthday',
      patientFullName: 'patient_full_name',
      patientPhoneNumber: 'patient_phone_number',
      callReason: 'call_reason',
      callId: 'call_id',
      sessionId: 'session_id',
      doctorId: 'doctor_id',
      backupDoctorId: 'backup_doctor_id',
      doctorPhone: 'doctor_phone',
      backupDoctorPhone: 'backup_doctor_phone',
      isReviewedByDoctor: 'is_reviewed_by_doctor',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Schema mapping for MySQL columns
   */
  protected getSchemaMapping(): Record<string, string> {
    return {
      id: 'id',
      patientBirthday: 'patient_birthday',
      patientFullName: 'patient_full_name',
      patientPhoneNumber: 'patient_phone_number',
      callReason: 'call_reason',
      callId: 'call_id',
      sessionId: 'session_id',
      doctorId: 'doctor_id',
      backupDoctorId: 'backup_doctor_id',
      doctorPhone: 'doctor_phone',
      backupDoctorPhone: 'backup_doctor_phone',
      isReviewedByDoctor: 'is_reviewed_by_doctor',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    };
  }

  /**
   * Entity validation
   */
  public validateEntity(entity: Partial<AfterHoursCall>): boolean {
    const errors: string[] = [];

    if (entity.patientBirthday !== undefined && !entity.patientBirthday) {
      errors.push('Patient birthday is required');
    }

    if (
      entity.patientFullName !== undefined &&
      (!entity.patientFullName || entity.patientFullName.trim().length === 0)
    ) {
      errors.push('Patient full name is required');
    }

    if (
      entity.patientPhoneNumber !== undefined &&
      (!entity.patientPhoneNumber || entity.patientPhoneNumber.trim().length === 0)
    ) {
      errors.push('Patient phone number is required');
    }

    if (
      entity.callReason !== undefined &&
      (!entity.callReason || entity.callReason.trim().length === 0)
    ) {
      errors.push('Call reason is required');
    }

    if (entity.callId !== undefined && (!entity.callId || entity.callId.trim().length === 0)) {
      errors.push('Call ID is required');
    }

    if (errors.length > 0) {
      throw new Error(`AfterHoursCall validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Convert MySQL row to AfterHoursCall entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): AfterHoursCall {
    // Handle date conversion properly
    let patientBirthday = new Date();
    if (row.patient_birthday) {
      if (row.patient_birthday instanceof Date) {
        patientBirthday = row.patient_birthday;
      } else if (typeof row.patient_birthday === 'string') {
        patientBirthday = new Date(row.patient_birthday);
      }
    }

    let createdAt = new Date();
    if (row.created_at) {
      if (row.created_at instanceof Date) {
        createdAt = row.created_at;
      } else if (typeof row.created_at === 'string') {
        createdAt = new Date(row.created_at);
      }
    }

    let updatedAt = new Date();
    if (row.updated_at) {
      if (row.updated_at instanceof Date) {
        updatedAt = row.updated_at;
      } else if (typeof row.updated_at === 'string') {
        updatedAt = new Date(row.updated_at);
      }
    }

    return {
      id: String(row.id), // Convert MySQL auto-increment number to string
      patientBirthday,
      patientFullName: row.patient_full_name as string,
      patientPhoneNumber: row.patient_phone_number as string,
      callReason: row.call_reason as string,
      callId: row.call_id as string,
      sessionId: (row.session_id as string) || undefined,
      doctorId: (row.doctor_id as string) || undefined,
      backupDoctorId: (row.backup_doctor_id as string) || undefined,
      doctorPhone: (row.doctor_phone as string) || undefined,
      backupDoctorPhone: (row.backup_doctor_phone as string) || undefined,
      isReviewedByDoctor: Boolean(row.is_reviewed_by_doctor),
      createdAt,
      updatedAt,
    };
  }

  /**
   * Convert AfterHoursCall entity to MySQL data
   */
  protected entityToMysqlData(entity: AfterHoursCall): Record<string, unknown> {
    return {
      // Don't include id for new records (MySQL auto-increment will handle it)
      // For updates, the id will be handled by the update method
      patient_birthday: entity.patientBirthday,
      patient_full_name: entity.patientFullName,
      patient_phone_number: entity.patientPhoneNumber,
      call_reason: entity.callReason,
      call_id: entity.callId,
      session_id: entity.sessionId || null,
      doctor_id: entity.doctorId || null,
      backup_doctor_id: entity.backupDoctorId || null,
      doctor_phone: entity.doctorPhone || null,
      backup_doctor_phone: entity.backupDoctorPhone || null,
      is_reviewed_by_doctor: entity.isReviewedByDoctor || false,
      created_at: entity.createdAt || new Date(),
      updated_at: entity.updatedAt || new Date(),
    };
  }

  /**
   * After Hours Call-specific query methods
   */

  /**
   * Find after-hours calls by call ID
   */
  async findByCallId(callId: string): Promise<PaginatedResult<AfterHoursCall>> {
    return this.findMany({
      where: { callId },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find after-hours calls by session ID
   */
  async findBySessionId(sessionId: string): Promise<PaginatedResult<AfterHoursCall>> {
    return this.findMany({
      where: { sessionId },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find after-hours calls by doctor ID
   */
  async findByDoctorId(doctorId: string): Promise<PaginatedResult<AfterHoursCall>> {
    return this.findMany({
      where: { doctorId },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find unreviewed after-hours calls
   */
  async findUnreviewed(): Promise<PaginatedResult<AfterHoursCall>> {
    return this.findMany({
      where: { isReviewedByDoctor: false },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Find reviewed after-hours calls
   */
  async findReviewed(): Promise<PaginatedResult<AfterHoursCall>> {
    return this.findMany({
      where: { isReviewedByDoctor: true },
      orderBy: [{ field: 'createdAt', direction: 'desc' }],
    });
  }

  /**
   * Get after-hours call statistics
   */
  async getAfterHoursCallStats(): Promise<{
    totalCalls: number;
    reviewedCalls: number;
    unreviewedCalls: number;
    callsByDoctor: Record<string, number>;
  }> {
    const result = await this.findMany({});

    const stats = {
      totalCalls: result.items.length,
      reviewedCalls: 0,
      unreviewedCalls: 0,
      callsByDoctor: {} as Record<string, number>,
    };

    result.items.forEach(call => {
      // Count reviewed/unreviewed calls
      if (call.isReviewedByDoctor) {
        stats.reviewedCalls++;
      } else {
        stats.unreviewedCalls++;
      }

      // Count by doctor
      if (call.doctorId) {
        stats.callsByDoctor[call.doctorId] = (stats.callsByDoctor[call.doctorId] || 0) + 1;
      }
    });

    return stats;
  }
}
