import { BaseRepository } from '../database/base-repository';
import { AppointmentReference } from '../models/appointment-reference';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Appointment reference repository with dual-database support
 * Handles MySQL-first reads with Firestore fallback
 */
export class AppointmentReferencesRepository extends BaseRepository<AppointmentReference> {
  constructor() {
    super('appointmentReferences', 'appointment_references', {
      id: 'id',
      provider: 'provider',
      externalId: 'external_id',
      providerId: 'provider_id',
      patientId: 'patient_id',
      patientName: 'patient_name',
      practitionerId: 'practitioner_id',
      practitionerName: 'practitioner_name',
      locationId: 'location_id',
      locationName: 'location_name',
      startTime: 'start_time',
      endTime: 'end_time',
      type: 'type',
      status: 'status',
      reason: 'reason',
      notes: 'notes',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: AppointmentReference): Record<string, unknown> {
    return {
      provider: entity.provider,
      externalId: entity.externalId,
      providerId: entity.providerId,
      patientId: entity.patientId,
      patientName: entity.patientName,
      practitionerId: entity.practitionerId,
      practitionerName: entity.practitionerName,
      locationId: entity.locationId,
      locationName: entity.locationName,
      startTime: entity.startTime,
      endTime: entity.endTime,
      type: entity.type,
      status: entity.status,
      reason: entity.reason,
      notes: entity.notes,
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): AppointmentReference {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      provider: data.provider as string,
      externalId: data.externalId as string,
      providerId: data.providerId as string,
      patientId: data.patientId as string,
      patientName: data.patientName as string,
      practitionerId: data.practitionerId as string,
      practitionerName: data.practitionerName as string,
      locationId: data.locationId as string,
      locationName: data.locationName as string,
      startTime: data.startTime as string,
      endTime: data.endTime as string,
      type: data.type as string,
      status: data.status as string,
      reason: data.reason as string,
      notes: data.notes as string,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): AppointmentReference {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    // Support both legacy `id` column or `uuid` column used in some schemas
    const rowId = (row.uuid || row.id) as string;

    return {
      id: rowId,
      provider: row.provider as string,
      externalId: row.external_id as string,
      providerId: row.provider_id as string,
      patientId: row.patient_id as string,
      patientName: row.patient_name as string,
      practitionerId: row.practitioner_id as string,
      practitionerName: row.practitioner_name as string,
      locationId: row.location_id as string,
      locationName: row.location_name as string,
      startTime: row.start_time as string,
      endTime: row.end_time as string,
      type: row.type as string,
      status: row.status as string,
      reason: row.reason as string,
      notes: row.notes as string,
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Find appointment reference by provider and external ID
   */
  async findByExternalId(
    provider: string,
    externalId: string,
  ): Promise<AppointmentReference | null> {
    const result = await this.findMany({
      where: { provider, externalId },
      limit: 1,
    });

    return result.items.length > 0 ? result.items[0] : null;
  }

  /**
   * Find appointment reference by provider and provider ID (backward compatibility)
   */
  async findByProviderId(
    provider: string,
    providerId: string,
  ): Promise<AppointmentReference | null> {
    const result = await this.findMany({
      where: { provider, providerId },
      limit: 1,
    });

    return result.items.length > 0 ? result.items[0] : null;
  }

  /**
   * Find appointment references by patient ID
   */
  async findByPatientId(provider: string, patientId: string): Promise<AppointmentReference[]> {
    const result = await this.findMany({
      where: { provider, patientId },
    });

    return result.items;
  }
}
