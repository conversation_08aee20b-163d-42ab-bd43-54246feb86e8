// Repository exports
export { AppointmentsRepository } from './appointments-repository';
export { LocationsRepository } from './locations-repository';
export { ClientsRepository } from './clients-repository';
export { CallsRepository } from './calls-repository';
export { UsersRepository } from './users-repository';
export { OnCallSchedulesRepository } from './on-call-schedules-repository';
export { StaffRepository } from './staff-repository';
export { PracticesRepository } from './practices-repository';
export { OnCallNotificationsRepository } from './on-call-notifications-repository';
export { PatientsRepository } from './patients-repository';
export { AfterHoursCallsRepository } from './after-hours-calls-repository';
export { AfterHoursCallsLogRepository } from './after-hours-calls-log-repository';

// Repository instances (singletons)
import { AppointmentsRepository } from './appointments-repository';
import { LocationsRepository } from './locations-repository';
import { ClientsRepository } from './clients-repository';
import { CallsRepository } from './calls-repository';
import { UsersRepository } from './users-repository';
import { mysqlService } from '../database/mysql-service';
import { OnCallSchedulesRepository } from './on-call-schedules-repository';
import { StaffRepository } from './staff-repository';
import { PracticesRepository } from './practices-repository';
import { OnCallNotificationsRepository } from './on-call-notifications-repository';
import { PatientsRepository } from './patients-repository';
import { AppointmentReferencesRepository } from './appointment-references-repository';
import { AfterHoursCallsRepository } from './after-hours-calls-repository';
import { AfterHoursCallsLogRepository } from './after-hours-calls-log-repository';

/**
 * Repository manager for centralized access to all repositories
 */
export class RepositoryManager {
  private static _instance: RepositoryManager;

  // Repository instances
  private _appointments: AppointmentsRepository;
  private _locations: LocationsRepository;
  private _clients: ClientsRepository;
  private _calls: CallsRepository;
  private _afterHoursCalls: AfterHoursCallsRepository;
  private _afterHoursCallsLog: AfterHoursCallsLogRepository;
  private _users: UsersRepository;
  private _onCallSchedules: OnCallSchedulesRepository;
  private _staff: StaffRepository;
  private _practices: PracticesRepository;
  private _onCallNotifications: OnCallNotificationsRepository;
  private _patients: PatientsRepository;
  private _appointmentReferences: AppointmentReferencesRepository;
  private _initialized: boolean = false;

  private constructor() {
    this._appointments = new AppointmentsRepository();
    this._locations = new LocationsRepository();
    this._clients = new ClientsRepository();
    this._calls = new CallsRepository();
    this._afterHoursCalls = new AfterHoursCallsRepository();
    this._afterHoursCallsLog = new AfterHoursCallsLogRepository();
    this._users = new UsersRepository();
    this._onCallSchedules = new OnCallSchedulesRepository();
    this._staff = new StaffRepository();
    this._practices = new PracticesRepository();
    this._onCallNotifications = new OnCallNotificationsRepository();
    this._patients = new PatientsRepository();
    this._appointmentReferences = new AppointmentReferencesRepository();
  }

  /**
   * Initialize the database connections
   */
  async initialize(): Promise<void> {
    if (this._initialized) {
      return; // Already initialized
    }

    try {
      await mysqlService.initialize();
      this._initialized = true;
      console.log('✅ RepositoryManager initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize RepositoryManager:', error);
      throw error;
    }
  }

  /**
   * Get singleton instance
   */
  static getInstance(): RepositoryManager {
    if (!RepositoryManager._instance) {
      RepositoryManager._instance = new RepositoryManager();
    }
    return RepositoryManager._instance;
  }

  /**
   * Get appointments repository
   */
  get appointments(): AppointmentsRepository {
    return this._appointments;
  }

  /**
   * Get locations repository
   */
  get locations(): LocationsRepository {
    return this._locations;
  }

  /**
   * Get clients repository
   */
  get clients(): ClientsRepository {
    return this._clients;
  }

  /**
   * Get calls repository
   */
  get calls(): CallsRepository {
    return this._calls;
  }

  /**
   * Get after-hours calls repository
   */
  get afterHoursCalls(): AfterHoursCallsRepository {
    return this._afterHoursCalls;
  }

  /**
   * Get after-hours calls log repository
  /**
   * Get after-hours calls log repository
   */
  get afterHoursCallsLog(): AfterHoursCallsLogRepository {
    return this._afterHoursCallsLog;
  }

  /**
   * Get on-call schedules repository
   */
  get onCallSchedules(): OnCallSchedulesRepository {
    return this._onCallSchedules;
  }

  /**
   * Get users repository
   */
  get users(): UsersRepository {
    return this._users;
  }

  /**
   * Get staff repository
   */
  get staff(): StaffRepository {
    return this._staff;
  }

  /**
   * Get practices repository
   */
  get practices(): PracticesRepository {
    return this._practices;
  }

  /**
   * Get on-call notifications repository
   */
  get onCallNotifications(): OnCallNotificationsRepository {
    return this._onCallNotifications;
  }

  /**
   * Get patients repository
   */
  get patients(): PatientsRepository {
    return this._patients;
  }

  /**
   * Get appointment references repository
   */
  get appointmentReferences(): AppointmentReferencesRepository {
    return this._appointmentReferences;
  }

  /**
   * Reset all repository instances (useful for testing)
   */
  reset(): void {
    this._appointments = new AppointmentsRepository();
    this._locations = new LocationsRepository();
    this._clients = new ClientsRepository();
    this._calls = new CallsRepository();
    this._afterHoursCalls = new AfterHoursCallsRepository();
    this._afterHoursCallsLog = new AfterHoursCallsLogRepository();
    this._users = new UsersRepository();
    this._onCallSchedules = new OnCallSchedulesRepository();
    this._staff = new StaffRepository();
    this._practices = new PracticesRepository();
    this._onCallNotifications = new OnCallNotificationsRepository();
    this._patients = new PatientsRepository();
    this._appointmentReferences = new AppointmentReferencesRepository();
  }

  /**
   * Health check for all repositories
   */
  async healthCheck(): Promise<{
    appointments: boolean;
    locations: boolean;
    overall: boolean;
  }> {
    const results = {
      appointments: false,
      locations: false,
      overall: false,
    };

    try {
      await this._appointments.healthCheck();
      results.appointments = true;
    } catch (error) {
      console.error('Appointments repository health check failed:', error);
    }

    try {
      await this._locations.healthCheck();
      results.locations = true;
    } catch (error) {
      console.error('Locations repository health check failed:', error);
    }

    results.overall = results.appointments && results.locations;

    return results;
  }

  /**
   * Get repository statistics
   */
  async getStats(): Promise<{
    appointments: {
      total: number;
      byStatus: Record<string, number>;
    };
    locations: {
      total: number;
      active: number;
      inactive: number;
    };
  }> {
    const [appointmentStats, locationStats] = await Promise.all([
      this._appointments.getAppointmentStats(),
      this._locations.getLocationStats(),
    ]);

    return {
      appointments: {
        total:
          appointmentStats.totalActive +
          appointmentStats.totalCompleted +
          appointmentStats.totalCancelled,
        byStatus: {
          active: appointmentStats.totalActive,
          completed: appointmentStats.totalCompleted,
          cancelled: appointmentStats.totalCancelled,
        },
      },
      locations: {
        total: locationStats.totalLocations,
        active: locationStats.activeLocations,
        inactive: locationStats.inactiveLocations,
      },
    };
  }
}

// Convenience function to get repository manager instance
export const getRepositories = (): RepositoryManager => {
  return RepositoryManager.getInstance();
};

// Individual repository getters for convenience
export const getAppointmentsRepository = (): AppointmentsRepository => {
  return RepositoryManager.getInstance().appointments;
};

export const getLocationsRepository = (): LocationsRepository => {
  return RepositoryManager.getInstance().locations;
};

export const getClientsRepository = (): ClientsRepository => {
  return RepositoryManager.getInstance().clients;
};

export const getCallsRepository = (): CallsRepository => {
  return getRepositories().calls;
};

export const getUsersRepository = (): UsersRepository => {
  return RepositoryManager.getInstance().users;
};

export const getOnCallSchedulesRepository = (): OnCallSchedulesRepository => {
  return getRepositories().onCallSchedules;
};

export const getStaffRepository = (): StaffRepository => {
  return getRepositories().staff;
};

export const getPracticesRepository = (): PracticesRepository => {
  return getRepositories().practices;
};

export const getOnCallNotificationsRepository = (): OnCallNotificationsRepository => {
  return getRepositories().onCallNotifications;
};

export const getPatientsRepository = (): PatientsRepository => {
  return getRepositories().patients;
};

export const getAppointmentReferencesRepository = (): AppointmentReferencesRepository => {
  return getRepositories().appointmentReferences;
};

export const getAfterHoursCallsLogRepository = (): AfterHoursCallsLogRepository => {
  return getRepositories().afterHoursCallsLog;
};
