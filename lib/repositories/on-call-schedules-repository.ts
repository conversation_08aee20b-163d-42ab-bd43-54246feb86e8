import { BaseRepository } from '../database/base-repository';
import { OnCallSchedule } from '../../models/OnCallSchedule';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Repository for on-call schedules with dual-database support (MySQL first, Firestore fallback)
 */
export class OnCallSchedulesRepository extends BaseRepository<OnCallSchedule> {
  constructor() {
    super('on-call-schedules', 'on_call_schedules', {
      id: 'id',
      doctorId: 'doctor_id',
      doctorName: 'doctor_name',
      doctorPhone: 'doctor_phone',
      locationId: 'location_id',
      clinicId: 'clinic_id',
      date: 'schedule_date',
      startTime: 'start_time',
      endTime: 'end_time',
      isActive: 'is_active',
      isPrimary: 'is_primary',
      timezone: 'timezone',
      notes: 'notes',
      createdBy: 'created_by',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: OnCallSchedule): Record<string, unknown> {
    return {
      doctorId: entity.doctorId,
      doctorName: entity.doctorName,
      doctorPhone: entity.doctorPhone || '',
      locationId: entity.locationId,
      clinicId: entity.clinicId,
      date: entity.date,
      startTime: entity.startTime,
      endTime: entity.endTime,
      isActive: entity.isActive,
      isPrimary: entity.isPrimary,
      timezone: entity.timezone,
      notes: entity.notes || '',
      createdBy: entity.createdBy,
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): OnCallSchedule {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      doctorId: data.doctorId as string,
      doctorName: data.doctorName as string,
      doctorPhone: data.doctorPhone as string | undefined,
      locationId: data.locationId as string,
      clinicId: data.clinicId as number,
      date: data.date as string,
      startTime: data.startTime as string,
      endTime: data.endTime as string,
      isActive: Boolean(data.isActive),
      isPrimary: Boolean(data.isPrimary),
      timezone: data.timezone as string,
      notes: (data.notes as string) || '',
      createdBy: data.createdBy as string,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): OnCallSchedule {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    return {
      id: row.id as string,
      doctorId: row.doctor_id as string,
      doctorName: row.doctor_name as string,
      doctorPhone: (row.doctor_phone as string) || undefined,
      locationId: row.location_id as string,
      clinicId: row.clinic_id as number,
      date: row.schedule_date as string,
      startTime: row.start_time as string,
      endTime: row.end_time as string,
      isActive: Boolean(row.is_active),
      isPrimary: Boolean(row.is_primary),
      timezone: row.timezone as string,
      notes: (row.notes as string) || '',
      createdBy: row.created_by as string,
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Custom validation for schedules
   */
  protected customValidateEntity(entity: Partial<OnCallSchedule>): boolean {
    const errors: string[] = [];

    if (!entity.doctorId?.trim()) {
      errors.push('Doctor ID is required');
    }

    if (!entity.doctorName?.trim()) {
      errors.push('Doctor name is required');
    }

    if (!entity.locationId?.trim()) {
      errors.push('Location ID is required');
    }

    if (!entity.clinicId || entity.clinicId <= 0) {
      errors.push('Valid clinic ID is required');
    }

    if (!entity.date?.match(/^\d{4}-\d{2}-\d{2}$/)) {
      errors.push('Date must be in YYYY-MM-DD format');
    }

    if (!entity.startTime?.match(/^\d{2}:\d{2}$/)) {
      errors.push('Start time must be in HH:MM format');
    }

    if (!entity.endTime?.match(/^\d{2}:\d{2}$/)) {
      errors.push('End time must be in HH:MM format');
    }

    if (errors.length > 0) {
      throw new Error(`On-call schedule validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Find active current/future schedules by location (and clinic)
   */
  async findActiveByLocation(locationId: string, clinicId: number): Promise<OnCallSchedule[]> {
    // First, deactivate any past schedules
    await this.deactivatePastSchedules();

    // Get current/future schedules only
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    const result = await this.findMany({
      where: { locationId, clinicId, isActive: true },
      orderBy: [{ field: 'date', direction: 'asc' }],
      limit: 365, // reasonable upper-bound
    });

    // Additional filter to ensure we only return current/future schedules
    const currentAndFutureSchedules = result.items.filter(schedule => {
      return schedule.date >= today;
    });

    return currentAndFutureSchedules;
  }

  /**
   * Deactivate schedules that are in the past (based on date and end time)
   */
  async deactivatePastSchedules(): Promise<void> {
    try {
      const now = new Date();
      const today = now.toISOString().split('T')[0]; // YYYY-MM-DD
      const currentTime = now.toTimeString().split(' ')[0].slice(0, 5); // HH:MM

      // Find all active schedules
      const allActiveSchedules = await this.findMany({
        where: { isActive: true },
        limit: 1000, // Get a reasonable batch
      });

      const schedulesToDeactivate = allActiveSchedules.items.filter(schedule => {
        // Deactivate if date is before today
        if (schedule.date < today) {
          return true;
        }

        // If date is today, check if end time has passed
        if (schedule.date === today && schedule.endTime < currentTime) {
          return true;
        }

        return false;
      });

      // Batch update to deactivate past schedules
      for (const schedule of schedulesToDeactivate) {
        await this.update(schedule.id, {
          isActive: false,
          updatedAt: new Date(),
        });
      }

      if (schedulesToDeactivate.length > 0) {
        console.log(`Deactivated ${schedulesToDeactivate.length} past on-call schedules`);
      }
    } catch (error) {
      console.error('Error deactivating past schedules:', error);
      // Don't throw - this should not break the main query
    }
  }
}
