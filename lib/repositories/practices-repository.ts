import { BaseRepository } from '../database/base-repository';
import { Practice } from '../../models/Practice';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Practices repository – MySQL-first with Firestore fallback
 */
export class PracticesRepository extends BaseRepository<Practice> {
  constructor() {
    super('practices', 'practices', {
      id: 'uuid',
      clinicId: 'clinic_id',
      name: 'name',
      description: 'description',
      isActive: 'is_active',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: Practice): Record<string, unknown> {
    return {
      clinicId: entity.clinicId,
      name: entity.name,
      description: entity.description || null,
      isActive: entity.isActive,
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): Practice {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      clinicId: data.clinicId as number,
      name: data.name as string,
      description: (data.description as string) || undefined,
      isActive: (data.isActive as boolean) ?? true,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): Practice {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    return {
      id: row.uuid as string,
      clinicId: row.clinic_id as number,
      name: row.name as string,
      description: (row.description as string) || undefined,
      isActive: Boolean(row.is_active),
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Custom entity validation
   */
  protected customValidateEntity(entity: Partial<Practice>): boolean {
    const errors: string[] = [];

    if (!entity.clinicId || entity.clinicId <= 0) {
      errors.push('Valid clinic ID is required');
    }

    if (!entity.name?.trim()) {
      errors.push('Practice name is required');
    }

    if (errors.length > 0) {
      throw new Error(`Practice validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Find practices by clinic
   */
  async findByClinicId(clinicId: number, limit = 100): Promise<Practice[]> {
    const result = await this.findMany({
      where: { clinicId },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Search practices by name (case-insensitive)
   */
  async searchByName(name: string, clinicId?: number, limit = 20): Promise<Practice[]> {
    const all = clinicId
      ? await this.findByClinicId(clinicId, limit * 2)
      : (await this.findMany({ limit: limit * 2 })).items;
    return all.filter(p => p.name.toLowerCase().includes(name.toLowerCase())).slice(0, limit);
  }
}
