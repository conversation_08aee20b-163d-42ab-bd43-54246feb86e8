import { BaseRepository } from '../database/base-repository';
import { OnCallNotification, NotificationStatus } from '../../models/OnCallNotification';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Repository for on-call SMS notifications.
 */
export class OnCallNotificationsRepository extends BaseRepository<OnCallNotification> {
  constructor() {
    super('onCallNotifications', 'on_call_notifications', {
      id: 'id',
      scheduleId: 'schedule_id',
      callSessionId: 'call_session_id',
      doctorId: 'doctor_id',
      clinicId: 'clinic_id',
      notificationTime: 'notification_time',
      smsMessageId: 'sms_message_id',
      status: 'status',
      callType: 'call_type',
      callerPhone: 'caller_phone',
      errorMessage: 'error_message',
      retryCount: 'retry_count',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: OnCallNotification): Record<string, unknown> {
    return {
      scheduleId: entity.scheduleId,
      callSessionId: entity.callSessionId,
      doctorId: entity.doctorId,
      clinicId: entity.clinicId,
      notificationTime: admin.firestore.Timestamp.fromDate(entity.notificationTime),
      smsMessageId: entity.smsMessageId || null,
      status: entity.status,
      callType: entity.callType || null,
      callerPhone: entity.callerPhone || null,
      errorMessage: entity.errorMessage || null,
      retryCount: entity.retryCount,
      createdAt: admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): OnCallNotification {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      scheduleId: data.scheduleId as string,
      callSessionId: data.callSessionId as string,
      doctorId: data.doctorId as string,
      clinicId: data.clinicId as number,
      notificationTime: DualDatabaseUtils.timestampToDate(data.notificationTime) || new Date(),
      smsMessageId: (data.smsMessageId as string) || undefined,
      status: data.status as NotificationStatus,
      callType: (data.callType as string) || undefined,
      callerPhone: (data.callerPhone as string) || undefined,
      errorMessage: (data.errorMessage as string) || undefined,
      retryCount: (data.retryCount as number) ?? 0,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): OnCallNotification {
    const notifTime = row.notification_time;
    const createdAt = row.created_at;

    return {
      id: row.id as string,
      scheduleId: row.schedule_id as string,
      callSessionId: row.call_session_id as string,
      doctorId: row.doctor_id as string,
      clinicId: row.clinic_id as number,
      notificationTime: notifTime instanceof Date ? notifTime : new Date(notifTime as string),
      smsMessageId: (row.sms_message_id as string) || undefined,
      status: row.status as NotificationStatus,
      callType: (row.call_type as string) || undefined,
      callerPhone: (row.caller_phone as string) || undefined,
      errorMessage: (row.error_message as string) || undefined,
      retryCount: (row.retry_count as number) ?? 0,
      createdAt: createdAt instanceof Date ? createdAt : new Date(createdAt as string),
    };
  }

  /**
   * Custom validation rules
   */
  protected customValidateEntity(entity: Partial<OnCallNotification>): boolean {
    const errors: string[] = [];

    if (!entity.scheduleId?.trim()) {
      errors.push('scheduleId is required');
    }
    if (!entity.callSessionId?.trim()) {
      errors.push('callSessionId is required');
    }
    if (!entity.doctorId?.trim()) {
      errors.push('doctorId is required');
    }
    if (!entity.clinicId) {
      errors.push('clinicId is required');
    }
    if (!entity.notificationTime) {
      errors.push('notificationTime is required');
    }
    if (!entity.status) {
      errors.push('status is required');
    }
    if (errors.length > 0) {
      throw new Error(`OnCallNotification validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Get notifications by clinic
   */
  async findByClinicId(clinicId: number, limit = 100): Promise<OnCallNotification[]> {
    const result = await this.findMany({
      where: { clinicId },
      limit,
      orderBy: [{ field: 'notificationTime', direction: 'desc' }],
    });
    return result.items;
  }
}
