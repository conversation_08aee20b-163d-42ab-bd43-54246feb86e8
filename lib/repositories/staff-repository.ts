import { UsersRepository } from './users-repository';
import { User, UserRole } from '../../models/auth';

/**
 * Staff repository – thin wrapper around UsersRepository scoped to STAFF role.
 */
export class StaffRepository extends UsersRepository {
  constructor() {
    super();
  }

  /**
   * Get all staff members for a clinic
   */
  async findByClinicId(clinicId: number, limit = 100): Promise<User[]> {
    const result = await this.findMany({
      where: { clinicId, role: UserRole.STAFF },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Get staff member by email (case-insensitive)
   */
  async findByEmail(email: string): Promise<User | null> {
    const lower = email.toLowerCase();
    const result = await this.findMany({
      where: { email: lower, role: UserRole.STAFF },
      limit: 1,
    });
    return result.items.length ? result.items[0] : null;
  }
}
