import { BaseRepository } from '../database/base-repository';
import { User, UserRole } from '../../models/auth';
import { DualDatabaseUtils } from '../database/dual-database-service';
import { mysqlService } from '../database/mysql-service';
import * as admin from 'firebase-admin';

/**
 * User entity repository with dual-database support
 * Handles MySQL-first reads with Firestore fallback
 */
export class UsersRepository extends BaseRepository<User> {
  constructor() {
    super('users', 'users', {
      id: 'id',
      email: 'email',
      phone: 'phone',
      name: 'name',
      role: 'role',
      specialty: 'specialty',
      clinicId: 'clinic_id',
      profilePicture: 'profile_picture',
      canTakeAppointments: 'can_take_appointments',
      locationIds: 'location_ids', // JSON field
      currentLocationId: 'current_location_id',
      practiceIds: 'practice_ids', // JSON field
      preferences: 'preferences', // JSON field
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: User): Record<string, unknown> {
    return {
      email: entity.email,
      phone: entity.phone || null,
      name: entity.name,
      role: entity.role,
      specialty: entity.specialty || null,
      clinicId: entity.clinicId,
      profilePicture: entity.profilePicture || null,
      canTakeAppointments: entity.canTakeAppointments,
      locationIds: entity.locationIds || [],
      currentLocationId: entity.currentLocationId || null,
      practiceIds: entity.practiceIds || [],
      createdAt:
        entity.createdAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.createdAt)
          : admin.firestore.Timestamp.now(),
      updatedAt:
        entity.updatedAt instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.updatedAt)
          : admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): User {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      email: data.email as string,
      phone: data.phone as string | undefined,
      name: data.name as string,
      role: data.role as UserRole,
      specialty: data.specialty as string | undefined,
      clinicId: data.clinicId as number | null,
      profilePicture: data.profilePicture as string | undefined,
      canTakeAppointments: data.canTakeAppointments as boolean,
      locationIds: (data.locationIds as string[]) || undefined,
      currentLocationId: data.currentLocationId as string | undefined,
      practiceIds: (data.practiceIds as string[]) || undefined,
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): User {
    const createdAt = row.created_at;
    const updatedAt = row.updated_at;

    let parsedCreatedAt: Date;
    let parsedUpdatedAt: Date;

    if (createdAt instanceof Date) {
      parsedCreatedAt = createdAt;
    } else if (typeof createdAt === 'string') {
      parsedCreatedAt = new Date(createdAt);
    } else {
      parsedCreatedAt = new Date();
    }

    if (updatedAt instanceof Date) {
      parsedUpdatedAt = updatedAt;
    } else if (typeof updatedAt === 'string') {
      parsedUpdatedAt = new Date(updatedAt);
    } else {
      parsedUpdatedAt = new Date();
    }

    return {
      id: row.id as string,
      email: row.email as string,
      phone: row.phone as string | undefined,
      name: row.name as string,
      role: row.role as UserRole,
      specialty: row.specialty as string | undefined,
      clinicId: row.clinic_id as number | null,
      profilePicture: row.profile_picture as string | undefined,
      canTakeAppointments: Boolean(row.can_take_appointments),
      locationIds: this.parseJsonField(row.location_ids, undefined),
      currentLocationId: row.current_location_id as string | undefined,
      practiceIds: this.parseJsonField(row.practice_ids, undefined),
      preferences: this.parseJsonField(row.preferences, undefined),
      createdAt: parsedCreatedAt,
      updatedAt: parsedUpdatedAt,
    };
  }

  /**
   * Custom validation for User entities
   */
  protected customValidateEntity(entity: Partial<User>): boolean {
    const errors: string[] = [];

    if (!entity.email?.trim()) {
      errors.push('Email is required');
    }

    if (entity.email && !this.isValidEmail(entity.email)) {
      errors.push('Invalid email format');
    }

    if (!entity.name?.trim()) {
      errors.push('Name is required');
    }

    if (!entity.role || !Object.values(UserRole).includes(entity.role)) {
      errors.push('Valid role is required');
    }

    if (entity.canTakeAppointments === undefined || entity.canTakeAppointments === null) {
      errors.push('canTakeAppointments flag is required');
    }

    if (errors.length > 0) {
      throw new Error(`User validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Parse JSON field helper
   */
  private parseJsonField<T>(value: string | unknown, defaultValue: T): T {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return defaultValue;
      }
    } else if (typeof value === 'object') {
      return value as T;
    }
    return defaultValue;
  }

  /**
   * Email validation helper
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    const result = await this.findMany({
      where: { email },
      limit: 1,
    });
    return result.items.length > 0 ? result.items[0] : null;
  }

  /**
   * Find users by clinic ID
   */
  async findByClinicId(clinicId: number, limit = 100): Promise<User[]> {
    const result = await this.findMany({
      where: { clinicId },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find users by role
   */
  async findByRole(role: UserRole, limit = 100): Promise<User[]> {
    const result = await this.findMany({
      where: { role },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find users by role and clinic
   */
  async findByRoleAndClinic(role: UserRole, clinicId: number, limit = 100): Promise<User[]> {
    const result = await this.findMany({
      where: { role, clinicId },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find users who can take appointments
   */
  async findCanTakeAppointments(
    clinicId?: number,
    locationId?: string,
    limit = 100,
  ): Promise<User[]> {
    const where: Record<string, unknown> = { canTakeAppointments: true };
    if (clinicId) {
      where.clinicId = clinicId;
    }

    if (locationId) {
      where.currentLocation = locationId;
    }

    const result = await this.findMany({
      where,
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find users by location ID
   */
  async findByLocationId(locationId: string, limit = 100): Promise<User[]> {
    // For now, get all users and filter in memory
    // TODO: Implement proper JSON array queries when MySQL support is available
    const result = await this.findMany({
      where: {},
      limit: limit * 2, // Get more to filter
      orderBy: [{ field: 'name', direction: 'asc' }],
    });

    return result.items.filter(user => user.locationIds?.includes(locationId)).slice(0, limit);
  }

  /**
   * Find users by specialty
   */
  async findBySpecialty(specialty: string, limit = 50): Promise<User[]> {
    const result = await this.findMany({
      where: { specialty },
      limit,
      orderBy: [{ field: 'name', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Find user by Firebase UID
   * This method queries the MySQL users table using the firebase_uid field
   */
  async findByFirebaseUid(firebaseUid: string): Promise<User | null> {
    try {
      // Use raw SQL query since firebase_uid is not in the standard field mapping
      const sql = 'SELECT * FROM users WHERE firebase_uid = ? LIMIT 1';
      const rows = await mysqlService.query<Record<string, unknown>>(sql, [firebaseUid]);

      if (rows.length === 0) {
        return null;
      }

      return this.mysqlDataToEntity(rows[0]);
    } catch (error) {
      console.error('Error finding user by Firebase UID:', error);
      // Fallback to Firestore if MySQL fails
      return this.findById(firebaseUid);
    }
  }

  /**
   * Search users by name (partial match)
   */
  async searchByName(name: string, clinicId?: number, limit = 20): Promise<User[]> {
    // Get users and filter in memory for now
    const where: Record<string, unknown> = {};
    if (clinicId) {
      where.clinicId = clinicId;
    }

    const result = await this.findMany({
      where,
      limit: limit * 5, // Get more to filter
      orderBy: [{ field: 'name', direction: 'asc' }],
    });

    return result.items
      .filter(user => user.name.toLowerCase().includes(name.toLowerCase()))
      .slice(0, limit);
  }

  /**
   * Update user's current location
   */
  async updateCurrentLocation(userId: string, locationId: string): Promise<User> {
    return this.update(userId, { currentLocationId: locationId });
  }

  /**
   * Add location to user's accessible locations
   */
  async addLocation(userId: string, locationId: string): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    const currentLocationIds = user.locationIds || [];
    if (!currentLocationIds.includes(locationId)) {
      const updatedLocationIds = [...currentLocationIds, locationId];
      return this.update(userId, { locationIds: updatedLocationIds });
    }

    return user;
  }

  /**
   * Remove location from user's accessible locations
   */
  async removeLocation(userId: string, locationId: string): Promise<User> {
    const user = await this.findById(userId);
    if (!user) {
      throw new Error(`User not found: ${userId}`);
    }

    const currentLocationIds = user.locationIds || [];
    const updatedLocationIds = currentLocationIds.filter(id => id !== locationId);

    // If removing current location, clear it
    const updates: Partial<User> = { locationIds: updatedLocationIds };
    if (user.currentLocationId === locationId) {
      updates.currentLocationId = undefined;
    }

    return this.update(userId, updates);
  }

  /**
   * Get user statistics for a clinic
   */
  async getUserStats(clinicId: number): Promise<{
    totalUsers: number;
    staffCount: number;
    adminCount: number;
    canTakeAppointmentsCount: number;
    activeUsers: number;
  }> {
    const users = await this.findByClinicId(clinicId, 1000);

    const totalUsers = users.length;
    const staffCount = users.filter(user => user.role === UserRole.STAFF).length;
    const adminCount = users.filter(
      user => user.role === UserRole.CLINIC_ADMIN || user.role === UserRole.SUPER_ADMIN,
    ).length;
    const canTakeAppointmentsCount = users.filter(user => user.canTakeAppointments).length;
    const activeUsers = totalUsers; // For now, consider all users active

    return {
      totalUsers,
      staffCount,
      adminCount,
      canTakeAppointmentsCount,
      activeUsers,
    };
  }
}
