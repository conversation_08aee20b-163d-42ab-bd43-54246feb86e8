import { BaseRepository } from '../database/base-repository';
import { Client } from '../../models/Client';
import { DualDatabaseUtils } from '../database/dual-database-service';
import * as admin from 'firebase-admin';

/**
 * Client entity repository with dual-database support
 * Handles MySQL-first reads with Firestore fallback
 */
export class ClientsRepository extends BaseRepository<Client> {
  constructor() {
    super('clients', 'clients', {
      id: 'id',
      fullName: 'full_name',
      birthday: 'birthday',
      phoneNumber: 'phone_number',
      email: 'email',
      medicalHistory: 'medical_history',
      recentNotes: 'recent_notes',
      insuranceCompany: 'insurance_company',
      insuranceGroupNumber: 'insurance_group_number',
      subscriberName: 'subscriber_name',
      listOfCalls: 'list_of_calls', // JSON field
      clinicId: 'clinic_id',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /**
   * Convert entity to Firestore data format
   */
  protected entityToFirestoreData(entity: Client): Record<string, unknown> {
    return {
      fullName: entity.fullName,
      birthday:
        entity.birthday instanceof Date
          ? admin.firestore.Timestamp.fromDate(entity.birthday)
          : entity.birthday,
      phoneNumber: entity.phoneNumber,
      email: entity.email || null,
      medicalHistory: entity.medicalHistory || null,
      recentNotes: entity.recentNotes || null,
      insuranceCompany: entity.insuranceCompany,
      insuranceGroupNumber: entity.insuranceGroupNumber,
      subscriberName: entity.subscriberName,
      listOfCalls: entity.listOfCalls || [],
      clinicId: entity.clinicId,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
    };
  }

  /**
   * Convert Firestore document to entity
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): Client {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      fullName: data.fullName as string,
      birthday: DualDatabaseUtils.timestampToDate(data.birthday) || new Date(),
      phoneNumber: data.phoneNumber as string,
      email: data.email as string | undefined,
      medicalHistory: data.medicalHistory as string | undefined,
      recentNotes: data.recentNotes as string | undefined,
      insuranceCompany: data.insuranceCompany as string,
      insuranceGroupNumber: data.insuranceGroupNumber as string,
      subscriberName: data.subscriberName as string,
      listOfCalls: (data.listOfCalls as string[]) || [],
      clinicId: data.clinicId as number,
    };
  }

  /**
   * Convert MySQL row data to entity
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): Client {
    const birthday = row.birthday;
    let parsedBirthday: Date;

    if (birthday instanceof Date) {
      parsedBirthday = birthday;
    } else if (typeof birthday === 'string') {
      parsedBirthday = new Date(birthday);
    } else {
      parsedBirthday = new Date();
    }

    return {
      id: row.id as string,
      fullName: row.full_name as string,
      birthday: parsedBirthday,
      phoneNumber: row.phone_number as string,
      email: row.email as string | undefined,
      medicalHistory: row.medical_history as string | undefined,
      recentNotes: row.recent_notes as string | undefined,
      insuranceCompany: row.insurance_company as string,
      insuranceGroupNumber: row.insurance_group_number as string,
      subscriberName: row.subscriber_name as string,
      listOfCalls: this.parseJsonField(row.list_of_calls, []),
      clinicId: row.clinic_id as number,
    };
  }

  /**
   * Custom validation for Client entities
   */
  protected customValidateEntity(entity: Partial<Client>): boolean {
    const errors: string[] = [];

    if (!entity.fullName?.trim()) {
      errors.push('Full name is required');
    }

    if (!entity.phoneNumber?.trim()) {
      errors.push('Phone number is required');
    }

    if (!entity.insuranceCompany?.trim()) {
      errors.push('Insurance company is required');
    }

    if (!entity.insuranceGroupNumber?.trim()) {
      errors.push('Insurance group number is required');
    }

    if (!entity.subscriberName?.trim()) {
      errors.push('Subscriber name is required');
    }

    if (!entity.clinicId || entity.clinicId <= 0) {
      errors.push('Valid clinic ID is required');
    }

    if (entity.email && !this.isValidEmail(entity.email)) {
      errors.push('Invalid email format');
    }

    if (errors.length > 0) {
      throw new Error(`Client validation failed: ${errors.join(', ')}`);
    }

    return true;
  }

  /**
   * Parse JSON field helper
   */
  private parseJsonField<T>(value: string | unknown, defaultValue: T): T {
    if (typeof value === 'string') {
      try {
        return JSON.parse(value);
      } catch {
        return defaultValue;
      }
    }
    return defaultValue;
  }

  /**
   * Find clients by phone number
   */
  async findByPhoneNumber(phoneNumber: string): Promise<Client[]> {
    const result = await this.findMany({
      where: { phoneNumber },
      limit: 10,
    });
    return result.items;
  }

  /**
   * Find clients by clinic ID
   */
  async findByClinicId(clinicId: number, limit = 50): Promise<Client[]> {
    const result = await this.findMany({
      where: { clinicId },
      limit,
      orderBy: [{ field: 'fullName', direction: 'asc' }],
    });
    return result.items;
  }

  /**
   * Search clients by name (partial match)
   */
  async searchByName(name: string, clinicId: number, limit = 20): Promise<Client[]> {
    // Use the base findMany method with Firestore filtering
    const allClients = await this.findByClinicId(clinicId, 100);
    return allClients
      .filter(client => client.fullName.toLowerCase().includes(name.toLowerCase()))
      .slice(0, limit);
  }

  /**
   * Add call ID to client's call list
   */
  async addCallToClient(clientId: string, callId: string): Promise<void> {
    const client = await this.findById(clientId);
    if (!client) {
      throw new Error(`Client not found: ${clientId}`);
    }

    const updatedCallList = [...(client.listOfCalls || []), callId];
    await this.update(clientId, { listOfCalls: updatedCallList });
  }

  /**
   * Remove call ID from client's call list
   */
  async removeCallFromClient(clientId: string, callId: string): Promise<void> {
    const client = await this.findById(clientId);
    if (!client) {
      throw new Error(`Client not found: ${clientId}`);
    }

    const updatedCallList = (client.listOfCalls || []).filter(id => id !== callId);
    await this.update(clientId, { listOfCalls: updatedCallList });
  }

  /**
   * Find clients with recent activity (calls in last N days)
   */
  async findClientsWithRecentActivity(clinicId: number): Promise<Client[]> {
    // For now, return all clients for this clinic
    // TODO: Implement proper join query when call repository is available
    return this.findByClinicId(clinicId);
  }

  /**
   * Email validation helper
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
