import { BaseRepository } from '../database/base-repository';
import { Patient, Address, Insurance, Identifier } from '@/lib/external-api/v2';
import * as admin from 'firebase-admin';
import { DualDatabaseUtils, BaseEntity } from '../database/dual-database-service';

export interface PatientEntity extends Patient, BaseEntity {
  provider?: string;
  providerId?: string;
}

export class PatientsRepository extends BaseRepository<PatientEntity> {
  constructor() {
    super('patients', 'patients', {
      id: 'id',
      firstName: 'first_name',
      lastName: 'last_name',
      dateOfBirth: 'date_of_birth',
      gender: 'gender',
      email: 'email',
      phoneNumber: 'phone_number',
      address: 'address',
      provider: 'provider',
      providerId: 'provider_id',
      notes: 'notes',
      insurances: 'insurances',
      identifiers: 'identifiers',
      createdAt: 'created_at',
      updatedAt: 'updated_at',
    });
  }

  /** Firestore conversion helpers */
  protected entityToFirestoreData(entity: PatientEntity): Record<string, unknown> {
    return {
      firstName: entity.firstName,
      lastName: entity.lastName,
      dateOfBirth: entity.dateOfBirth,
      gender: entity.gender || null,
      email: entity.email || null,
      phoneNumber: entity.phoneNumber || null,
      address: entity.address || null,
      providerInfo: {
        provider: entity.provider || entity.providerInfo?.provider || null,
        externalId: entity.providerId || entity.providerInfo?.externalId || null,
      },
      providerId: entity.providerId || null,
      notes: entity.notes || null,
      insurances: entity.insurances || [],
      identifiers: entity.identifiers || [],
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };
  }

  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): PatientEntity {
    const data = doc.data();
    if (!data) {
      throw new Error(`Patient document ${doc.id} has no data`);
    }

    return {
      id: doc.id,
      firstName: data.firstName as string,
      lastName: data.lastName as string,
      dateOfBirth: data.dateOfBirth as string,
      gender: data.gender as string | undefined,
      email: data.email as string | undefined,
      phoneNumber: data.phoneNumber as string | undefined,
      address: data.address as Address | undefined,
      provider: (data.providerInfo?.provider as string) || (data.provider as string | undefined),
      providerId:
        (data.providerInfo?.externalId as string) || (data.providerId as string | undefined),
      providerInfo: {
        provider: data.providerInfo?.provider || data.provider || '',
        externalId: data.providerInfo?.externalId || data.providerId || '',
      },
      notes: data.notes as string | undefined,
      insurances: (data.insurances as Insurance[]) || [],
      identifiers: (data.identifiers as Identifier[]) || [],
      createdAt: DualDatabaseUtils.timestampToDate(data.createdAt) || new Date(),
      updatedAt: DualDatabaseUtils.timestampToDate(data.updatedAt) || new Date(),
    };
  }

  protected mysqlDataToEntity(row: Record<string, unknown>): PatientEntity {
    const parseJson = <T>(value: unknown, defaultValue: T): T => {
      if (typeof value === 'string') {
        try {
          return JSON.parse(value) as T;
        } catch {
          return defaultValue;
        }
      }
      return (value as T) || defaultValue;
    };

    return {
      id: row.id as string,
      firstName: row.first_name as string,
      lastName: row.last_name as string,
      dateOfBirth: row.date_of_birth as string,
      gender: row.gender as string | undefined,
      email: row.email as string | undefined,
      phoneNumber: row.phone_number as string | undefined,
      address: parseJson<Address>(row.address, {
        line1: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
      }),
      provider: row.provider as string | undefined,
      providerId: row.provider_id as string | undefined,
      providerInfo: {
        provider: (row.provider as string) || '',
        externalId: (row.provider_id as string) || '',
      },
      notes: row.notes as string | undefined,
      insurances: parseJson<Insurance[]>(row.insurances, []),
      identifiers: parseJson<Identifier[]>(row.identifiers, []),
      createdAt:
        row.created_at instanceof Date ? row.created_at : new Date(row.created_at as string),
      updatedAt:
        row.updated_at instanceof Date ? row.updated_at : new Date(row.updated_at as string),
    };
  }

  /**
   * Find patient by provider + externalId
   */
  async findByProviderId(provider: string, providerId: string): Promise<PatientEntity | null> {
    const result = await this.findMany({
      where: { provider, providerId },
      limit: 1,
    });
    return result.items.length ? result.items[0] : null;
  }

  async findByPhoneNumberAndBirthDate({
    phoneNumber,
    dateOfBirth,
  }: {
    phoneNumber: string;
    dateOfBirth: string;
  }): Promise<PatientEntity[]> {
    const result = await this.findMany({
      where: { phoneNumber, dateOfBirth },
    });
    return result.items || [];
  }

  /**
   * Find patient by normalized phone number
   */
  async findByPhoneNumber(phoneNumber: string): Promise<PatientEntity | null> {
    const result = await this.findMany({
      where: { phoneNumber },
      limit: 1,
    });
    return result.items.length ? result.items[0] : null;
  }

  async findManyByPhoneNumber(phoneNumber: string): Promise<PatientEntity[]> {
    const result = await this.findMany({
      where: { phoneNumber },
    });
    return result.items || [];
  }

  protected customValidateEntity(entity: Partial<PatientEntity>): boolean {
    const errors: string[] = [];
    if (!entity.firstName?.trim()) errors.push('firstName is required');
    if (!entity.lastName?.trim()) errors.push('lastName is required');
    if (errors.length) throw new Error(`Patient validation failed: ${errors.join(', ')}`);
    return true;
  }
}
