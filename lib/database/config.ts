import { ConnectionOptions } from 'mysql2/promise';
import { Knex } from 'knex';
import { replaceScriptVariables } from './utils';

export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  acquireTimeoutMillis: number;
  timeoutMillis: number;
  idleTimeoutMillis: number;
  /**
   * Optional Unix domain socket path for Cloud SQL / local MySQL sockets.
   * When this is defined, the host and port fields are ignored by the client library.
   */
  socketPath?: string;
  ssl?: {
    rejectUnauthorized: boolean;
  };
}

export const getDatabaseConfig = (): DatabaseConfig => {
  // Default configuration with environment variable overrides
  const config: DatabaseConfig = {
    host: process.env.MYSQL_HOST || process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || process.env.DB_PORT || '3306', 10),
    user: process.env.MYSQL_USER || process.env.DB_USER || 'root',
    password: process.env.MYSQL_PASSWORD || process.env.DB_PWD || '',
    database: process.env.MYSQL_DATABASE || process.env.DB_NAME || 'frontdesk_ai',
    connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '10', 10),
    acquireTimeoutMillis: 60000,
    timeoutMillis: 30000,
    idleTimeoutMillis: 600000,
  };

  // Detect Cloud SQL socket path (preferred over TCP when running internal to GCP)
  // Priority: DB_SOCKET_PATH (explicit full path) > INSTANCE_CONNECTION_NAME/CLOUD_SQL_CONNECTION_NAME (mounted in /cloudsql)
  const instanceConnectionName =
    process.env.INSTANCE_CONNECTION_NAME || process.env.CLOUD_SQL_CONNECTION_NAME;

  if (process.env.DB_SOCKET_PATH) {
    config.socketPath = process.env.DB_SOCKET_PATH;
  } else if (instanceConnectionName && process.env.NODE_ENV === 'production') {
    config.socketPath = `/cloudsql/${instanceConnectionName}`;
  }

  // ---------------------------------------------
  // SSL Configuration Logic
  // ---------------------------------------------
  // We enable SSL based on the following rules:
  // 1. If MYSQL_SSL is explicitly set to a truthy value ("true" | "1"), **always** enable SSL.
  // 2. If MYSQL_SSL is explicitly set to a falsy value ("false" | "0"), **never** enable SSL.
  // 3. Otherwise (MYSQL_SSL not provided), enable SSL automatically only when NODE_ENV === "production".
  //    This provides a sensible default for GCP deployments while still allowing full override.

  const mysqlSslEnv = process.env.MYSQL_SSL?.toLowerCase();
  const sslExplicitTrue = mysqlSslEnv === 'true' || mysqlSslEnv === '1';
  const sslExplicitFalse = mysqlSslEnv === 'false' || mysqlSslEnv === '0';

  if (sslExplicitTrue || (!sslExplicitFalse && process.env.NODE_ENV === 'production')) {
    config.ssl = {
      rejectUnauthorized: false, // GCP MySQL uses server-side certificates or self-signed certs
    };
  }

  return config;
};

export const getMysql2Config = (): ConnectionOptions => {
  const config = getDatabaseConfig();

  return {
    host: config.host,
    port: config.port,
    // Use Unix socket path if provided (overrides host/port for mysql2)
    ...(config.socketPath ? { socketPath: config.socketPath } : {}),
    user: config.user,
    password: config.password,
    database: config.database,
    ssl: config.ssl,
    timezone: 'Z', // Use UTC timezone
    dateStrings: false,
    supportBigNumbers: true,
    bigNumberStrings: false,
    multipleStatements: false,
    queryFormat: replaceScriptVariables, // Custom query formatter for :variable syntax
  };
};

// MySQL configuration object similar to the user's example
export const MYSQL_CONFIG = {
  host: process.env.DB_HOST || process.env.MYSQL_HOST,
  user: process.env.DB_USER || process.env.MYSQL_USER,
  password: process.env.DB_PWD || process.env.MYSQL_PASSWORD,
  database: process.env.DB_NAME || process.env.MYSQL_DATABASE,
  port: parseInt(process.env.DB_PORT || process.env.MYSQL_PORT || '3306', 10),
  // Prefer explicit DB_SOCKET_PATH always. Fall back to Cloud SQL socket path only
  // when running in a production environment so that local development keeps a
  // simple TCP connection.
  ...(process.env.DB_SOCKET_PATH ||
  ((process.env.INSTANCE_CONNECTION_NAME || process.env.CLOUD_SQL_CONNECTION_NAME) &&
    process.env.NODE_ENV === 'production')
    ? {
        socketPath:
          process.env.DB_SOCKET_PATH ||
          `/cloudsql/${process.env.INSTANCE_CONNECTION_NAME || process.env.CLOUD_SQL_CONNECTION_NAME}`,
      }
    : {}),
  connectionLimit: 20,
  timezone: 'Z',
  queryFormat: replaceScriptVariables,
};

export const getKnexConfig = (): Knex.Config => {
  const config = getDatabaseConfig();

  return {
    client: 'mysql2',
    connection: {
      host: config.host,
      port: config.port,
      // Use Unix socket path if provided (overrides host/port for mysql2)
      ...(config.socketPath ? { socketPath: config.socketPath } : {}),
      user: config.user,
      password: config.password,
      database: config.database,
      ssl: config.ssl,
      timezone: 'Z',
      dateStrings: false,
      supportBigNumbers: true,
      bigNumberStrings: false,
      multipleStatements: false,
    },
    pool: {
      min: 2,
      max: config.connectionLimit,
      acquireTimeoutMillis: config.acquireTimeoutMillis,
      idleTimeoutMillis: config.idleTimeoutMillis,
    },
    migrations: {
      directory: './migrations',
      tableName: 'knex_migrations',
    },
  };
};
