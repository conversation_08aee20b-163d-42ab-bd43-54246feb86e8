import * as admin from 'firebase-admin';
import { mysqlService } from './mysql-service';
import {
  DatabaseConfig,
  getDatabaseConfig,
  WriteOptions,
  getWriteStrategy,
  DatabaseMetricsCollector,
} from './database-config';
import { FindCriteria, PaginatedResult } from './unit-of-work';

/**
 * Base entity interface that all entities should extend
 */
export interface BaseEntity {
  id: string;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Error types for dual-database operations
 */
export class DualDatabaseError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly database: 'mysql' | 'firestore' | 'dual',
    public readonly originalError?: Error,
  ) {
    super(message);
    this.name = 'DualDatabaseError';
    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, DualDatabaseError.prototype);
  }
}

/**
 * Result interface for dual-database operations
 */
export interface DualDatabaseResult<T> {
  data: T;
  source: 'mysql' | 'firestore';
  fallbackUsed: boolean;
  duration: number;
}

/**
 * Dual-database service interface
 * Write operations: dual write to both databases
 * Read operations: MySQL only (simplified)
 */
export interface DualDatabaseService<T extends BaseEntity> {
  // Write operations - dual write to both databases
  create(entity: Omit<T, 'id'>, options?: WriteOptions): Promise<T>;
  update(id: string, updates: Partial<T>, options?: WriteOptions): Promise<T>;
  delete(id: string, options?: WriteOptions): Promise<void>;

  // Read operations - MySQL only (simplified)
  findById(
    id: string,
    options?: {
      /** Force read from MySQL even if Firestore is available */
      forceMySQL?: boolean;
      /** Force read from Firestore even if MySQL is available */
      forceFirestore?: boolean;
    },
  ): Promise<T | null>;
  findMany(criteria: FindCriteria<T>): Promise<PaginatedResult<T>>;

  // Health and utility methods
  healthCheck(): Promise<{ mysql: boolean; firestore: boolean }>;
  validateEntity(entity: Partial<T>): boolean;
}

/**
 * Abstract base repository implementing dual-database functionality
 * Writes to both MySQL and Firestore, reads only from MySQL
 */
export abstract class BaseDualDatabaseService<T extends BaseEntity>
  implements DualDatabaseService<T>
{
  protected db: admin.firestore.Firestore;
  protected config: DatabaseConfig;

  constructor(
    protected readonly collectionName: string,
    protected readonly tableName: string,
  ) {
    this.db = admin.firestore();
    this.config = getDatabaseConfig();
  }

  /**
   * Create a new entity with dual-write capability
   */
  async create(entity: Omit<T, 'id'>, options: WriteOptions = {}): Promise<T> {
    const startTime = Date.now();
    const writeStrategy = getWriteStrategy(options);

    // Generate ID and timestamps
    const id = options?.generateEntityId?.(entity) || this.generateId();
    const now = new Date();
    const fullEntity = {
      ...entity,
      id,
      createdAt: now,
      updatedAt: now,
    } as T;

    const errors: string[] = [];
    let mysqlResult: T | null = null;
    let firestoreResult: T | null = null;

    try {
      // Validate entity before saving
      if (!this.validateEntity(fullEntity)) {
        throw new DualDatabaseError('Entity validation failed', 'create', 'dual');
      }

      // Write to MySQL first (if needed)
      if (writeStrategy.writeToMySQL) {
        try {
          mysqlResult = await this.createInMySQL(fullEntity);

          DatabaseMetricsCollector.recordMetric({
            operation: 'create',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `MySQL create failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'create',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Write to Firestore (if needed)
      if (writeStrategy.writeToFirestore) {
        try {
          firestoreResult = await this.createInFirestore(fullEntity);

          DatabaseMetricsCollector.recordMetric({
            operation: 'create',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `Firestore create failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'create',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Check if any required writes failed
      if (writeStrategy.writeToMySQL && !mysqlResult) {
        throw new DualDatabaseError(
          `Failed to create in MySQL: ${errors.filter(e => e.includes('MySQL')).join(', ')}`,
          'create',
          'mysql',
        );
      }

      if (writeStrategy.writeToFirestore && !firestoreResult) {
        throw new DualDatabaseError(
          `Failed to create in Firestore: ${errors.filter(e => e.includes('Firestore')).join(', ')}`,
          'create',
          'firestore',
        );
      }

      // Return the result from the primary database
      const result = (writeStrategy.writeToMySQL ? mysqlResult : firestoreResult) || fullEntity;

      console.log(
        `✅ Entity created successfully in ${writeStrategy.writeToMySQL && writeStrategy.writeToFirestore ? 'both databases' : writeStrategy.writeToMySQL ? 'MySQL' : 'Firestore'}`,
      );

      return result;
    } catch (error) {
      if (errors.length > 0) {
        console.error('🚨 Dual-write create errors:', errors);
      }

      throw error instanceof DualDatabaseError
        ? error
        : new DualDatabaseError(
            `Create operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            'create',
            'dual',
            error instanceof Error ? error : undefined,
          );
    }
  }

  /**
   * Update an existing entity with dual-write capability
   */
  async update(id: string, updates: Partial<T>, options: WriteOptions = {}): Promise<T> {
    const startTime = Date.now();
    const writeStrategy = getWriteStrategy(options);

    // Add updated timestamp
    const fullUpdates = {
      ...updates,
      updatedAt: new Date(),
    };

    const errors: string[] = [];
    let mysqlResult: T | null = null;
    let firestoreResult: T | null = null;

    try {
      // Update in MySQL (if needed)
      if (writeStrategy.writeToMySQL) {
        try {
          mysqlResult = await this.updateInMySQL(id, fullUpdates);

          DatabaseMetricsCollector.recordMetric({
            operation: 'update',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `MySQL update failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'update',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Update in Firestore (if needed)
      if (writeStrategy.writeToFirestore) {
        try {
          firestoreResult = await this.updateInFirestore(id, fullUpdates);

          DatabaseMetricsCollector.recordMetric({
            operation: 'update',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `Firestore update failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'update',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Check if any required updates failed
      if (writeStrategy.writeToMySQL && !mysqlResult) {
        throw new DualDatabaseError(
          `Failed to update in MySQL: ${errors.filter(e => e.includes('MySQL')).join(', ')}`,
          'update',
          'mysql',
        );
      }

      if (writeStrategy.writeToFirestore && !firestoreResult) {
        throw new DualDatabaseError(
          `Failed to update in Firestore: ${errors.filter(e => e.includes('Firestore')).join(', ')}`,
          'update',
          'firestore',
        );
      }

      // Return the result from the primary database
      const result = writeStrategy.writeToMySQL ? mysqlResult : firestoreResult;

      if (!result) {
        throw new DualDatabaseError('No result from update operation', 'update', 'dual');
      }

      console.log(
        `✅ Entity updated successfully in ${writeStrategy.writeToMySQL && writeStrategy.writeToFirestore ? 'both databases' : writeStrategy.writeToMySQL ? 'MySQL' : 'Firestore'}`,
      );

      return result;
    } catch (error) {
      if (errors.length > 0) {
        console.error('🚨 Dual-write update errors:', errors);
      }

      throw error instanceof DualDatabaseError
        ? error
        : new DualDatabaseError(
            `Update operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            'update',
            'dual',
            error instanceof Error ? error : undefined,
          );
    }
  }

  /**
   * Delete an entity with dual-write capability
   */
  async delete(id: string, options: WriteOptions = {}): Promise<void> {
    const startTime = Date.now();
    const writeStrategy = getWriteStrategy(options);

    const errors: string[] = [];
    let mysqlSuccess = false;
    let firestoreSuccess = false;

    try {
      // Delete from MySQL (if needed)
      if (writeStrategy.writeToMySQL) {
        try {
          await this.deleteInMySQL(id);
          mysqlSuccess = true;

          DatabaseMetricsCollector.recordMetric({
            operation: 'delete',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `MySQL delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'delete',
            database: 'mysql',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Delete from Firestore (if needed)
      if (writeStrategy.writeToFirestore) {
        try {
          await this.deleteInFirestore(id);
          firestoreSuccess = true;

          DatabaseMetricsCollector.recordMetric({
            operation: 'delete',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: true,
          });
        } catch (error) {
          const errorMessage = `Firestore delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMessage);

          DatabaseMetricsCollector.recordMetric({
            operation: 'delete',
            database: 'firestore',
            duration: Date.now() - startTime,
            success: false,
            error: errorMessage,
          });
        }
      }

      // Check if any required deletes failed
      if (writeStrategy.writeToMySQL && !mysqlSuccess) {
        throw new DualDatabaseError(
          `Failed to delete from MySQL: ${errors.filter(e => e.includes('MySQL')).join(', ')}`,
          'delete',
          'mysql',
        );
      }

      if (writeStrategy.writeToFirestore && !firestoreSuccess) {
        throw new DualDatabaseError(
          `Failed to delete from Firestore: ${errors.filter(e => e.includes('Firestore')).join(', ')}`,
          'delete',
          'firestore',
        );
      }

      console.log(
        `✅ Entity deleted successfully from ${writeStrategy.writeToMySQL && writeStrategy.writeToFirestore ? 'both databases' : writeStrategy.writeToMySQL ? 'MySQL' : 'Firestore'}`,
      );
    } catch (error) {
      if (errors.length > 0) {
        console.error('🚨 Dual-write delete errors:', errors);
      }

      throw error instanceof DualDatabaseError
        ? error
        : new DualDatabaseError(
            `Delete operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            'delete',
            'dual',
            error instanceof Error ? error : undefined,
          );
    }
  }

  /**
   * Find entity by ID - MySQL only (simplified)
   */
  async findById(
    id: string,
    options: { forceMySQL?: boolean; forceFirestore?: boolean } = {},
  ): Promise<T | null> {
    const { forceMySQL, forceFirestore } = options;

    // Guard against conflicting options
    if (forceMySQL && forceFirestore) {
      throw new DualDatabaseError(
        'Cannot force read from both MySQL and Firestore simultaneously',
        'findById',
        'dual',
      );
    }

    const startTime = Date.now();

    // Helper to record metrics succinctly
    const record = (db: 'mysql' | 'firestore', success: boolean, error?: string): void => {
      DatabaseMetricsCollector.recordMetric({
        operation: 'findById',
        database: db,
        duration: Date.now() - startTime,
        success,
        error,
      });
    };

    // 1. Forced MySQL read
    if (forceMySQL) {
      try {
        const res = await this.findByIdInMySQL(id);
        record('mysql', true);
        return res;
      } catch (err) {
        record('mysql', false, err instanceof Error ? err.message : 'Unknown error');
        throw new DualDatabaseError(
          'MySQL findById failed',
          'findById',
          'mysql',
          err instanceof Error ? err : undefined,
        );
      }
    }

    // 2. Forced Firestore read
    if (forceFirestore) {
      try {
        const res = await this.findByIdInFirestore(id);
        record('firestore', true);
        return res;
      } catch (err) {
        record('firestore', false, err instanceof Error ? err.message : 'Unknown error');
        throw new DualDatabaseError(
          'Firestore findById failed',
          'findById',
          'firestore',
          err instanceof Error ? err : undefined,
        );
      }
    }

    // 3. Default behaviour – try MySQL first, then fallback to Firestore
    try {
      const mysqlResult = await this.findByIdInMySQL(id);
      record('mysql', true);

      if (mysqlResult) {
        console.log('📊 findById: MySQL hit');
        return mysqlResult;
      }

      console.log('📊 findById: MySQL miss – falling back to Firestore');
    } catch (err) {
      // Log error but continue to fallback
      record('mysql', false, err instanceof Error ? err.message : 'Unknown error');
      console.warn('MySQL findById failed, attempting Firestore fallback');
    }

    // Attempt Firestore fallback
    try {
      const firestoreResult = await this.findByIdInFirestore(id);
      record('firestore', true);
      return firestoreResult;
    } catch (err) {
      record('firestore', false, err instanceof Error ? err.message : 'Unknown error');
      // Final failure – return null (do not throw) to satisfy tests expecting null
      return null;
    }
  }

  /**
   * Find multiple entities - MySQL only (simplified)
   */
  async findMany(criteria: FindCriteria<T>): Promise<PaginatedResult<T>> {
    const startTime = Date.now();

    try {
      const result = await this.findManyInMySQL(criteria);

      DatabaseMetricsCollector.recordMetric({
        operation: 'findMany',
        database: 'mysql',
        duration: Date.now() - startTime,
        success: true,
      });

      console.log(`📊 findMany: MySQL returned ${result.items.length} items`);
      return result;
    } catch (error) {
      const errorMessage = `MySQL findMany failed: ${error instanceof Error ? error.message : 'Unknown error'}`;

      DatabaseMetricsCollector.recordMetric({
        operation: 'findMany',
        database: 'mysql',
        duration: Date.now() - startTime,
        success: false,
        error: errorMessage,
      });

      throw new DualDatabaseError(
        errorMessage,
        'findMany',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Health check for both databases
   */
  async healthCheck(): Promise<{ mysql: boolean; firestore: boolean }> {
    const results = { mysql: false, firestore: false };

    try {
      const healthCheck = await mysqlService.healthCheck();
      results.mysql = healthCheck.status === 'healthy';
    } catch {
      results.mysql = false;
    }

    try {
      // Simple Firestore health check
      await this.db.doc('health/check').get();
      results.firestore = true;
    } catch {
      results.firestore = false;
    }

    return results;
  }

  /**
   * Basic entity validation - override in subclasses for specific validation
   */
  validateEntity(entity: Partial<T>): boolean {
    return entity.id !== undefined && entity.id !== null && entity.id.trim() !== '';
  }

  /**
   * Generate a unique ID for new entities
   */
  protected generateId(): string {
    return this.db.collection('temp').doc().id;
  }

  // Abstract methods that must be implemented by concrete repositories
  protected abstract createInMySQL(entity: T): Promise<T>;
  protected abstract createInFirestore(entity: T): Promise<T>;
  protected abstract updateInMySQL(id: string, updates: Partial<T>): Promise<T>;
  protected abstract updateInFirestore(id: string, updates: Partial<T>): Promise<T>;
  protected abstract deleteInMySQL(id: string): Promise<void>;
  protected abstract deleteInFirestore(id: string): Promise<void>;
  protected abstract findByIdInMySQL(id: string): Promise<T | null>;
  protected abstract findByIdInFirestore(id: string): Promise<T | null>;
  protected abstract findManyInMySQL(criteria: FindCriteria<T>): Promise<PaginatedResult<T>>;
  protected abstract findManyInFirestore(criteria: FindCriteria<T>): Promise<PaginatedResult<T>>;
}

/**
 * Utility functions for dual-database operations
 */
export const DualDatabaseUtils = {
  /**
   * Convert Firestore timestamp to Date
   */
  timestampToDate(
    timestamp: admin.firestore.Timestamp | Date | string | undefined,
  ): Date | undefined {
    if (!timestamp) return undefined;

    if (timestamp instanceof Date) return timestamp;

    if (typeof timestamp === 'string') {
      return new Date(timestamp);
    }

    if (timestamp && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    return undefined;
  },

  /**
   * Convert Date to MySQL-compatible format
   */
  dateToMySQLFormat(date: Date | undefined): string | null {
    if (!date) return null;
    return date.toISOString().slice(0, 19).replace('T', ' ');
  },

  /**
   * Retry operation with exponential backoff
   */
  async retry<T>(
    operation: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        if (attempt === maxAttempts) {
          break;
        }

        const delay = baseDelay * Math.pow(2, attempt - 1);
        console.log(`⏳ Retry attempt ${attempt} failed, waiting ${delay}ms before retry`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError;
  },
};
