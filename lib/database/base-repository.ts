import * as admin from 'firebase-admin';
import { mysqlService } from './mysql-service';
import { BaseDualDatabaseService, BaseEntity, DualDatabaseError } from './dual-database-service';
import { FindCriteria, PaginatedResult } from './unit-of-work';
import { DualDatabaseUtils } from './dual-database-service';

/**
 * Base repository providing common CRUD operations
 * Extends BaseDualDatabaseService with concrete implementations
 */
export abstract class BaseRepository<T extends BaseEntity> extends BaseDualDatabaseService<T> {
  constructor(
    protected readonly collectionName: string,
    protected readonly tableName: string,
    protected readonly sqlSchema: Record<string, string>, // Maps entity fields to SQL columns
  ) {
    super(collectionName, tableName);
  }

  /**
   * Create entity in MySQL database
   */
  protected async createInMySQL(entity: T): Promise<T> {
    try {
      // Entity field names (camelCase)
      const entityFields = Object.keys(this.sqlSchema);

      // Corresponding SQL column names (snake_case)
      const sqlColumns = Object.values(this.sqlSchema);

      const placeholders = entityFields.map(() => '?').join(', ');

      const sql = `INSERT INTO ${this.tableName} (${sqlColumns.join(', ')}) VALUES (${placeholders})`;

      // Map entity values in the same order as entityFields so they match placeholders
      const params = entityFields.map(field => this.getEntityValue(entity, field));

      await mysqlService.query(sql, params);

      // Return the created entity
      return entity;
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to create entity in MySQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'createInMySQL',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Create entity in Firestore database
   */
  protected async createInFirestore(entity: T): Promise<T> {
    try {
      const docRef = this.db.collection(this.collectionName).doc(entity.id);
      const firestoreData = this.entityToFirestoreData(entity);

      await docRef.set(firestoreData);

      return entity;
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to create entity in Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'createInFirestore',
        'firestore',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Update entity in MySQL database
   */
  protected async updateInMySQL(id: string, updates: Partial<T>): Promise<T> {
    try {
      // First check if the entity exists in MySQL
      const existingEntity = await this.findByIdInMySQL(id);
      if (!existingEntity) {
        throw new Error(`Entity with id ${id} does not exist in MySQL`);
      }

      // Build SET clause dynamically based on provided updates
      const updateFields = Object.keys(updates)
        .filter(key => key !== 'id' && this.sqlSchema[key])
        .map(key => `${this.sqlSchema[key]} = ?`);

      if (updateFields.length === 0) {
        throw new Error('No valid fields to update');
      }

      const sql = `
        UPDATE ${this.tableName}
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `;

      const params = Object.keys(updates)
        .filter(key => key !== 'id' && this.sqlSchema[key])
        .map(key => this.getEntityValue(updates, key))
        .concat([id]);

      await mysqlService.query(sql, params);

      // Fetch and return the updated entity
      const updatedEntity = await this.findByIdInMySQL(id);
      if (!updatedEntity) {
        throw new Error('Entity not found after update - possible concurrent deletion');
      }

      return updatedEntity;
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to update entity in MySQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'updateInMySQL',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Update entity in Firestore database
   */
  protected async updateInFirestore(id: string, updates: Partial<T>): Promise<T> {
    try {
      const docRef = this.db.collection(this.collectionName).doc(id);
      const firestoreData = this.entityToFirestoreData(updates as T);

      await docRef.update(firestoreData);

      // Fetch and return the updated entity
      const updatedEntity = await this.findByIdInFirestore(id);
      if (!updatedEntity) {
        throw new Error('Entity not found after update');
      }

      return updatedEntity;
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to update entity in Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'updateInFirestore',
        'firestore',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Delete entity from MySQL database
   */
  protected async deleteInMySQL(id: string): Promise<void> {
    try {
      const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
      await mysqlService.query(sql, [id]);
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to delete entity from MySQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'deleteInMySQL',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Delete entity from Firestore database
   */
  protected async deleteInFirestore(id: string): Promise<void> {
    try {
      const docRef = this.db.collection(this.collectionName).doc(id);
      await docRef.delete();
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to delete entity from Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'deleteInFirestore',
        'firestore',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Find entity by ID in MySQL database
   */
  protected async findByIdInMySQL(id: string): Promise<T | null> {
    try {
      const columns = Object.values(this.sqlSchema).join(', ');
      const sql = `SELECT ${columns} FROM ${this.tableName} WHERE id = ?`;

      const result = await mysqlService.queryFirst<Record<string, unknown>>(sql, [id]);

      if (!result) {
        return null;
      }

      return this.mysqlDataToEntity(result);
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to find entity by ID in MySQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'findByIdInMySQL',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Find entity by ID in Firestore database
   */
  protected async findByIdInFirestore(id: string): Promise<T | null> {
    try {
      const docRef = this.db.collection(this.collectionName).doc(id);
      const doc = await docRef.get();

      if (!doc.exists) {
        return null;
      }

      return this.firestoreDataToEntity(doc);
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to find entity by ID in Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'findByIdInFirestore',
        'firestore',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Find multiple entities in MySQL database
   */
  protected async findManyInMySQL(criteria: FindCriteria<T>): Promise<PaginatedResult<T>> {
    try {
      const columns = Object.values(this.sqlSchema).join(', ');
      let sql = `SELECT ${columns} FROM ${this.tableName}`;
      const params: unknown[] = [];

      // Build WHERE clause
      if (criteria.where && Object.keys(criteria.where).length > 0) {
        const whereConditions = Object.entries(criteria.where)
          .filter(([key]) => this.sqlSchema[key])
          .map(([key, value]) => {
            params.push(value);
            return `${this.sqlSchema[key]} = ?`;
          });

        if (whereConditions.length > 0) {
          sql += ` WHERE ${whereConditions.join(' AND ')}`;
        }
      }

      // Build ORDER BY clause
      if (criteria.orderBy && criteria.orderBy.length > 0) {
        const orderConditions = criteria.orderBy
          .filter(order => this.sqlSchema[order.field as string])
          .map(
            order => `${this.sqlSchema[order.field as string]} ${order.direction.toUpperCase()}`,
          );

        if (orderConditions.length > 0) {
          sql += ` ORDER BY ${orderConditions.join(', ')}`;
        }
      }

      // Add LIMIT and OFFSET (inline numeric values to avoid prepared statement issues with LIMIT ?)
      if (typeof criteria.limit === 'number') {
        const safeLimit = Math.max(0, Math.floor(criteria.limit)) + 1; // Fetch one extra to detect hasMore
        sql += ` LIMIT ${safeLimit}`;
      }

      if (typeof criteria.offset === 'number' && criteria.offset > 0) {
        const safeOffset = Math.max(0, Math.floor(criteria.offset));
        sql += ` OFFSET ${safeOffset}`;
      }

      const results = await mysqlService.query<Record<string, unknown>>(sql, params);

      let items = results;
      let hasMore = false;

      // Check if there are more items
      if (criteria.limit && results.length > criteria.limit) {
        items = results.slice(0, criteria.limit);
        hasMore = true;
      }

      const entities = items.map(row => this.mysqlDataToEntity(row));

      return {
        items: entities,
        hasMore,
        total: undefined, // Could be calculated with a separate COUNT query if needed
      };
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to find entities in MySQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'findManyInMySQL',
        'mysql',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Find multiple entities in Firestore database
   */
  protected async findManyInFirestore(criteria: FindCriteria<T>): Promise<PaginatedResult<T>> {
    try {
      let query: admin.firestore.Query = this.db.collection(this.collectionName);

      // Build WHERE conditions
      if (criteria.where && Object.keys(criteria.where).length > 0) {
        Object.entries(criteria.where).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.where(key, '==', value);
          }
        });
      }

      // Build ORDER BY conditions
      if (criteria.orderBy && criteria.orderBy.length > 0) {
        criteria.orderBy.forEach(order => {
          query = query.orderBy(order.field as string, order.direction === 'desc' ? 'desc' : 'asc');
        });
      }

      // Add pagination
      if (criteria.startAfter) {
        const startAfterDoc = await this.db
          .collection(this.collectionName)
          .doc(criteria.startAfter)
          .get();
        if (startAfterDoc.exists) {
          query = query.startAfter(startAfterDoc);
        }
      }

      if (criteria.limit) {
        query = query.limit(criteria.limit + 1); // Get one extra to check if there are more
      }

      const snapshot = await query.get();

      let docs = snapshot.docs;
      let hasMore = false;

      // Check if there are more items
      if (criteria.limit && docs.length > criteria.limit) {
        docs = docs.slice(0, criteria.limit);
        hasMore = true;
      }

      const entities = docs.map(doc => this.firestoreDataToEntity(doc));

      return {
        items: entities,
        hasMore,
        nextCursor: hasMore && docs.length > 0 ? docs[docs.length - 1].id : undefined,
      };
    } catch (error) {
      throw new DualDatabaseError(
        `Failed to find entities in Firestore: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'findManyInFirestore',
        'firestore',
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Get value from entity for a specific field
   */
  protected getEntityValue(entity: Partial<T>, field: string): unknown {
    const rawValue = (entity as Record<string, unknown>)[field];

    // Convert undefined to null for SQL compatibility
    const value = rawValue === undefined ? null : rawValue;

    // Handle Date objects for MySQL
    if (value instanceof Date) {
      return DualDatabaseUtils.dateToMySQLFormat(value);
    }

    // Handle JSON objects for MySQL (stringify them)
    if (typeof value === 'object' && value !== null && !(value instanceof Date)) {
      return JSON.stringify(value);
    }

    return value;
  }

  /**
   * Convert entity to Firestore data format
   * Override in subclasses for entity-specific transformations
   */
  protected entityToFirestoreData(entity: T): Record<string, unknown> {
    const data: Record<string, unknown> = {};

    Object.keys(entity).forEach(key => {
      const value = (entity as Record<string, unknown>)[key];

      // Convert Dates to Firestore Timestamps
      if (value instanceof Date) {
        data[key] = admin.firestore.Timestamp.fromDate(value);
      } else {
        data[key] = value;
      }
    });

    return data;
  }

  /**
   * Convert Firestore document to entity
   * Override in subclasses for entity-specific transformations
   */
  protected firestoreDataToEntity(doc: admin.firestore.DocumentSnapshot): T {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} has no data`);
    }

    const entity: Record<string, unknown> = {
      id: doc.id,
    };

    Object.keys(data).forEach(key => {
      const value = data[key];

      // Convert Firestore Timestamps to Dates
      if (value && typeof value.toDate === 'function') {
        entity[key] = value.toDate();
      } else {
        entity[key] = value;
      }
    });

    return entity as T;
  }

  /**
   * Convert MySQL row data to entity
   * Override in subclasses for entity-specific transformations
   */
  protected mysqlDataToEntity(row: Record<string, unknown>): T {
    const entity: Record<string, unknown> = {};

    // Map SQL columns back to entity fields
    Object.entries(this.sqlSchema).forEach(([entityField, sqlColumn]) => {
      let value = row[sqlColumn];

      // Handle JSON columns (parse them back to objects)
      if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
        try {
          value = JSON.parse(value);
        } catch {
          // If parsing fails, keep as string
        }
      }

      // Handle MySQL datetime strings to Date objects
      if (typeof value === 'string' && value.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
        value = new Date(value);
      }

      entity[entityField] = value;
    });

    return entity as T;
  }

  /**
   * Enhanced entity validation with field-specific checks
   */
  validateEntity(entity: Partial<T>): boolean {
    if (!super.validateEntity(entity)) {
      return false;
    }

    // Add custom validation logic in subclasses
    return this.customValidateEntity(entity);
  }

  /**
   * Custom validation to be implemented by subclasses
   */
  protected customValidateEntity(entity?: Partial<T>): boolean {
    // Base implementation returns true - override in subclasses for custom validation
    return entity !== undefined;
  }
}
