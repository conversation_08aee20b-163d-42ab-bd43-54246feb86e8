import { Knex } from 'knex';
import * as admin from 'firebase-admin';
import { mysqlService } from './mysql-service';
import { DatabaseConfig, getDatabaseConfig, DatabaseMetricsCollector } from './database-config';

/**
 * Repository interface for the Unit of Work pattern
 */
export interface Repository<T> {
  findById(id: string): Promise<T | null>;
  findByUuid(uuid: string): Promise<T | null>;
  create(entity: Omit<T, 'id'>): Promise<T>;
  update(id: string, updates: Partial<T>): Promise<T>;
  delete(id: string): Promise<void>;
  findMany(criteria: FindCriteria<T>): Promise<PaginatedResult<T>>;
}

/**
 * Generic find criteria interface
 */
export interface FindCriteria<T> {
  where?: Partial<T>;
  orderBy?: Array<{ field: keyof T; direction: 'asc' | 'desc' }>;
  limit?: number;
  offset?: number;
  startAfter?: string;
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  items: T[];
  total?: number;
  hasMore: boolean;
  nextCursor?: string;
}

/**
 * Transaction context interface
 */
export interface TransactionContext {
  mysqlTrx?: Knex.Transaction;
  firestoreBatch?: admin.firestore.WriteBatch;
  isActive: boolean;
  hasChanges: boolean;
}

/**
 * Unit of Work interface for transaction management
 */
export interface IUnitOfWork {
  begin(): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
  getRepository<T>(entityName: string): Repository<T>;
  isActive(): boolean;
  hasChanges(): boolean;
}

/**
 * Unit of Work implementation with dual-database support
 */
export class UnitOfWork implements IUnitOfWork {
  private context: TransactionContext;
  private repositories: Map<string, Repository<unknown>> = new Map();
  private config: DatabaseConfig;
  private db: admin.firestore.Firestore;

  constructor() {
    this.context = {
      isActive: false,
      hasChanges: false,
    };
    this.config = getDatabaseConfig();
    this.db = admin.firestore();
  }

  /**
   * Begin a new transaction across both databases
   */
  async begin(): Promise<void> {
    if (this.context.isActive) {
      throw new Error('Transaction is already active');
    }

    const startTime = Date.now();

    try {
      // Begin MySQL transaction if needed
      if (this.shouldUseMySQL()) {
        const knex = mysqlService.getKnex();
        this.context.mysqlTrx = await knex.transaction();
      }

      // Begin Firestore batch if needed
      if (this.shouldUseFirestore()) {
        this.context.firestoreBatch = this.db.batch();
      }

      this.context.isActive = true;
      this.context.hasChanges = false;

      DatabaseMetricsCollector.recordMetric({
        operation: 'transaction_begin',
        database: 'dual',
        duration: Date.now() - startTime,
        success: true,
      });

      console.log('📊 Transaction began successfully');
    } catch (error) {
      DatabaseMetricsCollector.recordMetric({
        operation: 'transaction_begin',
        database: 'dual',
        duration: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw new Error(
        `Failed to begin transaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Commit the transaction across both databases
   */
  async commit(): Promise<void> {
    if (!this.context.isActive) {
      throw new Error('No active transaction to commit');
    }

    const startTime = Date.now();
    const errors: string[] = [];

    try {
      // Commit MySQL transaction first (if exists)
      if (this.context.mysqlTrx) {
        try {
          await this.context.mysqlTrx.commit();
          console.log('✅ MySQL transaction committed');
        } catch (error) {
          errors.push(
            `MySQL commit failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }

      // Commit Firestore batch (if exists)
      if (this.context.firestoreBatch) {
        try {
          await this.context.firestoreBatch.commit();
          console.log('✅ Firestore batch committed');
        } catch (error) {
          errors.push(
            `Firestore commit failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );

          // If Firestore fails but MySQL succeeded, we have a problem
          if (!errors.some(e => e.includes('MySQL'))) {
            console.error(
              '🚨 Critical: MySQL committed but Firestore failed - data inconsistency possible',
            );
          }
        }
      }

      // Check if any commits failed
      if (errors.length > 0) {
        throw new Error(`Transaction commit failed: ${errors.join(', ')}`);
      }

      DatabaseMetricsCollector.recordMetric({
        operation: 'transaction_commit',
        database: 'dual',
        duration: Date.now() - startTime,
        success: true,
      });

      console.log('✅ Transaction committed successfully');
    } catch (error) {
      DatabaseMetricsCollector.recordMetric({
        operation: 'transaction_commit',
        database: 'dual',
        duration: Date.now() - startTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    } finally {
      this.cleanup();
    }
  }

  /**
   * Rollback the transaction across both databases
   */
  async rollback(): Promise<void> {
    if (!this.context.isActive) {
      throw new Error('No active transaction to rollback');
    }

    const startTime = Date.now();
    const errors: string[] = [];

    try {
      // Rollback MySQL transaction (if exists)
      if (this.context.mysqlTrx) {
        try {
          await this.context.mysqlTrx.rollback();
          console.log('🔄 MySQL transaction rolled back');
        } catch (error) {
          errors.push(
            `MySQL rollback failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          );
        }
      }

      // Note: Firestore batch operations cannot be rolled back once committed
      // This is a limitation of the dual-database approach
      if (this.context.firestoreBatch) {
        console.log('⚠️ Firestore batch cannot be rolled back - manual cleanup may be required');
      }

      DatabaseMetricsCollector.recordMetric({
        operation: 'transaction_rollback',
        database: 'dual',
        duration: Date.now() - startTime,
        success: errors.length === 0,
        error: errors.length > 0 ? errors.join(', ') : undefined,
      });

      if (errors.length > 0) {
        console.error('⚠️ Transaction rollback had issues:', errors.join(', '));
      } else {
        console.log('✅ Transaction rolled back successfully');
      }
    } finally {
      this.cleanup();
    }
  }

  /**
   * Get a repository for the specified entity type
   */
  getRepository<T>(entityName: string): Repository<T> {
    if (!this.repositories.has(entityName)) {
      // Create a new repository instance for this entity
      // This would be implemented by specific repository classes
      throw new Error(`Repository for entity '${entityName}' not registered`);
    }

    return this.repositories.get(entityName) as Repository<T>;
  }

  /**
   * Register a repository for an entity type
   */
  registerRepository<T>(entityName: string, repository: Repository<T>): void {
    this.repositories.set(entityName, repository as Repository<unknown>);
  }

  /**
   * Check if transaction is active
   */
  isActive(): boolean {
    return this.context.isActive;
  }

  /**
   * Check if transaction has changes
   */
  hasChanges(): boolean {
    return this.context.hasChanges;
  }

  /**
   * Mark transaction as having changes
   */
  markChanged(): void {
    this.context.hasChanges = true;
  }

  /**
   * Get the current transaction context
   */
  getContext(): TransactionContext {
    return { ...this.context };
  }

  /**
   * Clean up transaction resources
   */
  private cleanup(): void {
    this.context.isActive = false;
    this.context.hasChanges = false;
    this.context.mysqlTrx = undefined;
    this.context.firestoreBatch = undefined;
  }

  /**
   * Determine if MySQL should be used based on configuration
   */
  private shouldUseMySQL(): boolean {
    return (
      !this.config.firestoreOnly &&
      (this.config.primaryDatabase === 'mysql' || this.config.dualWriteEnabled)
    );
  }

  /**
   * Determine if Firestore should be used based on configuration
   */
  private shouldUseFirestore(): boolean {
    return (
      this.config.primaryDatabase === 'firestore' ||
      this.config.dualWriteEnabled ||
      this.config.firestoreOnly
    );
  }
}

/**
 * Unit of Work factory for creating instances
 */
export class UnitOfWorkFactory {
  private static instances: Map<string, UnitOfWork> = new Map();

  /**
   * Create a new Unit of Work instance
   */
  static create(id?: string): UnitOfWork {
    const unitOfWork = new UnitOfWork();

    if (id) {
      this.instances.set(id, unitOfWork);
    }

    return unitOfWork;
  }

  /**
   * Get an existing Unit of Work instance by ID
   */
  static get(id: string): UnitOfWork | undefined {
    return this.instances.get(id);
  }

  /**
   * Remove a Unit of Work instance
   */
  static remove(id: string): void {
    this.instances.delete(id);
  }

  /**
   * Get all active Unit of Work instances
   */
  static getActiveInstances(): UnitOfWork[] {
    return Array.from(this.instances.values()).filter(uow => uow.isActive());
  }

  /**
   * Clean up inactive instances
   */
  static cleanup(): void {
    const entries = Array.from(this.instances.entries());
    for (const [id, uow] of entries) {
      if (!uow.isActive()) {
        this.instances.delete(id);
      }
    }
  }
}

/**
 * Utility function to execute operations within a Unit of Work
 */
export async function withUnitOfWork<T>(
  operation: (uow: UnitOfWork) => Promise<T>,
  autoCommit: boolean = true,
): Promise<T> {
  const uow = UnitOfWorkFactory.create();

  try {
    await uow.begin();
    const result = await operation(uow);

    if (autoCommit && uow.hasChanges()) {
      await uow.commit();
    } else if (autoCommit) {
      await uow.rollback(); // No changes, clean up
    }

    return result;
  } catch (error) {
    if (uow.isActive()) {
      await uow.rollback();
    }
    throw error;
  }
}
