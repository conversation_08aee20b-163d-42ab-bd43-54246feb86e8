/**
 * Database Configuration for Dual-Database System
 * Controls behavior between MySQL and Firestore databases
 */

export interface DatabaseConfig {
  primaryDatabase: 'mysql' | 'firestore';
  enableFallback: boolean;
  dualWriteEnabled: boolean;
  fallbackOnReadError: boolean;
  mysqlFirst: boolean;
  firestoreOnly: boolean;
  performanceLogging: boolean;
  connectionTimeout: number;
  retryAttempts: number;
}

export interface ReadOptions {
  forceMySQL?: boolean; // Skip fallback, MySQL only
  forceFirestore?: boolean; // Skip MySQL, Firestore only
  skipFallback?: boolean; // Don't fallback if MySQL fails
  timeout?: number; // Query timeout in milliseconds
}

export interface WriteOptions {
  dualWrite?: boolean; // Write to both databases
  skipMySQL?: boolean; // Skip MySQL write
  skipFirestore?: boolean; // Skip Firestore write
  transactional?: boolean; // Use transaction across both databases
  generateEntityId?: (entity: Record<string, unknown>) => string | number | undefined; // Generate entity ID
}

/**
 * Default database configuration - can be overridden via environment variables
 */
const getDefaultConfig = (): DatabaseConfig => ({
  primaryDatabase: (process.env.PRIMARY_DATABASE as 'mysql' | 'firestore') || 'mysql',
  enableFallback: process.env.ENABLE_FALLBACK !== 'false',
  dualWriteEnabled: process.env.DUAL_WRITE_ENABLED !== 'false',
  fallbackOnReadError: process.env.FALLBACK_ON_READ_ERROR !== 'false',
  mysqlFirst: process.env.MYSQL_FIRST !== 'false',
  firestoreOnly: process.env.FIRESTORE_ONLY === 'true',
  performanceLogging: process.env.DB_PERFORMANCE_LOGGING === 'true',
  connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT || '10000'),
  retryAttempts: parseInt(process.env.DB_RETRY_ATTEMPTS || '3'),
});

/**
 * Current database configuration instance
 */
let currentConfig: DatabaseConfig = getDefaultConfig();

/**
 * Get the current database configuration
 */
export const getDatabaseConfig = (): DatabaseConfig => {
  return { ...currentConfig };
};

/**
 * Update database configuration at runtime
 */
export const updateDatabaseConfig = (updates: Partial<DatabaseConfig>): void => {
  currentConfig = { ...currentConfig, ...updates };
  console.log('🔧 Database configuration updated:', updates);
};

/**
 * Reset database configuration to defaults
 */
export const resetDatabaseConfig = (): void => {
  currentConfig = getDefaultConfig();
  console.log('🔄 Database configuration reset to defaults');
};

/**
 * Feature flag helpers
 */
export const DatabaseFeatures = {
  isFirestoreOnly: (): boolean => currentConfig.firestoreOnly,
  isMySQLPrimary: (): boolean =>
    currentConfig.primaryDatabase === 'mysql' && !currentConfig.firestoreOnly,
  isFirestorePrimary: (): boolean => currentConfig.primaryDatabase === 'firestore',
  isDualWriteEnabled: (): boolean => currentConfig.dualWriteEnabled && !currentConfig.firestoreOnly,
  isFallbackEnabled: (): boolean => currentConfig.enableFallback,
  shouldLogPerformance: (): boolean => currentConfig.performanceLogging,
};

/**
 * Environment-based configuration validation
 */
export const validateDatabaseConfig = (): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate primary database setting
  if (!['mysql', 'firestore'].includes(currentConfig.primaryDatabase)) {
    errors.push('PRIMARY_DATABASE must be either "mysql" or "firestore"');
  }

  // Validate timeout values
  if (currentConfig.connectionTimeout < 1000) {
    warnings.push('DB_CONNECTION_TIMEOUT is less than 1000ms, may cause connection issues');
  }

  // Validate retry attempts
  if (currentConfig.retryAttempts < 1 || currentConfig.retryAttempts > 10) {
    warnings.push('DB_RETRY_ATTEMPTS should be between 1 and 10');
  }

  // Logical configuration checks
  if (currentConfig.firestoreOnly && currentConfig.primaryDatabase === 'mysql') {
    warnings.push('FIRESTORE_ONLY is enabled but PRIMARY_DATABASE is mysql');
  }

  if (!currentConfig.enableFallback && currentConfig.fallbackOnReadError) {
    warnings.push('FALLBACK_ON_READ_ERROR is enabled but ENABLE_FALLBACK is disabled');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

/**
 * Get read strategy based on configuration and options
 */
export const getReadStrategy = (
  options: ReadOptions = {},
): {
  useMySQL: boolean;
  useFirestore: boolean;
  fallbackEnabled: boolean;
} => {
  const config = getDatabaseConfig();

  // Handle force options first
  if (options.forceMySQL) {
    return { useMySQL: true, useFirestore: false, fallbackEnabled: false };
  }

  if (options.forceFirestore) {
    return { useMySQL: false, useFirestore: true, fallbackEnabled: false };
  }

  // Handle firestore-only mode
  if (config.firestoreOnly) {
    return { useMySQL: false, useFirestore: true, fallbackEnabled: false };
  }

  // Handle primary database selection
  const primaryIsMySQL = config.primaryDatabase === 'mysql';
  const fallbackEnabled = config.enableFallback && !options.skipFallback;

  if (primaryIsMySQL) {
    return {
      useMySQL: true,
      useFirestore: fallbackEnabled,
      fallbackEnabled,
    };
  } else {
    return {
      useMySQL: fallbackEnabled,
      useFirestore: true,
      fallbackEnabled,
    };
  }
};

/**
 * Get write strategy based on configuration and options
 */
export const getWriteStrategy = (
  options: WriteOptions = {},
): {
  writeToMySQL: boolean;
  writeToFirestore: boolean;
  useTransaction: boolean;
} => {
  const config = getDatabaseConfig();

  // Handle skip options
  if (options.skipMySQL && options.skipFirestore) {
    throw new Error('Cannot skip both MySQL and Firestore writes');
  }

  if (options.skipMySQL && !options.skipFirestore) {
    return { writeToMySQL: false, writeToFirestore: true, useTransaction: false };
  }

  if (!options.skipMySQL && options.skipFirestore) {
    return { writeToMySQL: true, writeToFirestore: false, useTransaction: false };
  }

  // Handle firestore-only mode
  if (config.firestoreOnly) {
    return { writeToMySQL: false, writeToFirestore: true, useTransaction: false };
  }

  // Handle dual-write configuration
  const dualWrite = options.dualWrite ?? config.dualWriteEnabled;
  const useTransaction = options.transactional ?? dualWrite;

  if (dualWrite) {
    return {
      writeToMySQL: true,
      writeToFirestore: true,
      useTransaction,
    };
  }

  // Single database write based on primary
  const writeToMySQL = config.primaryDatabase === 'mysql';
  return {
    writeToMySQL,
    writeToFirestore: !writeToMySQL,
    useTransaction: false,
  };
};

/**
 * Performance monitoring interface
 */
export interface DatabaseMetrics {
  operation: string;
  database: 'mysql' | 'firestore' | 'dual';
  duration: number;
  success: boolean;
  error?: string;
  fallbackUsed?: boolean;
}

/**
 * Performance metrics collector
 */
export class DatabaseMetricsCollector {
  private static metrics: DatabaseMetrics[] = [];
  private static maxMetrics: number = 1000;

  static recordMetric(metric: DatabaseMetrics): void {
    if (!DatabaseFeatures.shouldLogPerformance()) return;

    this.metrics.push({
      ...metric,
      timestamp: Date.now(),
    } as DatabaseMetrics & { timestamp: number });

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log significant events
    if (!metric.success || metric.fallbackUsed) {
      console.log('📊 Database Metric:', metric);
    }
  }

  static getMetrics(): DatabaseMetrics[] {
    return [...this.metrics];
  }

  static getMetricsSummary(): {
    totalOperations: number;
    mysqlOperations: number;
    firestoreOperations: number;
    successRate: number;
    averageDuration: number;
    fallbackRate: number;
  } {
    const total = this.metrics.length;
    if (total === 0) {
      return {
        totalOperations: 0,
        mysqlOperations: 0,
        firestoreOperations: 0,
        successRate: 0,
        averageDuration: 0,
        fallbackRate: 0,
      };
    }

    const mysqlOps = this.metrics.filter(m => m.database === 'mysql').length;
    const firestoreOps = this.metrics.filter(m => m.database === 'firestore').length;
    const successfulOps = this.metrics.filter(m => m.success).length;
    const fallbackOps = this.metrics.filter(m => m.fallbackUsed).length;
    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);

    return {
      totalOperations: total,
      mysqlOperations: mysqlOps,
      firestoreOperations: firestoreOps,
      successRate: (successfulOps / total) * 100,
      averageDuration: totalDuration / total,
      fallbackRate: (fallbackOps / total) * 100,
    };
  }

  static clearMetrics(): void {
    this.metrics = [];
  }
}
