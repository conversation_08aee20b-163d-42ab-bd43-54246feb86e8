import mysql from 'mysql2/promise';

export function replaceScriptVariables(
  query: string,
  values: Record<string, unknown> | unknown[],
): string {
  // If no values provided, return the original query unchanged
  if (!values) {
    return query;
  }

  // ---------------------------------------------------------------------
  // 1. Handle positional placeholders (`?`) when `values` is an array
  // ---------------------------------------------------------------------
  // When the user calls `pool.query(sql, paramsArray)` the `queryFormat`
  // function (this one) receives that params array. In this scenario we
  // should **not** do the named replacement logic below – instead we want
  // the standard mysql2 formatting behaviour so that every `?` gets
  // safely escaped. We achieve this by delegating to `mysql.format`, which
  // replicates mysql2's default behaviour.
  // ---------------------------------------------------------------------
  if (Array.isArray(values)) {
    // `mysql.format` returns the fully-formatted query with the `?`
    // placeholders replaced using the provided array values and proper
    // escaping. By returning here we bypass the named-parameter logic that
    // follows, preventing the raw `?` tokens from leaking through to the
    // database engine (which causes the SQL syntax error observed).
    return mysql.format(query, values);
  }

  // ---------------------------------------------------------------------
  // 2. Named placeholder replacement ( :variable ) for object parameters
  // ---------------------------------------------------------------------
  const typedValues = values as Record<string, unknown>;

  const replace = (script: string): string =>
    script.replace(/:(\w+)/g, (txt: string, key: string): string => {
      if (Object.prototype.hasOwnProperty.call(typedValues, key)) {
        const value = typedValues[key];

        // Handle arrays specially - format as comma-separated values without outer quotes
        if (Array.isArray(value)) {
          return value.map(item => mysql.escape(item)).join(',');
        }

        // Handle ValueObject instances by converting to string
        const actualValue =
          value && typeof value === 'object' && 'toString' in value ? String(value) : value;

        // Use mysql2's escape function for proper SQL escaping
        return mysql.escape(actualValue);
      }

      // Support for literal replacements (key.literal)
      const literalKey = `${key}.literal`;
      if (Object.prototype.hasOwnProperty.call(typedValues, literalKey)) {
        const literalScript = typedValues[literalKey] as string;
        return replace(literalScript);
      }

      return txt;
    });

  return replace(query);
}

export function formatQuery(query: string, values?: Record<string, unknown> | unknown[]): string {
  if (!values) {
    return query;
  }
  return replaceScriptVariables(query, values as Record<string, unknown> | unknown[]);
}
