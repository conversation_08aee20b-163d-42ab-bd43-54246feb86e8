import mysql from 'mysql2/promise';
import { knex, Knex } from 'knex';
import { getMysql2Config, getKnexConfig } from './config';
import { formatQuery } from './utils';

export class MySQLService {
  private static instance: MySQLService;
  private pool: mysql.Pool | null = null;
  private knexInstance: Knex | null = null;

  private constructor() {}

  public static getInstance(): MySQLService {
    if (!MySQLService.instance) {
      MySQLService.instance = new MySQLService();
    }
    return MySQLService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      // Initialize connection pool
      this.pool = mysql.createPool(getMysql2Config());

      // Initialize Knex instance
      this.knexInstance = knex(getKnexConfig());

      // Test the connection
      await this.testConnection();

      console.log('✅ MySQL database connection established successfully');
    } catch (error) {
      console.error('❌ Failed to initialize MySQL connection:', error);
      throw error;
    }
  }

  private async testConnection(): Promise<void> {
    if (!this.pool) {
      throw new Error('Database pool not initialized');
    }

    const connection = await this.pool.getConnection();
    try {
      await connection.ping();
      console.log('✅ Database connection test successful');
    } finally {
      connection.release();
    }
  }

  public getPool(): mysql.Pool {
    if (!this.pool) {
      throw new Error('Database pool not initialized. Call initialize() first.');
    }
    return this.pool;
  }

  public getKnex(): Knex {
    if (!this.knexInstance) {
      throw new Error('Knex instance not initialized. Call initialize() first.');
    }
    return this.knexInstance;
  }

  // Enhanced query method supporting both traditional ? placeholders and :variable syntax
  public async query<T = unknown>(
    sql: string,
    params?: unknown[] | Record<string, unknown>,
  ): Promise<T[]> {
    const pool = this.getPool();

    // ENHANCED DEBUGGING: Log SQL details before execution
    const sqlLength = sql.length;
    const questionMarkCount = (sql.match(/\?/g) || []).length;
    const paramCount = Array.isArray(params)
      ? params.length
      : params
        ? Object.keys(params).length
        : 0;

    console.log('🔍 MySQL Service Query Details:', {
      sqlLength,
      questionMarkCount,
      paramCount,
      sqlPreview: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
      sqlEnd: sql.length > 200 ? '...' + sql.substring(sql.length - 100) : '',
      hasLimitParameter: sql.includes('LIMIT ?'),
      paramsPreview: Array.isArray(params) ? params.slice(0, 5) : params,
    });

    // CRITICAL DEBUGGING: Log SQL for phone_number queries
    if (sql.includes('phone_number')) {
      console.log('📋 COMPLETE SQL (for phone_number queries):');
      console.log(sql);
      console.log('📋 ALL PARAMETERS:', params);

      // Simplified phone_number condition validation
      const phoneRefs = (sql.match(/phone_number/g) || []).length;
      console.log('📋 Total phone_number references:', phoneRefs);
      if (phoneRefs > 0) {
        console.log('📋 Phone number filtering enabled in query');
      }

      // Check if SQL ends properly
      const sqlTrimmed = sql.trim();
      const endsWithLimit = sqlTrimmed.endsWith('LIMIT ?');
      const endsWithOffset = sqlTrimmed.endsWith('OFFSET ?');
      const endsCorrectly = endsWithLimit || endsWithOffset;

      console.log('📋 SQL ending check:', {
        endsWithLimit,
        endsWithOffset,
        endsCorrectly,
        lastChars: sqlTrimmed.substring(sqlTrimmed.length - 50),
      });
    }

    // If params is an object, use custom variable replacement
    if (params && typeof params === 'object' && !Array.isArray(params)) {
      const formattedSql = formatQuery(sql, params);
      const [rows] = await pool.execute(formattedSql);
      return rows as T[];
    }

    // Check if the query contains LIMIT with parameters - if so, use query() instead of execute()
    // This is because MySQL prepared statements don't allow parameters in LIMIT clauses
    const hasLimitParameter = sql.includes('LIMIT ?');

    let rows: unknown;

    // Convert params to array format for consistent handling
    const paramArray = Array.isArray(params) ? params : params ? [params] : [];

    try {
      // If no parameters, execute the SQL directly (fully constructed query)
      if (paramArray.length === 0) {
        console.log('🔧 Using pool.query() for fully constructed SQL (no parameters)');
        [rows] = await pool.query(sql);
      } else if (hasLimitParameter) {
        console.log(
          '🔧 Using pool.query() due to LIMIT parameter limitation in prepared statements',
        );
        [rows] = await pool.query(sql, paramArray);
      } else {
        // Otherwise use traditional array parameters with execute (prepared statements)
        [rows] = await pool.execute(sql, paramArray);
      }
    } catch (error) {
      console.error('🚨 MySQL Query Failed:', {
        sqlLength: sql.length,
        sqlPreview: sql.substring(0, 300),
        sqlEnd: sql.substring(sql.length - 200),
        paramCount,
        questionMarkCount,
        params: paramArray,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }

    return rows as T[];
  }

  public async queryFirst<T = unknown>(
    sql: string,
    params?: unknown[] | Record<string, unknown>,
  ): Promise<T | null> {
    const results = await this.query<T>(sql, params);
    return results.length > 0 ? results[0] : null;
  }

  // New method specifically for named parameter queries
  public async queryWithVariables<T = unknown>(
    sql: string,
    variables: Record<string, unknown>,
  ): Promise<T[]> {
    return this.query<T>(sql, variables);
  }

  public async queryFirstWithVariables<T = unknown>(
    sql: string,
    variables: Record<string, unknown>,
  ): Promise<T | null> {
    return this.queryFirst<T>(sql, variables);
  }

  public async transaction<T>(callback: (trx: Knex.Transaction) => Promise<T>): Promise<T> {
    const knexInstance = this.getKnex();
    return await knexInstance.transaction(callback);
  }

  public async runMigrations(): Promise<void> {
    const knexInstance = this.getKnex();
    try {
      await knexInstance.migrate.latest();
      console.log('✅ Database migrations completed successfully');
    } catch (error) {
      console.error('❌ Database migration failed:', error);
      throw error;
    }
  }

  public async rollbackMigrations(steps?: number): Promise<void> {
    const knexInstance = this.getKnex();
    try {
      if (steps) {
        // Roll back specific number of steps
        for (let i = 0; i < steps; i++) {
          await knexInstance.migrate.rollback();
        }
      } else {
        await knexInstance.migrate.rollback();
      }
      console.log('✅ Database rollback completed successfully');
    } catch (error) {
      console.error('❌ Database rollback failed:', error);
      throw error;
    }
  }

  public async checkConnection(): Promise<boolean> {
    try {
      await this.testConnection();
      return true;
    } catch (error) {
      console.error('Database connection check failed:', error);
      return false;
    }
  }

  public async close(): Promise<void> {
    try {
      if (this.knexInstance) {
        await this.knexInstance.destroy();
        this.knexInstance = null;
      }

      if (this.pool) {
        await this.pool.end();
        this.pool = null;
      }

      console.log('✅ Database connections closed successfully');
    } catch (error) {
      console.error('❌ Error closing database connections:', error);
      throw error;
    }
  }

  // Health check method for monitoring
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connectionPool: boolean;
      knexInstance: boolean;
      connectivity: boolean;
    };
  }> {
    const details = {
      connectionPool: !!this.pool,
      knexInstance: !!this.knexInstance,
      connectivity: false,
    };

    try {
      details.connectivity = await this.checkConnection();
    } catch {
      details.connectivity = false;
    }

    const status = Object.values(details).every(Boolean) ? 'healthy' : 'unhealthy';

    return { status, details };
  }
}

// Export singleton instance
export const mysqlService = MySQLService.getInstance();
