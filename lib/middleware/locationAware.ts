import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUserWithLocationContext } from '@/utils/firebase-admin';
import { Location } from '@/models/Location';

export interface LocationAwareRequest extends NextApiRequest {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    clinicId: number | null;
    locationIds?: string[];
    currentLocationId?: string;
  };
  currentLocation?: Location;
  availableLocations?: Location[];
}

/**
 * Middleware to add location context to API requests
 * This ensures all data operations are scoped to the user's current location
 */
export const withLocationContext = (
  handler: (req: LocationAwareRequest, res: NextApiResponse) => Promise<void> | void,
) => {
  return async (req: LocationAwareRequest, res: NextApiResponse) => {
    try {
      // Get user with location context
      const authResult = await verifyAuthAndGetUserWithLocationContext(req);

      if (!authResult) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized',
        });
      }

      // Add user and location context to request
      req.user = authResult.user;
      req.currentLocation = authResult.currentLocation;
      req.availableLocations = authResult.availableLocations;

      // Call the original handler
      return handler(req, res);
    } catch (error) {
      console.error('Location context middleware error:', error);
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  };
};

/**
 * Helper function to ensure user has access to a specific location
 */
export const requireLocationAccess = (req: LocationAwareRequest, locationId: string): boolean => {
  if (!req.user || !req.availableLocations) {
    return false;
  }

  // Super admins have access to all locations
  if (req.user.role === 'SUPER_ADMIN') {
    return true;
  }

  // Check if user has access to the requested location
  return req.availableLocations.some(location => location.id === locationId);
};

/**
 * Helper function to get current location ID from request
 */
export const getCurrentLocationId = (req: LocationAwareRequest): string | null => {
  return req.currentLocation?.id || null;
};

/**
 * Helper function to filter query parameters by current location
 */
export const addLocationFilter = (
  req: LocationAwareRequest,
  queryParams: Record<string, unknown>,
): Record<string, unknown> => {
  const currentLocationId = getCurrentLocationId(req);

  if (currentLocationId && !queryParams.locationId) {
    return {
      ...queryParams,
      locationId: currentLocationId,
    };
  }

  return queryParams;
};
