import type { NextApiRequest, NextApiResponse } from 'next';
import { getRepositories } from '../repositories';

/**
 * Ensures the MySQL pool is initialized before handling the request.
 *
 * Usage (in API route):
 *   await ensureDbInitialized();
 */
export const ensureDbInitialized = async (): Promise<void> => {
  await getRepositories().initialize();
};

/**
 * Express-like middleware wrapper for Next.js API routes if preferred.
 *
 * Example:
 *   export default withDbInit(async (req, res) => { ... });
 */
export const withDbInit = <T = unknown>(
  handler: (req: NextApiRequest, res: NextApiResponse<T>) => unknown | Promise<unknown>,
) => {
  return async (req: NextApiRequest, res: NextApiResponse<T>): Promise<void> => {
    try {
      await ensureDbInitialized();
      await handler(req, res);
    } catch (error) {
      console.error('Database initialization failed:', error);
      res.status(500).json({ message: 'Database initialization failed' } as unknown as T);
    }
  };
};
