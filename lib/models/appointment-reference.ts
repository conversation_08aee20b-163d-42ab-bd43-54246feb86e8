/**
 * AppointmentReference model
 * Represents a reference to an appointment in an external provider system
 */
export interface AppointmentReference {
  id: string; // Firestore document ID
  provider: string; // Provider name (e.g., "nextech")
  externalId: string; // ID in the provider's system
  providerId: string; // ID in the provider's system (kept for backward compatibility)
  patientId: string; // ID of the patient in the provider's system
  patientName?: string; // Patient's full name
  practitionerId: string; // ID of the practitioner in the provider's system
  practitionerName?: string; // Practitioner's full name
  locationId: string; // ID of the location in the provider's system
  locationName?: string; // Location's name
  startTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  endTime: string; // ISO format: YYYY-MM-DDTHH:MM:SS
  type: string; // Appointment type ID
  status: string; // Appointment status (e.g., "booked", "cancelled")
  reason?: string; // Reason for the appointment
  notes?: string; // Additional notes
  createdAt: Date;
  updatedAt: Date;
}
