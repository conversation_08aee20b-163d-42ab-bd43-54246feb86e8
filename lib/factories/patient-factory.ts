import { PatientReferenceService } from '../services/patient-reference-service';
import { PatientService } from '../services/patient-service';
import { PatientCoordinatorService } from '../services/patient-coordinator-service';

/**
 * Factory for patient-related services
 * Implements the singleton pattern to ensure only one instance of each service exists
 */
export class PatientFactory {
  private static instance: PatientFactory;
  private patientReferenceService: PatientReferenceService | null = null;
  private patientService: PatientService | null = null;
  private patientCoordinatorService: PatientCoordinatorService | null = null;

  /**
   * Get the singleton instance of the PatientFactory
   * @returns The PatientFactory instance
   */
  public static getInstance(): PatientFactory {
    if (!PatientFactory.instance) {
      PatientFactory.instance = new PatientFactory();
    }
    return PatientFactory.instance;
  }

  /**
   * Private constructor to prevent direct instantiation
   */
  private constructor() {}

  /**
   * Get the PatientReferenceService instance
   * @returns The PatientReferenceService instance
   */
  public getPatientReferenceService(): PatientReferenceService {
    if (!this.patientReferenceService) {
      this.patientReferenceService = new PatientReferenceService();
    }
    return this.patientReferenceService;
  }

  /**
   * Get the PatientService instance
   * @returns The PatientService instance
   */
  public getPatientService(): PatientService {
    if (!this.patientService) {
      this.patientService = new PatientService();
    }
    return this.patientService;
  }

  /**
   * Get the PatientCoordinatorService instance
   * @returns The PatientCoordinatorService instance
   */
  public getPatientCoordinatorService(): PatientCoordinatorService {
    if (!this.patientCoordinatorService) {
      this.patientCoordinatorService = new PatientCoordinatorService(
        this.getPatientReferenceService(),
        this.getPatientService(),
      );
    }
    return this.patientCoordinatorService;
  }
}

// Export a default instance for convenience
export const patientFactory = PatientFactory.getInstance();
