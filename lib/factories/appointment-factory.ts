import { AppointmentReferenceService } from '../services/appointment-reference-service';

/**
 * Factory for creating appointment-related services
 */
class AppointmentFactory {
  private appointmentReferenceService: AppointmentReferenceService | null = null;

  /**
   * Get the appointment reference service
   * @returns The appointment reference service
   */
  getAppointmentReferenceService(): AppointmentReferenceService {
    if (!this.appointmentReferenceService) {
      this.appointmentReferenceService = new AppointmentReferenceService();
    }
    return this.appointmentReferenceService;
  }
}

// Export a singleton instance
export const appointmentFactory = new AppointmentFactory();
