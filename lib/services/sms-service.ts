import twilio from 'twilio';
import logger from '../external-api/v2/utils/logger';
import { discordClient } from './discord.client';

const { TWILIO_NOTIFY_SERVICE_SID = '' } = process.env;

/**
 * Service for sending SMS messages using Twilio
 */
export class SmsService {
  private client: twilio.Twilio | null = null;
  private fromNumber: string = '';
  private initialized: boolean = false;

  constructor() {
    // Initialize in constructor for better testability
    try {
      this.initialize();
    } catch (error) {
      // Silently fail initialization in constructor
      // This allows the service to be imported without throwing errors
      // The error will be thrown when trying to use the service
      logger.debug(
        'Twilio client initialization deferred: ' +
          (error instanceof Error ? error.message : String(error)),
      );
    }
  }

  /**
   * Initialize the Twilio client
   * @throws Error if Twilio credentials are missing
   */
  private initialize(): void {
    if (this.initialized) return;

    // Initialize Twilio client with credentials from environment variables
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '';

    if (!accountSid || !authToken || !this.fromNumber) {
      throw new Error('Missing Twilio credentials in environment variables');
    }

    this.client = twilio(accountSid, authToken);
    this.initialized = true;

    logger.debug('Twilio client initialized successfully');
  }

  /**
   * Send a bulk SMS message to multiple numbers
   * @param to Array of phone numbers to send the message to
   * @param message Message content
   * @param params Optional parameters
   * @returns Array of message SID if successful
   */
  async sendBulkSms(
    to: string[],
    message: string,
    params?: {
      dryRun?: boolean;
    },
  ): Promise<void> {
    if (!to.length) {
      logger.info({ context: 'sendBulkSms' }, 'No to numbers provided');
      return;
    }
    if (!message) {
      logger.info({ context: 'sendBulkSms' }, 'No message provided');
      return;
    }

    const { dryRun = false } = params || {};
    logger.info(
      { context: 'sendBulkSms', toNumbers: to.length, message, dryRun },
      'Sending bulk SMS',
    );
    if (dryRun) {
      logger.info({ context: 'sendBulkSms' }, 'Dry run enabled');
      await discordClient.sendSmsChannelMessage(
        `**SMS**\n**TO:**\n${to.map(n => `\`${n}\``).join(', ')}\n**MESSAGE:**\n${message}`,
      );
      return;
    }

    // Initialize the client if not already initialized
    this.initialize();

    if (!this.client) {
      throw new Error('Twilio client not initialized');
    }

    const service = this.client.notify.v1.services(TWILIO_NOTIFY_SERVICE_SID);
    const batchSize = 10000;
    const totalBatches = Math.ceil(to.length / batchSize);

    logger.info(
      { context: 'sendBulkSms', totalNumbers: to.length, batchSize, totalBatches },
      'Processing bulk SMS in batches',
    );

    // Process each batch
    for (let i = 0; i < totalBatches; i++) {
      const start = i * batchSize;
      const end = Math.min(start + batchSize, to.length);
      const batch = to.slice(start, end);

      logger.info(
        { context: 'sendBulkSms', batch: i + 1, totalBatches, batchSize: batch.length },
        'Processing batch',
      );

      try {
        const toBinding = batch.map(n => JSON.stringify({ binding_type: 'sms', address: n }));
        const result = await service.notifications.create({
          toBinding,
          body: message,
        });

        logger.info(
          { context: 'sendBulkSms', batch: i + 1, result },
          'Batch SMS sent successfully',
        );
      } catch (error) {
        logger.error({ context: 'sendBulkSms', batch: i + 1, error }, 'Error sending batch SMS');
        throw new Error(
          `Failed to send SMS batch ${i + 1}: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }

    logger.info({ context: 'sendBulkSms', totalBatches }, 'All bulk SMS batches sent successfully');
  }

  /**
   * Send an SMS message
   * @param to Phone number to send the message to
   * @param message Message content
   * @returns Promise with the message SID if successful
   */
  async sendSms(
    to: string,
    message: string,
    params?: {
      dryRun?: boolean;
    },
  ): Promise<string> {
    const { dryRun = false } = params || {};
    try {
      // Validate phone number format (basic validation)
      if (!this.isValidPhoneNumber(to)) {
        throw new Error(`Invalid phone number format: ${to}`);
      }

      // Initialize the client if not already initialized
      this.initialize();

      // Send the message using Twilio
      if (!this.client) {
        throw new Error('Twilio client not initialized');
      }

      logger.info(
        {
          context: `SmsService.sendSms`,
          to: `***${to.slice(-4)}`, // mask the last 4 digits of the phone number
          message,
          dryRun,
        },
        'Sending SMS',
      );
      if (dryRun) {
        await discordClient.sendSmsChannelMessage(
          `**SMS**\n**TO:** ${to}\n**MESSAGE:** ${message}`,
        );
        return 'DRY_RUN_MESSAGE_SID';
      }

      const result = await this.client.messages.create({
        body: message,
        from: this.fromNumber,
        to: to,
      });

      logger.info(`SMS sent successfully to ${to}, SID: ${result.sid}`);
      return result.sid;
    } catch (error) {
      logger.error('Error sending SMS:', error);
      throw new Error(
        `Failed to send SMS: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }

  /**
   * Send an appointment confirmation SMS
   * @param to Phone number to send the message to
   * @param appointmentDetails Details about the appointment
   * @returns Promise with the message SID if successful
   */
  async sendAppointmentConfirmation(
    to: string,
    appointmentDetails: {
      patientName: string;
      date: string;
      time: string;
      location: string;
      locationAddress?: string;
      provider?: string;
      contactPhone?: string;
    },
    params?: {
      dryRun?: boolean;
    },
  ): Promise<string> {
    const { patientName, date, time, location, locationAddress, provider, contactPhone } =
      appointmentDetails;
    logger.debug(
      {
        context: `SmsService.sendAppointmentConfirmation`,
        appointmentDetails,
        params,
      },
      'Sending appointment confirmation',
    );

    let message = `Hello ${patientName}, this is a confirmation for your appointment on ${date} at ${time} at the ${location}`;

    if (locationAddress) {
      message += ` at ${locationAddress}`;
    }

    if (provider) {
      message += ` with ${provider}`;
    }

    if (contactPhone) {
      message += `. Call us at ${contactPhone} if you need to make any changes.`;
    } else {
      message += `. Don't reply to this message, instead call us if you need to cancel or reschedule.`;
    }

    return this.sendSms(to, message, params);
  }

  /**
   * Send an appointment cancellation confirmation SMS
   * @param to Phone number to send the message to
   * @param appointmentDetails Details about the appointment
   * @returns Promise with the message SID if successful
   */
  async sendAppointmentCancellationConfirmation(
    to: string,
    appointmentDetails: {
      patientName: string;
      date: string;
      time: string;
      location: string;
      locationAddress?: string;
      provider?: string;
      contactPhone?: string;
    },
  ): Promise<string> {
    const { patientName, date, time, location, locationAddress, provider, contactPhone } =
      appointmentDetails;

    let message = `Hello ${patientName}, this is a confirmation that your appointment on ${date} at ${time} at the ${location}`;

    if (locationAddress) {
      message += ` at ${locationAddress}`;
    }

    if (provider) {
      message += ` with ${provider}`;
    }

    message += ` has been cancelled`;

    if (contactPhone) {
      message += `. Call us at ${contactPhone} if you need to reschedule.`;
    } else {
      message += `. Don't reply to this message, instead call us if you need to reschedule.`;
    }

    return this.sendSms(to, message);
  }

  /**
   * Send new patient form link SMS
   * @param to Phone number to send the message to
   * @param patientName Name of the patient
   * @param params Optional parameters
   * @returns Promise with the message SID if successful
   */
  async sendNewPatientFormLink(
    to: string,
    patientName: string,
    params?: {
      dryRun?: boolean;
    },
  ): Promise<string> {
    const message = `Hello ${patientName}, as a new patient, please fill out this online form before arriving: https://www.uretina.com/new-patient-form/`;

    logger.info(
      {
        context: `SmsService.sendNewPatientFormLink`,
        to: `***${to.slice(-4)}`, // mask the last 4 digits of the phone number
        patientName,
      },
      'Sending new patient form link SMS',
    );

    return this.sendSms(to, message, params);
  }

  async sendNotShowReminder(
    to: string,
    appointmentDetails: {
      patientName: string;
      dateTime: string;
      location: string;
      locationPhone: string;
      locationAddress?: string;
    },
    params?: {
      dryRun?: boolean;
    },
  ): Promise<string> {
    logger.info(
      {
        context: `SmsService.sendNotShowReminder`,
        to: `***${to.slice(-4)}`, // mask the last 4 digits of the phone number
        appointmentDetails,
      },
      'Sending not show reminder',
    );

    const { patientName, dateTime, location, locationPhone, locationAddress } = appointmentDetails;
    let message = `Hello ${patientName}, you've missed your appointment on ${dateTime} at the ${location}. Please contact us at ${locationPhone} to reschedule.`;
    if (locationAddress) {
      message += ` at ${locationAddress}`;
    }
    const messageSid = await this.sendSms(to, message, params);
    logger.info(
      {
        context: `SmsService.sendNotShowReminder`,
        messageSid,
      },
      'Not show reminder sent',
    );

    return messageSid;
  }

  /**
   * Basic validation for phone numbers
   * @param phoneNumber Phone number to validate
   * @returns Boolean indicating if the phone number is valid
   */
  isValidPhoneNumber(phoneNumber: string): boolean {
    // Accepts formats like:
    // +**********, **********, (*************, 12252542523
    return /^(\+?\d{1,3})?[-\s.]?\(?(\d{3})\)?[-\s.]?(\d{3})[-\s.]?(\d{4})$/.test(phoneNumber);
  }
}

// Export a singleton instance
export const smsService = new SmsService();
