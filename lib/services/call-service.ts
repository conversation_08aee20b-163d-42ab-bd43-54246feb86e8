import twilio from 'twilio';
import logger from '../external-api/v2/utils/logger';
import {
  NOT_SHOW_REMINDER_CALL_DRU_RUN,
  NOT_SHOW_REMINDER_CALL_REDIRECT_NUMBER,
  URGENT_AFTER_HOURS_DOCTOR_CALL_DOCTOR_REDIRECT_NUMBER,
  URGENT_AFTER_HOURS_DOCTOR_CALL_DRU_RUN,
  URGENT_AFTER_HOURS_DOCTOR_CALL_PATIENT_REDIRECT_NUMBER,
  URGENT_AFTER_HOURS_DOCTOR_CALL_SANDBOX_MODE,
} from '@/app-config';
import { AppLinkBuilder } from '@/utils/app-link.builder';

const {
  TWILIO_RELAY_TRANSCRIPTION_PROVIDER,
  TWILIO_RELAY_SPEECH_MODEL,
  TWILIO_RELAY_TTS_PROVIDER,
  TWIL<PERSON>_RELAY_VOICE,
  TWILIO_REDIRECT_TWIML_URL = 'https://handler.twilio.com/twiml/EH300b32076aeaf6cd9869ee537f2218e8',
} = process.env;

/**
 * Service for sending SMS messages using Twilio
 */
export class CallService {
  private client: twilio.Twilio | null = null;
  private fromNumber: string = '';
  private initialized: boolean = false;

  constructor() {
    // Initialize in constructor for better testability
    try {
      this.initialize();
    } catch (error) {
      // Silently fail initialization in constructor
      // This allows the service to be imported without throwing errors
      // The error will be thrown when trying to use the service
      logger.debug(
        'Twilio client initialization deferred: ' +
          (error instanceof Error ? error.message : String(error)),
      );
    }
  }

  private createRedirectToAnyNumberTwimlUrl(phoneNumber: string): string {
    const queryParams = new URLSearchParams({ phoneNumber });
    return `${TWILIO_REDIRECT_TWIML_URL}?${queryParams}`;
  }

  /**
   * Initialize the Twilio client
   * @throws Error if Twilio credentials are missing
   */
  private initialize(): void {
    if (this.initialized) return;

    // Initialize Twilio client with credentials from environment variables
    const accountSid = process.env.TWILIO_ACCOUNT_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    this.fromNumber = process.env.TWILIO_PHONE_NUMBER || '';

    if (!accountSid || !authToken || !this.fromNumber) {
      throw new Error('Missing Twilio credentials in environment variables');
    }

    this.client = twilio(accountSid, authToken);
    this.initialized = true;

    logger.debug('Twilio client initialized successfully');
  }

  createDialNumberRule(params: { phoneNumber: string }) {
    return new twilio.twiml.VoiceResponse().dial(params.phoneNumber);
  }

  createEndCallRule() {
    return new twilio.twiml.VoiceResponse().hangup();
  }

  createInboundCallRules(params: {
    event?: string;
    callContext?: Record<string, string | undefined>;
  }) {
    const twiml = this.createCallRules({
      connectorName: 'TwilioHeatherNoShowConnector',
      callContext: params.callContext,
      event: params.event,
    });

    return twiml;
  }

  async createUrgencyDoctorCallReminder(params: {
    doctorNumber: string;
    patientNumber: string;
    greetingMessage?: string;
    dryRun?: boolean;
  }): Promise<string> {
    logger.info(
      {
        context: 'CallService.createUrgencyDoctorCallReminder',
        params,
      },
      'Creating a call',
    );
    let { doctorNumber, patientNumber } = params;
    const { greetingMessage, dryRun = URGENT_AFTER_HOURS_DOCTOR_CALL_DRU_RUN } = params;

    if (dryRun) {
      logger.info(
        {
          context: 'CallService.createUrgencyDoctorCallReminder',
          doctorNumber,
          patientNumber,
        },
        'Dry run call',
      );
      return 'DRY_RUN_CALL_SID';
    }
    if (URGENT_AFTER_HOURS_DOCTOR_CALL_SANDBOX_MODE) {
      logger.info(
        {
          context: 'CallService.createUrgencyDoctorCallReminder',
          oldDoctorNumber: doctorNumber,
          oldPatientNumber: patientNumber,
          newDoctorNumber: URGENT_AFTER_HOURS_DOCTOR_CALL_DOCTOR_REDIRECT_NUMBER,
          newPatientNumber: URGENT_AFTER_HOURS_DOCTOR_CALL_PATIENT_REDIRECT_NUMBER,
        },
        'Sandbox mode enabled, using redirect numbers',
      );
      doctorNumber = URGENT_AFTER_HOURS_DOCTOR_CALL_DOCTOR_REDIRECT_NUMBER;
      patientNumber = URGENT_AFTER_HOURS_DOCTOR_CALL_PATIENT_REDIRECT_NUMBER;
    }

    const twiml = this.createUrgencyDoctorCallReminderRules({
      patientNumber,
      greetingMessage,
    });

    logger.info(
      {
        context: 'CallService.createUrgencyDoctorCallReminder',
        doctorNumber,
        patientNumber,
      },
      'Creating a call',
    );

    const call = await this.client!.calls.create({
      from: this.fromNumber,
      to: doctorNumber,
      twiml,
    });

    logger.info(
      {
        context: 'CallService.createUrgencyDoctorCallReminder',
        callSid: call.sid,
      },
      'Call made',
    );

    return call.sid;
  }

  async makeCall(params: {
    toPhoneNumber: string;
    dryRun?: boolean;
    redirectPhoneNumber?: string;
    callContext?: Record<string, string | undefined>;
    event?: string;
  }): Promise<string> {
    const {
      event,
      callContext,
      toPhoneNumber,
      dryRun = NOT_SHOW_REMINDER_CALL_DRU_RUN,
      redirectPhoneNumber = NOT_SHOW_REMINDER_CALL_REDIRECT_NUMBER,
    } = params;
    const to = redirectPhoneNumber || toPhoneNumber;
    logger.info(
      {
        context: 'CallService.makeCall',
        toPhoneNumber,
        redirectPhoneNumber,
        to,
        dryRun,
        event,
      },
      'Making a call',
    );

    const appLinkBuilder = AppLinkBuilder.getInstance();
    const twiml = this.createCallRules({
      connectorName: 'TwilioHeatherNoShowConnector',
      callContext,
      event,
    });

    if (dryRun) {
      return 'DRY_RUN_CALL_SID';
    }

    const call = await this.client!.calls.create({
      from: this.fromNumber,
      to,
      statusCallback: appLinkBuilder.getOutboundCallStatusEndpoint(),
      statusCallbackMethod: 'POST',
      statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
      twiml,
    });

    logger.info(
      {
        context: 'CallService.makeCall',
        callSid: call.sid,
      },
      'Call made',
    );

    return call.sid;
  }

  createAdkInboundRelayRules(params: {
    userId: string;
    callId: string;
    callContext?: Record<string, string | undefined>;
    interruptible?: boolean;
  }): string {
    const { callContext, userId, callId, interruptible = false } = params;
    const wsUrl = AppLinkBuilder.getInstance().getAdkWsTextEndpoint(userId, callId);
    const hangUpWebhookUrl = AppLinkBuilder.getInstance().getCallHangUpEndpoint();
    const twiml = new twilio.twiml.VoiceResponse()
      .connect({
        action: hangUpWebhookUrl,
      })
      .conversationRelay({
        url: wsUrl,
        interruptible,
      })
      .language({
        code: 'en-US',
        ttsProvider: TWILIO_RELAY_TTS_PROVIDER,
        voice: TWILIO_RELAY_VOICE,
        transcriptionProvider: TWILIO_RELAY_TRANSCRIPTION_PROVIDER,
        speechModel: TWILIO_RELAY_SPEECH_MODEL,
      });

    // Add custom parameters to the call context
    if (callContext) {
      Object.entries(callContext)
        .filter(([, value]) => Boolean(value))
        .forEach(([name, value]) => {
          twiml.parameter({ name, value: value });
        });
    }

    return twiml.toString();
  }

  createAdkInboundStreamRules(params: {
    userId: string;
    callId: string;
    callContext?: Record<string, string | undefined>;
  }): string {
    const { callContext, userId, callId } = params;
    const wsUrl = AppLinkBuilder.getInstance().getAdkWsEndpoint(userId, callId);
    const hangUpWebhookUrl = AppLinkBuilder.getInstance().getCallHangUpEndpoint();
    const twiml = new twilio.twiml.VoiceResponse()
      .connect({
        action: hangUpWebhookUrl,
      })
      .stream({
        url: wsUrl,
      });

    // Add custom parameters to the call context
    if (callContext) {
      Object.entries(callContext)
        .filter(([, value]) => Boolean(value))
        .forEach(([name, value]) => {
          twiml.parameter({ name, value: value });
        });
    }

    return twiml.toString();
  }

  private createUrgencyDoctorCallReminderRules(params: {
    patientNumber: string;
    greetingMessage?: string;
  }): string {
    const { patientNumber, greetingMessage } = params;
    const twiml = new twilio.twiml.VoiceResponse()
      .gather({
        action: this.createRedirectToAnyNumberTwimlUrl(patientNumber),
        numDigits: 1,
      })
      .say(greetingMessage || 'You have an urgent message. Press 1 to reach out to the patient');

    return twiml.toString();
  }

  private createCallRules(params: {
    connectorName: string;
    callContext?: Record<string, string | undefined>;
    event?: string;
  }) {
    const { connectorName, callContext, event } = params;
    const hangUpWebhookUrl = AppLinkBuilder.getInstance().getCallHangUpEndpoint();
    const twiml = new twilio.twiml.VoiceResponse()
      .connect({ action: hangUpWebhookUrl })
      .virtualAgent({ connectorName });

    // Set up default agent configuration such as voice, language, etc.
    // List of available voices: https://cloud.google.com/text-to-speech/docs/list-voices-and-types
    twiml.config({ name: 'language', value: 'en-us' });
    twiml.config({ name: 'voiceName', value: 'en-US-Chirp3-HD-Aoede' });
    if (event) {
      twiml.config({ name: 'welcomeIntent', value: event });
    }

    // Add custom parameters to the call context
    if (callContext) {
      Object.entries(callContext)
        .filter(([, value]) => Boolean(value))
        .forEach(([name, value]) => {
          twiml.parameter({ name, value: value });
        });
    }

    return twiml;
  }
}

// Export a singleton instance
export const callService = new CallService();
