import admin from 'firebase-admin';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';

import {
  OnCallSchedule,
  OnCallScheduleCreateRequest,
  OnCallScheduleUpdateRequest,
  OnCallScheduleFilters,
  ScheduleValidationResult,
  ScheduleConflict,
} from '@/models/OnCallSchedule';
import { OnCallSchedulesRepository } from '@/lib/repositories/on-call-schedules-repository';
import { locationsService } from '@/utils/firestore';
import { getRepositories } from '../repositories';

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

// Conditional logger import for server-side compatibility
const logger = (() => {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return require('@/lib/external-api/v2/utils/logger').default;
  } catch {
    return console; // Fallback to console if logger not available
  }
})();

/**
 * Service for managing on-call doctor schedules
 * Handles CRUD operations, validation, conflict detection, and timezone calculations
 */
export class OnCallScheduleService {
  private static readonly COLLECTION_NAME = 'on-call-schedules';
  private static db = admin.firestore();

  /**
   * Create a new on-call schedule
   * @param scheduleData Schedule creation data
   * @param createdBy User ID who is creating the schedule
   * @returns Created schedule
   */
  static async createSchedule(
    scheduleData: OnCallScheduleCreateRequest,
    createdBy: string,
  ): Promise<OnCallSchedule> {
    try {
      // Validate schedule data
      const validation = this.validateScheduleData(scheduleData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      // Get location to verify existence and get clinic info
      const locationDoc = await this.db.collection('locations').doc(scheduleData.locationId).get();
      if (!locationDoc.exists) {
        throw new Error(`Location ${scheduleData.locationId} not found`);
      }

      const locationData = locationDoc.data()!;
      const location = {
        id: locationDoc.id,
        clinicId: locationData.clinicId || 0,
        timeZone: locationData.timeZone || 'America/Chicago',
        name: locationData.name || 'Unknown Location',
        ...locationData,
      };

      // Get doctor information
      const doctorDoc = await this.db.collection('users').doc(scheduleData.doctorId).get();
      if (!doctorDoc.exists) {
        throw new Error(`Doctor ${scheduleData.doctorId} not found`);
      }

      const doctorData = doctorDoc.data();
      if (!doctorData?.canTakeAppointments) {
        throw new Error(`Doctor ${scheduleData.doctorId} cannot take appointments`);
      }

      if (!doctorData.phone) {
        throw new Error(`Doctor ${scheduleData.doctorId} has no phone number for notifications`);
      }

      // Check for scheduling conflicts
      const conflicts = await this.checkScheduleConflicts(
        scheduleData.locationId,
        scheduleData.date,
        scheduleData.startTime,
        scheduleData.endTime,
      );

      if (conflicts.length > 0) {
        throw new Error(
          `Schedule conflicts detected with existing schedules: ${conflicts
            .map(c => c.conflictingSchedule.doctorName)
            .join(', ')}`,
        );
      }

      // Create schedule document
      const now = new Date();
      const scheduleDoc: Omit<OnCallSchedule, 'id'> = {
        doctorId: scheduleData.doctorId,
        doctorName: doctorData.name || 'Unknown Doctor',
        doctorPhone: this.formatPhoneNumber(doctorData.phone),
        locationId: scheduleData.locationId,
        clinicId: location.clinicId,
        date: scheduleData.date,
        startTime: scheduleData.startTime,
        endTime: scheduleData.endTime,
        isActive: true,
        timezone: location.timeZone || 'America/Chicago',
        notes: scheduleData.notes || '',
        createdAt: now,
        updatedAt: now,
        createdBy,
      };

      // Save to Firestore
      const docRef = await this.db.collection(this.COLLECTION_NAME).add(scheduleDoc);

      if (typeof logger?.info === 'function') {
        logger.info(
          {
            scheduleId: docRef.id,
            doctorId: scheduleData.doctorId,
            locationId: scheduleData.locationId,
            date: scheduleData.date,
          },
          'On-call schedule created successfully',
        );
      }

      return {
        id: docRef.id,
        ...scheduleDoc,
      };
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            scheduleData,
            createdBy,
          },
          'Error creating on-call schedule',
        );
      }
      throw error;
    }
  }

  /**
   * Update an existing on-call schedule
   * @param id Schedule ID to update
   * @param updates Update data
   * @param updatedBy User ID who is updating the schedule
   * @returns Updated schedule
   */
  static async updateSchedule(
    id: string,
    updates: OnCallScheduleUpdateRequest,
    updatedBy: string,
  ): Promise<OnCallSchedule> {
    try {
      // Get existing schedule
      const scheduleDoc = await this.db.collection(this.COLLECTION_NAME).doc(id).get();
      if (!scheduleDoc.exists) {
        throw new Error(`Schedule ${id} not found`);
      }

      const existingSchedule = { id: scheduleDoc.id, ...scheduleDoc.data() } as OnCallSchedule;

      // Validate updates if schedule time/date is being changed
      if (updates.date || updates.startTime || updates.endTime) {
        const updatedData = {
          doctorId: updates.doctorId || existingSchedule.doctorId,
          locationId: existingSchedule.locationId,
          date: updates.date || existingSchedule.date,
          startTime: updates.startTime || existingSchedule.startTime,
          endTime: updates.endTime || existingSchedule.endTime,
          notes: updates.notes,
        };

        const validation = this.validateScheduleData(updatedData);
        if (!validation.isValid) {
          throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
        }

        // Check for conflicts (excluding current schedule)
        const conflicts = await this.checkScheduleConflicts(
          existingSchedule.locationId,
          updatedData.date,
          updatedData.startTime,
          updatedData.endTime,
          id,
        );

        if (conflicts.length > 0) {
          throw new Error(
            `Schedule conflicts detected: ${conflicts
              .map(c => c.conflictingSchedule.doctorName)
              .join(', ')}`,
          );
        }
      }

      // If doctor is being changed, validate new doctor
      if (updates.doctorId && updates.doctorId !== existingSchedule.doctorId) {
        const doctorDoc = await this.db.collection('users').doc(updates.doctorId).get();
        if (!doctorDoc.exists) {
          throw new Error(`Doctor ${updates.doctorId} not found`);
        }

        const doctorData = doctorDoc.data();
        if (!doctorData?.canTakeAppointments) {
          throw new Error(`Doctor ${updates.doctorId} cannot take appointments`);
        }

        if (!doctorData.phone) {
          throw new Error(`Doctor ${updates.doctorId} has no phone number for notifications`);
        }

        updates.doctorName = doctorData.name || 'Unknown Doctor';
        updates.doctorPhone = this.formatPhoneNumber(doctorData.phone);
      }

      // Update document
      const updateData = {
        ...updates,
        updatedAt: new Date(),
      };

      await this.db.collection(this.COLLECTION_NAME).doc(id).update(updateData);

      if (typeof logger?.info === 'function') {
        logger.info(
          {
            scheduleId: id,
            updates,
            updatedBy,
          },
          'On-call schedule updated successfully',
        );
      }

      // Return updated schedule
      const updatedDoc = await this.db.collection(this.COLLECTION_NAME).doc(id).get();
      return { id: updatedDoc.id, ...updatedDoc.data() } as OnCallSchedule;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            scheduleId: id,
            updates,
            updatedBy,
          },
          'Error updating on-call schedule',
        );
      }
      throw error;
    }
  }

  /**
   * Delete an on-call schedule (soft delete)
   * @param id Schedule ID to delete
   * @param deletedBy User ID who is deleting the schedule
   */
  static async deleteSchedule(id: string, deletedBy: string): Promise<void> {
    try {
      const scheduleDoc = await this.db.collection(this.COLLECTION_NAME).doc(id).get();
      if (!scheduleDoc.exists) {
        throw new Error(`Schedule ${id} not found`);
      }

      // Soft delete by setting isActive to false
      await this.db.collection(this.COLLECTION_NAME).doc(id).update({
        isActive: false,
        updatedAt: new Date(),
      });

      if (typeof logger?.info === 'function') {
        logger.info(
          {
            scheduleId: id,
            deletedBy,
          },
          'On-call schedule deleted successfully',
        );
      }
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            scheduleId: id,
            deletedBy,
          },
          'Error deleting on-call schedule',
        );
        throw error;
      }
    }
  }

  /**
   * Get schedules for a location within date range
   * @param locationId Location ID
   * @param startDate Start date (ISO format)
   * @param endDate End date (ISO format)
   * @param clinicId Clinic ID for isolation
   * @returns Array of schedules
   */
  static async getSchedulesByLocation(
    locationId: string,
    startDate: Date,
    endDate: Date,
    clinicId: number,
  ): Promise<OnCallSchedule[]> {
    try {
      const startDateStr = dayjs(startDate).format('YYYY-MM-DD');
      const endDateStr = dayjs(endDate).format('YYYY-MM-DD');

      const query = this.db
        .collection(this.COLLECTION_NAME)
        .where('locationId', '==', locationId)
        .where('clinicId', '==', clinicId)
        .where('isActive', '==', true)
        .where('date', '>=', startDateStr)
        .where('date', '<=', endDateStr)
        .orderBy('date', 'asc')
        .orderBy('startTime', 'asc');

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as OnCallSchedule[];
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            locationId,
            startDate,
            endDate,
            clinicId,
          },
          'Error fetching schedules by location',
        );
      }
      throw error;
    }
  }

  /**
   * Get schedules for a doctor within date range
   * @param doctorId Doctor ID
   * @param startDate Start date
   * @param endDate End date
   * @param clinicId Clinic ID for isolation
   * @returns Array of schedules
   */
  static async getSchedulesByDoctor(
    doctorId: string,
    startDate: Date,
    endDate: Date,
    clinicId: number,
  ): Promise<OnCallSchedule[]> {
    try {
      const startDateStr = dayjs(startDate).format('YYYY-MM-DD');
      const endDateStr = dayjs(endDate).format('YYYY-MM-DD');

      // Use repository pattern with MySQL-only reads (the current findMany implementation already uses MySQL only)
      const schedulesRepo = new OnCallSchedulesRepository();
      const result = await schedulesRepo.findMany({
        where: {
          doctorId,
          clinicId,
          isActive: true,
        },
        orderBy: [
          { field: 'date', direction: 'asc' },
          { field: 'startTime', direction: 'asc' },
        ],
        limit: 500, // Reasonable upper bound
      });

      // Filter for date range in memory (since MySQL findMany doesn't support range queries directly)
      const filteredSchedules = result.items.filter(schedule => {
        return schedule.date >= startDateStr && schedule.date <= endDateStr;
      });

      return filteredSchedules;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            doctorId,
            startDate,
            endDate,
            clinicId,
          },
          'Error fetching schedules by doctor',
        );
      }
      throw error;
    }
  }

  /**
   * Get currently on-call doctor for a location
   * Uses location's timezone from database for calculations
   * @param locationId Location ID
   * @param currentTime Optional current time for testing
   * @returns Current on-call schedule or null
   */
  static async getCurrentOnCallDoctor(
    locationId: string,
    currentTime?: Date,
    isPrimary?: boolean,
  ): Promise<OnCallSchedule | null> {
    try {
      // Get location to get its timezone
      const location = await locationsService.getLocationById(locationId);
      if (!location) {
        throw new Error(`Location ${locationId} not found`);
      }

      const now = dayjs(currentTime || new Date());
      const locationTime = now.tz(location.timeZone || 'America/Chicago');
      const currentDate = locationTime.format('YYYY-MM-DD');
      const currentTimeStr = locationTime.format('HH:mm');

      // Query schedules for today
      const repoManager = getRepositories();
      await repoManager.initialize();
      const schedulesRepo = repoManager.onCallSchedules;
      const result = await schedulesRepo.findMany({
        where: {
          locationId,
          clinicId: location.clinicId,
          isActive: true,
          date: currentDate,
          isPrimary: isPrimary ?? true,
        },
        orderBy: [{ field: 'startTime', direction: 'asc' }],
        limit: 20, // or any reasonable upper bound
      });

      const todaySchedules = result.items;

      // Find schedule that covers current time
      const currentSchedule = todaySchedules.find(schedule => {
        return currentTimeStr >= schedule.startTime && currentTimeStr < schedule.endTime;
      });

      if (currentSchedule) {
        if (typeof logger?.info === 'function') {
          logger.info(
            {
              locationId,
              doctorName: currentSchedule.doctorName,
              scheduleTime: `${currentSchedule.startTime}-${currentSchedule.endTime}`,
              currentTime: locationTime.format('YYYY-MM-DD HH:mm:ss'),
            },
            'Found current on-call doctor',
          );
        }
      }

      return currentSchedule || null;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            locationId,
            currentTime,
          },
          'Error finding current on-call doctor',
        );
      }
      throw error;
    }
  }

  /**
   * Check for scheduling conflicts
   * @param locationId Location ID
   * @param date Schedule date
   * @param startTime Start time
   * @param endTime End time
   * @param excludeScheduleId Optional schedule ID to exclude from conflict check
   * @returns Array of conflicting schedules
   */
  static async checkScheduleConflicts(
    locationId: string,
    date: string,
    startTime: string,
    endTime: string,
    excludeScheduleId?: string,
    isPrimary?: boolean,
  ): Promise<ScheduleConflict[]> {
    try {
      // Get all schedules for the same location and date
      const query = this.db
        .collection(this.COLLECTION_NAME)
        .where('locationId', '==', locationId)
        .where('date', '==', date)
        .where('isActive', '==', true);

      const snapshot = await query.get();
      const existingSchedules = snapshot.docs
        .filter(doc => doc.id !== excludeScheduleId)
        .map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as OnCallSchedule[];

      const conflicts: ScheduleConflict[] = [];

      const period1IsPrimary = isPrimary ?? true;

      for (const schedule of existingSchedules) {
        const period2IsPrimary = schedule.isPrimary ?? true;
        const conflict = this.detectOverlap(
          { startTime, endTime, isPrimary: period1IsPrimary },
          { startTime: schedule.startTime, endTime: schedule.endTime, isPrimary: period2IsPrimary },
          schedule,
        );

        if (conflict) {
          conflicts.push(conflict);
        }
      }

      return conflicts;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            locationId,
            date,
            startTime,
            endTime,
          },
          'Error checking schedule conflicts',
        );
      }
      throw error;
    }
  }

  /**
   * Validate schedule data
   * @param scheduleData Schedule data to validate
   * @returns Validation result
   */
  static validateScheduleData(
    scheduleData: OnCallScheduleCreateRequest | Partial<OnCallScheduleCreateRequest>,
  ): ScheduleValidationResult {
    const errors: string[] = [];

    // Required fields validation
    if (!scheduleData.doctorId) {
      errors.push('Doctor ID is required');
    }

    if (!scheduleData.locationId) {
      errors.push('Location ID is required');
    }

    if (!scheduleData.date) {
      errors.push('Date is required');
    }

    if (!scheduleData.startTime) {
      errors.push('Start time is required');
    }

    if (!scheduleData.endTime) {
      errors.push('End time is required');
    }

    // Date format validation
    if (scheduleData.date && !/^\d{4}-\d{2}-\d{2}$/.test(scheduleData.date)) {
      errors.push('Date must be in YYYY-MM-DD format');
    }

    // Time format validation
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (scheduleData.startTime && !timeRegex.test(scheduleData.startTime)) {
      errors.push('Start time must be in HH:MM format');
    }

    if (scheduleData.endTime && !timeRegex.test(scheduleData.endTime)) {
      errors.push('End time must be in HH:MM format');
    }

    // Logical validation
    if (scheduleData.startTime && scheduleData.endTime) {
      if (scheduleData.startTime >= scheduleData.endTime) {
        errors.push('Start time must be before end time');
      }

      // Minimum duration check (30 minutes)
      const startMinutes = this.timeToMinutes(scheduleData.startTime);
      const endMinutes = this.timeToMinutes(scheduleData.endTime);
      if (endMinutes - startMinutes < 30) {
        errors.push('Schedule must be at least 30 minutes long');
      }
    }

    // Date not in past (allow today)
    if (scheduleData.date) {
      const today = dayjs().format('YYYY-MM-DD');
      if (scheduleData.date < today) {
        errors.push('Schedule date cannot be in the past');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get schedules with optional filters
   * @param filters Query filters
   * @returns Array of schedules
   */
  static async getSchedules(filters: OnCallScheduleFilters = {}): Promise<OnCallSchedule[]> {
    try {
      let query = this.db.collection(this.COLLECTION_NAME) as admin.firestore.Query;

      // Apply filters
      if (filters.locationId) {
        query = query.where('locationId', '==', filters.locationId);
      }

      if (filters.doctorId) {
        query = query.where('doctorId', '==', filters.doctorId);
      }

      if (filters.clinicId) {
        query = query.where('clinicId', '==', filters.clinicId);
      }

      if (filters.isActive !== undefined) {
        query = query.where('isActive', '==', filters.isActive);
      }

      if (filters.startDate) {
        query = query.where('date', '>=', filters.startDate);
      }

      if (filters.endDate) {
        query = query.where('date', '<=', filters.endDate);
      }

      // Order by date and time
      query = query.orderBy('date', 'asc').orderBy('startTime', 'asc');

      const snapshot = await query.get();
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as OnCallSchedule[];
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            filters,
          },
          'Error fetching schedules with filters',
        );
      }
      throw error;
    }
  }

  /**
   * Get schedule by ID
   * @param id Schedule ID
   * @returns Schedule or null if not found
   */
  static async getScheduleById(id: string): Promise<OnCallSchedule | null> {
    try {
      const doc = await this.db.collection(this.COLLECTION_NAME).doc(id).get();
      if (!doc.exists) {
        return null;
      }

      return {
        id: doc.id,
        ...doc.data(),
      } as OnCallSchedule;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            scheduleId: id,
          },
          'Error fetching schedule by ID',
        );
      }
      throw error;
    }
  }

  // Helper methods

  /**
   * Format phone number to E.164 format
   * @param phone Phone number
   * @returns Formatted phone number
   */
  private static formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');

    // Add country code if not present
    if (cleaned.length === 10) {
      return `+1${cleaned}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return `+${cleaned}`;
    }

    return phone; // Return as-is if already formatted or unknown format
  }

  /**
   * Convert time string to minutes since midnight
   * @param time Time in HH:MM format
   * @returns Minutes since midnight
   */
  private static timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Detect overlap between two time periods
   * @param period1 First time period
   * @param period2 Second time period
   * @param conflictingSchedule The schedule that causes conflict
   * @returns Conflict information or null if no overlap
   */
  private static detectOverlap(
    period1: { startTime: string; endTime: string; isPrimary: boolean },
    period2: { startTime: string; endTime: string; isPrimary: boolean },
    conflictingSchedule: OnCallSchedule,
  ): ScheduleConflict | null {
    const start1 = this.timeToMinutes(period1.startTime);
    const end1 = this.timeToMinutes(period1.endTime);
    const start2 = this.timeToMinutes(period2.startTime);
    const end2 = this.timeToMinutes(period2.endTime);

    // Check for overlap (exclusive end times)
    if (start1 < end2 && start2 < end1 && period1.isPrimary === period2.isPrimary) {
      const overlapStart = Math.max(start1, start2);
      const overlapEnd = Math.min(end1, end2);

      let conflictType: 'complete_overlap' | 'partial_overlap' | 'adjacent';

      if (start1 === start2 && end1 === end2) {
        conflictType = 'complete_overlap';
      } else if ((start1 <= start2 && end1 >= end2) || (start2 <= start1 && end2 >= end1)) {
        conflictType = 'complete_overlap';
      } else {
        conflictType = 'partial_overlap';
      }

      return {
        conflictingSchedule,
        overlapStart: this.minutesToTime(overlapStart),
        overlapEnd: this.minutesToTime(overlapEnd),
        conflictType,
      };
    }

    return null;
  }

  /**
   * Convert minutes since midnight to time string
   * @param minutes Minutes since midnight
   * @returns Time in HH:MM format
   */
  private static minutesToTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}
