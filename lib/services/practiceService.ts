import { Practice } from '@/models/Practice';
import { Location } from '@/models/Location';
import { User, UserRole } from '@/models/auth';
import admin from 'firebase-admin';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseFeatures } from '@/lib/database/database-config';
import { PracticesRepository } from '@/lib/repositories/practices-repository';
import { LocationsRepository } from '@/lib/repositories/locations-repository';
import { getRepositories } from '@/lib/repositories';

// Initialize Firestore
const db = admin.firestore();
const practicesCollection = db.collection('practices');
const locationsCollection = db.collection('locations');

/**
 * Service for managing Practice entities with proper business logic and validation
 */
export class PracticeService {
  private static practicesRepo = new PracticesRepository();
  private static locationsRepo = new LocationsRepository();

  /**
   * Get all practices for a specific clinic
   */
  static async getPracticesByClinicId(clinicId: number): Promise<Practice[]> {
    // Ensure repositories & MySQL are initialized
    await this.ensureRepositoriesInitialized();

    // Use MySQL-first repository unless running in Firestore-only mode
    if (!DatabaseFeatures.isFirestoreOnly()) {
      try {
        const practices = await this.practicesRepo.findByClinicId(clinicId);
        // Return only active practices sorted alphabetically (to match previous behaviour)
        return practices.filter(p => p.isActive).sort((a, b) => a.name.localeCompare(b.name));
      } catch (error) {
        console.error('Error getting practices by clinic ID (repository):', error);
        throw new Error('Failed to retrieve practices');
      }
    }

    // Fallback to legacy Firestore behaviour
    try {
      const snapshot = await practicesCollection
        .where('clinicId', '==', clinicId)
        .where('isActive', '==', true)
        .get();

      const practices = snapshot.docs.map(doc => this.convertToPractice(doc));
      return practices.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error('Error getting practices by clinic ID (firestore):', error);
      throw new Error('Failed to retrieve practices');
    }
  }

  /**
   * Get a specific practice by ID with clinic scope validation
   */
  static async getPracticeById(practiceId: string, clinicId: number): Promise<Practice | null> {
    await this.ensureRepositoriesInitialized();
    if (!DatabaseFeatures.isFirestoreOnly()) {
      try {
        const practice = await this.practicesRepo.findById(practiceId);
        if (!practice || practice.clinicId !== clinicId) {
          return null;
        }
        return practice;
      } catch (error) {
        console.error('Error getting practice by ID (repository):', error);
        throw new Error('Failed to retrieve practice');
      }
    }

    // Legacy Firestore path
    try {
      const doc = await practicesCollection.doc(practiceId).get();
      if (!doc.exists) {
        return null;
      }
      const practice = this.convertToPractice(doc);
      if (practice.clinicId !== clinicId) {
        return null;
      }
      return practice;
    } catch (error) {
      console.error('Error getting practice by ID (firestore):', error);
      throw new Error('Failed to retrieve practice');
    }
  }

  /**
   * Create a new practice with validation
   */
  static async createPractice(
    practiceData: Omit<Practice, 'id' | 'createdAt' | 'updatedAt'>,
    user: User,
  ): Promise<Practice> {
    await this.ensureRepositoriesInitialized();
    // Validate user permissions & data (shared regardless of backend)
    this.validateUserPermissions(user, practiceData.clinicId);
    await this.validatePracticeData(practiceData);

    // Duplicate name validation
    await this.validateUniqueNameInClinic(practiceData.name, practiceData.clinicId);

    if (!DatabaseFeatures.isFirestoreOnly()) {
      try {
        const created = await this.practicesRepo.create({
          ...practiceData,
          isActive: practiceData.isActive ?? true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        return created;
      } catch (error) {
        console.error('Error creating practice (repository):', error);
        throw error;
      }
    }

    // Legacy Firestore behaviour
    try {
      const id = uuidv4();
      const now = new Date();
      const newPractice: Practice = {
        ...practiceData,
        id,
        isActive: practiceData.isActive ?? true,
        createdAt: now,
        updatedAt: now,
      };
      await practicesCollection.doc(id).set(newPractice);
      return newPractice;
    } catch (error) {
      console.error('Error creating practice (firestore):', error);
      throw error;
    }
  }

  /**
   * Update an existing practice with validation
   */
  static async updatePractice(
    practiceId: string,
    updates: Partial<Omit<Practice, 'id' | 'clinicId' | 'createdAt' | 'updatedAt'>>,
    user: User,
  ): Promise<Practice> {
    await this.ensureRepositoriesInitialized();
    // Fetch existing practice via helper (handles backend selection)
    const existingPractice = await this.getPracticeById(practiceId, user.clinicId!);
    if (!existingPractice) {
      throw new Error('Practice not found');
    }

    // Validate permissions
    this.validateUserPermissions(user, existingPractice.clinicId);

    // Validate updated data if needed
    if (updates.name || updates.description !== undefined) {
      await this.validatePracticeData({ ...existingPractice, ...updates });
    }

    // Duplicate name check
    if (updates.name && updates.name !== existingPractice.name) {
      await this.validateUniqueNameInClinic(updates.name, existingPractice.clinicId, practiceId);
    }

    if (!DatabaseFeatures.isFirestoreOnly()) {
      try {
        const updated = await this.practicesRepo.update(practiceId, updates);
        return updated;
      } catch (error) {
        console.error('Error updating practice (repository):', error);
        throw error;
      }
    }

    // Legacy Firestore update
    try {
      const updatedData = { ...updates, updatedAt: new Date() };
      await practicesCollection.doc(practiceId).update(updatedData);
      return { ...existingPractice, ...updatedData } as Practice;
    } catch (error) {
      console.error('Error updating practice (firestore):', error);
      throw error;
    }
  }

  /**
   * Soft delete a practice (set isActive to false)
   */
  static async deletePractice(practiceId: string, user: User): Promise<void> {
    await this.ensureRepositoriesInitialized();
    const existingPractice = await this.getPracticeById(practiceId, user.clinicId!);
    if (!existingPractice) {
      throw new Error('Practice not found');
    }
    this.validateUserPermissions(user, existingPractice.clinicId);

    // Ensure no active locations
    const hasActive = await this.hasActiveLocations(practiceId);
    if (hasActive) {
      throw new Error(
        'Cannot delete practice with active locations. Please reassign or delete locations first.',
      );
    }

    if (!DatabaseFeatures.isFirestoreOnly()) {
      // Soft delete via repository
      await this.practicesRepo.update(practiceId, { isActive: false });
      return;
    }

    // Legacy Firestore soft delete
    await practicesCollection.doc(practiceId).update({
      isActive: false,
      updatedAt: new Date(),
    });
  }

  /**
   * Get all locations for a specific practice
   */
  static async getLocationsByPracticeId(
    practiceId: string,
    clinicId: number,
    options: { includeInactive?: boolean } = {},
  ): Promise<Location[]> {
    await this.ensureRepositoriesInitialized();
    if (!DatabaseFeatures.isFirestoreOnly()) {
      try {
        const locations = options.includeInactive
          ? await this.locationsRepo.findByPracticeId(practiceId)
          : await this.locationsRepo.findActiveByPractice(practiceId);
        return locations;
      } catch (error) {
        console.error('Error getting locations by practice ID (repository):', error);
        throw new Error('Failed to retrieve practice locations');
      }
    }

    // Legacy Firestore path
    try {
      let query = locationsCollection
        .where('practiceId', '==', practiceId)
        .where('clinicId', '==', clinicId);

      if (!options.includeInactive) {
        query = query.orderBy('name');
      }

      const snapshot = await query.get();
      return snapshot.docs.map(doc => this.convertToLocation(doc));
    } catch (error) {
      console.error('Error getting locations by practice ID (firestore):', error);
      throw new Error('Failed to retrieve practice locations');
    }
  }

  /**
   * Validate user has permission to manage practices
   */
  private static validateUserPermissions(user: User, clinicId: number): void {
    // Only CLINIC_ADMIN and SUPER_ADMIN can manage practices
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      throw new Error('Insufficient permissions to manage practices');
    }

    // CLINIC_ADMIN can only manage practices in their own clinic
    if (user.role === UserRole.CLINIC_ADMIN && user.clinicId !== clinicId) {
      throw new Error('Cannot manage practices outside your clinic');
    }
  }

  /**
   * Validate practice data
   */
  private static async validatePracticeData(
    practice: Omit<Practice, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<void> {
    if (!practice.name || practice.name.trim().length === 0) {
      throw new Error('Practice name is required');
    }

    if (practice.name.length > 100) {
      throw new Error('Practice name must be 100 characters or less');
    }

    if (practice.description && practice.description.length > 500) {
      throw new Error('Practice description must be 500 characters or less');
    }

    if (!practice.clinicId || practice.clinicId <= 0) {
      throw new Error('Valid clinic ID is required');
    }
  }

  /**
   * Validate practice name is unique within clinic
   */
  private static async validateUniqueNameInClinic(
    name: string,
    clinicId: number,
    excludePracticeId?: string,
  ): Promise<void> {
    await this.ensureRepositoriesInitialized();
    if (!DatabaseFeatures.isFirestoreOnly()) {
      const existing = await this.practicesRepo.findMany({
        where: { clinicId, name: name.trim(), isActive: true },
        limit: 5,
      });
      const duplicates = existing.items.filter(pr => pr.id !== excludePracticeId);
      if (duplicates.length > 0) {
        throw new Error('A practice with this name already exists in your clinic');
      }
      return;
    }

    // Legacy Firestore duplicate check
    const query = practicesCollection
      .where('clinicId', '==', clinicId)
      .where('name', '==', name.trim())
      .where('isActive', '==', true);

    const snapshot = await query.get();
    const duplicates = snapshot.docs.filter(doc => doc.id !== excludePracticeId);

    if (duplicates.length > 0) {
      throw new Error('A practice with this name already exists in your clinic');
    }
  }

  /**
   * Check if practice has active locations
   */
  private static async hasActiveLocations(practiceId: string): Promise<boolean> {
    await this.ensureRepositoriesInitialized();
    if (!DatabaseFeatures.isFirestoreOnly()) {
      const locations = await this.locationsRepo.findActiveByPractice(practiceId, 1);
      return locations.length > 0;
    }

    // Legacy Firestore check
    const snapshot = await locationsCollection.where('practiceId', '==', practiceId).limit(1).get();
    return !snapshot.empty;
  }

  /**
   * Convert Firestore document to Practice model
   */
  private static convertToPractice(doc: admin.firestore.DocumentSnapshot): Practice {
    const data = doc.data();
    if (!data) throw new Error(`Practice with ID ${doc.id} not found`);

    return {
      id: doc.id,
      clinicId: data.clinicId || 0,
      name: data.name || '',
      description: data.description || undefined,
      isActive: data.isActive ?? true,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    };
  }

  /**
   * Convert Firestore document to Location model
   */
  private static convertToLocation(doc: admin.firestore.DocumentSnapshot): Location {
    const data = doc.data();
    if (!data) throw new Error(`Location with ID ${doc.id} not found`);

    return {
      id: doc.id,
      clinicId: data.clinicId || 0,
      practiceId: data.practiceId || '',
      name: data.name || '',
      address: data.address || '',
      phone: data.phone || undefined,
      timeZone: data.timeZone || 'America/New_York',
      isActive: data.isActive ?? true,
      practiceName: data.practiceName || '',
      officeHours: data.officeHours || undefined,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    };
  }

  private static async ensureRepositoriesInitialized(): Promise<void> {
    const repoManager = getRepositories();
    await repoManager.initialize();
  }
}
