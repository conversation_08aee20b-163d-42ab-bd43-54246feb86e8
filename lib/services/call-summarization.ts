import { callsService } from '@/utils/firestore';
import { LLMClient } from '@/utils/llm';
import { Call } from '@/models/Call';

/**
 * Structure of transcript items in transcriptionWithAudio
 */
interface TranscriptItem {
  text: string;
  recordUrl: string;
}

/**
 * Service for call summarization with fallback logic
 */
export class CallSummarizationService {
  private static instance: CallSummarizationService;

  private constructor() {}

  public static getInstance(): CallSummarizationService {
    if (!CallSummarizationService.instance) {
      CallSummarizationService.instance = new CallSummarizationService();
    }
    return CallSummarizationService.instance;
  }

  /**
   * Gets transcription from call with fallback logic
   * @param call - The call object
   * @returns The transcription text or null if not available
   */
  private getTranscriptionFromCall(call: Call): string | null {
    // First try the primary transcription field
    let transcription = call.transcription || null;

    // If not available, try the fallback transcriptionWithAudio
    if (!this.isTranscriptionValid(transcription) && call.transcriptionWithAudio) {
      // Handle both string and parsed object cases
      transcription = this.extractTextFromTranscriptionWithAudio(call.transcriptionWithAudio);
    }

    return this.isTranscriptionValid(transcription) ? transcription : null;
  }

  /**
   * Extracts text from transcriptionWithAudio array structure
   * @param transcriptionWithAudio - JSON string or parsed array containing transcript objects
   * @returns Concatenated text from all transcript objects
   */
  private extractTextFromTranscriptionWithAudio(transcriptionWithAudio: unknown): string | null {
    try {
      let transcriptArray: TranscriptItem[];

      // Check if it's already a parsed array
      if (Array.isArray(transcriptionWithAudio)) {
        transcriptArray = transcriptionWithAudio;
      } else if (typeof transcriptionWithAudio === 'string') {
        // Try to parse as JSON string
        transcriptArray = JSON.parse(transcriptionWithAudio);
      } else {
        // Unknown type, return null
        return null;
      }

      // Validate that it's an array (in case JSON.parse didn't return an array)
      if (!Array.isArray(transcriptArray)) {
        return null;
      }

      // Extract text from each object and join them
      const textParts = transcriptArray
        .map((item: TranscriptItem) => item.text)
        .filter((text: string) => text && text.trim() !== '');

      return textParts.length > 0 ? textParts.join('\n') : null;
    } catch (error) {
      // If parsing fails, return null
      console.warn('Failed to parse transcriptionWithAudio:', error);
      return null;
    }
  }

  /**
   * Validates if a transcription is usable
   * @param transcription - The transcription text
   * @returns True if transcription is valid and usable
   */
  private isTranscriptionValid(transcription: string | null | undefined): boolean {
    if (!transcription) return false;

    const text = transcription.trim().toUpperCase();
    return Boolean(text) && text !== '' && text !== 'DEFAULT' && text !== 'NONE';
  }

  /**
   * Summarizes a call by ID with fallback transcription logic
   * @param callId - The ID of the call to summarize
   * @returns Promise that resolves to the summary string
   * @throws Error if call not found or transcription unavailable
   */
  public async summarizeCallById(callId: string): Promise<string> {
    // Get the call with merged detail data from MySQL
    const call = await callsService.getCallWithDetail(callId);
    if (!call) {
      throw new Error('Call not found');
    }

    // Return existing summary if available
    if (call.summary) {
      return call.summary;
    }

    // Get transcription with fallback logic
    const transcription = this.getTranscriptionFromCall(call);
    if (!transcription) {
      throw new Error('Transcript is not available');
    }

    // Generate the summary
    const ai = LLMClient.getInstance();
    const summary = await ai.summarizeCall(transcription);

    if (!summary) {
      throw new Error('Failed to generate call summary');
    }

    // Update the call with the new summary (this will update the MySQL calls table)
    await callsService.updateCall(callId, {
      summary,
    });

    return summary;
  }
}
