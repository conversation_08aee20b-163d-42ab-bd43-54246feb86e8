import { v4 as uuidv4 } from 'uuid';
import admin from '@/utils/firebase-admin';
import { PatientReference } from '../models/patient-reference';

/**
 * Service for managing patient references in Firestore
 * Tracks patients created through our service in external provider systems
 */
export class PatientReferenceService {
  private patientReferencesCollection: admin.firestore.CollectionReference;

  constructor() {
    // Initialize Firestore collection
    const db = admin.firestore();
    this.patientReferencesCollection = db.collection('patientReferences');
  }

  /**
   * Store a new patient reference
   * @param patient Patient reference data without id, createdAt, and updatedAt
   * @returns The created patient reference with id, createdAt, and updatedAt
   */
  async storeNewPatient(
    patient: Omit<PatientReference, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<PatientReference> {
    try {
      const id = uuidv4();
      const now = new Date();

      const newPatient: PatientReference = {
        ...patient,
        id,
        createdAt: now,
        updatedAt: now,
      };

      await this.patientReferencesCollection.doc(id).set(newPatient);
      return newPatient;
    } catch (error) {
      console.error('Error storing new patient reference:', error);
      throw error;
    }
  }

  /**
   * Find a patient reference by provider and provider ID
   * @param provider Provider name (e.g., "nextech")
   * @param providerId ID in the provider's system
   * @returns The patient reference or null if not found
   */
  async findByProviderId(provider: string, providerId: string): Promise<PatientReference | null> {
    try {
      const snapshot = await this.patientReferencesCollection
        .where('provider', '==', provider)
        .where('providerId', '==', providerId)
        .limit(1)
        .get();

      if (snapshot.empty) {
        return null;
      }

      const doc = snapshot.docs[0];
      return {
        ...doc.data(),
        id: doc.id,
        createdAt: doc.data().createdAt.toDate(),
        updatedAt: doc.data().updatedAt.toDate(),
      } as PatientReference;
    } catch (error) {
      console.error('Error finding patient reference by provider ID:', error);
      throw error;
    }
  }

  /**
   * Update a patient reference if data has changed
   * @param patient Updated patient reference data
   * @returns The updated patient reference
   */
  async updateIfChanged(patient: PatientReference): Promise<PatientReference> {
    try {
      // Get the current patient reference
      const doc = await this.patientReferencesCollection.doc(patient.id).get();

      if (!doc.exists) {
        throw new Error(`Patient reference with ID ${patient.id} not found`);
      }

      const currentData = doc.data() as PatientReference;

      // Check if any fields have changed
      const hasChanged = patient.phoneNumber !== currentData.phoneNumber;

      // Only update if data has changed
      if (hasChanged) {
        const now = new Date();
        const updatedPatient = {
          ...patient,
          updatedAt: now,
        };

        await this.patientReferencesCollection.doc(patient.id).update(updatedPatient);
        return updatedPatient;
      }

      // Return the original patient if no changes
      return patient;
    } catch (error) {
      console.error('Error updating patient reference:', error);
      throw error;
    }
  }
}
