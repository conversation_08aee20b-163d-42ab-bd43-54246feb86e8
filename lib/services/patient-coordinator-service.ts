import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import { PatientReferenceService } from './patient-reference-service';
import { normalizePhoneNumberForCaching, PatientService } from './patient-service';
import { PatientReference } from '../models/patient-reference';
import { Patient } from '@/lib/external-api/v2';
import { providerRegistry } from '@/lib/external-api/v2';
import { NotFoundError } from '@/lib/external-api/v2';
import logger from '../external-api/v2/utils/logger';

dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Service for coordinating patient data between external providers and local storage
 * Acts as a facade for the PatientReferenceService, PatientService, and external provider services
 */
export class PatientCoordinatorService {
  private patientReferenceService: PatientReferenceService;
  private patientService: PatientService;

  constructor(patientReferenceService?: PatientReferenceService, patientService?: PatientService) {
    this.patientReferenceService = patientReferenceService || new PatientReferenceService();
    this.patientService = patientService || new PatientService();
  }

  async excludeExistingPatients(patients: Patient[]): Promise<Patient[]> {
    const existingPatients = (
      await Promise.all(
        patients.map(async patient => {
          const existingPatients = await this.patientService.findByPhoneNumberAndBirthDate({
            phoneNumber: patient.phoneNumber ?? '',
            dateOfBirth: patient.dateOfBirth,
          });
          if (existingPatients.length > 1) {
            logger.warn(
              {
                context: 'patient.coordinator.service',
                phoneNumber: patient.phoneNumber ?? '',
                dateOfBirth: patient.dateOfBirth,
                providerId: patient.providerInfo?.externalId,
              },
              'Multiple patients found for the same phone number and date of birth',
            );
          }
          return existingPatients;
        }),
      )
    ).flat();

    const numberBirthDateSet = new Set(
      existingPatients.map(
        ep =>
          `${normalizePhoneNumberForCaching(ep.phoneNumber)}-${dayjs
            .utc(ep.dateOfBirth)
            .format('YYYY-MM-DD')}`,
      ),
    );

    const newPatients = patients.filter(
      p =>
        !numberBirthDateSet.has(
          `${normalizePhoneNumberForCaching(p.phoneNumber)}-${p.dateOfBirth}`,
        ),
    );

    logger.info({
      context: 'patient.coordinator.service',
      nextechPatients: patients.length,
      dbPatients: existingPatients.length,
      newPatients: newPatients.length,
    });

    return newPatients;
  }

  /**
   * Store a reference to a patient in Firestore
   * @param patientReference Patient reference data without id, createdAt, and updatedAt
   * @returns The created patient reference
   */
  async storePatientReference(
    patientReference: Omit<PatientReference, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<PatientReference> {
    try {
      // Store and return the patient reference
      return await this.patientReferenceService.storeNewPatient(patientReference);
    } catch (error) {
      console.error('Error storing patient reference:', error);
      throw error;
    }
  }

  /**
   * Store a complete patient record in Firestore
   * @param patient Patient data without id, createdAt, and updatedAt
   * @returns The created patient with id, createdAt, and updatedAt
   */
  async storePatient(
    patient: Omit<Patient, 'id'> & { createdAt?: Date; updatedAt?: Date },
  ): Promise<Patient & { createdAt: Date; updatedAt: Date }> {
    try {
      // Check if a patient record already exists by externalId
      if (patient.providerInfo?.externalId) {
        const existingPatient = await this.patientService.findByProviderId(
          patient.providerInfo.provider,
          patient.providerInfo.externalId,
        );

        if (existingPatient) {
          logger.info(
            {
              context: 'patient.coordinator.service',
              patientId: existingPatient.id,
              provider: patient.providerInfo?.provider,
              externalId: patient.providerInfo?.externalId,
            },
            'Patient already exists',
          );
          // Return the existing patient record
          return existingPatient;
        }
      }

      logger.info(
        {
          context: 'patient.coordinator.service',
          provider: patient.providerInfo?.provider,
          externalId: patient.providerInfo?.externalId,
        },
        'Storing new patient',
      );
      // Store and return the complete patient record only if it doesn't exist
      return await this.patientService.storeNewPatient(patient);
    } catch (error) {
      console.error('Error storing patient:', error);
      throw error;
    }
  }

  /**
   * Get a patient by provider and provider ID, updating the reference if needed
   * @param provider Provider name
   * @param providerId ID in the provider's system
   * @returns The patient reference
   */
  async getPatientByProviderId(provider: string, providerId: string): Promise<PatientReference> {
    try {
      // First, check if we have a reference to this patient
      const existingReference = await this.patientReferenceService.findByProviderId(
        provider,
        providerId,
      );

      // Get the provider service
      const providerService = providerRegistry.getProvider(provider);
      const patientService = providerService.getPatientService();

      // Get the patient from the external provider
      const externalPatient = await patientService.getPatientById(providerId);

      if (!externalPatient) {
        throw new NotFoundError(`Patient not found in ${provider} with ID ${providerId}`);
      }

      // If we don't have a reference yet, create one
      if (!existingReference) {
        const patientReference: Omit<PatientReference, 'id' | 'createdAt' | 'updatedAt'> = {
          provider,
          providerId: externalPatient.id,
          phoneNumber: externalPatient.phoneNumber,
        };

        return await this.patientReferenceService.storeNewPatient(patientReference);
      }

      // If we have a reference, check if it needs to be updated
      const updatedReference: PatientReference = {
        ...existingReference,
        phoneNumber: externalPatient.phoneNumber,
      };

      // Update the reference if needed and return it
      return await this.patientReferenceService.updateIfChanged(updatedReference);
    } catch (error) {
      console.error('Error getting patient by provider ID:', error);

      // Just re-throw all errors for simplicity
      throw error;
    }
  }

  /**
   * Get a complete patient record by provider and provider ID, updating the record if needed
   * @param provider Provider name
   * @param providerId ID in the provider's system
   * @returns The complete patient record
   */
  async getCompletePatientByProviderId(
    provider: string,
    providerId: string,
  ): Promise<Patient & { createdAt: Date; updatedAt: Date }> {
    try {
      // First, check if we have a complete patient record
      const existingPatient = await this.patientService.findByProviderId(provider, providerId);

      // Get the provider service
      const providerService = providerRegistry.getProvider(provider);
      const externalPatientService = providerService.getPatientService();

      // Get the patient from the external provider
      const externalPatient = await externalPatientService.getPatientById(providerId);

      if (!externalPatient) {
        throw new NotFoundError(`Patient not found in ${provider} with ID ${providerId}`);
      }

      // If we don't have a complete record yet, create one
      if (!existingPatient) {
        const patientData: Omit<Patient, 'id'> = {
          ...externalPatient,
          providerInfo: {
            provider,
            externalId: externalPatient.id,
          },
        };

        return await this.patientService.storeNewPatient(patientData);
      }

      // If we have a record, check if it needs to be updated
      const updatedPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        ...existingPatient,
        ...externalPatient,
        providerInfo: {
          provider,
          externalId: externalPatient.id,
        },
      };

      // Update the record if needed and return it
      return await this.patientService.updateIfChanged(updatedPatient);
    } catch (error) {
      console.error('Error getting complete patient by provider ID:', error);

      // Just re-throw all errors for simplicity
      throw error;
    }
  }

  /**
   * Get a complete patient record by internal ID
   * @param id Internal patient ID
   * @returns The complete patient record or null if not found
   */
  async getCompletePatientById(
    id: string,
  ): Promise<(Patient & { createdAt: Date; updatedAt: Date }) | null> {
    try {
      return await this.patientService.findById(id);
    } catch (error) {
      console.error('Error getting complete patient by ID:', error);
      throw error;
    }
  }

  /**
   * Get a complete patient records by phone number
   * @param phoneNumber Phone number
   * @returns The complete patient records
   */
  async getCompletePatientByPhoneNumber(phoneNumber: string): Promise<Patient[]> {
    try {
      return await this.patientService.findManyByPhoneNumber(phoneNumber);
    } catch (error) {
      console.error('Error getting complete patient by phone number:', error);
      throw error;
    }
  }
}
