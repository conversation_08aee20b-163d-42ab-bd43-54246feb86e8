import admin from 'firebase-admin';
import { Location } from '@/models/Location';
import { LocationService } from '@/lib/services/locationService';
import logger from '@/lib/external-api/v2/utils/logger';
import { Timestamp } from 'firebase-admin/firestore';

/**
 * Agent-Location Mapping interface
 * Maps DialogFlow agent IDs to specific locations
 */
export interface AgentLocationMapping {
  agentId: string;
  locationId: string;
  clinicId: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Firestore document structure for agent-location mappings
 */
interface AgentLocationMappingDoc {
  agentId: string;
  locationId: string;
  clinicId: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

/**
 * Service for managing agent-to-location mappings
 * Enables mapping DialogFlow agents to specific clinic locations
 */
export class AgentLocationMappingService {
  private static readonly COLLECTION_NAME = 'agent-location-mappings';
  private static db = admin.firestore();

  /**
   * Get location by agent ID
   * @param agentId The DialogFlow agent ID
   * @returns Location object or null if not found
   */
  static async getLocationByAgentId(agentId: string): Promise<Location | null> {
    try {
      if (!agentId) {
        logger.warn('AgentLocationMappingService.getLocationByAgentId called with empty agentId');
        return null;
      }

      // Query for agent mapping
      const mappingSnapshot = await this.db
        .collection(this.COLLECTION_NAME)
        .where('agentId', '==', agentId)
        .where('isActive', '==', true)
        .limit(1)
        .get();

      if (mappingSnapshot.empty) {
        logger.info({ agentId }, `No active mapping found for agent ${agentId}`);
        return null;
      }

      const mappingDoc = mappingSnapshot.docs[0];
      const mapping = mappingDoc.data() as AgentLocationMappingDoc;

      // Get the location details (LocationService.getLocationById requires clinicId)
      const location = await LocationService.getLocationById(mapping.locationId, mapping.clinicId);

      if (!location) {
        logger.warn(
          { agentId, locationId: mapping.locationId },
          `Location ${mapping.locationId} not found for agent ${agentId}`,
        );
        return null;
      }

      logger.info(
        { agentId, locationId: location.id, locationName: location.name },
        `Found location ${location.name} for agent ${agentId}`,
      );

      return location;
    } catch (error) {
      logger.error({ error, agentId }, `Error getting location for agent ${agentId}`);
      throw error;
    }
  }

  /**
   * Create a new agent-to-location mapping
   * @param agentId The DialogFlow agent ID
   * @param locationId The location ID to map to
   * @returns The created mapping
   */
  static async createMapping(agentId: string, locationId: string): Promise<AgentLocationMapping> {
    try {
      if (!agentId || !locationId) {
        throw new Error('AgentId and locationId are required');
      }

      // First get the location to validate it exists and get clinic info
      // We need to find the location's clinic ID first
      const locationDoc = await this.db.collection('locations').doc(locationId).get();
      if (!locationDoc.exists) {
        throw new Error(`Location ${locationId} not found`);
      }
      const locationData = locationDoc.data()!;
      const clinicId = locationData.clinicId;

      // Now get the full location details with clinic validation
      const location = await LocationService.getLocationById(locationId, clinicId);
      if (!location) {
        throw new Error(`Location ${locationId} not found or invalid`);
      }

      // Check if mapping already exists
      const existingMapping = await this.getMappingByAgentId(agentId);
      if (existingMapping) {
        throw new Error(
          `Agent ${agentId} is already mapped to location ${existingMapping.locationId}`,
        );
      }

      const now = Timestamp.now();
      const mappingData: AgentLocationMappingDoc = {
        agentId,
        locationId,
        clinicId: location.clinicId,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      };

      // Use agentId as document ID for easy retrieval
      await this.db.collection(this.COLLECTION_NAME).doc(agentId).set(mappingData);

      logger.info(
        { agentId, locationId, clinicId: location.clinicId },
        `Created agent-location mapping: ${agentId} -> ${locationId}`,
      );

      return {
        agentId,
        locationId,
        clinicId: location.clinicId,
        isActive: true,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      };
    } catch (error) {
      logger.error(
        { error, agentId, locationId },
        `Error creating mapping for agent ${agentId} to location ${locationId}`,
      );
      throw error;
    }
  }

  /**
   * Update an existing agent-to-location mapping
   * @param agentId The DialogFlow agent ID
   * @param locationId The new location ID to map to
   * @returns The updated mapping
   */
  static async updateMapping(agentId: string, locationId: string): Promise<AgentLocationMapping> {
    try {
      if (!agentId || !locationId) {
        throw new Error('AgentId and locationId are required');
      }

      // First get the location to validate it exists and get clinic info
      // We need to find the location's clinic ID first
      const locationDoc = await this.db.collection('locations').doc(locationId).get();
      if (!locationDoc.exists) {
        throw new Error(`Location ${locationId} not found`);
      }
      const locationData = locationDoc.data()!;
      const clinicId = locationData.clinicId;

      // Now get the full location details with clinic validation
      const location = await LocationService.getLocationById(locationId, clinicId);
      if (!location) {
        throw new Error(`Location ${locationId} not found or invalid`);
      }

      // Check if mapping exists
      const existingMapping = await this.getMappingByAgentId(agentId);
      if (!existingMapping) {
        throw new Error(`No mapping found for agent ${agentId}`);
      }

      const now = Timestamp.now();
      const updateData = {
        locationId,
        clinicId: location.clinicId,
        updatedAt: now,
      };

      await this.db.collection(this.COLLECTION_NAME).doc(agentId).update(updateData);

      logger.info(
        { agentId, oldLocationId: existingMapping.locationId, newLocationId: locationId },
        `Updated agent-location mapping: ${agentId} -> ${locationId}`,
      );

      return {
        ...existingMapping,
        locationId,
        clinicId: location.clinicId,
        updatedAt: now.toDate(),
      };
    } catch (error) {
      logger.error(
        { error, agentId, locationId },
        `Error updating mapping for agent ${agentId} to location ${locationId}`,
      );
      throw error;
    }
  }

  /**
   * Delete an agent-to-location mapping
   * @param agentId The DialogFlow agent ID
   */
  static async deleteMapping(agentId: string): Promise<void> {
    try {
      if (!agentId) {
        throw new Error('AgentId is required');
      }

      const mappingDoc = await this.db.collection(this.COLLECTION_NAME).doc(agentId).get();
      if (!mappingDoc.exists) {
        throw new Error(`No mapping found for agent ${agentId}`);
      }

      await this.db.collection(this.COLLECTION_NAME).doc(agentId).delete();

      logger.info({ agentId }, `Deleted agent-location mapping for agent ${agentId}`);
    } catch (error) {
      logger.error({ error, agentId }, `Error deleting mapping for agent ${agentId}`);
      throw error;
    }
  }

  /**
   * Get all mappings for a specific clinic
   * @param clinicId The clinic ID
   * @returns Array of mappings for the clinic
   */
  static async getMappingsByClinic(clinicId: number): Promise<AgentLocationMapping[]> {
    try {
      const mappingsSnapshot = await this.db
        .collection(this.COLLECTION_NAME)
        .where('clinicId', '==', clinicId)
        .where('isActive', '==', true)
        .get();

      const mappings: AgentLocationMapping[] = [];
      mappingsSnapshot.docs.forEach((doc: admin.firestore.QueryDocumentSnapshot) => {
        const data = doc.data() as AgentLocationMappingDoc;
        mappings.push({
          agentId: data.agentId,
          locationId: data.locationId,
          clinicId: data.clinicId,
          isActive: data.isActive,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
        });
      });

      logger.info(
        { clinicId, count: mappings.length },
        `Found ${mappings.length} agent mappings for clinic ${clinicId}`,
      );

      return mappings;
    } catch (error) {
      logger.error({ error, clinicId }, `Error getting mappings for clinic ${clinicId}`);
      throw error;
    }
  }

  /**
   * Get mapping by agent ID (internal helper)
   * @param agentId The DialogFlow agent ID
   * @returns Mapping or null if not found
   */
  private static async getMappingByAgentId(agentId: string): Promise<AgentLocationMapping | null> {
    try {
      const mappingDoc = await this.db.collection(this.COLLECTION_NAME).doc(agentId).get();

      if (!mappingDoc.exists) {
        return null;
      }

      const data = mappingDoc.data() as AgentLocationMappingDoc;
      return {
        agentId: data.agentId,
        locationId: data.locationId,
        clinicId: data.clinicId,
        isActive: data.isActive,
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate(),
      };
    } catch (error) {
      logger.error({ error, agentId }, `Error getting mapping for agent ${agentId}`);
      throw error;
    }
  }

  /**
   * Get all active mappings (for admin purposes)
   * @returns Array of all active mappings
   */
  static async getAllActiveMappings(): Promise<AgentLocationMapping[]> {
    try {
      const mappingsSnapshot = await this.db
        .collection(this.COLLECTION_NAME)
        .where('isActive', '==', true)
        .get();

      const mappings: AgentLocationMapping[] = [];
      mappingsSnapshot.docs.forEach((doc: admin.firestore.QueryDocumentSnapshot) => {
        const data = doc.data() as AgentLocationMappingDoc;
        mappings.push({
          agentId: data.agentId,
          locationId: data.locationId,
          clinicId: data.clinicId,
          isActive: data.isActive,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
        });
      });

      logger.info({ count: mappings.length }, `Found ${mappings.length} active agent mappings`);

      return mappings;
    } catch (error) {
      logger.error({ error }, 'Error getting all active mappings');
      throw error;
    }
  }
}
