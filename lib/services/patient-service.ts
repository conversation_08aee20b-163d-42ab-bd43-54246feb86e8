import { v4 as uuidv4 } from 'uuid';
import admin from '@/utils/firebase-admin';
import { Patient } from '../external-api/v2/models/types';
import { RepositoryManager } from '@/lib/repositories';

/**
 * Service for managing patients in Firestore
 * Stores complete patient information from external provider systems
 */
export class PatientService {
  private patientsCollection: admin.firestore.CollectionReference;
  private repositoryManager: RepositoryManager;

  constructor() {
    const db = admin.firestore();
    this.patientsCollection = db.collection('patients');
    this.repositoryManager = RepositoryManager.getInstance();
  }

  /** Ensure MySQL connection */
  private async ensureInitialized() {
    await this.repositoryManager.initialize();
  }

  /**
   * Store a new patient
   * @param patient Patient data without id, createdAt, and updatedAt
   * @returns The created patient with id, createdAt, and updatedAt
   */
  async storeNewPatient(
    patient: Omit<Patient, 'id'> & { createdAt?: Date; updatedAt?: Date },
  ): Promise<Patient & { createdAt: Date; updatedAt: Date }> {
    try {
      await this.ensureInitialized();

      const id = uuidv4();
      const now = new Date();

      const newPatient: Patient & {
        provider: string;
        providerId: string;
        createdAt: Date;
        updatedAt: Date;
      } = {
        ...patient,
        id,
        insurances: patient.insurances ?? [],
        provider: patient.providerInfo?.provider,
        providerId: patient.providerInfo?.externalId,
        phoneNumber: normalizePhoneNumberForCaching(patient.phoneNumber),
        identifiers: [],
        createdAt: now,
        updatedAt: now,
      };

      const created = (await this.repositoryManager.patients.create(newPatient)) as Patient & {
        createdAt: Date;
        updatedAt: Date;
      };
      return created;
    } catch (error) {
      console.error('Error storing new patient:', error);
      throw error;
    }
  }

  /**
   * Find a patient by provider and provider ID
   * @param provider Provider name (e.g., "nextech")
   * @param providerId ID in the provider's system
   * @returns The patient or null if not found
   */
  async findByProviderId(
    provider: string,
    providerId: string,
  ): Promise<(Patient & { createdAt: Date; updatedAt: Date }) | null> {
    try {
      await this.ensureInitialized();
      return (await this.repositoryManager.patients.findByProviderId(provider, providerId)) as
        | (Patient & { createdAt: Date; updatedAt: Date })
        | null;
    } catch (error) {
      console.error('Error finding patient by provider ID:', error);
      throw error;
    }
  }

  async findByPhoneNumberAndBirthDate({
    phoneNumber,
    dateOfBirth,
  }: {
    phoneNumber: string;
    dateOfBirth: string;
  }): Promise<Patient[]> {
    try {
      await this.ensureInitialized();
      const normalizedPhoneNumber = normalizePhoneNumberForCaching(phoneNumber);
      return (await this.repositoryManager.patients.findByPhoneNumberAndBirthDate({
        phoneNumber: normalizedPhoneNumber ?? '',
        dateOfBirth,
      })) as Patient[];
    } catch (error) {
      console.error('Error finding patients by provider ID:', error);
      throw error;
    }
  }

  /**
   * Find a patient by phone number
   * @param phoneNumber Phone number to search for (will be normalized to digits only)
   * @returns The patient or null if not found
   */
  async findByPhoneNumber(
    phoneNumber: string,
  ): Promise<(Patient & { createdAt: Date; updatedAt: Date }) | null> {
    try {
      await this.ensureInitialized();
      const normalizedPhoneNumber = normalizePhoneNumberForCaching(phoneNumber);
      return (await this.repositoryManager.patients.findByPhoneNumber(
        normalizedPhoneNumber ?? '',
      )) as (Patient & { createdAt: Date; updatedAt: Date }) | null;
    } catch (error) {
      console.error('Error finding patient by phone number:', error);
      throw error;
    }
  }

  async findManyByPhoneNumber(phoneNumber: string): Promise<Patient[]> {
    try {
      await this.ensureInitialized();
      const normalizedPhoneNumber = normalizePhoneNumberForCaching(phoneNumber);
      return (await this.repositoryManager.patients.findManyByPhoneNumber(
        normalizedPhoneNumber ?? '',
      )) as Patient[];
    } catch (error) {
      console.error('Error finding patients by phone number:', error);
      throw error;
    }
  }

  /**
   * Update a patient if data has changed
   * @param patient Updated patient data
   * @returns The updated patient
   */
  async updateIfChanged(
    patient: Patient & { createdAt: Date; updatedAt: Date },
  ): Promise<Patient & { createdAt: Date; updatedAt: Date }> {
    try {
      await this.ensureInitialized();
      const currentData = (await this.repositoryManager.patients.findById(patient.id)) as
        | (Patient & { createdAt: Date; updatedAt: Date })
        | null;

      if (!currentData) {
        throw new Error(`Patient with ID ${patient.id} not found`);
      }

      // Check if any fields have changed (comparing key fields)
      const hasChanged =
        patient.firstName !== currentData.firstName ||
        patient.lastName !== currentData.lastName ||
        patient.dateOfBirth !== currentData.dateOfBirth ||
        patient.gender !== currentData.gender ||
        patient.email !== currentData.email ||
        patient.phoneNumber !== currentData.phoneNumber ||
        patient.notes !== currentData.notes ||
        JSON.stringify(patient.address) !== JSON.stringify(currentData.address) ||
        JSON.stringify(patient.insurances) !== JSON.stringify(currentData.insurances) ||
        JSON.stringify(patient.identifiers) !== JSON.stringify(currentData.identifiers);

      // Only update if data has changed
      if (hasChanged) {
        const now = new Date();
        const updatedPatient: Patient & { providerId: string; createdAt: Date; updatedAt: Date } = {
          ...patient,
          providerId: patient.providerInfo?.externalId,
          phoneNumber: normalizePhoneNumberForCaching(patient.phoneNumber),
          identifiers: undefined,
          updatedAt: now,
        };

        await this.repositoryManager.patients.update(patient.id, updatedPatient);
        return updatedPatient;
      }

      // Return the original patient if no changes
      return patient;
    } catch (error) {
      console.error('Error updating patient:', error);
      throw error;
    }
  }

  /**
   * Find a patient by ID
   * @param id Patient ID
   * @returns The patient or null if not found
   */
  async findById(id: string): Promise<(Patient & { createdAt: Date; updatedAt: Date }) | null> {
    try {
      await this.ensureInitialized();
      return (await this.repositoryManager.patients.findById(id)) as
        | (Patient & {
            createdAt: Date;
            updatedAt: Date;
          })
        | null;
    } catch (error) {
      console.error('Error finding patient by ID:', error);
      throw error;
    }
  }
}

// Helper function to normalize phone numbers for searching
export const normalizePhoneNumberForCaching = (phoneNumber?: string): string | undefined => {
  if (phoneNumber) {
    let normalizedPhoneNumber = phoneNumber.replace(/\D/g, ''); // Remove all non-digit characters
    // Remove leading 1 if it exists
    if (normalizedPhoneNumber.length === 11 && normalizedPhoneNumber.startsWith('1')) {
      normalizedPhoneNumber = normalizedPhoneNumber.substring(1);
    }
    return normalizedPhoneNumber;
  }
  return undefined;
};
