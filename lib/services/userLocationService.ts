import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import admin from 'firebase-admin';

// Initialize Firestore
const db = admin.firestore();
const usersCollection = db.collection('users');
const locationsCollection = db.collection('locations');
const practicesCollection = db.collection('practices');

/**
 * Service for managing User-Location assignments with proper business logic and validation
 */
export class UserLocationService {
  /**
   * Get all locations assigned to a specific user
   */
  static async getUserLocations(
    userId: string,
    clinicId: number,
  ): Promise<{
    user: User;
    locations: Location[];
    practices: Practice[];
    currentLocationId?: string;
  }> {
    try {
      // Get user data
      const userDoc = await usersCollection.doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const user = this.convertToUser(userDoc);

      // Validate clinic scope
      if (user.clinicId !== clinicId) {
        throw new Error('User does not belong to the specified clinic');
      }

      // Get user's assigned locations
      const locations: Location[] = [];
      if (user.locationIds && user.locationIds.length > 0) {
        // Filter out any empty, null, or invalid location IDs
        const validLocationIds = user.locationIds.filter(
          id => id && typeof id === 'string' && id.trim() !== '',
        );

        if (validLocationIds.length > 0) {
          const locationDocs = await Promise.all(
            validLocationIds.map(locationId => locationsCollection.doc(locationId).get()),
          );

          for (const locationDoc of locationDocs) {
            if (locationDoc.exists) {
              const location = this.convertToLocation(locationDoc);
              // Double-check clinic scope
              if (location.clinicId === clinicId) {
                locations.push(location);
              }
            }
          }
        }
      }

      // Get unique practices from locations
      const practiceIds = Array.from(new Set(locations.map(loc => loc.practiceId)));
      const practices: Practice[] = [];

      // Filter out any empty, null, or invalid practice IDs
      const validPracticeIds = practiceIds.filter(
        id => id && typeof id === 'string' && id.trim() !== '',
      );

      if (validPracticeIds.length > 0) {
        const practiceDocs = await Promise.all(
          validPracticeIds.map(practiceId => practicesCollection.doc(practiceId).get()),
        );

        for (const practiceDoc of practiceDocs) {
          if (practiceDoc.exists) {
            const practice = this.convertToPractice(practiceDoc);
            // Double-check clinic scope
            if (practice.clinicId === clinicId) {
              practices.push(practice);
            }
          }
        }
      }

      return {
        user,
        locations,
        practices,
        currentLocationId: user.currentLocationId,
      };
    } catch (error) {
      console.error('Error getting user locations:', error);
      throw error;
    }
  }

  /**
   * Get all users assigned to a specific location
   */
  static async getLocationUsers(
    locationId: string,
    clinicId: number,
    options: { limit?: number; offset?: number } = {},
  ): Promise<{
    location: Location;
    users: User[];
    total: number;
  }> {
    try {
      // Get location data
      const locationDoc = await locationsCollection.doc(locationId).get();
      if (!locationDoc.exists) {
        throw new Error('Location not found');
      }

      const location = this.convertToLocation(locationDoc);

      // Validate clinic scope
      if (location.clinicId !== clinicId) {
        throw new Error('Location does not belong to the specified clinic');
      }

      // Get all users who have this location in their locationIds array
      const usersSnapshot = await usersCollection
        .where('clinicId', '==', clinicId)
        .where('locationIds', 'array-contains', locationId)
        .get();

      const allUsers = usersSnapshot.docs.map(doc => this.convertToUser(doc));

      // Apply pagination
      const { limit = 50, offset = 0 } = options;
      const paginatedUsers = allUsers.slice(offset, offset + limit);

      return {
        location,
        users: paginatedUsers,
        total: allUsers.length,
      };
    } catch (error) {
      console.error('Error getting location users:', error);
      throw error;
    }
  }

  /**
   * Assign user to additional locations
   */
  static async assignUserToLocations(
    userId: string,
    locationIds: string[],
    requestingUser: User,
    options: { setAsCurrent?: boolean } = {},
  ): Promise<User> {
    try {
      // Validate permissions
      this.validateUserPermissions(requestingUser);

      // Get current user data
      const userDoc = await usersCollection.doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const currentUser = this.convertToUser(userDoc);

      // Validate clinic scope
      if (currentUser.clinicId !== requestingUser.clinicId) {
        throw new Error('Cannot assign locations to users outside your clinic');
      }

      // Validate all locations belong to the same clinic
      await this.validateLocationsInClinic(locationIds, requestingUser.clinicId!);

      // Get current locationIds array (or initialize empty array)
      const currentLocationIds = currentUser.locationIds || [];

      // Merge with new location IDs (remove duplicates)
      const updatedLocationIds = Array.from(new Set([...currentLocationIds, ...locationIds]));

      // Prepare update data
      const updateData: Partial<User> = {
        locationIds: updatedLocationIds,
        updatedAt: new Date(),
      };

      // Set current location if requested and valid
      if (options.setAsCurrent && locationIds.length > 0) {
        const newCurrentLocationId = locationIds[0];
        if (updatedLocationIds.includes(newCurrentLocationId)) {
          updateData.currentLocationId = newCurrentLocationId;
        }
      }

      // Update user in Firestore using transaction for consistency
      await db.runTransaction(async transaction => {
        const userRef = usersCollection.doc(userId);
        transaction.update(userRef, updateData);
      });

      // Return updated user
      return {
        ...currentUser,
        ...updateData,
      };
    } catch (error) {
      console.error('Error assigning user to locations:', error);
      throw error;
    }
  }

  /**
   * Remove user from specific locations
   */
  static async removeUserFromLocations(
    userId: string,
    locationIds: string[],
    requestingUser: User,
  ): Promise<User> {
    try {
      // Validate permissions
      this.validateUserPermissions(requestingUser);

      // Get current user data
      const userDoc = await usersCollection.doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const currentUser = this.convertToUser(userDoc);

      // Validate clinic scope
      if (currentUser.clinicId !== requestingUser.clinicId) {
        throw new Error('Cannot modify locations for users outside your clinic');
      }

      // Get current locationIds array
      const currentLocationIds = currentUser.locationIds || [];

      // Remove specified location IDs
      const updatedLocationIds = currentLocationIds.filter(
        locationId => !locationIds.includes(locationId),
      );

      // Prepare update data
      const updateData: Partial<User> = {
        locationIds: updatedLocationIds,
        updatedAt: new Date(),
      };

      // Clear current location if it's being removed
      if (currentUser.currentLocationId && locationIds.includes(currentUser.currentLocationId)) {
        updateData.currentLocationId =
          updatedLocationIds.length > 0 ? updatedLocationIds[0] : undefined;
      }

      // Update user in Firestore using transaction for consistency
      await db.runTransaction(async transaction => {
        const userRef = usersCollection.doc(userId);
        transaction.update(userRef, updateData);
      });

      // Return updated user
      return {
        ...currentUser,
        ...updateData,
      };
    } catch (error) {
      console.error('Error removing user from locations:', error);
      throw error;
    }
  }

  /**
   * Switch user's current location
   */
  static async switchUserLocation(
    userId: string,
    newLocationId: string,
    requestingUser: User,
  ): Promise<User> {
    try {
      // Get current user data
      const userDoc = await usersCollection.doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const currentUser = this.convertToUser(userDoc);

      // Validate permissions (user can switch their own location, or admin can switch any)
      if (userId !== requestingUser.id) {
        this.validateUserPermissions(requestingUser);

        // Validate clinic scope for admin operations
        if (currentUser.clinicId !== requestingUser.clinicId) {
          throw new Error('Cannot modify locations for users outside your clinic');
        }
      }

      // Validate user has access to the new location
      const userLocationIds = currentUser.locationIds || [];
      if (!userLocationIds.includes(newLocationId)) {
        throw new Error('User does not have access to the specified location');
      }

      // Validate location exists and belongs to correct clinic
      const locationDoc = await locationsCollection.doc(newLocationId).get();
      if (!locationDoc.exists) {
        throw new Error('Location not found');
      }

      const location = this.convertToLocation(locationDoc);
      if (location.clinicId !== currentUser.clinicId) {
        throw new Error("Location does not belong to user's clinic");
      }

      // Update current location
      const updateData = {
        currentLocationId: newLocationId,
        updatedAt: new Date(),
      };

      await usersCollection.doc(userId).update(updateData);

      // Return updated user
      return {
        ...currentUser,
        ...updateData,
      };
    } catch (error) {
      console.error('Error switching user location:', error);
      throw error;
    }
  }

  /**
   * Bulk assign users to locations
   */
  static async bulkAssignUsersToLocations(
    assignments: Array<{ userId: string; locationIds: string[] }>,
    requestingUser: User,
  ): Promise<{
    successful: Array<{ userId: string; user: User }>;
    failed: Array<{ userId: string; error: string }>;
  }> {
    try {
      // Validate permissions
      this.validateUserPermissions(requestingUser);

      const successful: Array<{ userId: string; user: User }> = [];
      const failed: Array<{ userId: string; error: string }> = [];

      // Process assignments in batches to avoid overwhelming Firestore
      const batchSize = 10;
      for (let i = 0; i < assignments.length; i += batchSize) {
        const batch = assignments.slice(i, i + batchSize);

        await Promise.all(
          batch.map(async assignment => {
            try {
              const updatedUser = await this.assignUserToLocations(
                assignment.userId,
                assignment.locationIds,
                requestingUser,
              );
              successful.push({ userId: assignment.userId, user: updatedUser });
            } catch (error) {
              failed.push({
                userId: assignment.userId,
                error: error instanceof Error ? error.message : String(error),
              });
            }
          }),
        );
      }

      return { successful, failed };
    } catch (error) {
      console.error('Error in bulk assignment:', error);
      throw error;
    }
  }

  /**
   * Get user's current location context
   */
  static async getUserLocationContext(userId: string): Promise<{
    currentLocation?: Location;
    availableLocations: Location[];
    availablePractices: Practice[];
  }> {
    try {
      // Get user data
      const userDoc = await usersCollection.doc(userId).get();
      if (!userDoc.exists) {
        throw new Error('User not found');
      }

      const user = this.convertToUser(userDoc);
      let currentLocation: Location | undefined;
      const availableLocations: Location[] = [];

      // Admin users get access to ALL locations in their clinic
      const isAdmin = user.role === UserRole.CLINIC_ADMIN || user.role === UserRole.SUPER_ADMIN;

      if (isAdmin && user.clinicId) {
        console.log(`Admin user ${userId} getting all locations for clinic ${user.clinicId}`);

        // Get all locations in the clinic for admin users (including inactive ones)
        let allLocationsSnapshot;
        if (isAdmin) {
          // Admin users can see ALL locations, including inactive ones
          allLocationsSnapshot = await locationsCollection
            .where('clinicId', '==', user.clinicId)
            .get();
        } else {
          // Non-admin users only see active locations
          allLocationsSnapshot = await locationsCollection
            .where('clinicId', '==', user.clinicId)
            .where('isActive', '==', true)
            .get();
        }

        allLocationsSnapshot.forEach(doc => {
          const location = this.convertToLocation(doc);
          availableLocations.push(location);

          // Set current location if it matches
          if (user.currentLocationId === location.id) {
            currentLocation = location;
          }
        });

        // If admin has no current location set, use the first available one
        if (!currentLocation && availableLocations.length > 0) {
          currentLocation = availableLocations[0];
          console.log(`Setting default current location for admin: ${currentLocation.id}`);
        }
      } else {
        // Regular users only get their assigned locations
        const userLocationIds = user.locationIds || [];
        console.log(`Regular user ${userId} getting assigned locations:`, userLocationIds);

        // Filter out any empty, null, or invalid location IDs
        const validLocationIds = userLocationIds.filter(
          id => id && typeof id === 'string' && id.trim() !== '',
        );

        if (validLocationIds.length > 0) {
          const locationDocs = await Promise.all(
            validLocationIds.map(locationId => locationsCollection.doc(locationId).get()),
          );

          for (const locationDoc of locationDocs) {
            if (locationDoc.exists) {
              const location = this.convertToLocation(locationDoc);
              availableLocations.push(location);

              // Set current location if it matches
              if (user.currentLocationId === location.id) {
                currentLocation = location;
              }
            }
          }
        }
      }

      // Get all practices from available locations
      const practiceIds = Array.from(new Set(availableLocations.map(loc => loc.practiceId)));
      const availablePractices: Practice[] = [];

      // Filter out any empty, null, or invalid practice IDs
      const validPracticeIds = practiceIds.filter(
        id => id && typeof id === 'string' && id.trim() !== '',
      );

      if (validPracticeIds.length > 0) {
        const practiceDocs = await Promise.all(
          validPracticeIds.map(practiceId => practicesCollection.doc(practiceId).get()),
        );

        for (const practiceDoc of practiceDocs) {
          if (practiceDoc.exists) {
            availablePractices.push(this.convertToPractice(practiceDoc));
          }
        }
      }

      console.log(`User ${userId} location context:`, {
        isAdmin,
        currentLocationId: currentLocation?.id,
        availableLocationsCount: availableLocations.length,
        availablePracticesCount: availablePractices.length,
      });

      return {
        currentLocation,
        availableLocations,
        availablePractices,
      };
    } catch (error) {
      console.error('Error getting user location context:', error);
      throw error;
    }
  }

  /**
   * Validate user has permission to manage user-location assignments
   */
  private static validateUserPermissions(user: User): void {
    // Only CLINIC_ADMIN and SUPER_ADMIN can manage user-location assignments
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      throw new Error('Insufficient permissions to manage user-location assignments');
    }

    // Ensure user has a clinic ID
    if (!user.clinicId) {
      throw new Error('User must belong to a clinic to manage assignments');
    }
  }

  /**
   * Validate all locations belong to the specified clinic
   */
  private static async validateLocationsInClinic(
    locationIds: string[],
    clinicId: number,
  ): Promise<void> {
    if (locationIds.length === 0) return;

    // Filter out any empty, null, or invalid location IDs
    const validLocationIds = locationIds.filter(
      id => id && typeof id === 'string' && id.trim() !== '',
    );

    if (validLocationIds.length === 0) {
      throw new Error('No valid location IDs provided');
    }

    const locationDocs = await Promise.all(
      validLocationIds.map(locationId => locationsCollection.doc(locationId).get()),
    );

    for (let i = 0; i < locationDocs.length; i++) {
      const locationDoc = locationDocs[i];
      const locationId = validLocationIds[i];

      if (!locationDoc.exists) {
        throw new Error(`Location ${locationId} not found`);
      }

      const location = this.convertToLocation(locationDoc);
      if (location.clinicId !== clinicId) {
        throw new Error(`Location ${locationId} does not belong to your clinic`);
      }
    }
  }

  /**
   * Convert Firestore document to User model
   */
  private static convertToUser(doc: admin.firestore.DocumentSnapshot): User {
    const data = doc.data();
    if (!data) throw new Error(`User with ID ${doc.id} not found`);

    return {
      id: doc.id,
      email: data.email || '',
      phone: data.phone || undefined,
      name: data.name || '',
      role: data.role || UserRole.STAFF,
      specialty: data.specialty || undefined,
      clinicId: data.clinicId || null,
      profilePicture: data.profilePicture || undefined,
      canTakeAppointments: data.canTakeAppointments || false,
      locationIds: data.locationIds || [],
      currentLocationId: data.currentLocationId || undefined,
      practiceIds: data.practiceIds || undefined,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    };
  }

  /**
   * Convert Firestore document to Location model
   */
  private static convertToLocation(doc: admin.firestore.DocumentSnapshot): Location {
    const data = doc.data();
    if (!data) throw new Error(`Location with ID ${doc.id} not found`);

    return {
      id: doc.id,
      clinicId: data.clinicId || 0,
      practiceId: data.practiceId || '',
      name: data.name || '',
      address: data.address || '',
      phone: data.phone || undefined,
      timeZone: data.timeZone || 'America/New_York',
      isActive: data.isActive ?? true,
      practiceName: data.practiceName || '',
      officeHours: data.officeHours || undefined,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    };
  }

  /**
   * Convert Firestore document to Practice model
   */
  private static convertToPractice(doc: admin.firestore.DocumentSnapshot): Practice {
    const data = doc.data();
    if (!data) throw new Error(`Practice with ID ${doc.id} not found`);

    return {
      id: doc.id,
      clinicId: data.clinicId || 0,
      name: data.name || '',
      description: data.description || undefined,
      isActive: data.isActive ?? true,
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    };
  }
}
