import admin from 'firebase-admin';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import { User, UserRole } from '@/models/auth';
import { getRepositories } from '@/lib/repositories';

/**
 * Service class for managing location operations with practice relationships
 */
export class LocationService {
  private static db = admin.firestore();

  /**
   * Get all locations for a clinic with optional filtering
   */
  static async getLocationsByClinicId(
    clinicId: number,
    options: {
      practiceId?: string;
      includeInactive?: boolean;
      limit?: number;
      offset?: number;
    } = {},
  ): Promise<{ locations: Location[]; total: number }> {
    try {
      const { practiceId, includeInactive = false, limit = 50, offset = 0 } = options;

      // Ensure repositories & MySQL are initialized
      const repoManager = getRepositories();
      await repoManager.initialize();

      const locationsRepo = repoManager.locations;

      // Build dynamic WHERE clause
      const where: Partial<Location> = { clinicId } as Partial<Location>;
      if (practiceId) {
        (where as Partial<Location>).practiceId = practiceId;
      }
      if (!includeInactive) {
        (where as Partial<Location>).isActive = true;
      }

      const result = await locationsRepo.findMany({
        where,
        orderBy: [{ field: 'name', direction: 'asc' }],
        limit,
        offset,
      });

      const locations = result.items;
      const total =
        result.total !== undefined
          ? result.total
          : offset + locations.length + (result.hasMore ? 1 : 0);

      return {
        locations,
        total,
      };
    } catch (error) {
      console.error('Error getting locations by clinic ID (MySQL):', error);
      return {
        locations: [],
        total: 0,
      };
    }
  }

  /**
   * Get a location by ID with clinic validation
   */
  static async getLocationById(locationId: string, clinicId: number): Promise<Location | null> {
    try {
      const locationDoc = await this.db.collection('locations').doc(locationId).get();

      if (!locationDoc.exists) {
        return null;
      }

      const data = locationDoc.data()!;

      // Validate clinic ownership
      if (data.clinicId !== clinicId) {
        throw new Error('Location does not belong to the specified clinic');
      }

      return {
        id: locationDoc.id,
        clinicId: data.clinicId || 0,
        practiceId: data.practiceId || '',
        name: data.name || '',
        address: data.address || '',
        phone: data.phone || undefined,
        timeZone: data.timeZone || 'America/New_York',
        isActive: data.isActive ?? true,
        practiceName: data.practiceName || '',
        officeHours: data.officeHours || undefined,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      };
    } catch (error) {
      console.error('Error getting location by ID:', error);
      if (error instanceof Error && error.message.includes('does not belong')) {
        throw error;
      }
      throw new Error('Failed to retrieve location');
    }
  }

  /**
   * Create a new location (requires practice)
   */
  static async createLocation(
    locationData: {
      name: string;
      address: string;
      phone?: string;
      officeHours?: Record<string, { start: string; end: string } | null>;
      practiceId: string;
    },
    clinicId: number,
    requestingUser: User,
  ): Promise<Location> {
    try {
      // Validate permissions
      if (
        requestingUser.role !== UserRole.CLINIC_ADMIN &&
        requestingUser.role !== UserRole.SUPER_ADMIN
      ) {
        throw new Error('Insufficient permissions to create locations');
      }

      // Validate practice exists and belongs to clinic
      const practiceDoc = await this.db.collection('practices').doc(locationData.practiceId).get();
      if (!practiceDoc.exists) {
        throw new Error('Practice not found');
      }

      const practiceData = practiceDoc.data()!;
      if (practiceData.clinicId !== clinicId) {
        throw new Error('Practice does not belong to the specified clinic');
      }

      // Validate required fields
      if (!locationData.name?.trim()) {
        throw new Error('Location name is required');
      }

      if (!locationData.address?.trim()) {
        throw new Error('Location address is required');
      }

      // Create location document
      const now = admin.firestore.Timestamp.now();
      const locationRef = this.db.collection('locations').doc();

      const newLocationData = {
        clinicId,
        practiceId: locationData.practiceId,
        name: locationData.name.trim(),
        address: locationData.address.trim(),
        phone: locationData.phone?.trim() || null,
        officeHours: locationData.officeHours || null,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      };

      await locationRef.set(newLocationData);

      return {
        id: locationRef.id,
        clinicId,
        practiceId: locationData.practiceId,
        name: locationData.name.trim(),
        address: locationData.address.trim(),
        phone: locationData.phone?.trim(),
        timeZone: 'America/New_York',
        isActive: true,
        practiceName: practiceData.name || '',
        officeHours: locationData.officeHours,
        createdAt: now.toDate(),
        updatedAt: now.toDate(),
      };
    } catch (error) {
      console.error('Error creating location:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create location');
    }
  }

  /**
   * Update an existing location
   */
  static async updateLocation(
    locationId: string,
    updateData: {
      name?: string;
      address?: string;
      phone?: string;
      officeHours?: Record<string, { start: string; end: string } | null>;
      isActive?: boolean;
    },
    clinicId: number,
    requestingUser: User,
  ): Promise<Location> {
    try {
      // Validate permissions
      if (
        requestingUser.role !== UserRole.CLINIC_ADMIN &&
        requestingUser.role !== UserRole.SUPER_ADMIN
      ) {
        throw new Error('Insufficient permissions to update locations');
      }

      // Get existing location
      const existingLocation = await this.getLocationById(locationId, clinicId);
      if (!existingLocation) {
        throw new Error('Location not found');
      }

      // Validate update data
      const updates: Record<string, unknown> = {
        updatedAt: admin.firestore.Timestamp.now(),
      };

      if (updateData.name !== undefined) {
        if (!updateData.name.trim()) {
          throw new Error('Location name cannot be empty');
        }
        updates.name = updateData.name.trim();
      }

      if (updateData.address !== undefined) {
        if (!updateData.address.trim()) {
          throw new Error('Location address cannot be empty');
        }
        updates.address = updateData.address.trim();
      }

      if (updateData.phone !== undefined) {
        updates.phone = updateData.phone?.trim() || null;
      }

      if (updateData.officeHours !== undefined) {
        updates.officeHours = updateData.officeHours;
      }

      if (updateData.isActive !== undefined) {
        updates.isActive = updateData.isActive;
      }

      // --- Dual-database update ---
      // 1) Firestore (existing behavior)
      await this.db.collection('locations').doc(locationId).update(updates);

      // 2) MySQL via LocationsRepository
      try {
        const { getRepositories } = await import('../repositories');
        const repoManager = getRepositories();
        await repoManager.initialize();
        const locationsRepo = repoManager.locations;

        // Prepare update payload for MySQL (exclude Firestore Timestamp objects)
        const mysqlUpdates: Record<string, unknown> = { ...updates };
        if (mysqlUpdates.updatedAt instanceof admin.firestore.Timestamp) {
          mysqlUpdates.updatedAt = mysqlUpdates.updatedAt.toDate();
        }

        await locationsRepo.update(locationId, mysqlUpdates, { skipFirestore: true });
      } catch (mysqlError) {
        // Log but do not block if MySQL update fails
        console.error('MySQL update for location failed:', mysqlError);
      }

      // Return updated location (reads from Firestore to preserve previous behavior)
      const updatedLocation = await this.getLocationById(locationId, clinicId);
      if (!updatedLocation) {
        throw new Error('Failed to retrieve updated location');
      }
      return updatedLocation;
    } catch (error) {
      console.error('Error updating location:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update location');
    }
  }

  /**
   * Transfer location to a different practice
   */
  static async transferLocationToPractice(
    locationId: string,
    newPracticeId: string,
    clinicId: number,
    requestingUser: User,
  ): Promise<Location> {
    try {
      // Validate permissions
      if (
        requestingUser.role !== UserRole.CLINIC_ADMIN &&
        requestingUser.role !== UserRole.SUPER_ADMIN
      ) {
        throw new Error('Insufficient permissions to transfer locations');
      }

      // Validate location exists and belongs to clinic
      const existingLocation = await this.getLocationById(locationId, clinicId);
      if (!existingLocation) {
        throw new Error('Location not found');
      }

      // Validate new practice exists and belongs to clinic
      const practiceDoc = await this.db.collection('practices').doc(newPracticeId).get();
      if (!practiceDoc.exists) {
        throw new Error('Target practice not found');
      }

      const practiceData = practiceDoc.data()!;
      if (practiceData.clinicId !== clinicId) {
        throw new Error('Target practice does not belong to the specified clinic');
      }

      // Update location's practice
      await this.db.collection('locations').doc(locationId).update({
        practiceId: newPracticeId,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      // Return updated location
      const updatedLocation = await this.getLocationById(locationId, clinicId);
      if (!updatedLocation) {
        throw new Error('Failed to retrieve updated location');
      }
      return updatedLocation;
    } catch (error) {
      console.error('Error transferring location to practice:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to transfer location');
    }
  }

  /**
   * Delete a location (soft delete by setting isActive to false)
   */
  static async deleteLocation(
    locationId: string,
    clinicId: number,
    requestingUser: User,
  ): Promise<void> {
    try {
      // Validate permissions
      if (
        requestingUser.role !== UserRole.CLINIC_ADMIN &&
        requestingUser.role !== UserRole.SUPER_ADMIN
      ) {
        throw new Error('Insufficient permissions to delete locations');
      }

      // Validate location exists and belongs to clinic
      const existingLocation = await this.getLocationById(locationId, clinicId);
      if (!existingLocation) {
        throw new Error('Location not found');
      }

      // Check if location has assigned users
      const usersSnapshot = await this.db
        .collection('users')
        .where('clinicId', '==', clinicId)
        .where('locationIds', 'array-contains', locationId)
        .get();

      if (!usersSnapshot.empty) {
        throw new Error(
          'Cannot delete location with assigned users. Please remove all user assignments first.',
        );
      }

      // Soft delete by setting isActive to false
      await this.db.collection('locations').doc(locationId).update({
        isActive: false,
        updatedAt: admin.firestore.Timestamp.now(),
      });
    } catch (error) {
      console.error('Error deleting location:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete location');
    }
  }

  /**
   * Get practice information for a location
   */
  static async getLocationPractice(locationId: string, clinicId: number): Promise<Practice | null> {
    try {
      const location = await this.getLocationById(locationId, clinicId);
      if (!location || !location.practiceId) {
        return null;
      }

      const practiceDoc = await this.db.collection('practices').doc(location.practiceId).get();
      if (!practiceDoc.exists) {
        return null;
      }

      const data = practiceDoc.data()!;
      return {
        id: practiceDoc.id,
        clinicId: data.clinicId || 0,
        name: data.name || '',
        description: data.description || undefined,
        isActive: data.isActive !== false,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      };
    } catch (error) {
      console.error('Error getting location practice:', error);
      throw new Error('Failed to retrieve location practice');
    }
  }
}
