import { v4 as uuidv4 } from 'uuid';
import { AppointmentReference } from '../models/appointment-reference';
import { getAppointmentReferencesRepository } from '../repositories';
import { ensureDbInitialized } from '../middleware/db-init';

/**
 * Service for managing appointment references with dual-database support
 * Tracks appointments created through our service in external provider systems
 * Uses MySQL-first approach with Firestore fallback
 */
export class AppointmentReferenceService {
  constructor() {
    // No initialization needed - repository handles database connections
  }

  /**
   * Store a new appointment reference
   * @param appointment Appointment reference data without id, createdAt, and updatedAt
   * @returns The created appointment reference with id, createdAt, and updatedAt
   */
  async storeNewAppointment(
    appointment: Omit<AppointmentReference, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<AppointmentReference> {
    try {
      // Ensure database is initialized
      await ensureDbInitialized();

      const id = uuidv4();
      const now = new Date();

      const newAppointment: AppointmentReference = {
        ...appointment,
        id,
        createdAt: now,
        updatedAt: now,
      };

      const repository = getAppointmentReferencesRepository();
      await repository.create(newAppointment);

      return newAppointment;
    } catch (error) {
      console.error('Error storing new appointment reference:', error);
      throw error;
    }
  }

  /**
   * Find an appointment reference by provider and external ID
   * @param provider Provider name (e.g., "nextech")
   * @param externalId ID in the provider's system
   * @returns The appointment reference or null if not found
   */
  async findByExternalId(
    provider: string,
    externalId: string,
  ): Promise<AppointmentReference | null> {
    try {
      // Ensure database is initialized
      await ensureDbInitialized();

      const repository = getAppointmentReferencesRepository();
      return await repository.findByExternalId(provider, externalId);
    } catch (error) {
      console.error('Error finding appointment reference by external ID:', error);
      throw error;
    }
  }

  /**
   * Find an appointment reference by provider and provider ID
   * @param provider Provider name (e.g., "nextech")
   * @param providerId ID in the provider's system
   * @returns The appointment reference or null if not found
   */
  async findByProviderId(
    provider: string,
    providerId: string,
  ): Promise<AppointmentReference | null> {
    try {
      // Ensure database is initialized
      await ensureDbInitialized();

      const repository = getAppointmentReferencesRepository();
      return await repository.findByProviderId(provider, providerId);
    } catch (error) {
      console.error('Error finding appointment reference by provider ID:', error);
      throw error;
    }
  }

  /**
   * Find appointment references by patient ID
   * @param provider Provider name (e.g., "nextech")
   * @param patientId Patient ID in the provider's system
   * @returns Array of appointment references
   */
  async findByPatientId(provider: string, patientId: string): Promise<AppointmentReference[]> {
    try {
      // Ensure database is initialized
      await ensureDbInitialized();

      const repository = getAppointmentReferencesRepository();
      return await repository.findByPatientId(provider, patientId);
    } catch (error) {
      console.error('Error finding appointment references by patient ID:', error);
      throw error;
    }
  }

  /**
   * Update an appointment reference
   * @param appointment Updated appointment reference data
   * @returns The updated appointment reference
   */
  async updateAppointment(appointment: AppointmentReference): Promise<AppointmentReference> {
    try {
      // Ensure database is initialized
      await ensureDbInitialized();

      const updatedAppointment = {
        ...appointment,
        updatedAt: new Date(),
      };

      const repository = getAppointmentReferencesRepository();
      return await repository.update(appointment.id, updatedAppointment);
    } catch (error) {
      console.error('Error updating appointment reference:', error);
      throw error;
    }
  }
}
