import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';

// Conditional logger import for browser compatibility
const logger = (() => {
  if (typeof window === 'undefined') {
    // Server-side: use the real logger
    try {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      return require('@/lib/external-api/v2/utils/logger').default;
    } catch {
      return console; // Fallback to console if logger not available
    }
  } else {
    // Client-side: use console to avoid Node.js module issues
    return console;
  }
})();

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

/**
 * Office hours status interface
 * Provides comprehensive information about location office hours
 */
export interface OfficeHoursStatus {
  isOpen: boolean;
  currentStatus: 'open' | 'closed' | 'unknown';
  nextOpenTime?: string;
  nextCloseTime?: string;
  todayHours?: { start: string; end: string } | null;
  timezone: string;
}

/**
 * Office hours format interface
 * Days are represented as keys "1"-"7" where 1 is Monday and 7 is Sunday
 * null values indicate the location is closed on that day
 */
export interface OfficeHoursConfig {
  [key: string]: { start: string; end: string } | null;
}

/**
 * Service for managing office hours calculations and status checks
 * Handles timezone conversions and business logic for office hours
 */
export class OfficeHoursService {
  /**
   * Check if location is currently open based on office hours
   * @param officeHours Location office hours in format: { "1": { "start": "09:00", "end": "16:00" }, "6": null, "7": null }
   * @param timezone Location timezone (e.g., "America/Chicago")
   * @param currentTime Optional current time for testing (defaults to now)
   * @returns Office hours status with comprehensive information
   */
  static checkOfficeHours(
    officeHours: OfficeHoursConfig,
    timezone: string,
    currentTime?: Date,
  ): OfficeHoursStatus {
    try {
      // Check if office hours is empty (should return unknown status)
      if (!officeHours || Object.keys(officeHours).length === 0) {
        return {
          isOpen: false,
          currentStatus: 'unknown',
          timezone,
        };
      }

      // Use provided time or current time
      const now = dayjs(currentTime || new Date());

      // Convert current time to location timezone
      const locationTime = now.tz(timezone);

      // Get current day of week (1-7, where 1 is Monday)
      const currentDayIndex = this.getIsoDayOfWeek(locationTime.day());
      const currentDayKey = currentDayIndex.toString();

      // Get today's hours
      const todayHours = officeHours[currentDayKey];

      if (typeof logger?.debug === 'function') {
        logger.debug(
          {
            timezone,
            locationTime: locationTime.format('YYYY-MM-DD HH:mm:ss'),
            currentDayIndex,
            todayHours,
          },
          'Checking office hours',
        );
      }

      const result: OfficeHoursStatus = {
        isOpen: false,
        currentStatus: 'closed',
        timezone,
        todayHours,
      };

      // If no hours defined for today (closed day)
      if (!todayHours) {
        result.currentStatus = 'closed';

        // Find next open day
        const nextOpen = this.getNextOpenTime(officeHours, timezone, locationTime);
        if (nextOpen) {
          result.nextOpenTime = nextOpen;
        }

        return result;
      }

      // Parse today's hours
      const { start: startTime, end: endTime } = todayHours;

      // Create start and end times for today in location timezone
      const todayStart = this.parseTimeInTimezone(locationTime, startTime);
      const todayEnd = this.parseTimeInTimezone(locationTime, endTime);

      // Check if current time is within office hours (inclusive start, exclusive end)
      const isCurrentlyOpen = locationTime.isBetween(todayStart, todayEnd, null, '[)');

      result.isOpen = isCurrentlyOpen;
      result.currentStatus = isCurrentlyOpen ? 'open' : 'closed';

      // Set next open/close times
      if (isCurrentlyOpen) {
        // If open now, set next close time
        result.nextCloseTime = todayEnd.format('YYYY-MM-DD HH:mm:ss');
      } else {
        // If closed now, find next open time
        if (locationTime.isBefore(todayStart)) {
          // Before today's opening time
          result.nextOpenTime = todayStart.format('YYYY-MM-DD HH:mm:ss');
        } else {
          // After today's closing time, find next business day
          const nextOpen = this.getNextOpenTime(officeHours, timezone, locationTime);
          if (nextOpen) {
            result.nextOpenTime = nextOpen;
          }
        }
      }

      return result;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            officeHours,
            timezone,
            currentTime,
          },
          'Error checking office hours',
        );
      } else {
        console.error('Error checking office hours:', error);
      }

      // Return unknown status on error
      return {
        isOpen: false,
        currentStatus: 'unknown',
        timezone,
      };
    }
  }

  /**
   * Get the next business day and hours
   * @param officeHours Location office hours configuration
   * @param timezone Location timezone
   * @param fromTime Starting time to search from (defaults to now)
   * @returns Next business day information or null if no business days found
   */
  static getNextBusinessDay(
    officeHours: OfficeHoursConfig,
    timezone: string,
    fromTime?: Date,
  ): { date: string; hours: { start: string; end: string } } | null {
    try {
      const startTime = dayjs(fromTime || new Date()).tz(timezone);

      // Look ahead up to 14 days to find next business day
      for (let daysAhead = 1; daysAhead <= 14; daysAhead++) {
        const checkDate = startTime.add(daysAhead, 'day');
        const dayIndex = this.getIsoDayOfWeek(checkDate.day());
        const dayKey = dayIndex.toString();

        const dayHours = officeHours[dayKey];
        if (dayHours) {
          return {
            date: checkDate.format('YYYY-MM-DD'),
            hours: dayHours,
          };
        }
      }

      // No business days found in the next 2 weeks
      return null;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error(
          {
            error,
            officeHours,
            timezone,
            fromTime,
          },
          'Error getting next business day',
        );
      } else {
        console.error('Error getting next business day:', error);
      }
      return null;
    }
  }

  /**
   * Get next open time as formatted string
   * @param officeHours Location office hours configuration
   * @param timezone Location timezone
   * @param fromTime Starting time to search from
   * @returns Formatted next open time string or null
   */
  private static getNextOpenTime(
    officeHours: OfficeHoursConfig,
    timezone: string,
    fromTime: dayjs.Dayjs,
  ): string | null {
    try {
      // Look ahead up to 14 days
      for (let daysAhead = 1; daysAhead <= 14; daysAhead++) {
        const checkDate = fromTime.add(daysAhead, 'day');
        const dayIndex = this.getIsoDayOfWeek(checkDate.day());
        const dayKey = dayIndex.toString();

        const dayHours = officeHours[dayKey];
        if (dayHours) {
          const nextOpenDate = this.parseTimeInTimezone(checkDate, dayHours.start);
          return nextOpenDate.format('YYYY-MM-DD HH:mm:ss');
        }
      }

      return null;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error({ error }, 'Error getting next open time');
      } else {
        console.error('Error getting next open time:', error);
      }
      return null;
    }
  }

  /**
   * Parse time string (HH:MM) and create dayjs object with specified time
   * @param baseDate Base date to use
   * @param timeString Time in HH:MM format
   * @returns dayjs object with the specified time
   */
  private static parseTimeInTimezone(baseDate: dayjs.Dayjs, timeString: string): dayjs.Dayjs {
    const [hours, minutes] = timeString.split(':').map(Number);
    return baseDate.hour(hours).minute(minutes).second(0).millisecond(0);
  }

  /**
   * Get ISO day of week (1-7, where 1 is Monday)
   * dayjs.day() returns 0-6 where 0 is Sunday
   * @param dayOfWeek Day of week from dayjs (0-6)
   * @returns ISO day number (1-7)
   */
  private static getIsoDayOfWeek(dayOfWeek: number): number {
    return dayOfWeek === 0 ? 7 : dayOfWeek; // Convert Sunday from 0 to 7
  }

  /**
   * Validate office hours configuration
   * @param officeHours Office hours configuration to validate
   * @returns True if valid, false otherwise
   */
  static validateOfficeHours(officeHours: OfficeHoursConfig): boolean {
    try {
      const validDays = ['1', '2', '3', '4', '5', '6', '7'];
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

      for (const [day, hours] of Object.entries(officeHours)) {
        // Check if day is valid
        if (!validDays.includes(day)) {
          if (typeof logger?.warn === 'function') {
            logger.warn({ day }, 'Invalid day in office hours configuration');
          }
          return false;
        }

        // If hours is null, that's valid (closed day)
        if (hours === null) {
          continue;
        }

        // Check if hours object has required properties
        if (typeof hours !== 'object' || !hours.start || !hours.end) {
          if (typeof logger?.warn === 'function') {
            logger.warn({ day, hours }, 'Invalid hours object in office hours configuration');
          }
          return false;
        }

        // Validate time format
        if (!timeRegex.test(hours.start) || !timeRegex.test(hours.end)) {
          if (typeof logger?.warn === 'function') {
            logger.warn({ day, hours }, 'Invalid time format in office hours configuration');
          }
          return false;
        }

        // Check if start time is before end time
        const [startHour, startMin] = hours.start.split(':').map(Number);
        const [endHour, endMin] = hours.end.split(':').map(Number);
        const startMinutes = startHour * 60 + startMin;
        const endMinutes = endHour * 60 + endMin;

        if (startMinutes >= endMinutes) {
          if (typeof logger?.warn === 'function') {
            logger.warn({ day, hours }, 'Start time must be before end time');
          }
          return false;
        }
      }

      return true;
    } catch (error) {
      if (typeof logger?.error === 'function') {
        logger.error({ error, officeHours }, 'Error validating office hours');
      } else {
        console.error('Error validating office hours:', error);
      }
      return false;
    }
  }

  /**
   * Enhanced office hours validation with detailed error reporting
   * @param officeHours Office hours configuration to validate
   * @returns Validation result with detailed errors
   */
  static validateOfficeHoursWithErrors(officeHours: OfficeHoursConfig): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const validDays = ['1', '2', '3', '4', '5', '6', '7'];
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

    for (const [day, hours] of Object.entries(officeHours)) {
      if (!validDays.includes(day)) {
        errors.push(`Invalid day: ${day}. Must be 1-7 (Monday-Sunday)`);
        continue;
      }

      if (hours === null) continue; // Closed day is valid

      if (!hours || typeof hours !== 'object') {
        errors.push(`Day ${day}: Hours must be an object with start and end times`);
        continue;
      }

      if (!hours.start || !hours.end) {
        errors.push(`Day ${day}: Both start and end times required`);
        continue;
      }

      if (!timeRegex.test(hours.start)) {
        errors.push(`Day ${day}: Invalid start time format. Use HH:MM (24-hour format)`);
      }

      if (!timeRegex.test(hours.end)) {
        errors.push(`Day ${day}: Invalid end time format. Use HH:MM (24-hour format)`);
      }

      if (hours.start >= hours.end) {
        errors.push(`Day ${day}: Start time must be before end time`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate timezone string
   * @param timezone Timezone string to validate
   * @returns True if valid timezone
   */
  static validateTimezone(timezone: string): boolean {
    try {
      // Test if timezone is valid by creating a date
      dayjs().tz(timezone);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get office hours with timezone metadata for UI conversion
   * Returns both raw data and location timezone for frontend processing
   * @param officeHours Office hours configuration
   * @param locationTimezone Location timezone
   * @returns Enhanced office hours data for UI
   */
  static getOfficeHoursForUI(
    officeHours: OfficeHoursConfig,
    locationTimezone: string,
  ): {
    raw: OfficeHoursConfig;
    locationTimezone: string;
    formatted: Record<string, string>;
    timezoneLabel: string;
  } {
    return {
      raw: officeHours,
      locationTimezone,
      formatted: this.formatOfficeHoursForDisplay(officeHours, locationTimezone),
      timezoneLabel: this.getTimezoneLabel(locationTimezone),
    };
  }

  /**
   * Get human-readable timezone label
   * @param timezone Timezone string
   * @returns Human-readable timezone label
   */
  private static getTimezoneLabel(timezone: string): string {
    try {
      const now = dayjs().tz(timezone);
      const offset = now.format('Z');
      const abbr = now.format('z');
      return `${timezone} (${abbr} ${offset})`;
    } catch {
      return timezone;
    }
  }

  /**
   * Format office hours for display
   * @param officeHours Office hours configuration
   * @param timezone Timezone for display (currently unused but kept for future enhancement)
   * @returns Formatted office hours string
   */
  static formatOfficeHoursForDisplay(
    officeHours: OfficeHoursConfig,
    timezone: string, // eslint-disable-line @typescript-eslint/no-unused-vars
  ): Record<string, string> {
    const dayNames = {
      '1': 'Monday',
      '2': 'Tuesday',
      '3': 'Wednesday',
      '4': 'Thursday',
      '5': 'Friday',
      '6': 'Saturday',
      '7': 'Sunday',
    };

    const formatted: Record<string, string> = {};

    for (const [dayKey, hours] of Object.entries(officeHours)) {
      const dayName = dayNames[dayKey as keyof typeof dayNames];
      if (!dayName) continue;

      if (!hours) {
        formatted[dayName] = 'Closed';
      } else {
        // Convert 24-hour format to 12-hour format for display
        const startTime = this.formatTimeForDisplay(hours.start);
        const endTime = this.formatTimeForDisplay(hours.end);
        formatted[dayName] = `${startTime} - ${endTime}`;
      }
    }

    return formatted;
  }

  /**
   * Format time from 24-hour to 12-hour format
   * @param time Time in HH:MM format
   * @returns Time in 12-hour format
   */
  private static formatTimeForDisplay(time: string): string {
    const [hours, minutes] = time.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  }
}
