import axios, { AxiosError } from 'axios';
import logger from '../external-api/v2/utils/logger';

const { DISCORD_SMS_WEBHOOK_PATH = '' } = process.env;

class DiscordClient {
  private readonly axiosInstance = axios.create({
    baseURL: 'https://discord.com/',
    headers: { 'Content-Type': 'application/json' },
  });

  async sendMessage(params: {
    webhookPath: string;
    username?: string;
    content: string;
  }): Promise<void> {
    const { webhookPath, content, username = 'FdHashnodeBot' } = params;
    if (!webhookPath) {
      logger.warn(
        {
          context: `DiscordClient.sendMessage`,
        },
        'No webhook path provided, skipping message',
      );
      return;
    }
    if (!content) {
      logger.warn(
        {
          context: `DiscordClient.sendMessage`,
        },
        'No content provided, skipping message',
      );
      return;
    }

    const body: string = JSON.stringify({
      username,
      content,
    });

    logger.debug(
      {
        context: `DiscordClient.sendMessage`,
        body: {
          username,
          content,
        },
      },
      'Sending Discord message...',
    );
    try {
      await this.axiosInstance.post(webhookPath, body);
    } catch (error) {
      logger.error(
        {
          context: `DiscordClient.sendMessage`,
          error: (error as AxiosError)?.toJSON() || error?.toString(),
        },
        'Error during sending discord message.',
      );
    }
  }

  sendSmsChannelMessage(content: string): Promise<void> {
    return this.sendMessage({
      webhookPath: DISCORD_SMS_WEBHOOK_PATH,
      content,
    });
  }
}

export const discordClient = new DiscordClient();
