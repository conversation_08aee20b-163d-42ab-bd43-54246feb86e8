import axios, { AxiosError } from 'axios';
import { SignJWT } from 'jose';
import * as crypto from 'crypto';

/**
 * Interface for GCP token response
 */
interface GcpTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * Class for managing Dialogflow (GCP) API authentication
 */
export class DialogflowAuthService {
  private static token: string | null = null;
  private static tokenExpiration: Date | null = null;
  private static readonly EXPIRATION_BUFFER_MS = 5 * 60 * 1000; // 5 minute buffer

  /**
   * Get a valid GCP access token for Dialogflow API
   * Uses cached token if valid, otherwise fetches a new one
   */
  public static async getAccessToken(): Promise<string> {
    // Check if we have a valid cached token
    if (this.token !== null && this.tokenExpiration !== null && new Date() < this.tokenExpiration) {
      console.log('Using cached token that expires at', this.tokenExpiration);
      return this.token;
    }

    // Otherwise, fetch a new token
    return this.fetchAccessToken();
  }

  /**
   * Fetch a new access token from GCP using the service account key
   */
  private static async fetchAccessToken(): Promise<string> {
    try {
      console.log('Fetching new OAuth access token for Dialogflow API');

      let privateKey = process.env.GCP_SERVICE_ACCOUNT_PRIVATE_KEY;
      const clientEmail = process.env.GCP_SERVICE_ACCOUNT_CLIENT_EMAIL;

      if (!privateKey || !clientEmail) {
        throw new Error('Missing required GCP service account credentials');
      }

      // Fix common private key format issues
      // Replace literal \n with actual newlines if needed
      privateKey = privateKey.replace(/\\n/g, '\n');

      // Generate JWT claim
      const now = Math.floor(Date.now() / 1000);
      const expirationTime = now + 3600; // 1 hour expiration

      try {
        // Create a proper crypto KeyObject from the private key
        const privateKeyObject = crypto.createPrivateKey({
          key: privateKey,
          format: 'pem',
        });

        const jwt = await new SignJWT({
          scope: 'https://www.googleapis.com/auth/dialogflow', // Scope for Dialogflow API
        })
          .setProtectedHeader({ alg: 'RS256', typ: 'JWT' })
          .setIssuedAt(now)
          .setExpirationTime(expirationTime)
          .setIssuer(clientEmail)
          .setAudience('https://oauth2.googleapis.com/token')
          .setSubject(clientEmail)
          .sign(privateKeyObject);

        // Exchange JWT for access token
        const tokenResponse = await axios.post<GcpTokenResponse>(
          'https://oauth2.googleapis.com/token',
          {
            grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            assertion: jwt,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );

        // Save the token and its expiration time
        const { access_token, expires_in } = tokenResponse.data;
        this.token = access_token;
        this.tokenExpiration = new Date(Date.now() + expires_in * 1000 - this.EXPIRATION_BUFFER_MS);

        console.log('Successfully obtained access token, expires in', expires_in, 'seconds');
        return access_token;
      } catch (keyError) {
        console.error('Error with private key format:', keyError);

        // Try an alternative approach if the key format might be incorrect
        // Check if key has the proper PEM format with BEGIN/END markers
        if (!privateKey.includes('-----BEGIN PRIVATE KEY-----')) {
          throw new Error(
            'Private key is not in valid PEM format. Please ensure it has proper BEGIN/END markers.',
          );
        }

        throw keyError;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('Error generating access token:', errorMessage);

      if (error instanceof AxiosError && error.response) {
        console.error('Response data:', error.response.data);
        console.error('Status code:', error.response.status);
      }

      throw new Error(`Failed to get Dialogflow access token: ${errorMessage}`);
    }
  }

  /**
   * Clear the token cache
   * Useful when a token becomes invalid before its expiration
   */
  public static clearTokenCache(): void {
    this.token = null;
    this.tokenExpiration = null;
    console.log('Token cache cleared');
  }
}
