import axios from 'axios';

/**
 * Interface for Dialogflow Conversation object
 */
export interface DialogflowConversation {
  name: string;
  type?: string;
  languageCode?: string;
  startTime?: string;
  endTime?: string;
  duration?: string;
  interactions?: DialogflowInteraction[];
  metrics?: Record<string, unknown>;
  intents?: Record<string, unknown>[];
  flows?: Record<string, unknown>[];
  [key: string]: unknown;
}

/**
 * Interface for Dialogflow Message object
 */
export interface DialogflowMessage {
  text?: {
    text?: string[];
    [key: string]: unknown;
  };
  outputAudioText?: {
    text?: string;
    ssml?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

/**
 * Interface for Dialogflow Interaction object
 */
export interface DialogflowInteraction {
  name?: string;
  startTime?: string;
  endTime?: string;
  request?: {
    intent?: string;
    text?: string;
    queryInput?: {
      text?: {
        text?: string;
      };
      [key: string]: unknown;
    };
    [key: string]: unknown;
  };
  response?: {
    queryResult?: {
      text?: string;
      transcript?: string;
      languageCode?: string;
      intent?: {
        displayName?: string;
        [key: string]: unknown;
      };
      match?: {
        intent?: {
          displayName?: string;
          [key: string]: unknown;
        };
        [key: string]: unknown;
      };
      responseMessages?: DialogflowMessage[];
      [key: string]: unknown;
    };
    responseType?: string;
    text?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
  transcript?: string;
}

/**
 * Interface for Dialogflow API Options
 */
interface DialogflowApiOptions {
  projectId: string;
  locationId: string;
  agentId: string;
  sessionId: string;
  accessToken: string;
}

/**
 * Fetches Dialogflow conversation data from the Google Cloud API
 * @param options Parameters for the Dialogflow API request
 * @returns The Dialogflow conversation data
 * @throws Error if the API request fails
 */
export const getDialogflowConversation = async (
  options: DialogflowApiOptions,
): Promise<DialogflowConversation> => {
  try {
    const { projectId, locationId, agentId, sessionId, accessToken } = options;

    // Construct the Dialogflow API URL
    const url = `https://${locationId}-dialogflow.googleapis.com/v3beta1/projects/${projectId}/locations/${locationId}/agents/${agentId}/conversations/${sessionId}`;

    // Make the API request with the proper authorization header
    const response = await axios.get(url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    });

    return response.data as DialogflowConversation;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const status = error.response?.status;
      const message = error.response?.data?.error?.message || error.message;

      throw new Error(`Failed to fetch Dialogflow conversation: ${status} - ${message}`);
    }

    throw new Error(`Failed to fetch Dialogflow conversation: ${(error as Error).message}`);
  }
};

/**
 * Extracts transcript from Dialogflow conversation
 * @param conversation The Dialogflow conversation object
 * @returns A string containing the properly formatted conversation transcript
 */
export const extractTranscriptFromConversation = (conversation: DialogflowConversation): string => {
  if (!conversation?.interactions || conversation.interactions.length === 0) {
    return '';
  }

  // Log information about what we're working with
  console.log(
    `Extracting transcript from conversation with ${conversation.interactions.length} interactions`,
  );

  // Using a different approach - we'll reconstruct the conversation turn by turn

  // Function to extract customer message from an interaction
  const getCustomerMessage = (interaction: DialogflowInteraction): string | null => {
    // In Dialogflow CX/ES, customer messages typically appear in these locations

    // Check for transcript in queryResult
    if (
      interaction.response?.queryResult?.transcript &&
      typeof interaction.response.queryResult.transcript === 'string' &&
      interaction.response.queryResult.transcript.trim()
    ) {
      return interaction.response.queryResult.transcript.trim();
    }

    // The most reliable source for customer utterances is queryText in the queryResult
    if (
      interaction.response?.queryResult?.queryText &&
      typeof interaction.response.queryResult.queryText === 'string' &&
      interaction.response.queryResult.queryText.trim()
    ) {
      return interaction.response.queryResult.queryText.trim();
    }

    // Check request object which sometimes contains user messages
    if (
      interaction.request?.text &&
      typeof interaction.request.text === 'string' &&
      interaction.request.text.trim()
    ) {
      return interaction.request.text.trim();
    }

    // Check for text in queryInput - safely navigate the object structure
    if (
      interaction.request?.queryInput &&
      interaction.request.queryInput.text &&
      interaction.request.queryInput.text.text &&
      typeof interaction.request.queryInput.text.text === 'string' &&
      interaction.request.queryInput.text.text.trim()
    ) {
      return interaction.request.queryInput.text.text.trim();
    }

    // Other possible locations for user messages
    if (
      interaction.request?.transcript &&
      typeof interaction.request.transcript === 'string' &&
      interaction.request.transcript.trim()
    ) {
      return interaction.request.transcript.trim();
    }

    // In some Dialogflow formats, the transcript is directly on the interaction
    if (
      interaction.transcript &&
      typeof interaction.transcript === 'string' &&
      interaction.transcript.trim()
    ) {
      return interaction.transcript.trim();
    }

    // Look for input in context parameters
    if (
      interaction.response?.queryResult?.parameters &&
      typeof interaction.response.queryResult.parameters === 'object' &&
      interaction.response.queryResult.parameters !== null
    ) {
      const params = interaction.response.queryResult.parameters as Record<string, unknown>;
      if (params.input && typeof params.input === 'string') {
        return params.input.trim();
      }
    }

    return null;
  };

  // Function to extract agent message from an interaction
  const getAgentMessages = (interaction: DialogflowInteraction): string[] => {
    const messages: string[] = [];

    // Primary location for agent responses in Dialogflow CX/ES
    if (interaction.response?.queryResult?.responseMessages) {
      const responseMessages = interaction.response.queryResult.responseMessages;

      if (Array.isArray(responseMessages)) {
        for (const msg of responseMessages) {
          // Text responses
          if (msg.text?.text && Array.isArray(msg.text.text)) {
            for (const text of msg.text.text) {
              if (typeof text === 'string' && text.trim()) {
                messages.push(text.trim());
              }
            }
          }
        }
      }
    }

    // Specific location for the final agent response
    if (
      interaction.response?.queryResult?.fulfillmentText &&
      typeof interaction.response.queryResult.fulfillmentText === 'string' &&
      interaction.response.queryResult.fulfillmentText.trim()
    ) {
      messages.push(interaction.response.queryResult.fulfillmentText.trim());
    }

    // Other possible locations
    if (messages.length === 0) {
      // Try other common locations for agent responses
      if (
        interaction.response?.text &&
        typeof interaction.response.text === 'string' &&
        interaction.response.text.trim()
      ) {
        messages.push(interaction.response.text.trim());
      }
    }

    return messages;
  };

  // Process interactions chronologically (oldest first)
  const sortedInteractions = [...conversation.interactions].reverse();

  // Build the transcript with turns clearly marked
  const transcript: string[] = [];

  // Debug counter for interactions
  let interactionCount = 0;

  for (const interaction of sortedInteractions) {
    interactionCount++;

    // First add the customer message if present
    const customerMessage = getCustomerMessage(interaction);
    if (customerMessage) {
      transcript.push(`Patient: ${customerMessage}`);
    }

    // Then add any agent responses
    const agentMessages = getAgentMessages(interaction);
    for (const message of agentMessages) {
      transcript.push(`Heather: ${message}`);
    }

    // Debug: If we didn't find either customer or agent message, log this interaction
    if (!customerMessage && agentMessages.length === 0) {
      console.log(
        `No messages found in interaction ${interactionCount}. Structure:`,
        JSON.stringify(
          {
            request: interaction.request,
            response: {
              text: interaction.response?.text,
              queryResult: {
                queryText: interaction.response?.queryResult?.queryText,
                fulfillmentText: interaction.response?.queryResult?.fulfillmentText,
              },
            },
          },
          null,
          2,
        ),
      );
    }
  }

  // Final debug info
  console.log(`Extracted ${transcript.length} messages from ${interactionCount} interactions`);

  return transcript.join('\n');
};

/**
 * Interface for LLM-based summarization options
 */
interface SummarizationOptions {
  maxLength?: number;
  includeMetrics?: boolean;
  focusTopics?: string[];
}

/**
 * Generates a semantic summary of a conversation using an LLM
 * This function is async and requires implementation of an actual LLM API call
 * @param conversation The Dialogflow conversation to summarize
 * @param options Configuration options for the summarization
 * @returns A promise resolving to a detailed summary of the conversation
 */
export const generateLLMSummary = async (
  conversation: DialogflowConversation,
  options: SummarizationOptions = {},
): Promise<string> => {
  // Get the transcript to use as input for the LLM
  const transcript = extractTranscriptFromConversation(conversation);

  if (!transcript) {
    return 'No transcript available to summarize.';
  }

  // Log the options being used (this prevents the 'unused options' lint error)
  const maxLength = options.maxLength || 200;
  const includeMetrics = options.includeMetrics || false;
  const focusTopics = options.focusTopics || [];

  // These values will be used when the actual LLM implementation is added
  console.log('LLM Summary options:', {
    maxLength,
    includeMetrics,
    focusTopics,
  });

  // TODO: Implement actual LLM API call here
  // For now, we'll return a placeholder message
  return 'LLM-based summary will be implemented in a future update. The transcript is available for review.';
};

/**
 * Interface for conversation interaction with audio record
 */
export interface InteractionWithAudio {
  text: string;
  recordUrl: string;
}

/**
 * Extracts transcript from Dialogflow conversation and pairs it with audio records
 * @param conversation The Dialogflow conversation object
 * @param audioFiles Array of audio files with their URLs
 * @param bucketName The GCP bucket name for formatting the record URLs
 * @returns An array of objects containing text and record URL for each interaction
 */
export const extractTranscriptWithAudioRecords = (
  conversation: DialogflowConversation,
  audioFiles: Array<{ fileName: string; url: string; text: string }>,
  bucketName: string,
): InteractionWithAudio[] => {
  if (
    !conversation?.interactions ||
    conversation.interactions.length === 0 ||
    !audioFiles ||
    audioFiles.length === 0
  ) {
    return [];
  }

  // Log information about what we're working with
  console.log(
    `Extracting transcript with audio records from conversation with ${conversation.interactions.length} interactions and ${audioFiles.length} audio files`,
  );

  // Extract timestamp from filename
  const getTimestamp = (fileName: string): number => {
    // Match the last sequence of digits after an underscore
    // This handles both cases: with extension (_12345.mp3) and without extension (_12345)
    const match = fileName.match(/_([0-9]+)(?:\.[^.]+)?$/);
    return match && match[1] ? parseInt(match[1], 10) : 0;
  };

  // First, determine if the audio files are in ascending or descending order
  // by checking the first and last files
  let isDescending = false;
  if (audioFiles.length >= 2) {
    const firstTimestamp = getTimestamp(audioFiles[0].fileName);
    const lastTimestamp = getTimestamp(audioFiles[audioFiles.length - 1].fileName);
    isDescending = firstTimestamp > lastTimestamp;
  }

  console.log(`Audio files appear to be in ${isDescending ? 'descending' : 'ascending'} order`);

  // Sort audio files by timestamp in ascending order (oldest first)
  const sortedAudioFiles = [...audioFiles].sort((a, b) => {
    return getTimestamp(a.fileName) - getTimestamp(b.fileName);
  });

  // Process interactions chronologically (oldest first)
  const sortedInteractions = [...conversation.interactions].sort((a, b) => {
    const aTime = a.startTime ? new Date(a.startTime).getTime() : 0;
    const bTime = b.startTime ? new Date(b.startTime).getTime() : 0;
    return aTime - bTime;
  });

  // Log the sorted interactions and audio files for debugging
  console.log(
    `Sorted ${sortedInteractions.length} interactions and ${sortedAudioFiles.length} audio files`,
  );
  if (sortedInteractions.length > 0 && sortedInteractions[0].startTime) {
    console.log(`First interaction time: ${sortedInteractions[0].startTime}`);
  }
  if (sortedAudioFiles.length > 0) {
    console.log(`First audio file: ${sortedAudioFiles[0].fileName}`);
  }

  // Function to extract customer message from an interaction
  const getCustomerMessage = (interaction: DialogflowInteraction): string | null => {
    // In Dialogflow CX/ES, customer messages typically appear in these locations

    // Check for transcript in queryResult
    if (
      interaction.response?.queryResult?.transcript &&
      typeof interaction.response.queryResult.transcript === 'string' &&
      interaction.response.queryResult.transcript.trim()
    ) {
      return `Patient: ${interaction.response.queryResult.transcript.trim()}`;
    }

    // The most reliable source for customer utterances is queryText in the queryResult
    if (
      interaction.response?.queryResult?.queryText &&
      typeof interaction.response.queryResult.queryText === 'string' &&
      interaction.response.queryResult.queryText.trim()
    ) {
      return `Patient: ${interaction.response.queryResult.queryText.trim()}`;
    }

    // Check request object which sometimes contains user messages
    if (
      interaction.request?.text &&
      typeof interaction.request.text === 'string' &&
      interaction.request.text.trim()
    ) {
      return `Patient: ${interaction.request.text.trim()}`;
    }

    // Check for text in queryInput - safely navigate the object structure
    if (
      interaction.request?.queryInput &&
      interaction.request.queryInput.text &&
      interaction.request.queryInput.text.text &&
      typeof interaction.request.queryInput.text.text === 'string' &&
      interaction.request.queryInput.text.text.trim()
    ) {
      return `Patient: ${interaction.request.queryInput.text.text.trim()}`;
    }

    // Other possible locations for user messages
    if (
      interaction.request?.transcript &&
      typeof interaction.request.transcript === 'string' &&
      interaction.request.transcript.trim()
    ) {
      return `Patient: ${interaction.request.transcript.trim()}`;
    }

    // In some Dialogflow formats, the transcript is directly on the interaction
    if (
      interaction.transcript &&
      typeof interaction.transcript === 'string' &&
      interaction.transcript.trim()
    ) {
      return `Patient: ${interaction.transcript.trim()}`;
    }

    // Look for input in context parameters
    if (
      interaction.response?.queryResult?.parameters &&
      typeof interaction.response.queryResult.parameters === 'object' &&
      interaction.response.queryResult.parameters !== null
    ) {
      const params = interaction.response.queryResult.parameters as Record<string, unknown>;
      if (params.input && typeof params.input === 'string') {
        return `Patient: ${params.input.trim()}`;
      }
    }

    return null;
  };

  // Function to extract agent message from an interaction
  const getAgentMessage = (interaction: DialogflowInteraction): string | null => {
    // Primary location for agent responses in Dialogflow CX/ES
    if (interaction.response?.queryResult?.responseMessages) {
      const responseMessages = interaction.response.queryResult.responseMessages;

      if (Array.isArray(responseMessages)) {
        const textMessages: string[] = [];

        for (const msg of responseMessages) {
          // Text responses
          if (msg.text?.text && Array.isArray(msg.text.text)) {
            for (const text of msg.text.text) {
              if (typeof text === 'string' && text.trim()) {
                textMessages.push(text.trim());
              }
            }
          }
        }

        if (textMessages.length > 0) {
          return `Heather: ${textMessages.join(' ')}`;
        }
      }
    }

    // Specific location for the final agent response
    if (
      interaction.response?.queryResult?.fulfillmentText &&
      typeof interaction.response.queryResult.fulfillmentText === 'string' &&
      interaction.response.queryResult.fulfillmentText.trim()
    ) {
      return `Heather: ${interaction.response.queryResult.fulfillmentText.trim()}`;
    }

    // Other possible locations
    if (
      interaction.response?.text &&
      typeof interaction.response.text === 'string' &&
      interaction.response.text.trim()
    ) {
      return `Heather: ${interaction.response.text.trim()}`;
    }

    return null;
  };

  // Build the result array with interactions and audio records
  const result: InteractionWithAudio[] = [];

  // We'll try a different approach for matching interactions with audio files
  // First, let's create a map of all interactions with their texts
  const allMessages: { text: string; timestamp: number }[] = [];

  // Process all interactions to extract messages
  for (const interaction of sortedInteractions) {
    const customerMessage = getCustomerMessage(interaction);
    const agentMessage = getAgentMessage(interaction);
    const timestamp = interaction.startTime ? new Date(interaction.startTime).getTime() : 0;

    if (customerMessage) {
      allMessages.push({
        text: customerMessage,
        timestamp,
      });
    }

    if (agentMessage) {
      // Agent messages typically come after customer messages, so add a small offset
      allMessages.push({
        text: agentMessage,
        timestamp: timestamp + 100, // Add a small offset to ensure correct ordering
      });
    }
  }

  // Sort all messages by timestamp
  allMessages.sort((a, b) => a.timestamp - b.timestamp);

  // Now pair messages with audio files
  // If we have more messages than audio files, we'll use the available audio files
  // If we have more audio files than messages, we'll use generic messages for the extra files
  const minLength = Math.min(allMessages.length, sortedAudioFiles.length);

  // First, match the available messages with audio files
  for (let i = 0; i < minLength; i++) {
    result.push({
      text: allMessages[i].text,
      recordUrl: `gcp://${bucketName}/${sortedAudioFiles[i].fileName}`,
    });
  }

  // If we have more audio files than messages, add generic descriptions for the remaining files
  let audioIndex = minLength;

  // If we have more audio files than interactions, add them with generic descriptions
  while (audioIndex < sortedAudioFiles.length) {
    // Format the record URL in the gcp:// format
    const recordUrl = `gcp://${bucketName}/${sortedAudioFiles[audioIndex].fileName}`;

    // Use generic text based on position
    let text = '';

    // If it's the first message, it's likely the greeting
    if (audioIndex === 0) {
      text = 'Hello, how can I help you today?';
    }
    // If it's the last message, it's likely the closing
    else if (audioIndex === sortedAudioFiles.length - 1) {
      text = 'Thank you for calling. Have a great day!';
    }
    // Otherwise, it's a middle part of the conversation
    else {
      // Alternate between patient and agent messages
      if (audioIndex % 2 === 0) {
        text = 'Patient message in conversation';
      } else {
        text = 'Agent response in conversation';
      }
    }

    result.push({
      text,
      recordUrl,
    });

    audioIndex++;
  }

  // Ensure the conversation is in the correct chronological order
  // The first message should be the greeting and the last should be the closing
  if (result.length > 0) {
    // Check if we have a greeting message at the beginning
    let hasGreetingAtBeginning = false;
    for (let i = 0; i < Math.min(5, result.length); i++) {
      if (
        result[i].text.includes('Hi, I am Heather') ||
        result[i].text.includes('Hello, how can I help') ||
        result[i].text.includes('intelligent receptionist')
      ) {
        hasGreetingAtBeginning = true;
        break;
      }
    }

    // Check if we have a closing message at the end
    let hasClosingAtEnd = false;
    for (let i = Math.max(0, result.length - 5); i < result.length; i++) {
      if (
        result[i].text.includes('Thank you for calling') ||
        result[i].text.includes('Have a great day')
      ) {
        hasClosingAtEnd = true;
        break;
      }
    }

    // Check if greeting is at the end
    let hasGreetingAtEnd = false;
    for (let i = Math.max(0, result.length - 5); i < result.length; i++) {
      if (
        result[i].text.includes('Hi, I am Heather') ||
        result[i].text.includes('Hello, how can I help') ||
        result[i].text.includes('intelligent receptionist')
      ) {
        hasGreetingAtEnd = true;
        break;
      }
    }

    // Check if closing is at the beginning
    let hasClosingAtBeginning = false;
    for (let i = 0; i < Math.min(5, result.length); i++) {
      if (
        result[i].text.includes('Thank you for calling') ||
        result[i].text.includes('Have a great day')
      ) {
        hasClosingAtBeginning = true;
        break;
      }
    }

    // If the conversation appears to be in reverse order, fix it
    if (
      (hasGreetingAtEnd && !hasGreetingAtBeginning) ||
      (hasClosingAtBeginning && !hasClosingAtEnd) ||
      (!hasGreetingAtBeginning && !hasClosingAtEnd)
    ) {
      console.log('Conversation appears to be in reverse order, fixing...');
      result.reverse();
    }
  }

  // Log the result
  console.log(`Created ${result.length} interaction records with audio`);

  return result;
};
