import { NextechAuth } from '../../../../../lib/external-api/v2/providers/nextech/auth';
import { AuthFactory } from '../../../../../lib/external-api/v2/auth/auth-factory';
import { ApiError } from '../../../../../lib/external-api/v2/utils/errors';
import logger from '../../../../../lib/external-api/v2/utils/logger';

jest.mock('../../../../../lib/external-api/v2/auth/auth-factory');
jest.mock('../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechAuth', () => {
  let nextechAuth: NextechAuth;
  const mockAuthService = {
    getAccessToken: jest.fn(),
    clearTokenCache: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (AuthFactory.createAuthService as jest.Mock).mockReturnValue(mockAuthService);
    nextechAuth = new NextechAuth();
  });

  it('should get authorization header with valid token', async () => {
    mockAuthService.getAccessToken.mockResolvedValue('test-token');
    const header = await nextechAuth.getAuthorizationHeader();
    expect(header).toEqual({ Authorization: 'Bearer test-token', 'nx-practice-id': '' });
  });

  it('should handle authentication error and sanitize the error message', async () => {
    const authError = new ApiError(
      401,
      'AUTH_ERROR',
      `Invalid credentials: "client_id" : "my-client-id", "client_secret" : "my-client-secret", "token" : "my-token"`,
    );
    // The error message needs to match the regex in sanitizeError
    mockAuthService.getAccessToken.mockRejectedValue(authError);

    await expect(nextechAuth.getAuthorizationHeader()).rejects.toThrow(authError);
    expect(logger.error).toHaveBeenCalled();

    // Get the first argument of the first call to logger.error
    const logArgs = (logger.error as jest.Mock).mock.calls[0][0];

    // Check that sanitized error is in the logged context
    expect(logArgs.error).toContain('REDACTED');
    expect(logArgs.error).not.toContain('my-client-id');
    expect(logArgs.error).not.toContain('my-client-secret');
    expect(logArgs.error).not.toContain('my-token');
  });

  it('should clear token cache', () => {
    nextechAuth.clearTokenCache();
    expect(mockAuthService.clearTokenCache).toHaveBeenCalled();
  });
});
