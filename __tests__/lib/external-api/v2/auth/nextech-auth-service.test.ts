import axios, { AxiosError } from 'axios';
import { NextechAuthService } from '../../../../../lib/external-api/v2/auth/services/nextech-auth-service';
import { ICredentialProvider } from '../../../../../lib/external-api/v2/auth/interfaces/credential-provider.interface';
import { ITokenCache } from '../../../../../lib/external-api/v2/auth/interfaces/token-cache.interface';
import { RateLimitError } from '../../../../../lib/external-api/v2/auth/errors/auth-error';

/* eslint-disable @typescript-eslint/no-explicit-any */
// This file contains tests that need to access private members
// and mock complex behaviors, which sometimes requires 'any' type

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('NextechAuthService', () => {
  // Mock dependencies
  let mockCredentialProvider: jest.Mocked<ICredentialProvider>;
  let mockTokenCache: jest.Mocked<ITokenCache>;
  let authService: NextechAuthService;

  beforeEach(() => {
    // Create fresh mocks for each test
    mockCredentialProvider = {
      getCredential: jest.fn(),
      hasCredential: jest.fn(),
    };

    mockTokenCache = {
      getToken: jest.fn(),
      setToken: jest.fn(),
      clearToken: jest.fn(),
    };

    // Create auth service with mocked dependencies and test mode enabled
    authService = new NextechAuthService(mockCredentialProvider, mockTokenCache, {
      isTestMode: true,
    });

    // Reset axios mock
    jest.clearAllMocks();
  });

  describe('getProviderName', () => {
    it('should return "nextech"', () => {
      // Act
      const result = authService.getProviderName();

      // Assert
      expect(result).toBe('nextech');
    });
  });

  describe('getAccessToken', () => {
    it('should return cached token if available', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue('cached-token');

      // Act
      const result = await authService.getAccessToken();

      // Assert
      expect(result).toBe('cached-token');
      expect(mockTokenCache.getToken).toHaveBeenCalledWith('nextech');
      expect(mockedAxios.post).not.toHaveBeenCalled();
    });

    it('should fetch new token if no cached token available', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue(null);
      mockCredentialProvider.getCredential.mockImplementation(key => {
        switch (key) {
          case 'NEXTECH_CLIENT_ID':
            return Promise.resolve('test-client-id');
          case 'NEXTECH_CLIENT_SECRET':
            return Promise.resolve('test-client-secret');
          case 'NEXTECH_RESOURCE':
            return Promise.resolve('test-resource');
          case 'NEXTECH_BASE_URL':
            return Promise.resolve('http://api.test');
          default:
            return Promise.reject(new Error(`Unknown key: ${key}`));
        }
      });

      mockedAxios.post.mockResolvedValue({
        data: {
          access_token: 'new-token',
          expires_in: 3600,
          token_type: 'Bearer',
        },
      });

      // Act
      const result = await authService.getAccessToken();

      // Assert
      expect(result).toBe('new-token');
      expect(mockTokenCache.getToken).toHaveBeenCalledWith('nextech');
      expect(mockedAxios.post).toHaveBeenCalledWith(
        'https://login.microsoftonline.com/nextech-api.com/oauth2/token',
        expect.any(URLSearchParams),
        expect.any(Object),
      );
      expect(mockTokenCache.setToken).toHaveBeenCalledWith('nextech', 'new-token', 3600);
    });

    it('should retry on transient errors', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue(null);
      mockCredentialProvider.getCredential.mockImplementation(key => {
        switch (key) {
          case 'NEXTECH_CLIENT_ID':
            return Promise.resolve('test-client-id');
          case 'NEXTECH_CLIENT_SECRET':
            return Promise.resolve('test-client-secret');
          case 'NEXTECH_RESOURCE':
            return Promise.resolve('test-resource');
          case 'NEXTECH_BASE_URL':
            return Promise.resolve('http://api.test');
          default:
            return Promise.reject(new Error(`Unknown key: ${key}`));
        }
      });

      // Fail on first attempt, succeed on second
      mockedAxios.post.mockRejectedValueOnce(new Error('Network error')).mockResolvedValueOnce({
        data: {
          access_token: 'new-token',
          expires_in: 3600,
          token_type: 'Bearer',
        },
      });

      // Mock the private fetchTokenWithRetry method to skip the delay
      const originalFetchTokenWithRetry = (authService as any).fetchTokenWithRetry;
      (authService as any).fetchTokenWithRetry = async function () {
        let attempt = 0;
        let lastError: Error | null = null;

        while (attempt < this.maxRetries) {
          try {
            return await this.fetchNewToken();
          } catch (error) {
            lastError = error as Error;

            if (error instanceof RateLimitError) {
              throw error;
            }

            // No delay in tests
            attempt++;
          }
        }

        throw lastError || new Error('Unknown error');
      };

      try {
        // Act
        const result = await authService.getAccessToken();

        // Assert
        expect(result).toBe('new-token');
        expect(mockedAxios.post).toHaveBeenCalledTimes(2);
        expect(mockTokenCache.setToken).toHaveBeenCalledWith('nextech', 'new-token', 3600);
      } finally {
        // Restore original method
        (authService as any).fetchTokenWithRetry = originalFetchTokenWithRetry;
      }
    });

    it('should throw RateLimitError when rate limited', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue(null);
      mockCredentialProvider.getCredential.mockImplementation(key => {
        switch (key) {
          case 'NEXTECH_CLIENT_ID':
            return Promise.resolve('test-client-id');
          case 'NEXTECH_CLIENT_SECRET':
            return Promise.resolve('test-client-secret');
          case 'NEXTECH_RESOURCE':
            return Promise.resolve('test-resource');
          case 'NEXTECH_BASE_URL':
            return Promise.resolve('http://api.test');
          default:
            return Promise.reject(new Error(`Unknown key: ${key}`));
        }
      });

      // Create a proper Axios error with 429 status
      const axiosError = new Error('Rate limit exceeded') as AxiosError;
      axiosError.isAxiosError = true;
      axiosError.response = {
        status: 429,
        statusText: 'Too Many Requests',
        headers: {
          'retry-after': '30',
        },
        data: 'Rate limit exceeded',
        config: {} as any, // Reverting to 'any' for simplicity in test context
      } as AxiosError['response'];

      mockedAxios.post.mockRejectedValue(axiosError);

      // Act & Assert
      try {
        await authService.getAccessToken();
        fail('Expected RateLimitError was not thrown');
      } catch (error) {
        expect(error instanceof Error).toBe(true);
        expect((error as Error).name).toBe('RateLimitError');
        expect((error as RateLimitError).retryAfter).toBe(30);
      }
    });

    it('should throw AuthenticationError on HTTP 401', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue(null);
      mockCredentialProvider.getCredential.mockImplementation(key => {
        switch (key) {
          case 'NEXTECH_CLIENT_ID':
            return Promise.resolve('test-client-id');
          case 'NEXTECH_CLIENT_SECRET':
            return Promise.resolve('test-client-secret');
          case 'NEXTECH_RESOURCE':
            return Promise.resolve('test-resource');
          case 'NEXTECH_BASE_URL':
            return Promise.resolve('http://api.test');
          default:
            return Promise.reject(new Error(`Unknown key: ${key}`));
        }
      });

      // Create a proper Axios error with 401 status
      const axiosError = new Error('Request failed with status code 401') as AxiosError;
      axiosError.isAxiosError = true;
      axiosError.response = {
        status: 401,
        statusText: 'Unauthorized',
        headers: {},
        data: 'Unauthorized',
        config: {} as any, // Reverting to 'any' for simplicity in test context
      } as AxiosError['response'];

      mockedAxios.post.mockRejectedValue(axiosError);

      // Act & Assert
      try {
        await authService.getAccessToken();
        fail('Expected AuthenticationError was not thrown');
      } catch (error) {
        expect(error instanceof Error).toBe(true);
        expect((error as Error).name).toBe('AuthenticationError');
        expect((error as { code: string }).code).toContain('HTTP_401');
      }
    });

    it('should validate input parameters before sending request', async () => {
      // Arrange
      mockTokenCache.getToken.mockResolvedValue(null);
      mockCredentialProvider.getCredential.mockImplementation(key => {
        switch (key) {
          case 'NEXTECH_CLIENT_ID':
            return Promise.resolve(''); // Empty client ID
          case 'NEXTECH_CLIENT_SECRET':
            return Promise.resolve('test-client-secret');
          case 'NEXTECH_RESOURCE':
            return Promise.resolve('test-resource');
          case 'NEXTECH_BASE_URL':
            return Promise.resolve('http://api.test');
          default:
            return Promise.reject(new Error(`Unknown key: ${key}`));
        }
      });

      // Act & Assert
      try {
        await authService.getAccessToken();
        fail('Expected error was not thrown');
      } catch (error) {
        expect(error instanceof Error).toBe(true);
        expect((error as Error).name).toBe('AuthenticationError');
        expect((error as Error).message).toContain('Invalid empty value for clientId');
      }

      expect(mockedAxios.post).not.toHaveBeenCalled();
    });
  });

  describe('clearTokenCache', () => {
    it('should clear the token cache', () => {
      // Act
      authService.clearTokenCache();

      // Assert
      expect(mockTokenCache.clearToken).toHaveBeenCalledWith('nextech');
    });
  });
});
