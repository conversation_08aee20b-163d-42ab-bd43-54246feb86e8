import { EnvCredentialProvider } from '../../../../../lib/external-api/v2/auth/providers/env-credential-provider';

describe('EnvCredentialProvider', () => {
  let provider: EnvCredentialProvider;
  const originalEnv = process.env;

  beforeEach(() => {
    // Create a fresh copy of process.env for each test
    process.env = { ...originalEnv };
    provider = new EnvCredentialProvider();
  });

  afterAll(() => {
    // Restore original env
    process.env = originalEnv;
  });

  describe('getCredential', () => {
    it('should return the credential when it exists', async () => {
      // Arrange
      process.env.TEST_CREDENTIAL = 'test-value';

      // Act
      const result = await provider.getCredential('TEST_CREDENTIAL');

      // Assert
      expect(result).toBe('test-value');
    });

    it('should throw AuthenticationError when credential does not exist', async () => {
      // Act & Assert
      await expect(provider.getCredential('NON_EXISTENT_CREDENTIAL')).rejects.toMatchObject({
        name: 'AuthenticationError',
        message: expect.stringContaining('NON_EXISTENT_CREDENTIAL'),
        code: 'CREDENTIAL_NOT_FOUND',
      });
    });

    it('should include the key name in the error message', async () => {
      // Act & Assert
      await expect(provider.getCredential('NON_EXISTENT_CREDENTIAL')).rejects.toMatchObject({
        message: expect.stringContaining('NON_EXISTENT_CREDENTIAL'),
        code: 'CREDENTIAL_NOT_FOUND',
      });
    });
  });

  describe('hasCredential', () => {
    it('should return true when credential exists', async () => {
      // Arrange
      process.env.TEST_CREDENTIAL = 'test-value';

      // Act
      const result = await provider.hasCredential('TEST_CREDENTIAL');

      // Assert
      expect(result).toBe(true);
    });

    it('should return false when credential does not exist', async () => {
      // Act
      const result = await provider.hasCredential('NON_EXISTENT_CREDENTIAL');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when credential is empty string', async () => {
      // Arrange
      process.env.EMPTY_CREDENTIAL = '';

      // Act
      const result = await provider.hasCredential('EMPTY_CREDENTIAL');

      // Assert
      expect(result).toBe(false);
    });
  });
});
