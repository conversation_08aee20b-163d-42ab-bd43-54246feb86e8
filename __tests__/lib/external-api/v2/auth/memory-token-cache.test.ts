import { MemoryTokenCache } from '../../../../../lib/external-api/v2/auth/cache/memory-token-cache';

describe('MemoryTokenCache', () => {
  let cache: MemoryTokenCache;

  beforeEach(() => {
    cache = new MemoryTokenCache();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('getToken', () => {
    it('should return null when token does not exist', async () => {
      // Act
      const result = await cache.getToken('test-provider');

      // Assert
      expect(result).toBeNull();
    });

    it('should return the token when it exists and not expired', async () => {
      // Arrange
      const now = Date.now();
      jest.setSystemTime(now);
      await cache.setToken('test-provider', 'test-token', 3600);

      // Act
      const result = await cache.getToken('test-provider');

      // Assert
      expect(result).toBe('test-token');
    });

    it('should return null when token is expired', async () => {
      // Arrange
      const now = Date.now();
      jest.setSystemTime(now);
      await cache.setToken('test-provider', 'test-token', 60);

      // Advance time past expiration (plus 30s buffer)
      jest.setSystemTime(now + 91 * 1000);

      // Act
      const result = await cache.getToken('test-provider');

      // Assert
      expect(result).toBeNull();
    });

    it('should remove expired token from cache', async () => {
      // Arrange
      const now = Date.now();
      jest.setSystemTime(now);
      await cache.setToken('test-provider', 'test-token', 60);

      // Advance time past expiration
      jest.setSystemTime(now + 91 * 1000);

      // Act
      await cache.getToken('test-provider');

      // Set time back before expiration to ensure token is actually removed
      jest.setSystemTime(now);
      const result = await cache.getToken('test-provider');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('setToken', () => {
    it('should store token with expiration time', async () => {
      // Arrange
      const now = Date.now();
      jest.setSystemTime(now);

      // Act
      await cache.setToken('test-provider', 'test-token', 3600);

      // Assert
      const result = await cache.getToken('test-provider');
      expect(result).toBe('test-token');
    });

    it('should overwrite existing token', async () => {
      // Arrange
      await cache.setToken('test-provider', 'old-token', 3600);

      // Act
      await cache.setToken('test-provider', 'new-token', 3600);

      // Assert
      const result = await cache.getToken('test-provider');
      expect(result).toBe('new-token');
    });

    it('should apply 30-second buffer to expiration time', async () => {
      // Arrange
      const now = Date.now();
      jest.setSystemTime(now);
      await cache.setToken('test-provider', 'test-token', 60);

      // Advance time to just before expiration (with buffer)
      jest.setSystemTime(now + 29 * 1000);

      // Act - Token should still be valid
      const result1 = await cache.getToken('test-provider');

      // Advance time to just after expiration (with buffer)
      jest.setSystemTime(now + 31 * 1000);

      // Act - Token should be invalid
      const result2 = await cache.getToken('test-provider');

      // Assert
      expect(result1).toBe('test-token');
      expect(result2).toBeNull();
    });
  });

  describe('clearToken', () => {
    it('should remove token from cache', async () => {
      // Arrange
      await cache.setToken('test-provider', 'test-token', 3600);

      // Act
      await cache.clearToken('test-provider');

      // Assert
      const result = await cache.getToken('test-provider');
      expect(result).toBeNull();
    });

    it('should not throw when clearing non-existent token', async () => {
      // Act & Assert
      await expect(cache.clearToken('non-existent-provider')).resolves.not.toThrow();
    });
  });
});
