import { CallService } from '@/lib/external-api/v2/services/call-service';
import { CallType } from '@/models/CallTypes';
import { callsService, callSessionsService } from '@/utils/firestore';
import { eventEmitter, EVENTS } from '@/lib/external-api/v2/utils/events';

// Mock dependencies
jest.mock('@/utils/firestore', () => ({
  callsService: {
    createCall: jest.fn(),
    getCallsBySessionId: jest.fn(),
  },
  callSessionsService: {
    findCallSessionsByPhone: jest.fn(),
    getCallSessionBySessionId: jest.fn(),
  },
}));

jest.mock('@/lib/external-api/v2/utils/events', () => ({
  eventEmitter: {
    emit: jest.fn(),
  },
  EVENTS: {
    CALL: {
      CREATED: 'call.created',
    },
  },
}));

jest.mock('@/lib/external-api/v2/utils/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
}));

describe('CallService', () => {
  let callService: CallService;
  let mockCallsService: jest.Mocked<typeof callsService>;
  let mockCallSessionsService: jest.Mocked<typeof callSessionsService>;
  let mockEventEmitter: jest.Mocked<typeof eventEmitter>;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Get mocked services
    mockCallsService = callsService as jest.Mocked<typeof callsService>;
    mockCallSessionsService = callSessionsService as jest.Mocked<typeof callSessionsService>;
    mockEventEmitter = eventEmitter as jest.Mocked<typeof eventEmitter>;

    // Create service instance
    callService = new CallService();
  });

  describe('createEmptyCall', () => {
    const mockCreatedCall = {
      id: 'call-123',
      clientId: 'patient-456',
      userId: 'system',
      clinicId: 12,
      locationId: 118,
      date: expect.any(Date),
      recordingUrl: 'https://example.com/dummy-recording.mp3',
      reason: 'Incoming call',
      summary: '',
      transcription: '',
      transcriptionWithAudio: '',
      notes: '',
      priorityScore: 0,
      urgent: false,
      tags: [],
      phoneNumber: '+**********',
      sessionId: 'session-789',
      agentId: 'agent-abc',
      hasVoiceMail: false,
      voicemailUrl: '',
      duration: '0 min',
      type: CallType.OTHER,
      isOutboundCall: false,
    };

    beforeEach(() => {
      mockCallsService.createCall.mockResolvedValue(mockCreatedCall);
    });

    it('should create an empty call with minimal data', async () => {
      // Arrange
      const callData = {
        sessionId: 'session-789',
        phoneNumber: '+**********',
        agentId: 'agent-abc',
      };
      const patientId = 'patient-456';

      // Act
      const result = await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        {
          clientId: 'patient-456',
          userId: 'system',
          clinicId: 12,
          locationId: 118,
          date: expect.any(Date),
          recordingUrl: 'https://example.com/dummy-recording.mp3',
          reason: 'Incoming call',
          summary: '',
          transcription: '',
          transcriptionWithAudio: '',
          notes: '',
          priorityScore: 0,
          urgent: false,
          tags: [],
          phoneNumber: '+**********',
          sessionId: 'session-789',
          agentId: 'agent-abc',
          hasVoiceMail: false,
          voicemailUrl: '',
          duration: '0 min',
          type: CallType.OTHER,
          isOutboundCall: false,
        },
        undefined,
      );

      expect(mockEventEmitter.emit).toHaveBeenCalledWith(EVENTS.CALL.CREATED, mockCreatedCall);
      expect(result).toEqual(mockCreatedCall);
    });

    it('should create an empty call with custom call type', async () => {
      // Arrange
      const callData = {
        sessionId: 'session-789',
        phoneNumber: '+**********',
        type: CallType.VOICEMAIL,
        hasVoiceMail: true,
      };
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          type: CallType.VOICEMAIL,
          hasVoiceMail: true,
        }),
        undefined,
      );
    });

    it('should create an empty call for outbound call', async () => {
      // Arrange
      const callData = {
        sessionId: 'session-789',
        phoneNumber: '+**********',
        isOutboundCall: true,
        reason: 'Follow-up call',
      };
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          isOutboundCall: true,
          reason: 'Follow-up call',
        }),
        undefined,
      );
    });

    it('should use default values when optional fields are not provided', async () => {
      // Arrange
      const callData = {};
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          phoneNumber: '',
          sessionId: '',
          agentId: '',
          reason: 'Incoming call',
          type: CallType.OTHER,
          hasVoiceMail: false,
          isOutboundCall: false,
        }),
        undefined,
      );
    });

    it('should set correct location for clinic 12', async () => {
      // Arrange
      const callData = {
        clinicId: 12,
      };
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          clinicId: 12,
          locationId: 118, // Lombard location for clinic 12
        }),
        undefined,
      );
    });

    it('should set default location for other clinics', async () => {
      // Arrange
      const callData = {
        clinicId: 5,
      };
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          clinicId: 5,
          locationId: 0, // Default location for other clinics
        }),
        undefined,
      );
    });

    it('should use custom practitioner ID when provided', async () => {
      // Arrange
      const callData = {
        practitionerId: 'doctor-123',
      };
      const patientId = 'patient-456';

      // Act
      await callService.createEmptyCall(callData, patientId);

      // Assert
      expect(mockCallsService.createCall).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'doctor-123',
        }),
        undefined,
      );
    });

    it('should handle database errors gracefully', async () => {
      // Arrange
      const callData = {
        sessionId: 'session-789',
        phoneNumber: '+**********',
      };
      const patientId = 'patient-456';
      const dbError = new Error('Database connection failed');

      mockCallsService.createCall.mockRejectedValue(dbError);

      // Act & Assert
      await expect(callService.createEmptyCall(callData, patientId)).rejects.toThrow(
        'Database connection failed',
      );

      expect(mockEventEmitter.emit).not.toHaveBeenCalled();
    });
  });

  describe('findCallSessionByPhone', () => {
    it('should return the most recent call session for a phone number', async () => {
      // Arrange
      const phoneNumber = '+**********';
      const mockSessions = [
        {
          id: 'call-session-1',
          sessionId: 'session-1',
          callerPhone: phoneNumber,
          createdAt: new Date('2023-12-01T10:00:00Z'),
          updatedAt: new Date('2023-12-01T10:00:00Z'),
        },
        {
          id: 'call-session-2',
          sessionId: 'session-2',
          callerPhone: phoneNumber,
          createdAt: new Date('2023-12-01T09:00:00Z'),
          updatedAt: new Date('2023-12-01T09:00:00Z'),
        },
      ];

      mockCallSessionsService.findCallSessionsByPhone.mockResolvedValue(mockSessions);

      // Act
      const result = await callService.findCallSessionByPhone(phoneNumber);

      // Assert
      expect(mockCallSessionsService.findCallSessionsByPhone).toHaveBeenCalledWith(phoneNumber);
      expect(result).toEqual(mockSessions[0]); // Should return the first (most recent) session
    });

    it('should return null when no sessions are found', async () => {
      // Arrange
      const phoneNumber = '+**********';
      mockCallSessionsService.findCallSessionsByPhone.mockResolvedValue([]);

      // Act
      const result = await callService.findCallSessionByPhone(phoneNumber);

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when sessions array is null/undefined', async () => {
      // Arrange
      const phoneNumber = '+**********';
      mockCallSessionsService.findCallSessionsByPhone.mockResolvedValue(null as never);

      // Act
      const result = await callService.findCallSessionByPhone(phoneNumber);

      // Assert
      expect(result).toBeNull();
    });
  });
});
