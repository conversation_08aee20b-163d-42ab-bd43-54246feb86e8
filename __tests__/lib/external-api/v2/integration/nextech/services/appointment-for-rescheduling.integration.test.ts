import axios from 'axios';
import Mock<PERSON>dapter from 'axios-mock-adapter';
import { NextechApiClient } from '@/lib/external-api/v2/providers/nextech/client';
import { NextechAppointmentService } from '@/lib/external-api/v2/providers/nextech/services/appointment';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';

// Mock the NextechApiClient
jest.mock('@/lib/external-api/v2/providers/nextech/client', () => {
  return {
    NextechApiClient: jest.fn().mockImplementation(() => {
      return {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
        authenticate: jest.fn().mockResolvedValue({
          access_token: 'mock-token',
          token_type: 'Bearer',
          expires_in: 3600,
        }),
      };
    }),
  };
});

describe('NextechAppointmentService - getAppointmentsForRescheduling Integration', () => {
  let apiClient: NextechApiClient;
  let appointmentService: NextechAppointmentService;
  let mockAxios: MockAdapter;
  const baseURL = 'https://api.example.com';

  // Sample appointment data
  const mockFutureAppointments = {
    resourceType: 'Bundle',
    type: 'searchset',
    total: 2,
    entry: [
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-123',
          status: 'booked',
          start: '2099-05-15T10:00:00Z',
          end: '2099-05-15T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-124',
          status: 'cancelled',
          start: '2099-05-16T10:00:00Z',
          end: '2099-05-16T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
    ],
  };

  const mockPastAppointments = {
    resourceType: 'Bundle',
    type: 'searchset',
    total: 2,
    entry: [
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-125',
          status: 'noshow',
          start: '2023-05-10T10:00:00Z',
          end: '2023-05-10T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-126',
          status: 'noshow',
          start: '2023-05-05T10:00:00Z',
          end: '2023-05-05T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
    ],
  };

  beforeEach(() => {
    // Set up axios mock
    mockAxios = new MockAdapter(axios);

    // Create a mocked API client
    apiClient = new NextechApiClient(baseURL);

    // Create appointment service with the mocked client
    appointmentService = new NextechAppointmentService(apiClient);

    // Clear mocks before each test
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Reset axios mock
    mockAxios.reset();
  });

  it('should return future non-cancelled appointments when they exist', async () => {
    // Mock the get method to return future appointments
    (apiClient.get as jest.Mock).mockResolvedValueOnce(mockFutureAppointments);

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', false);

    // Verify the result
    expect(result.items.length).toBe(1); // Only one non-cancelled appointment
    expect(result.items[0].status).toBe(AppointmentStatus.BOOKED);
    expect(result.items[0].providerInfo.externalId).toBe('appt-123');
  });

  it('should return most recent no-show appointment when no future appointments exist and isForRescheduling is true', async () => {
    // Mock the get method to return empty future appointments and then past appointments
    (apiClient.get as jest.Mock)
      .mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      })
      .mockResolvedValueOnce(mockPastAppointments);

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', true);

    // Verify the result
    expect(result.items.length).toBe(1); // Only the most recent no-show
    expect(result.items[0].status).toBe(AppointmentStatus.NOSHOW);
    expect(result.items[0].providerInfo.externalId).toBe('appt-125'); // The most recent no-show
  });

  it('should return empty array when no future appointments and no recent no-shows exist', async () => {
    // Mock the get method to return empty future appointments and empty past appointments
    (apiClient.get as jest.Mock)
      .mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      })
      .mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', true);

    // Verify the result
    expect(result.items.length).toBe(0);
  });

  it('should handle API errors gracefully', async () => {
    // Mock the get method to throw an error
    (apiClient.get as jest.Mock).mockRejectedValueOnce(new Error('API Error'));

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', false);

    // Verify the result is an empty array
    expect(result.items.length).toBe(0);
    expect(result.pagination.totalCount).toBe(0);
  });
});
