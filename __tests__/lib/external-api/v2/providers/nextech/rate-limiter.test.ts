import { NextechRateLimiter } from '../../../../../../lib/external-api/v2/providers/nextech/rate-limiter';

describe('NextechRateLimiter', () => {
  // Setup to control time
  let originalDateNow: () => number;
  let mockNow = 1000000;
  let rateLimiter: NextechRateLimiter;

  beforeEach(() => {
    // Mock Date.now to have controlled time
    originalDateNow = Date.now;
    Date.now = jest.fn(() => mockNow);
    jest.useFakeTimers();
    rateLimiter = new NextechRateLimiter();
  });

  afterEach(() => {
    // Clean up any pending timeouts
    rateLimiter.cleanup();
    // Restore original Date.now
    Date.now = originalDateNow;
    jest.useRealTimers();
  });

  it('should allow requests within rate limit', async () => {
    // Arrange
    const endpoint = '/api/patients';

    // Act - make 20 requests (the limit)
    const promises = [];
    for (let i = 0; i < 20; i++) {
      promises.push(rateLimiter.acquireToken(endpoint));
    }

    // Assert - all should resolve without waiting
    await Promise.all(promises);
    // If we got here without timeouts, test passes
  });

  it('should track requests per endpoint separately', async () => {
    // Arrange
    const endpoint1 = '/api/patients';
    const endpoint2 = '/api/appointments';

    // Act - make 20 requests to each endpoint
    const promises1 = [];
    const promises2 = [];
    for (let i = 0; i < 20; i++) {
      promises1.push(rateLimiter.acquireToken(endpoint1));
      promises2.push(rateLimiter.acquireToken(endpoint2));
    }

    // Assert - all should resolve without waiting
    await Promise.all([...promises1, ...promises2]);
    // If we got here without timeouts, test passes
  });

  it('should wait when rate limit is exceeded', async () => {
    // Arrange
    const endpoint = '/api/patients';

    // Act - make 20 requests (the limit)
    for (let i = 0; i < 20; i++) {
      await rateLimiter.acquireToken(endpoint);
    }

    // Create a promise for the 21st request (over the limit)
    const overLimitPromise = rateLimiter.acquireToken(endpoint);

    // The promise should not resolve immediately since we're over the limit
    let resolved = false;
    overLimitPromise.then(() => {
      resolved = true;
    });

    // Verify promise hasn't resolved yet
    await Promise.resolve(); // Allow any pending microtasks to run
    expect(resolved).toBe(false);

    // Advance time by 1 second (the time window)
    mockNow += 1000; // Update our mocked Date.now value
    jest.advanceTimersByTime(1000); // Advance timer to trigger setTimeout callback

    // Now the promise should resolve
    await overLimitPromise;
    expect(resolved).toBe(true);
  });

  it('should clear rate limiting data', async () => {
    // Arrange
    const endpoint = '/api/patients';

    // Act - make 20 requests (the limit)
    for (let i = 0; i < 20; i++) {
      await rateLimiter.acquireToken(endpoint);
    }

    // Clear the rate limiter
    rateLimiter.clear();

    // Should be able to make another request without waiting
    await rateLimiter.acquireToken(endpoint);
    // If we got here without timeouts, test passes
  });

  it('should clean up all timeouts', async () => {
    // Arrange
    const endpoint = '/api/patients';

    // Fill up the rate limiter
    for (let i = 0; i < 20; i++) {
      await rateLimiter.acquireToken(endpoint);
    }

    // Start a request that will be rate limited
    // We'll use a flag to track if the promise resolves or rejects
    let promiseResolved = false;
    let promiseRejected = false;

    const pendingPromise = rateLimiter
      .acquireToken(endpoint)
      .then(() => {
        promiseResolved = true;
      })
      .catch(() => {
        promiseRejected = true;
      });

    // Don't await it, just clean up
    rateLimiter.cleanup();

    // Should be able to make a new request immediately
    await rateLimiter.acquireToken(endpoint);

    // We don't need to wait for the pending promise since cleanup should have
    // canceled it or allowed it to resolve/reject immediately

    // Verify we can continue without waiting for the original promise
    expect(true).toBe(true);
  });
});
