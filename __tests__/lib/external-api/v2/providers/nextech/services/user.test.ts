import { NextechApiClient } from '../../../../../../../lib/external-api/v2/providers/nextech/client';
import { NextechUserService } from '../../../../../../../lib/external-api/v2/providers/nextech/services/user';
import {
  NextechUserResponse,
  FHIRPractitioner,
} from '../../../../../../../lib/external-api/v2/providers/nextech/services/mappers';
import logger from '../../../../../../../lib/external-api/v2/utils/logger';

// Mock the NextechApiClient
jest.mock('../../../../../../../lib/external-api/v2/providers/nextech/client');

// Mock the logger
jest.mock('../../../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechUserService', () => {
  let mockClient: jest.Mocked<NextechApiClient>;
  let userService: NextechUserService;

  beforeEach(() => {
    // Clear and reset mocks
    jest.clearAllMocks();

    // Set up the mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;
    mockClient.get = jest.fn();

    // Create the service with the mock client
    userService = new NextechUserService(mockClient);
  });

  // Using mockFHIRPractitioner instead of mockUserResponse

  const mockFHIRPractitioner: FHIRPractitioner = {
    resourceType: 'Practitioner',
    id: 'provider-123',
    identifier: [{ use: 'official', value: 'provider-123' }],
    active: true,
    name: [{ use: 'official', text: 'Jane L Smith', family: 'Smith', given: ['Jane', 'L'] }],
    telecom: [
      { system: 'phone', value: '************', use: 'work' },
      { system: 'email', value: '<EMAIL>', use: 'work' },
    ],
  };

  describe('getUsers', () => {
    it('should return mapped users from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUsers();

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?_count=10&_getpagesoffset=0&active=true&phone=************',
      );
      expect(result.items).toHaveLength(1);
      expect(result.items[0].providerInfo.externalId).toBe('provider-123');
    });

    it('should apply filters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      const filters = {
        name: 'Smith',
        specialty: 'dermatology',
        other: 'Ignore this',
      };

      // Act
      await userService.getUsers(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?name=Smith&specialty=dermatology&other=Ignore+this&_count=10&_getpagesoffset=0&active=true&phone=************',
      );
    });

    it('should handle empty response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await userService.getUsers();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
    });

    it('should handle error and return empty array', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await userService.getUsers();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getUserById', () => {
    it('should return mapped user from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockFHIRPractitioner);

      // Act
      const result = await userService.getUserById('provider-123');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/Practitioner/provider-123');
      expect(result).not.toBeNull();
      expect(result?.providerInfo.externalId).toBe('provider-123');
    });

    it('should handle 404 error and return null', async () => {
      // Arrange
      const error = new Error('Not Found: 404');
      mockClient.get.mockRejectedValue(error);

      // Act
      const result = await userService.getUserById('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle other errors and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await userService.getUserById('provider-123');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getUserByPhone', () => {
    it('should clean phone number and return the user', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUserByPhone('(*************');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/Practitioner?phone=**********&_count=1');
      expect(result).not.toBeNull();
      expect(result?.phoneNumber).toBe('************');
    });

    it('should return null when no users match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await userService.getUserByPhone('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await userService.getUserByPhone('************');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getUserByEmail', () => {
    it('should normalize email to lowercase and return the user', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUserByEmail('<EMAIL>');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?telecom=email%7Cjane.smith%40clinic.com&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
      expect(result?.email).toBe('<EMAIL>');
    });

    it('should return null when no users match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await userService.getUserByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await userService.getUserByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getUserByFullName', () => {
    it("should parse 'LastName, FirstName' format and return the user", async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUserByFullName('Smith, Jane');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?family=Smith&given=Jane&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
      expect(result?.firstName).toBe('Jane');
      expect(result?.lastName).toBe('Smith');
    });

    it("should parse 'FirstName LastName' format and return the user", async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUserByFullName('Jane Smith');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?family=Smith&given=Jane&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
    });

    it('should handle complex names correctly', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPractitioner }],
      });

      // Act
      const result = await userService.getUserByFullName('Jane L. Smith-Jones');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Practitioner?family=L.+Smith-Jones&given=Jane&_count=1&_getpagesoffset=0',
      );
    });

    it('should return null when no users match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await userService.getUserByFullName('Not Found');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await userService.getUserByFullName('Jane Smith');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
