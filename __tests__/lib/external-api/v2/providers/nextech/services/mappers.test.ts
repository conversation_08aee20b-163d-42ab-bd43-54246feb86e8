import { v4 as uuidv4 } from 'uuid';
import {
  NextechClinicResponse,
  NextechLocationResponse,
  NextechPatientResponse,
  NextechUserResponse,
  mapNextechClinicToClinic,
  mapNextechLocationToLocation,
  mapNextechPatientToPatient,
  mapNextechUserToUser,
  FHIRPractitioner,
  FHIRPatient,
  FHIRLocation,
  FHIRAppointment,
  mapFHIRPractitionerToUser,
  mapFHIRPatientToPatient,
  mapFHIRLocationToLocation,
  mapFHIRAppointmentToAppointment,
  mapFHIRAppointmentTypeToAppointmentType,
} from '../../../../../../../lib/external-api/v2/providers/nextech/services/mappers';
import { AppointmentStatus } from '../../../../../../../lib/external-api/v2/models/types';

// Mock uuid to return predictable values
jest.mock('uuid');
(uuidv4 as jest.Mock).mockReturnValue('mocked-uuid');

describe('Nextech Mappers', () => {
  describe('mapNextechClinicToClinic', () => {
    it('should correctly map a Nextech clinic to our Clinic model', () => {
      // Arrange
      const nextechClinic: NextechClinicResponse = {
        practiceId: 'practice-123',
        name: 'Test Clinic',
        website: 'https://testclinic.com',
        email: '<EMAIL>',
        address1: '123 Main St',
        address2: 'Suite 100',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
        phoneNumber: '************',
        faxNumber: '************',
        active: true,
      };

      // Act
      const result = mapNextechClinicToClinic(nextechClinic);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Clinic',
        address: {
          line1: '123 Main St',
          line2: 'Suite 100',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        phoneNumber: '************',
        emailAddress: '<EMAIL>',
        website: 'https://testclinic.com',
        providerInfo: {
          provider: 'nextech',
          externalId: 'practice-123',
        },
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const nextechClinic: NextechClinicResponse = {
        practiceId: 'practice-123',
        name: 'Test Clinic',
        address1: '123 Main St',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
        phoneNumber: '************',
        active: true,
      };

      // Act
      const result = mapNextechClinicToClinic(nextechClinic);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Clinic',
        address: {
          line1: '123 Main St',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        phoneNumber: '************',
        emailAddress: undefined,
        website: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'practice-123',
        },
      });
    });
  });

  describe('mapNextechLocationToLocation', () => {
    it('should correctly map a Nextech location to our Location model', () => {
      // Arrange
      const nextechLocation: NextechLocationResponse = {
        locationId: 'location-123',
        practiceId: 'practice-123',
        name: 'Test Location',
        address1: '456 Main St',
        address2: 'Floor 2',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
        phoneNumber: '************',
        faxNumber: '************',
        active: true,
      };

      // Act
      const result = mapNextechLocationToLocation(nextechLocation);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Location',
        address: {
          line1: '456 Main St',
          line2: 'Floor 2',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        clinicId: 'practice-123',
        phoneNumber: '************',
        emailAddress: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'location-123',
        },
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const nextechLocation: NextechLocationResponse = {
        locationId: 'location-123',
        practiceId: 'practice-123',
        name: 'Test Location',
        address1: '456 Main St',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
        active: true,
      };

      // Act
      const result = mapNextechLocationToLocation(nextechLocation);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Location',
        address: {
          line1: '456 Main St',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        clinicId: 'practice-123',
        phoneNumber: undefined,
        emailAddress: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'location-123',
        },
      });
    });
  });

  describe('mapNextechPatientToPatient', () => {
    it('should correctly map a Nextech patient to our Patient model', () => {
      // Arrange
      const nextechPatient: NextechPatientResponse = {
        patientId: 'patient-123',
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'M',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneHome: '************',
        phoneMobile: '************',
        phoneWork: '************',
        address1: '789 Main St',
        address2: 'Apt 123',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
        active: true,
      };

      // Act
      const result = mapNextechPatientToPatient(nextechPatient);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************', // Should use mobile first
        address: {
          line1: '789 Main St',
          line2: 'Apt 123',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        providerInfo: {
          provider: 'nextech',
          externalId: 'patient-123',
        },
      });
    });

    it('should handle missing mobile phone by falling back to home phone', () => {
      // Arrange
      const nextechPatient: NextechPatientResponse = {
        patientId: 'patient-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        phoneHome: '************',
        address1: '789 Main St',
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        active: true,
      };

      // Act
      const result = mapNextechPatientToPatient(nextechPatient);

      // Assert
      expect(result.phoneNumber).toBe('************');
    });

    it('should handle missing address information', () => {
      // Arrange
      const nextechPatient: NextechPatientResponse = {
        patientId: 'patient-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        active: true,
      };

      // Act
      const result = mapNextechPatientToPatient(nextechPatient);

      // Assert
      expect(result.address).toBeUndefined();
    });
  });

  describe('mapNextechUserToUser', () => {
    it('should correctly map a Nextech user to our User model', () => {
      // Arrange
      const nextechUser: NextechUserResponse = {
        userId: 'user-123',
        providerId: 'provider-123',
        firstName: 'Jane',
        lastName: 'Smith',
        middleName: 'L',
        email: '<EMAIL>',
        phone: '************',
        role: 'physician',
        specialty: 'dermatology',
        practiceId: 'practice-123',
        locationId: 'location-123',
        active: true,
      };

      // Act
      const result = mapNextechUserToUser(nextechUser);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phoneNumber: '************',
        role: 'physician',
        speciality: 'dermatology',
        clinicId: 'practice-123',
        locationId: 'location-123',
        providerInfo: {
          provider: 'nextech',
          externalId: 'provider-123',
        },
      });
    });

    it('should fallback to userId when providerId is missing', () => {
      // Arrange
      const nextechUser: NextechUserResponse = {
        userId: 'user-123',
        providerId: '', // Empty provider ID
        firstName: 'Jane',
        lastName: 'Smith',
        active: true,
      };

      // Act
      const result = mapNextechUserToUser(nextechUser);

      // Assert
      expect(result.providerInfo.externalId).toBe('user-123');
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const nextechUser: NextechUserResponse = {
        userId: 'user-123',
        providerId: 'provider-123',
        firstName: 'Jane',
        lastName: 'Smith',
        active: true,
      };

      // Act
      const result = mapNextechUserToUser(nextechUser);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        firstName: 'Jane',
        lastName: 'Smith',
        email: undefined,
        phoneNumber: undefined,
        role: undefined,
        speciality: undefined,
        clinicId: undefined,
        locationId: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'provider-123',
        },
      });
    });
  });

  describe('mapFHIRPractitionerToUser', () => {
    it('should correctly map a FHIR Practitioner to our User model', () => {
      // Arrange
      const fhirPractitioner: FHIRPractitioner = {
        resourceType: 'Practitioner',
        id: 'provider-123',
        identifier: [
          { use: 'official', value: 'provider-123' },
          { system: 'http://hl7.org/fhir/sid/us-npi', value: '**********' },
        ],
        active: true,
        name: [{ use: 'official', text: 'Jane Smith', family: 'Smith', given: ['Jane'] }],
        telecom: [
          { system: 'phone', value: '************', use: 'work' },
          { system: 'email', value: '<EMAIL>', use: 'work' },
        ],
        address: [{ use: 'work', type: 'both', country: 'US' }],
      };

      // Act
      const result = mapFHIRPractitionerToUser(fhirPractitioner);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        phoneNumber: '************',
        role: undefined,
        speciality: undefined,
        clinicId: undefined,
        locationId: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'provider-123',
        },
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const fhirPractitioner: FHIRPractitioner = {
        resourceType: 'Practitioner',
        id: 'provider-123',
        active: true,
        name: [{ family: 'Smith', given: ['Jane'] }],
      };

      // Act
      const result = mapFHIRPractitionerToUser(fhirPractitioner);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        firstName: 'Jane',
        lastName: 'Smith',
        email: undefined,
        phoneNumber: undefined,
        role: undefined,
        speciality: undefined,
        clinicId: undefined,
        locationId: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'provider-123',
        },
      });
    });
  });

  describe('mapFHIRPatientToPatient', () => {
    it('should correctly map a FHIR Patient to our Patient model', () => {
      // Arrange
      const fhirPatient: FHIRPatient = {
        resourceType: 'Patient',
        id: 'patient-123',
        identifier: [{ use: 'official', value: 'patient-123' }],
        active: true,
        name: [{ use: 'official', text: 'John Doe', family: 'Doe', given: ['John'] }],
        gender: 'male',
        birthDate: '1980-01-01',
        telecom: [
          { system: 'phone', value: '************', use: 'home' },
          { system: 'phone', value: '************', use: 'mobile' },
          { system: 'phone', value: '************', use: 'work' },
          { system: 'email', value: '<EMAIL>' },
        ],
        address: [
          {
            use: 'home',
            line: ['789 Main St', 'Apt 123'],
            city: 'Test City',
            state: 'TS',
            postalCode: '12345',
            country: 'US',
          },
        ],
      };

      // Act
      const result = mapFHIRPatientToPatient(fhirPatient);

      // Assert
      expect(result).toEqual({
        id: 'patient-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        address: {
          line1: '789 Main St',
          line2: 'Apt 123',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        notes: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'patient-123',
        },
        identifiers: [
          {
            use: 'official',
            system: '',
            value: 'patient-123',
          },
        ],
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const fhirPatient: FHIRPatient = {
        resourceType: 'Patient',
        id: 'patient-123',
        active: true,
        name: [{ family: 'Doe', given: ['John'] }],
        gender: 'male',
      };

      // Act
      const result = mapFHIRPatientToPatient(fhirPatient);

      // Assert
      expect(result).toEqual({
        id: 'patient-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '',
        gender: 'male',
        email: undefined,
        phoneNumber: undefined,
        address: undefined,
        notes: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'patient-123',
        },
        identifiers: [],
      });
    });
  });

  describe('mapFHIRLocationToLocation', () => {
    it('should correctly map a FHIR Location to our Location model', () => {
      // Arrange
      const fhirLocation: FHIRLocation = {
        resourceType: 'Location',
        id: 'location-123',
        identifier: [{ use: 'official', value: 'location-123' }],
        status: 'active',
        name: 'Test Location',
        telecom: [{ system: 'phone', value: '************', use: 'work' }],
        address: {
          line: ['456 Main St', 'Floor 2'],
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        managingOrganization: {
          reference: 'Practice/practice-123',
          display: 'Test Clinic',
        },
      };

      // Act
      const result = mapFHIRLocationToLocation(fhirLocation);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Clinic',
        address: {
          line1: '456 Main St',
          line2: 'Floor 2',
          city: 'Test City',
          state: 'TS',
          postalCode: '12345',
          country: 'US',
        },
        clinicId: 'practice-123',
        phoneNumber: '************',
        emailAddress: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'location-123',
        },
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const fhirLocation: FHIRLocation = {
        resourceType: 'Location',
        id: 'location-123',
        status: 'active',
        name: 'Test Location',
      };

      // Act
      const result = mapFHIRLocationToLocation(fhirLocation);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Test Location',
        address: {
          line1: '',
          city: '',
          state: '',
          postalCode: '',
          country: 'US',
        },
        clinicId: '',
        phoneNumber: undefined,
        emailAddress: undefined,
        providerInfo: {
          provider: 'nextech',
          externalId: 'location-123',
        },
      });
    });
  });

  describe('mapFHIRAppointmentTypeToAppointmentType', () => {
    it('should correctly map a FHIR appointment-type to our AppointmentType model', () => {
      // Arrange
      const fhirAppointmentType = {
        resourceType: 'appointment-type',
        id: '1',
        extension: [
          {
            url: 'appointment-type',
            valueString: 'Consult New Patient',
          },
        ],
      };

      // Act
      const result = mapFHIRAppointmentTypeToAppointmentType(fhirAppointmentType);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: 'Consult New Patient',
        description: '',
        duration: 0,
        color: undefined,
        isActive: true,
        providerInfo: {
          provider: 'nextech',
          externalId: '1',
        },
      });
    });

    it('should handle missing extension', () => {
      // Arrange
      const fhirAppointmentType = {
        resourceType: 'appointment-type',
        id: '1',
      };

      // Act
      const result = mapFHIRAppointmentTypeToAppointmentType(fhirAppointmentType);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: '',
        description: '',
        duration: 0,
        color: undefined,
        isActive: true,
        providerInfo: {
          provider: 'nextech',
          externalId: '1',
        },
      });
    });

    it('should handle extension without valueString', () => {
      // Arrange
      const fhirAppointmentType = {
        resourceType: 'appointment-type',
        id: '1',
        extension: [
          {
            url: 'appointment-type',
          },
        ],
      };

      // Act
      const result = mapFHIRAppointmentTypeToAppointmentType(fhirAppointmentType);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        name: '',
        description: '',
        duration: 0,
        color: undefined,
        isActive: true,
        providerInfo: {
          provider: 'nextech',
          externalId: '1',
        },
      });
    });
  });

  describe('mapFHIRAppointmentToAppointment', () => {
    it('should correctly map a FHIR Appointment to our Appointment model', () => {
      // Arrange
      const fhirAppointment: FHIRAppointment = {
        resourceType: 'Appointment',
        id: 'appt-123',
        status: 'booked',
        serviceType: [{ text: 'Follow-up' }],
        reason: [{ text: 'Checkup' }],
        start: '2023-05-15T09:00:00',
        end: '2023-05-15T09:30:00',
        created: '2023-05-01T08:00:00',
        comment: 'Patient notes',
        participant: [
          {
            actor: {
              reference: 'Patient/patient-123',
              display: 'John Doe',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Practitioner/provider-123',
              display: 'Dr. Jane Smith',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Location/location-123',
              display: 'Test Location',
            },
            status: 'accepted',
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/practice',
            valueReference: {
              reference: 'Practice/practice-123',
              display: 'Test Clinic',
            },
          },
          {
            url: 'appointment-type',
            valueReference: {
              reference: 'appointment-type/Follow-up',
              display: 'Follow-up',
            },
          },
        ],
      };

      // Act
      const result = mapFHIRAppointmentToAppointment(fhirAppointment);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        patientId: 'patient-123',
        patientName: 'John Doe',
        providerId: 'provider-123',
        practitionerId: 'provider-123',
        practitionerName: 'Dr. Jane Smith',
        locationId: 'location-123',
        locationName: 'Test Location',
        clinicId: 'practice-123',
        startTime: '2023-05-15T09:00:00',
        endTime: '2023-05-15T09:30:00',
        status: AppointmentStatus.BOOKED,
        type: 'Follow-up',
        reason: 'Checkup',
        notes: 'Patient notes',
        createdAt: '2023-05-01T08:00:00',
        updatedAt: '2023-05-01T08:00:00',
        providerInfo: {
          provider: 'nextech',
          externalId: 'appt-123',
        },
      });
    });

    it('should handle missing optional fields', () => {
      // Arrange
      const fhirAppointment: FHIRAppointment = {
        resourceType: 'Appointment',
        id: 'appt-123',
        status: 'proposed',
        start: '2023-05-15T09:00:00',
        end: '2023-05-15T09:30:00',
        participant: [
          {
            actor: {
              reference: 'Patient/patient-123',
            },
            status: 'accepted',
          },
        ],
      };

      // Act
      const result = mapFHIRAppointmentToAppointment(fhirAppointment);

      // Assert
      expect(result).toEqual({
        id: 'mocked-uuid',
        patientId: 'patient-123',
        patientName: '',
        providerId: '',
        practitionerId: '',
        practitionerName: '',
        locationId: '',
        locationName: '',
        clinicId: '',
        startTime: '2023-05-15T09:00:00',
        endTime: '2023-05-15T09:30:00',
        status: AppointmentStatus.PROPOSED,
        type: undefined,
        reason: undefined,
        notes: undefined,
        createdAt: '',
        updatedAt: '',
        providerInfo: {
          provider: 'nextech',
          externalId: 'appt-123',
        },
      });
    });
  });
});
