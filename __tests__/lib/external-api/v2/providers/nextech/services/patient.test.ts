import { NextechApiClient } from '@/lib/external-api/v2/providers/nextech/client';
import { NextechPatientService } from '@/lib/external-api/v2/providers/nextech/services';
import { FHIRPatient } from '@/lib/external-api/v2/providers/nextech/services';
import logger from '../../../../../../../lib/external-api/v2/utils/logger';

// Mock the logger
jest.mock('../../../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

// Mock the NextechApiClient
jest.mock('../../../../../../../lib/external-api/v2/providers/nextech/client');

describe('NextechPatientService', () => {
  let mockClient: jest.Mocked<NextechApiClient>;
  let patientService: NextechPatientService;

  beforeEach(() => {
    // Clear and reset mocks
    jest.clearAllMocks();

    // Set up the mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;
    mockClient.get = jest.fn();

    // Create the service with the mock client
    patientService = new NextechPatientService(mockClient);
  });

  // Using mockFHIRPatient instead of mockPatientResponse

  const mockFHIRPatient: FHIRPatient = {
    resourceType: 'Patient',
    id: 'patient-123',
    identifier: [{ use: 'official', value: 'patient-123' }],
    active: true,
    name: [
      {
        use: 'official',
        text: 'John M Doe',
        family: 'Doe',
        given: ['John', 'M'],
      },
    ],
    gender: 'male',
    birthDate: '1980-01-01',
    telecom: [
      { system: 'phone', value: '************', use: 'home' },
      { system: 'phone', value: '************', use: 'mobile' },
      { system: 'phone', value: '************', use: 'work' },
      { system: 'email', value: '<EMAIL>' },
    ],
    address: [
      {
        use: 'home',
        line: ['789 Main St', 'Apt 123'],
        city: 'Test City',
        state: 'TS',
        postalCode: '12345',
        country: 'US',
      },
    ],
  };

  describe('getPatients', () => {
    it('should return mapped patients from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act
      const result = await patientService.getPatients();

      // Assert
      expect(mockClient.get).toHaveBeenCalled();
      expect(result.items).toHaveLength(1);
      expect(result.items[0].providerInfo.externalId).toBe('patient-123');
    });

    it('should apply filters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        name: 'Doe',
        birthdate: '1980-01-01',
        other: 'Ignore this',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?name=Doe&birthdate=1980-01-01&other=Ignore+this&_count=10&_getpagesoffset=0',
      );
    });

    it('should correctly parse fullName filter into given and family parameters using first 3 letters', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        fullName: 'John Doe',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?given=Joh&family=Doe&_count=10&_getpagesoffset=0',
      );
    });

    it('should handle fullName filter without a space (single name) using first 3 letters', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        fullName: 'John',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/Patient?given=Joh&_count=10&_getpagesoffset=0');
    });

    it('should correctly apply dateOfBirth filter', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        dateOfBirth: '1980-01-01',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?birthdate=1980-01-01&_count=10&_getpagesoffset=0',
      );
    });

    it('should correctly apply phoneNumber filter', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        phoneNumber: '************',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('phone=**********'));
    });

    it('should correctly apply multiple filters together (fullName, dateOfBirth, phoneNumber) using first 3 letters', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        fullName: 'John Doe',
        dateOfBirth: '1980-01-01',
        phoneNumber: '************',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringMatching(
          /^\/Patient\?given=Joh&family=Doe&birthdate=1980-01-01&phone=**********/,
        ),
      );
    });

    it('should correctly handle complex names with middle name or suffix using first 3 letters', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      const filters = {
        fullName: 'John Michael Doe Jr.',
      };

      // Act
      await patientService.getPatients(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?given=Joh&family=Mic&_count=10&_getpagesoffset=0',
      );
    });

    it('should handle empty response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await patientService.getPatients();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
    });

    it('should handle error and return empty array', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatients();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getPatientById', () => {
    it('should return mapped patient from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockFHIRPatient);

      // Act
      const result = await patientService.getPatientById('patient-123');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/Patient/patient-123');
      expect(result).not.toBeNull();
      expect(result?.providerInfo.externalId).toBe('patient-123');
    });

    it('should handle 404 error and return null', async () => {
      // Arrange
      const error = new Error('Not Found: 404');
      mockClient.get.mockRejectedValue(error);

      // Act
      const result = await patientService.getPatientById('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle other errors, log (masking sensitive parts of the ID), and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientById('patient-123');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalledWith(
        expect.objectContaining({
          provider: 'nextech',
          context: 'Patient Service - Get Patient By ID',
          patientIdPrefix: 'pat',
          error: expect.any(Error),
        }),
        expect.any(String),
      );
    });
  });

  describe('getPatientByPhone', () => {
    it('should clean phone number and return the patient', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act
      const result = await patientService.getPatientByPhone('(*************');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('phone=**********'));
      expect(result).not.toBeNull();
      expect(result?.phoneNumber).toBe('************');
    });

    it('should try with country code fallback for 10-digit US number', async () => {
      // Arrange
      // First call with original number fails
      mockClient.get.mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Second call with added country code succeeds
      mockClient.get.mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act - use a 10-digit US number without country code
      const result = await patientService.getPatientByPhone('**********');

      // Assert
      // Should have been called with both the original number and with country code added
      expect(mockClient.get).toHaveBeenCalledTimes(2);
      expect(mockClient.get).toHaveBeenNthCalledWith(
        1,
        expect.stringContaining('phone=**********'),
      );
      expect(mockClient.get).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('phone=1**********'),
      );
      expect(result).not.toBeNull();
    });

    it('should try without country code fallback for 11-digit US number', async () => {
      // Arrange
      // First call with original number fails
      mockClient.get.mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Second call without country code succeeds
      mockClient.get.mockResolvedValueOnce({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act - use an 11-digit US number with country code
      const result = await patientService.getPatientByPhone('1**********');

      // Assert
      // Should have been called with both the original number and without country code
      expect(mockClient.get).toHaveBeenCalledTimes(2);
      expect(mockClient.get).toHaveBeenNthCalledWith(
        1,
        expect.stringContaining('phone=1**********'),
      );
      expect(mockClient.get).toHaveBeenNthCalledWith(
        2,
        expect.stringContaining('phone=**********'),
      );
      expect(result).not.toBeNull();
    });

    it('should return null when no patients match with any phone format', async () => {
      // Arrange
      // All calls return empty results
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await patientService.getPatientByPhone('**********');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientByPhone('************');

      // Assert
      expect(result).toBeNull();
      // Since we're using searchPatientsWithPhoneFormats, the error is logged there,
      // not directly in getPatientByPhone, so we don't check for the logger call here
    });
  });

  describe('getPatientByEmail', () => {
    it('should normalize email to lowercase and return the patient', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act
      const result = await patientService.getPatientByEmail('<EMAIL>');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?telecom=email%7Cjohn.doe%40example.com&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
      expect(result?.email).toBe('<EMAIL>');
    });

    it('should return null when no patients match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await patientService.getPatientByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientByEmail('<EMAIL>');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getPatientByFullNameAndDob', () => {
    it("should parse 'LastName, FirstName' format and return the patient", async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act
      const result = await patientService.getPatientByFullNameAndDob('Doe, John', '1980-01-01');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?family=Doe&given=John&birthdate=1980-01-01&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
      expect(result?.firstName).toBe('John');
      expect(result?.lastName).toBe('Doe');
    });

    it("should parse 'FirstName LastName' format and return the patient", async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRPatient }],
      });

      // Act
      const result = await patientService.getPatientByFullNameAndDob('John Doe', '1980-01-01');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Patient?family=Doe&given=John&birthdate=1980-01-01&_count=1&_getpagesoffset=0',
      );
      expect(result).not.toBeNull();
    });

    it('should return null when no patients match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      // Act
      const result = await patientService.getPatientByFullNameAndDob('Not Found', '1980-01-01');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle error and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientByFullNameAndDob('John Doe', '1980-01-01');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getPatientTypes', () => {
    it('should return mapped patient types from API response', async () => {
      // Arrange
      const mockPatientTypeResponse = {
        entry: [
          {
            resource: {
              patientTypeId: 'type-123',
              name: 'Regular',
              description: 'Regular patient type',
              active: true,
            },
          },
        ],
      };
      mockClient.get.mockResolvedValue(mockPatientTypeResponse);

      // Act
      const result = await patientService.getPatientTypes();

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/PatientType?_count=10');
      expect(result.items).toHaveLength(1);
      expect(result.items[0].providerInfo.externalId).toBe('type-123');
      expect(result.items[0].name).toBe('Regular');
    });

    it('should apply filters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({ entry: [] });

      const filters = {
        active: true,
        name: 'VIP',
      };

      // Act
      await patientService.getPatientTypes(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/PatientType?name=VIP&_count=10');
    });

    it('should handle empty response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({});

      // Act
      const result = await patientService.getPatientTypes();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
    });

    it('should handle error and return empty array', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientTypes();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getPatientTypeById', () => {
    it('should return mapped patient type from API response', async () => {
      // Arrange
      const mockPatientTypeResponse = {
        patientTypeId: 'type-123',
        name: 'Regular',
        description: 'Regular patient type',
        active: true,
      };
      mockClient.get.mockResolvedValue(mockPatientTypeResponse);

      // Act
      const result = await patientService.getPatientTypeById('type-123');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/PatientType/type-123');
      expect(result).not.toBeNull();
      expect(result?.providerInfo.externalId).toBe('type-123');
      expect(result?.name).toBe('Regular');
    });

    it('should handle 404 error and return null', async () => {
      // Arrange
      const error = new Error('Not Found: 404');
      mockClient.get.mockRejectedValue(error);

      // Act
      const result = await patientService.getPatientTypeById('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle other errors and return null', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await patientService.getPatientTypeById('type-123');

      // Assert
      expect(result).toBeNull();
      expect(logger.error).toHaveBeenCalled();
    });
  });
});
