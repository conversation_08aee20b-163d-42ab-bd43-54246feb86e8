import { NextechApiClient } from '../../../../../../../lib/external-api/v2/providers/nextech/client';
import { NextechAppointmentService } from '../../../../../../../lib/external-api/v2/providers/nextech/services/appointment';
import { AppointmentStatus } from '../../../../../../../lib/external-api/v2/models/types';
import { CreateAppointmentDto } from '../../../../../../../lib/external-api/v2/models/dto';
import {
  FHIRAppointment,
  FHIRSlot,
} from '../../../../../../../lib/external-api/v2/providers/nextech/services/mappers';
import logger from '../../../../../../../lib/external-api/v2/utils/logger';

// Mock the NextechApiClient
jest.mock('../../../../../../../lib/external-api/v2/providers/nextech/client');

// Mock the logger
jest.mock('../../../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechAppointmentService', () => {
  let appointmentService: NextechAppointmentService;
  let mockClient: jest.Mocked<NextechApiClient>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;

    // Create service with mock client
    appointmentService = new NextechAppointmentService(mockClient);
  });

  // Mock FHIR Appointment
  const mockFHIRAppointment: FHIRAppointment = {
    resourceType: 'Appointment',
    id: 'appt-123',
    status: 'booked',
    serviceType: [{ text: 'Follow-up' }],
    reason: [{ text: 'Checkup' }],
    start: '2023-05-15T09:00:00',
    end: '2023-05-15T09:30:00',
    created: '2023-05-01T08:00:00',
    comment: 'Patient notes',
    participant: [
      {
        actor: {
          reference: 'Patient/patient-123',
          display: 'John Doe',
        },
        status: 'accepted',
      },
      {
        actor: {
          reference: 'Practitioner/provider-123',
          display: 'Dr. Jane Smith',
        },
        status: 'accepted',
      },
      {
        actor: {
          reference: 'Location/location-123',
          display: 'Test Location',
        },
        status: 'accepted',
      },
    ],
    extension: [
      {
        url: 'https://api.pm.nextech.com/api/structuredefinition/practice',
        valueReference: {
          reference: 'Practice/practice-123',
          display: 'Test Clinic',
        },
      },
    ],
  };

  describe('getAppointments', () => {
    it('Should correctly call the API with default parameters', async () => {
      // Setup mock
      mockClient.get.mockResolvedValue({
        entry: [],
      });

      // Call the method
      await appointmentService.getAppointments();

      // Expectations - Verify API is called with correct parameters
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?_count=30&_getpagesoffset=0&location.id=118',
      );
    });

    it('Should correctly set the date parameters', async () => {
      // Setup mock
      mockClient.get.mockResolvedValue({
        entry: [],
      });

      // Call the method with start and end date
      await appointmentService.getAppointmentByDateRange(
        new Date('2023-01-01').toISOString(),
        new Date('2023-01-31').toString(),
      );

      // Expectations - Verify URL contains date parameters, but don't require exact matching due to URL encoding
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('/Appointment?'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('date=ge2023-01-01'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('date=lt2023-01-31'));
    });

    it('Should correctly handle the pagination parameters', async () => {
      // Setup mock
      mockClient.get.mockResolvedValue({
        entry: [],
      });

      // Call the method with pagination parameters
      await appointmentService.getAppointments(
        {},
        {
          limit: 25,
          offset: 50,
        },
      );

      // Expectations - Verify pagination parameters are included
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('/Appointment?'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('_count=25'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('_getpagesoffset=50'));
    });

    it('Should map appointments correctly from the API response', async () => {
      // Setup the API response with the mock appointment in FHIRBundle format
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [
          {
            resource: mockFHIRAppointment,
          },
        ],
      });

      // Call the method
      const result = await appointmentService.getAppointments();

      // Expectations - Verify the API call and mapping
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?_count=30&_getpagesoffset=0&location.id=118',
      );
      expect(result.items.length).toBe(1);
      expect(result.items[0].providerInfo.externalId).toBe('appt-123');
    });

    it('should handle various date formats and pagination parameters correctly', async () => {
      // Arrange
      const mockAppointmentsResponse = {
        items: [],
        totalCount: 0,
      };
      mockClient.get.mockResolvedValue(mockAppointmentsResponse);

      // Test with various date formats and pagination values
      const filters = {
        startDate: '2023/05/15', // Non-standard date format
        endDate: new Date('2023-05-20').toISOString(), // ISO string with time component
      };
      const pagination = {
        limit: 15, // Number as expected by PaginationParams
        offset: 5, // Number as expected by PaginationParams
      };

      // Act
      await appointmentService.getAppointmentByDateRange(
        filters.startDate,
        filters.endDate,
        pagination,
      );

      // Verify that the API was called with the correct parameters
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?date=ge2023-05-15&date=lt2023-05-20&_count=15&_getpagesoffset=5&location.id=118',
      );

      // Verify that the mock was called
      expect(mockClient.get).toHaveBeenCalled();

      // Get the actual parameters that were passed
      const mockCall = mockClient.get.mock.calls[0];
      expect(mockCall).toBeDefined();

      // Get the URL that was called
      const url = mockCall?.[0];
      expect(url).toBeDefined();

      // Verify that the URL contains properly formatted dates
      expect(url).toContain('date=');
      expect(url).toContain('_count=15');
      expect(url).toContain('_getpagesoffset=5');
    });

    it('should handle invalid pagination parameters', async () => {
      // Arrange
      const mockAppointmentsResponse = {
        items: [],
        totalCount: 0,
      };
      mockClient.get.mockResolvedValue(mockAppointmentsResponse);

      // Test with invalid pagination values
      const pagination = {
        limit: -5, // Negative limit
        offset: NaN, // Invalid number
      };

      // Act
      await appointmentService.getAppointments({}, pagination);

      // Verify that the mock was called
      expect(mockClient.get).toHaveBeenCalled();

      // Get the actual parameters that were passed
      const mockCall = mockClient.get.mock.calls[0];
      expect(mockCall).toBeDefined();

      // Get the URL that was called
      const url = mockCall?.[0];
      expect(url).toBeDefined();

      // Expectations - invalid values should be converted to valid ones
      expect(url).toContain('_count=1'); // Should be converted to minimum valid value
      expect(url).toContain('_getpagesoffset=0'); // Should be converted to minimum valid value
    });

    it('should return mapped appointments', async () => {
      // Arrange
      const mockFHIRAppointment2: FHIRAppointment = {
        resourceType: 'Appointment',
        id: 'appt-456',
        status: 'cancelled',
        serviceType: [{ text: 'New Patient' }],
        reason: [{ text: 'Initial visit' }],
        start: '2023-05-16T10:00:00',
        end: '2023-05-16T10:30:00',
        created: '2023-05-02T11:00:00',
        comment: 'New patient notes',
        participant: [
          {
            actor: {
              reference: 'Patient/patient-456',
              display: 'Jane Smith',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Practitioner/provider-456',
              display: 'Dr. John Doe',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Location/location-456',
              display: 'Other Location',
            },
            status: 'accepted',
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/practice',
            valueReference: {
              reference: 'Practice/practice-456',
              display: 'Other Clinic',
            },
          },
        ],
      };

      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 2,
        entry: [{ resource: mockFHIRAppointment }, { resource: mockFHIRAppointment2 }],
        link: [
          { relation: 'self', url: '/Appointment?_count=10&_getpagesoffset=0' },
          {
            relation: 'next',
            url: '/Appointment?_count=10&_getpagesoffset=10',
          },
        ],
      });

      // Act
      const result = await appointmentService.getAppointments();

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?_count=30&_getpagesoffset=0&location.id=118',
      );
      expect(result.items).toHaveLength(2);
      expect(result.items[0].providerInfo.externalId).toBe('appt-123');
      expect(result.items[0].status).toBe(AppointmentStatus.BOOKED);
      expect(result.items[1].providerInfo.externalId).toBe('appt-456');
      expect(result.items[1].status).toBe(AppointmentStatus.CANCELLED);
    });

    it('should apply filters correctly', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      });

      const filters = {
        patientId: 'patient-123',
        startDate: '2023-05-15',
        endDate: '2023-05-15',
      };

      // Act
      await appointmentService.getAppointments(filters);

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?date=ge2023-05-15&date=lt2023-05-15&patient=patient-123&_count=30&_getpagesoffset=0&location.id=118',
      );
    });

    it('should handle error and return empty array', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await appointmentService.getAppointments();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });

    it('should format dates correctly in the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({ entry: [] });

      // Act
      await appointmentService.getAppointments({
        startMin: '2023-01-01',
        startMax: '2023-01-07',
      });

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?startMin=2023-01-01&startMax=2023-01-07&_count=30&_getpagesoffset=0&location.id=118',
      );
    });

    it('should apply pagination parameters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        items: [],
      });

      const pagination = {
        limit: 25,
        offset: 25,
      };

      // Act
      await appointmentService.getAppointments({}, pagination);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?_count=25&_getpagesoffset=25&location.id=118',
      );
    });
  });

  describe('getAppointmentById', () => {
    it('should return mapped appointment when found', async () => {
      // Using mockFHIRAppointment defined at the top of the file

      // Setup mock
      mockClient.get.mockResolvedValue(mockFHIRAppointment);

      // Call the method
      const result = await appointmentService.getAppointmentById('appt-123');

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith('/Appointment/appt-123');
      expect(result?.providerInfo.externalId).toBe('appt-123');
      expect(result?.status).toBe(AppointmentStatus.BOOKED);
    });

    it('should return null when appointment not found', async () => {
      // Setup mock to throw error with 404 message
      mockClient.get.mockRejectedValue(new Error('404 Not Found'));

      // Call the method
      const result = await appointmentService.getAppointmentById('not-found');

      // Expectations
      expect(result).toBeNull();
    });

    it('should propagate other errors', async () => {
      // Setup mock to throw non-404 error
      const error = new Error('Network Error');
      mockClient.get.mockRejectedValue(error);

      // Call the method and expect it to throw
      await expect(appointmentService.getAppointmentById('appt-123')).rejects.toThrow(error);
    });
  });

  describe('createAppointment', () => {
    it('should create and return a new appointment using FHIR format', async () => {
      // Mock data for creating appointment
      const createData: CreateAppointmentDto = {
        patientId: 'patient-123',
        practitionerId: 'provider-123', // Changed from providerId to practitionerId
        locationId: 'location-123',
        clinicId: 'practice-123',
        startTime: '2023-05-15T09:00:00',
        endTime: '2023-05-15T09:30:00',
        type: 'Follow-up',
        reason: 'Checkup',
        notes: 'Patient notes',
      };

      // Mock response from Nextech API - using FHIR format
      const mockCreatedFHIRAppointment: FHIRAppointment = {
        resourceType: 'Appointment',
        id: 'appt-123',
        status: 'booked',
        serviceType: [{ text: 'Follow-up' }],
        reason: [{ text: 'Checkup' }],
        start: '2023-05-15T09:00:00',
        end: '2023-05-15T09:30:00',
        created: '2023-05-01T10:00:00',
        comment: 'Patient notes',
        participant: [
          {
            actor: {
              reference: 'Patient/patient-123',
              display: 'John Doe',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Practitioner/provider-123',
              display: 'Dr. Jane Smith',
            },
            status: 'accepted',
          },
          {
            actor: {
              reference: 'Location/location-123',
              display: 'Test Location',
            },
            status: 'accepted',
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/practice',
            valueReference: {
              reference: 'Practice/practice-123',
              display: 'Test Clinic',
            },
          },
        ],
      };

      // Setup mock
      mockClient.post.mockResolvedValue(mockCreatedFHIRAppointment);

      // Call the method
      const result = await appointmentService.createAppointment(createData);

      // Expectations
      // We need to use a more flexible matcher because of timezone handling
      expect(mockClient.post).toHaveBeenCalledWith(
        '/Appointment',
        expect.objectContaining({
          resourceType: 'Appointment',
          status: 'proposed', // Changed from booked to proposed as per Nextech example
          participant: expect.arrayContaining([
            expect.objectContaining({
              actor: { reference: 'Patient/patient-123' },
              status: 'accepted',
            }),
            expect.objectContaining({
              actor: { reference: 'Location/location-123' },
              status: 'accepted',
            }),
            expect.objectContaining({
              actor: { reference: 'Practitioner/provider-123' },
              status: 'accepted',
            }),
          ]),
          extension: expect.arrayContaining([
            expect.objectContaining({
              url: 'appointment-type',
              valueReference: {
                reference: 'appointment-type/Follow-up',
                display: 'Follow-up',
              },
            }),
          ]),
          reason: [{ text: 'Checkup' }],
          comment: 'Patient notes',
        }),
      );

      // Verify that start and end times have timezone information
      const postData = mockClient.post.mock.calls[0][1] as any;
      expect(postData.start).toMatch(/^2023-05-15T09:00:00(-0[56]:00|Z)$/);
      expect(postData.end).toMatch(/^2023-05-15T09:30:00(-0[56]:00|Z)$/);

      expect(result.providerInfo.externalId).toBe('appt-123');
      expect(result.status).toBe(AppointmentStatus.BOOKED);
    });
  });

  describe('updateAppointment', () => {
    it('should update and return the appointment', async () => {
      // Mock response for getting appointment - first for the initial fetch, then for the fetch after update
      mockClient.get.mockResolvedValueOnce({
        appointmentId: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        practiceId: 'practice-123',
        startDateTime: '2023-05-15T09:00:00',
        endDateTime: '2023-05-15T09:30:00',
        status: 'scheduled',
        appointmentType: 'Follow-up',
        reason: 'Checkup',
        notes: 'Patient notes',
        createdDate: '2023-05-01T10:00:00',
        modifiedDate: '2023-05-01T10:00:00',
      });

      // Mock response for getting the updated appointment
      mockClient.get.mockResolvedValueOnce({
        id: 'appt-123',
        status: 'pending',
        start: '2023-05-15T10:00:00',
        end: '2023-05-15T10:30:00',
        reason: [
          {
            text: 'Rescheduled',
          },
        ],
        participant: [
          {
            actor: {
              reference: 'Patient/patient-123',
            },
          },
          {
            actor: {
              reference: 'Practitioner/provider-123',
            },
          },
          {
            actor: {
              reference: 'Location/location-123',
            },
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/practice',
            valueReference: {
              reference: 'Practice/practice-123',
            },
          },
        ],
        created: '2023-05-01T10:00:00',
        meta: {
          lastUpdated: '2023-05-01T11:00:00',
        },
      });

      // Mock response for updating appointment status
      mockClient.patch.mockResolvedValue({
        appointmentId: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        practiceId: 'practice-123',
        startDateTime: '2023-05-15T10:00:00',
        endDateTime: '2023-05-15T10:30:00',
        status: 'pending',
        appointmentType: 'Follow-up',
        reason: 'Rescheduled',
        notes: 'Patient notes',
        createdDate: '2023-05-01T10:00:00',
        modifiedDate: '2023-05-01T11:00:00',
      });

      // Update data
      const updateData = {
        status: AppointmentStatus.PENDING,
        reason: 'Rescheduled',
      };

      // Call the method
      const result = await appointmentService.updateAppointment('appt-123', updateData);

      // Only check that the status is in the payload
      expect(mockClient.patch).toHaveBeenCalledWith(
        '/Appointment/appt-123',
        expect.objectContaining({
          resourceType: 'Appointment',
          status: 'pending',
          participant: expect.any(Array),
        }),
      );
      expect(result.providerInfo.externalId).toBe('appt-123');
      expect(result.status).toBe(AppointmentStatus.PENDING);
      expect(result.reason).toBe('Rescheduled');
    });
  });

  describe('cancelAppointment', () => {
    it('should cancel an appointment', async () => {
      // Mock response for getting appointment
      mockClient.get.mockResolvedValue({
        appointmentId: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        practiceId: 'practice-123',
        startDateTime: '2023-05-15T09:00:00',
        endDateTime: '2023-05-15T09:30:00',
        status: 'scheduled',
        appointmentType: 'Follow-up',
        reason: 'Checkup',
        notes: 'Patient notes',
        createdDate: '2023-05-01T10:00:00',
        modifiedDate: '2023-05-01T10:00:00',
      });

      // Mock response for updating appointment
      mockClient.patch.mockResolvedValue({
        appointmentId: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        practiceId: 'practice-123',
        startDateTime: '2023-05-15T09:00:00',
        endDateTime: '2023-05-15T09:30:00',
        status: 'cancelled',
        appointmentType: 'Follow-up',
        reason: 'Patient requested cancellation',
        notes: 'Patient notes',
        createdDate: '2023-05-01T10:00:00',
        modifiedDate: '2023-05-01T11:00:00',
      });

      // Call the method
      const result = await appointmentService.cancelAppointment(
        'appt-123',
        'Patient requested cancellation',
      );

      // Expectations - we only expect the status to be included in the API call
      expect(mockClient.patch).toHaveBeenCalledWith(
        '/Appointment/appt-123',
        expect.objectContaining({
          resourceType: 'Appointment',
          status: 'cancelled',
          participant: expect.any(Array),
        }),
      );
      expect(result).toBe(true);
    });

    it('should handle appointment not found', async () => {
      // Setup mock to throw error with 404 message
      mockClient.get.mockRejectedValue(new Error('404 Not Found'));

      // Call the method
      const result = await appointmentService.cancelAppointment('not-found');

      // Expectations
      expect(result).toBe(false);
    });
  });

  describe('getAppointmentByPatientId', () => {
    it('should return appointments for a patient', async () => {
      // Using mockFHIRAppointment defined at the top of the file

      // Setup mock
      mockClient.get.mockResolvedValue({
        resourceType: 'Bundle',
        type: 'searchset',
        total: 1,
        entry: [{ resource: mockFHIRAppointment }],
      });

      // Call the method
      const result = await appointmentService.getAppointmentByPatientId('patient-123');

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        '/Appointment?patient=patient-123&_count=30&_getpagesoffset=0&location.id=118',
      );
      expect(result.items).toHaveLength(1);
      expect(result.items[0].patientId).toBe('patient-123');
    });
  });

  describe('getAppointmentTypes', () => {
    it('should return appointment types from FHIR response', async () => {
      // Mock FHIR response
      const mockResponse = {
        resourceType: 'Bundle',
        total: 2,
        entry: [
          {
            resource: {
              resourceType: 'appointment-type',
              id: '1',
              extension: [
                {
                  url: 'appointment-type',
                  valueString: 'Follow-up',
                },
              ],
            },
          },
          {
            resource: {
              resourceType: 'appointment-type',
              id: '2',
              extension: [
                {
                  url: 'appointment-type',
                  valueString: 'New Patient',
                },
              ],
            },
          },
        ],
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockResponse);

      // Call the method
      const result = await appointmentService.getAppointmentTypes();

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringMatching(/\/appointment-type\?_count=30&_getpagesoffset=0/),
      );
      expect(result.items).toHaveLength(2);
      expect(result.pagination.totalCount).toBe(2);
      expect(result.pagination.hasMore).toBe(false);
      expect(result.items[0].name).toBe('Follow-up');
      expect(result.items[1].name).toBe('New Patient');
    });

    it('should handle empty response', async () => {
      // Mock empty FHIR response
      const mockResponse = {
        resourceType: 'Bundle',
        total: 0,
        entry: [],
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockResponse);

      // Call the method
      const result = await appointmentService.getAppointmentTypes();

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringMatching(/\/appointment-type\?_count=30&_getpagesoffset=0/),
      );
      expect(result.items).toHaveLength(0);
      expect(result.pagination.totalCount).toBe(0);
      expect(result.pagination.hasMore).toBe(false);
    });

    it('should handle pagination parameters', async () => {
      // Mock FHIR response
      const mockResponse = {
        resourceType: 'Bundle',
        total: 100,
        entry: [
          {
            resource: {
              resourceType: 'appointment-type',
              id: '50',
              extension: [
                {
                  url: 'appointment-type',
                  valueString: 'Follow-up',
                },
              ],
            },
          },
        ],
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockResponse);

      // Call the method with pagination
      const pagination = { limit: 1, offset: 50 };
      const result = await appointmentService.getAppointmentTypes(undefined, pagination);

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringMatching(/\/appointment-type\?_count=1&_getpagesoffset=50/),
      );
      expect(result.items).toHaveLength(1);
      expect(result.pagination.totalCount).toBe(100);
      expect(result.pagination.hasMore).toBe(true);
    });
  });

  describe('getAppointmentTypeById', () => {
    it('should return appointment type by ID from FHIR response', async () => {
      // Mock FHIR response
      const mockAppointmentType = {
        resourceType: 'appointment-type',
        id: 'type-123',
        extension: [
          {
            url: 'appointment-type',
            valueString: 'Follow-up',
          },
        ],
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockAppointmentType);

      // Call the method
      const result = await appointmentService.getAppointmentTypeById('type-123');

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith('/appointment-type/type-123');
      expect(result?.providerInfo.externalId).toBe('type-123');
      expect(result?.name).toBe('Follow-up');
    });

    it('should return null when appointment type not found', async () => {
      // Setup mock to throw error with 404 message
      mockClient.get.mockRejectedValue(new Error('404 Not Found'));

      // Call the method
      const result = await appointmentService.getAppointmentTypeById('not-found');

      // Expectations
      expect(result).toBeNull();
    });
  });

  describe('getAvailableSlots', () => {
    it('should return available slots', async () => {
      // Mock FHIR Slot resources
      const mockSlot1: FHIRSlot = {
        resourceType: 'Slot',
        id: 'slot-123',
        schedule: {
          reference: 'Schedule/schedule-123',
          display: 'Dr. Smith Schedule',
        },
        status: 'free',
        start: '2023-05-15T09:00:00',
        end: '2023-05-15T09:30:00',
        serviceType: [
          {
            coding: [
              {
                system: 'http://terminology.hl7.org/CodeSystem/service-type',
                code: 'type-123',
                display: 'Follow-up',
              },
            ],
            text: 'Follow-up',
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/location',
            valueReference: {
              reference: 'Location/location-123',
              display: 'Main Clinic',
            },
          },
        ],
      };

      const mockSlot2: FHIRSlot = {
        resourceType: 'Slot',
        id: 'slot-456',
        schedule: {
          reference: 'Schedule/schedule-123',
          display: 'Dr. Smith Schedule',
        },
        status: 'free',
        start: '2023-05-15T10:00:00',
        end: '2023-05-15T10:30:00',
        serviceType: [
          {
            coding: [
              {
                system: 'http://terminology.hl7.org/CodeSystem/service-type',
                code: 'type-123',
                display: 'Follow-up',
              },
            ],
            text: 'Follow-up',
          },
        ],
        extension: [
          {
            url: 'https://api.pm.nextech.com/api/structuredefinition/location',
            valueReference: {
              reference: 'Location/location-123',
              display: 'Main Clinic',
            },
          },
        ],
      };

      // Mock FHIR Bundle response
      const mockResponse = {
        resourceType: 'Bundle',
        type: 'searchset',
        total: 2,
        entry: [{ resource: mockSlot1 }, { resource: mockSlot2 }],
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockResponse);

      // Call the method
      const filters = {
        appointmentTypeId: 'type-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        startDate: '2023-05-15',
        endDate: '2023-05-15',
      };
      const result = await appointmentService.getAvailableSlots(filters);

      // Expectations - verify the correct FHIR URL is used
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('/slot?'));

      // Verify the URL contains the correct parameters
      const url = mockClient.get.mock.calls[0][0];
      expect(url).toContain('appointment-type=appointment-type%2Ftype-123');
      expect(url).toContain('start=ge2023-05-15');
      expect(url).toContain('start=le2023-05-15');

      // Verify the result
      expect(result.items).toHaveLength(2);
      expect(result.items[0].startDateTime).toBe('2023-05-15T09:00:00');
      expect(result.items[0].endDateTime).toBe('2023-05-15T09:30:00');
    });
  });

  describe('getAppointmentPurposes', () => {
    it('should return appointment purposes', async () => {
      // Mock response
      const mockResponse = {
        items: [
          {
            appointmentPurposeId: 'purpose-123',
            name: 'Checkup',
            description: 'Regular checkup',
            active: true,
          },
          {
            appointmentPurposeId: 'purpose-456',
            name: 'Surgery',
            description: 'Surgical procedure',
            active: true,
          },
        ],
        totalCount: 2,
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockResponse);

      // Call the method
      const result = await appointmentService.getAppointmentPurposes();

      // Expectations - don't be too strict about params
      expect(mockClient.get).toHaveBeenCalledWith('/appointment-purposes', expect.anything());
      expect(result.items).toHaveLength(2);
      expect(result.items[0].providerInfo.externalId).toBe('purpose-123');
      expect(result.items[0].name).toBe('Checkup');
    });
  });

  describe('getAppointmentPurposeById', () => {
    it('should return appointment purpose by ID', async () => {
      // Mock response
      const mockPurpose = {
        appointmentPurposeId: 'purpose-123',
        name: 'Checkup',
        description: 'Regular checkup',
        active: true,
      };

      // Setup mock
      mockClient.get.mockResolvedValue(mockPurpose);

      // Call the method
      const result = await appointmentService.getAppointmentPurposeById('purpose-123');

      // Expectations
      expect(mockClient.get).toHaveBeenCalledWith('/appointment-purposes/purpose-123');
      expect(result?.providerInfo.externalId).toBe('purpose-123');
      expect(result?.name).toBe('Checkup');
    });

    it('should return null when appointment purpose not found', async () => {
      // Setup mock to throw error with 404 message
      mockClient.get.mockRejectedValue(new Error('404 Not Found'));

      // Call the method
      const result = await appointmentService.getAppointmentPurposeById('not-found');

      // Expectations
      expect(result).toBeNull();
    });
  });
});
