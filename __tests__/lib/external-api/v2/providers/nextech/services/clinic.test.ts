import { NextechApiClient } from '../../../../../../../lib/external-api/v2/providers/nextech/client';
import { NextechClinicService } from '../../../../../../../lib/external-api/v2/providers/nextech/services/clinic';
import { NextechClinicResponse } from '../../../../../../../lib/external-api/v2/providers/nextech/services/mappers';
import { PaginatedResult } from '../../../../../../../lib/external-api/v2/models/pagination';
import { Clinic } from '../../../../../../../lib/external-api/v2/models/types';
import logger from '../../../../../../../lib/external-api/v2/utils/logger';

// Mock the NextechApiClient
jest.mock('../../../../../../../lib/external-api/v2/providers/nextech/client');

// Mock the logger
jest.mock('../../../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechClinicService', () => {
  let mockClient: jest.Mocked<NextechApiClient>;
  let clinicService: NextechClinicService;

  beforeEach(() => {
    // Clear and reset mocks
    jest.clearAllMocks();

    // Set up the mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;
    mockClient.get = jest.fn();

    // Create the service with the mock client
    clinicService = new NextechClinicService(mockClient);
  });

  const mockClinicResponse: NextechClinicResponse = {
    practiceId: 'practice-123',
    name: 'Test Clinic',
    website: 'https://testclinic.com',
    email: '<EMAIL>',
    address1: '123 Main St',
    address2: 'Suite 100',
    city: 'Test City',
    state: 'TS',
    postalCode: '12345',
    country: 'US',
    phoneNumber: '************',
    faxNumber: '************',
    active: true,
  };

  describe('getClinics', () => {
    it('should return mapped clinics from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        items: [mockClinicResponse],
        totalCount: 1,
      });

      // Act
      const result = await clinicService.getClinics();

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/v1/practices', {
        params: {
          limit: '10',
          offset: '0',
        },
      });
      expect(result.items).toHaveLength(1);
      expect(result.items[0].providerInfo.externalId).toBe('practice-123');
      expect(result.pagination.totalCount).toBe(1);
    });

    it('should apply filters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        items: [mockClinicResponse],
        totalCount: 1,
      });

      const filters = {
        name: 'Test',
        phone: '555-123',
        other: 'Ignore this',
      };

      // Act
      await clinicService.getClinics(filters);

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/v1/practices', {
        params: {
          name: 'Test',
          phone: '555-123',
          limit: '10',
          offset: '0',
        },
      });
    });

    it('should filter out inactive clinics', async () => {
      // Arrange
      const inactiveClinic = { ...mockClinicResponse, active: false };
      mockClient.get.mockResolvedValue({
        items: [mockClinicResponse, inactiveClinic],
        totalCount: 2,
      });

      // Act
      const result = await clinicService.getClinics();

      // Assert
      expect(result.items).toHaveLength(1);
    });

    it('should return empty paginated result on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await clinicService.getClinics();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(result.pagination.hasMore).toBe(false);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getClinicById', () => {
    it('should return mapped clinic from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockClinicResponse);

      // Act
      const result = await clinicService.getClinicById('practice-123');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/v1/practices/practice-123');
      expect(result).not.toBeNull();
      expect(result?.providerInfo.externalId).toBe('practice-123');
    });

    it('should return null for inactive clinics', async () => {
      // Arrange
      const inactiveClinic = { ...mockClinicResponse, active: false };
      mockClient.get.mockResolvedValue(inactiveClinic);

      // Act
      const result = await clinicService.getClinicById('practice-123');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await clinicService.getClinicById('practice-123');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getClinicByPhone', () => {
    it('should call getClinics with phone filter', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        items: [mockClinicResponse],
        totalCount: 1,
      });

      // Act
      const result = await clinicService.getClinicByPhone('************');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/v1/practices', {
        params: {
          phone: '************',
          limit: '10',
          offset: '0',
        },
      });
      expect(result).not.toBeNull();
      expect(result?.phoneNumber).toBe('************');
    });

    it('should return null when no clinics match', async () => {
      // Arrange
      mockClient.get.mockResolvedValue({
        items: [],
        totalCount: 0,
      });

      // Act
      const result = await clinicService.getClinicByPhone('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await clinicService.getClinicByPhone('************');

      // Assert
      expect(result).toBeNull();
    });
  });
});
