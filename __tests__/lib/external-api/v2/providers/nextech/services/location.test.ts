import { NextechApiClient } from '../../../../../../../lib/external-api/v2/providers/nextech/client';
import { NextechLocationService } from '../../../../../../../lib/external-api/v2/providers/nextech/services/location';
import { NextechLocationResponse } from '../../../../../../../lib/external-api/v2/providers/nextech/services/mappers';
import { PaginatedResult } from '../../../../../../../lib/external-api/v2/models/pagination';
import { Location } from '../../../../../../../lib/external-api/v2/models/types';
import logger from '../../../../../../../lib/external-api/v2/utils/logger';

// Mock the NextechApiClient
jest.mock('../../../../../../../lib/external-api/v2/providers/nextech/client');

// Mock the logger
jest.mock('../../../../../../../lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechLocationService', () => {
  let mockClient: jest.Mocked<NextechApiClient>;
  let locationService: NextechLocationService;

  beforeEach(() => {
    // Clear and reset mocks
    jest.clearAllMocks();

    // Set up the mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;
    mockClient.get = jest.fn();

    // Create the service with the mock client
    locationService = new NextechLocationService(mockClient);
  });

  // Define a mock location response matching the real structure
  const mockLocationResponse = {
    resourceType: 'Location',
    id: 'location-123',
    status: 'active',
    name: 'Main Office',
    telecom: [
      {
        system: 'phone',
        value: '************',
        use: 'work',
      },
    ],
    address: {
      line: ['123 Main St', 'Suite 100'],
      city: 'Test City',
      state: 'TS',
      postalCode: '12345',
      country: 'US',
    },
    managingOrganization: {
      reference: 'Practice/practice-123',
      display: 'Test Practice',
    },
  };

  // Create mock bundle that would be returned by the API
  const mockLocationBundle = {
    resourceType: 'Bundle',
    type: 'searchset',
    total: 1,
    entry: [
      {
        resource: mockLocationResponse,
      },
    ],
  };

  describe('getLocations', () => {
    it('should return mapped locations from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockLocationBundle);

      // Act
      const result = await locationService.getLocations();

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith(
        '/location?_count=10&_getpagesoffset=0&phone=************&status=active',
      );
      expect(result.items).toHaveLength(1);
      expect(result.items[0].providerInfo.externalId).toBe('location-123');
    });

    it('should apply filters to the API request', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockLocationBundle);

      const filters = {
        name: 'Test',
        phone: '************',
        clinicId: 'practice-123',
        other: 'Ignore this',
      };

      // Act
      await locationService.getLocations(filters);

      // Assert
      // Note: Using expect.stringContaining because URL encoding makes exact matches difficult
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringContaining('/location?_count=10&_getpagesoffset=0&name=Test'),
      );
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('phone=************'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('status=active'));
    });

    it('should filter out inactive locations', async () => {
      // Arrange
      const inactiveLocationResponse = {
        ...mockLocationResponse,
        status: 'inactive',
      };
      const mockBundle = {
        resourceType: 'Bundle',
        type: 'searchset',
        total: 2,
        entry: [{ resource: mockLocationResponse }, { resource: inactiveLocationResponse }],
      };
      mockClient.get.mockResolvedValue(mockBundle);

      // Act
      const result = await locationService.getLocations();

      // Assert
      expect(result.items).toHaveLength(1);
    });

    it('should return empty array on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await locationService.getLocations();

      // Assert
      expect(result.items).toEqual([]);
      expect(result.pagination.totalCount).toBe(0);
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('getLocationById', () => {
    it('should return mapped location from API response', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockLocationResponse);

      // Act
      const result = await locationService.getLocationById('location-123');

      // Assert
      expect(mockClient.get).toHaveBeenCalledWith('/location/location-123');
      expect(result).not.toBeNull();
      expect(result?.providerInfo.externalId).toBe('location-123');
    });

    it('should return null for inactive locations', async () => {
      // Arrange
      const inactiveLocationResponse = {
        ...mockLocationResponse,
        status: 'inactive',
      };
      mockClient.get.mockResolvedValue(inactiveLocationResponse);

      // Act
      const result = await locationService.getLocationById('location-123');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await locationService.getLocationById('location-123');

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('getLocationByPhone', () => {
    it('should call getLocations with phone filter', async () => {
      // Arrange
      mockClient.get.mockResolvedValue(mockLocationBundle);

      // Act
      const result = await locationService.getLocationByPhone('331-777-230');

      // Assert
      // Note: Using expect.stringContaining because URL encoding makes exact matches difficult
      expect(mockClient.get).toHaveBeenCalledWith(
        expect.stringContaining('/location?_count=10&_getpagesoffset=0'),
      );
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('phone=************'));
      expect(mockClient.get).toHaveBeenCalledWith(expect.stringContaining('status=active'));
      expect(result).not.toBeNull();
      expect(result?.phoneNumber).toBe('************');
    });

    it('should return null when no locations match', async () => {
      // Arrange
      const emptyBundle = {
        resourceType: 'Bundle',
        type: 'searchset',
        total: 0,
        entry: [],
      };
      mockClient.get.mockResolvedValue(emptyBundle);

      // Act
      const result = await locationService.getLocationByPhone('not-found');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null on error', async () => {
      // Arrange
      mockClient.get.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await locationService.getLocationByPhone('************');

      // Assert
      expect(result).toBeNull();
    });
  });
});
