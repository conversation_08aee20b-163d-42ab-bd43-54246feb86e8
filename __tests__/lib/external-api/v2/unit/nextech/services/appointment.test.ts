import { NextechApiClient } from '@/lib/external-api/v2/providers/nextech/client';
import { NextechAppointmentService } from '@/lib/external-api/v2/providers/nextech/services/appointment';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';
import { UpdateAppointmentDto } from '@/lib/external-api/v2/models/dto';

// Mock the NextechApiClient
jest.mock('@/lib/external-api/v2/providers/nextech/client');

describe('NextechAppointmentService', () => {
  let appointmentService: NextechAppointmentService;
  let mockClient: jest.Mocked<NextechApiClient>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create a mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;

    // Create the appointment service with the mock client
    appointmentService = new NextechAppointmentService(mockClient);
  });

  describe('updateAppointment', () => {
    it('should only update the appointment status according to Nextech API limitations', async () => {
      // Mock the getAppointmentById method to return a valid appointment
      const mockAppointment = {
        id: '123',
        patientId: '456',
        providerId: '789',
        locationId: '101',
        clinicId: '202',
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T11:00:00Z',
        status: AppointmentStatus.BOOKED,
        createdAt: '2023-01-01T09:00:00Z',
        updatedAt: '2023-01-01T09:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: '123',
        },
      };

      jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(mockAppointment);

      // Mock the client.patch method
      mockClient.patch.mockResolvedValue({
        resourceType: 'Appointment',
        id: '123',
        status: 'arrived',
        start: '2023-01-01T10:00:00Z',
        end: '2023-01-01T11:00:00Z',
        participant: [
          {
            actor: {
              reference: 'Patient/456',
            },
            status: 'accepted',
          },
        ],
      });

      // Create update data with multiple fields
      const updateData: UpdateAppointmentDto = {
        patientId: 'new-patient-id', // This should be ignored
        practitionerId: 'new-practitioner-id', // This should be ignored
        locationId: 'new-location-id', // This should be ignored
        startTime: '2023-01-01T12:00:00Z', // This should be ignored
        endTime: '2023-01-01T13:00:00Z', // This should be ignored
        status: AppointmentStatus.ARRIVED, // Only this should be used
        notes: 'New notes', // This should be ignored
      };

      // Call the method
      await appointmentService.updateAppointment('123', updateData);

      // Verify that the client.patch was called with the correct parameters
      expect(mockClient.patch).toHaveBeenCalledWith('/Appointment/123', {
        resourceType: 'Appointment',
        status: 'arrived', // Mapped from AppointmentStatus.ARRIVED
        participant: [
          {
            status: 'accepted',
          },
        ],
        extension: [
          {
            url: 'modifiedBy',
            valueIdentifier: {
              id: 'modifiedBy',
              value: 'Practice+ Partner',
            },
          },
        ],
      });

      // Verify that only one call was made
      expect(mockClient.patch).toHaveBeenCalledTimes(1);
    });

    it('should throw an error if status is not provided', async () => {
      // Mock the getAppointmentById method to return a valid appointment
      const mockAppointment = {
        id: '123',
        patientId: '456',
        providerId: '789',
        locationId: '101',
        clinicId: '202',
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T11:00:00Z',
        status: AppointmentStatus.BOOKED,
        createdAt: '2023-01-01T09:00:00Z',
        updatedAt: '2023-01-01T09:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: '123',
        },
      };

      jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(mockAppointment);

      // Create update data without status
      const updateData: UpdateAppointmentDto = {
        notes: 'New notes',
      };

      // Call the method and expect it to throw
      await expect(appointmentService.updateAppointment('123', updateData)).rejects.toThrow(
        'Appointment status is required for updates',
      );
    });
  });

  describe('cancelAppointment', () => {
    it('should update the appointment status to CANCELLED', async () => {
      // Mock the updateAppointment method
      jest.spyOn(appointmentService, 'updateAppointment').mockResolvedValue({} as any);

      // Call the method
      const result = await appointmentService.cancelAppointment('123', 'Cancellation reason');

      // Verify that updateAppointment was called with the correct parameters
      expect(appointmentService.updateAppointment).toHaveBeenCalledWith('123', {
        status: AppointmentStatus.CANCELLED,
      });

      // Verify that the result is true
      expect(result).toBe(true);
    });

    it('should return false if updateAppointment throws an error', async () => {
      // Mock the updateAppointment method to throw an error
      jest
        .spyOn(appointmentService, 'updateAppointment')
        .mockRejectedValue(new Error('Test error'));

      // Call the method
      const result = await appointmentService.cancelAppointment('123');

      // Verify that the result is false
      expect(result).toBe(false);
    });
  });
});
