import { NextechApiClient } from '@/lib/external-api/v2/providers/nextech/client';
import { NextechAppointmentService } from '@/lib/external-api/v2/providers/nextech/services/appointment';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';
import logger from '@/lib/external-api/v2/utils/logger';

// Mock the NextechApiClient
jest.mock('@/lib/external-api/v2/providers/nextech/client');

// Mock the logger
jest.mock('@/lib/external-api/v2/utils/logger', () => ({
  error: jest.fn(),
  warn: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
}));

describe('NextechAppointmentService - getAppointmentsForRescheduling', () => {
  let appointmentService: NextechAppointmentService;
  let mockClient: jest.Mocked<NextechApiClient>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create a mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;

    // Create the appointment service with the mock client
    appointmentService = new NextechAppointmentService(mockClient);

    // Mock the formatDateToYYYYMMDD method
    jest
      .spyOn(appointmentService as any, 'formatDateToYYYYMMDD')
      .mockImplementation((...args: unknown[]) => {
        const dateStr = args[0] as string;
        const date = new Date(dateStr);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      });
  });

  // Mock FHIR appointment data
  const mockFutureAppointments = {
    resourceType: 'Bundle',
    type: 'searchset',
    total: 2,
    entry: [
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-123',
          status: 'booked',
          start: '2099-05-15T10:00:00Z',
          end: '2099-05-15T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-124',
          status: 'cancelled',
          start: '2099-05-16T10:00:00Z',
          end: '2099-05-16T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
    ],
  };

  const mockPastAppointments = {
    resourceType: 'Bundle',
    type: 'searchset',
    total: 2,
    entry: [
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-125',
          status: 'noshow',
          start: '2023-05-10T10:00:00Z',
          end: '2023-05-10T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
      {
        resource: {
          resourceType: 'Appointment',
          id: 'appt-126',
          status: 'noshow',
          start: '2023-05-05T10:00:00Z',
          end: '2023-05-05T11:00:00Z',
          participant: [
            {
              actor: {
                reference: 'Patient/patient-123',
                display: 'John Doe',
              },
              status: 'accepted',
            },
          ],
        },
      },
    ],
  };

  it('should return future non-cancelled appointments when they exist', async () => {
    // Mock getAppointments to return future appointments
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: mockFutureAppointments.entry.map(entry => ({
        id: entry.resource.id,
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        clinicId: 'clinic-123',
        startTime: entry.resource.start,
        endTime: entry.resource.end,
        status:
          entry.resource.status === 'booked'
            ? AppointmentStatus.BOOKED
            : AppointmentStatus.CANCELLED,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: entry.resource.id,
        },
      })),
      pagination: {
        totalCount: 2,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', false);

    // Verify getAppointments was called with correct parameters
    expect(appointmentService.getAppointments).toHaveBeenCalledWith(
      {
        patientId: 'patient-123',
        startDate: expect.any(String),
      },
      { limit: 50, offset: 0 },
    );

    // Verify result contains only non-cancelled appointments
    expect(result.items.length).toBe(1);
    expect(result.items[0].id).toBe('appt-123');
    expect(result.items[0].status).toBe(AppointmentStatus.BOOKED);
  });

  it('should return empty array when no future appointments exist and isForRescheduling is false', async () => {
    // Mock getAppointments to return empty array for future appointments
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: [],
      pagination: {
        totalCount: 0,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Call the method with isForRescheduling = false
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', false);

    // Verify getAppointments was called with correct parameters
    expect(appointmentService.getAppointments).toHaveBeenCalledWith(
      {
        patientId: 'patient-123',
        startDate: expect.any(String),
      },
      { limit: 50, offset: 0 },
    );

    // Verify result is empty
    expect(result.items.length).toBe(0);
    // Verify second call to getAppointments was not made
    expect(appointmentService.getAppointments).toHaveBeenCalledTimes(1);
  });

  it('should return most recent no-show appointment when no future appointments exist and isForRescheduling is true', async () => {
    // Mock getAppointments to return empty array for future appointments
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: [],
      pagination: {
        totalCount: 0,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Mock getAppointments to return past appointments with no-shows
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: mockPastAppointments.entry.map(entry => ({
        id: entry.resource.id,
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        clinicId: 'clinic-123',
        startTime: entry.resource.start,
        endTime: entry.resource.end,
        status: AppointmentStatus.NOSHOW,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: entry.resource.id,
        },
      })),
      pagination: {
        totalCount: 2,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Call the method with isForRescheduling = true
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', true);

    // Verify getAppointments was called with correct parameters for both calls
    expect(appointmentService.getAppointments).toHaveBeenCalledTimes(2);
    expect(appointmentService.getAppointments).toHaveBeenNthCalledWith(
      1,
      {
        patientId: 'patient-123',
        startDate: expect.any(String),
      },
      { limit: 50, offset: 0 },
    );
    expect(appointmentService.getAppointments).toHaveBeenNthCalledWith(2, {
      patientId: 'patient-123',
      startDate: expect.any(String),
      endDate: expect.any(String),
    });

    // Verify result contains only the most recent no-show appointment
    expect(result.items.length).toBe(1);
    expect(result.items[0].id).toBe('appt-125'); // The most recent no-show
    expect(result.items[0].status).toBe(AppointmentStatus.NOSHOW);
  });

  it('should return empty array when no future appointments and no recent no-shows exist', async () => {
    // Mock getAppointments to return empty array for future appointments
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: [],
      pagination: {
        totalCount: 0,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Mock getAppointments to return empty array for past appointments
    jest.spyOn(appointmentService, 'getAppointments').mockResolvedValueOnce({
      items: [],
      pagination: {
        totalCount: 0,
        limit: 30,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    // Call the method with isForRescheduling = true
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', true);

    // Verify getAppointments was called with correct parameters for both calls
    expect(appointmentService.getAppointments).toHaveBeenCalledTimes(2);

    // Verify result is empty
    expect(result.items.length).toBe(0);
  });

  it('should handle errors and return empty array', async () => {
    // Mock getAppointments to throw an error
    jest.spyOn(appointmentService, 'getAppointments').mockRejectedValueOnce(new Error('API Error'));

    // Call the method
    const result = await appointmentService.getAppointmentsForRescheduling('patient-123', false);

    // Verify error was logged
    expect(logger.error).toHaveBeenCalled();

    // Verify result is empty
    expect(result.items.length).toBe(0);
    expect(result.pagination.totalCount).toBe(0);
  });
});
