import { NextechApiClient } from '@/lib/external-api/v2/providers/nextech/client';
import { NextechAppointmentService } from '@/lib/external-api/v2/providers/nextech/services/appointment';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';
import { UpdateAppointmentDto } from '@/lib/external-api/v2/models/dto';
import { NextechAppointmentTypes } from '@/models/AppointmentTypes';

// Mock the NextechApiClient
jest.mock('@/lib/external-api/v2/providers/nextech/client');

describe('NextechAppointmentService - changeAppointment', () => {
  let appointmentService: NextechAppointmentService;
  let mockClient: jest.Mocked<NextechApiClient>;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create a mock client
    mockClient = new NextechApiClient('') as jest.Mocked<NextechApiClient>;

    // Create the appointment service with the mock client
    appointmentService = new NextechAppointmentService(mockClient);
  });

  it('should create a new appointment with updated fields and cancel the original', async () => {
    // Mock the getAppointmentById method to return a valid appointment
    const originalAppointment = {
      id: '123',
      patientId: '456',
      providerId: '789',
      locationId: '101',
      clinicId: '202',
      startTime: '2023-01-01T10:00:00Z',
      endTime: '2023-01-01T11:00:00Z',
      status: AppointmentStatus.BOOKED,
      type: NextechAppointmentTypes.FOLLOW_UP,
      reason: 'Original reason',
      notes: 'Original notes',
      createdAt: '2023-01-01T09:00:00Z',
      updatedAt: '2023-01-01T09:00:00Z',
      providerInfo: {
        provider: 'nextech',
        externalId: '123',
      },
    };

    jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(originalAppointment);

    // Mock the createAppointment method
    const newAppointment = {
      ...originalAppointment,
      id: '456',
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
      reason: 'Updated reason',
      createdAt: '2023-01-01T15:00:00Z',
      updatedAt: '2023-01-01T15:00:00Z',
    };

    jest.spyOn(appointmentService, 'createAppointment').mockResolvedValue(newAppointment);

    // Mock the cancelAppointment method
    jest.spyOn(appointmentService, 'cancelAppointment').mockResolvedValue(true);

    // Create update data
    const updateData: UpdateAppointmentDto = {
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
      reason: 'Updated reason',
    };

    // Call the method
    const result = await appointmentService.changeAppointment('123', updateData);

    // Verify that getAppointmentById was called with the correct ID
    expect(appointmentService.getAppointmentById).toHaveBeenCalledWith('123');

    // Verify that createAppointment was called with the correct data
    expect(appointmentService.createAppointment).toHaveBeenCalledWith({
      patientId: originalAppointment.patientId,
      practitionerId: originalAppointment.providerId,
      locationId: originalAppointment.locationId,
      clinicId: originalAppointment.clinicId,
      startTime: updateData.startTime,
      endTime: updateData.endTime,
      type: originalAppointment.type,
      reason: updateData.reason,
      notes: originalAppointment.notes,
    });

    // Verify that cancelAppointment was called with the correct ID
    expect(appointmentService.cancelAppointment).toHaveBeenCalledWith(
      '123',
      'Replaced by updated appointment',
    );

    // Verify that the result is the new appointment
    expect(result).toEqual(newAppointment);
  });

  it('should throw an error if the original appointment is not found', async () => {
    // Mock the getAppointmentById method to return null
    jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(null);

    // Create update data
    const updateData: UpdateAppointmentDto = {
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
    };

    // Call the method and expect it to throw
    await expect(appointmentService.changeAppointment('123', updateData)).rejects.toThrow(
      'Appointment with ID 123 not found',
    );
  });

  it('should still return the new appointment even if cancellation fails', async () => {
    // Mock the getAppointmentById method to return a valid appointment
    const originalAppointment = {
      id: '123',
      patientId: '456',
      providerId: '789',
      locationId: '101',
      clinicId: '202',
      startTime: '2023-01-01T10:00:00Z',
      endTime: '2023-01-01T11:00:00Z',
      status: AppointmentStatus.BOOKED,
      type: NextechAppointmentTypes.FOLLOW_UP,
      reason: 'Original reason',
      notes: 'Original notes',
      createdAt: '2023-01-01T09:00:00Z',
      updatedAt: '2023-01-01T09:00:00Z',
      providerInfo: {
        provider: 'nextech',
        externalId: '123',
      },
    };

    jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(originalAppointment);

    // Mock the createAppointment method
    const newAppointment = {
      ...originalAppointment,
      id: '456',
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
      createdAt: '2023-01-01T15:00:00Z',
      updatedAt: '2023-01-01T15:00:00Z',
    };

    jest.spyOn(appointmentService, 'createAppointment').mockResolvedValue(newAppointment);

    // Mock the cancelAppointment method to fail
    jest.spyOn(appointmentService, 'cancelAppointment').mockResolvedValue(false);

    // Create update data
    const updateData: UpdateAppointmentDto = {
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
    };

    // Call the method
    const result = await appointmentService.changeAppointment('123', updateData);

    // Verify that the result is still the new appointment
    expect(result).toEqual(newAppointment);
  });

  it('should throw an error if createAppointment fails', async () => {
    // Mock the getAppointmentById method to return a valid appointment
    const originalAppointment = {
      id: '123',
      patientId: '456',
      providerId: '789',
      locationId: '101',
      clinicId: '202',
      startTime: '2023-01-01T10:00:00Z',
      endTime: '2023-01-01T11:00:00Z',
      status: AppointmentStatus.BOOKED,
      type: NextechAppointmentTypes.FOLLOW_UP,
      reason: 'Original reason',
      notes: 'Original notes',
      createdAt: '2023-01-01T09:00:00Z',
      updatedAt: '2023-01-01T09:00:00Z',
      providerInfo: {
        provider: 'nextech',
        externalId: '123',
      },
    };

    jest.spyOn(appointmentService, 'getAppointmentById').mockResolvedValue(originalAppointment);

    // Mock the createAppointment method to throw an error
    const error = new Error('Failed to create appointment');
    jest.spyOn(appointmentService, 'createAppointment').mockRejectedValue(error);

    // Create update data
    const updateData: UpdateAppointmentDto = {
      startTime: '2023-01-02T10:00:00Z',
      endTime: '2023-01-02T11:00:00Z',
    };

    // Call the method and expect it to throw
    await expect(appointmentService.changeAppointment('123', updateData)).rejects.toThrow(
      'Failed to create appointment',
    );
  });
});
