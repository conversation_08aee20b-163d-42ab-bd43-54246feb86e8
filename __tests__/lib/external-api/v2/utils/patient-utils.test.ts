import { Patient } from '@/lib/external-api/v2/models/types';
import { findPatientByCriteria } from '@/lib/external-api/v2/utils/patient-utils';

// Mock Firebase Admin to prevent initialization errors
jest.mock('@/utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
    update: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock the patient factory to prevent Firebase dependencies
jest.mock('@/lib/factories/patient-factory', () => ({
  patientFactory: {
    getPatientCoordinatorService: jest.fn().mockReturnValue({
      storePatientReference: jest.fn(),
    }),
  },
}));

describe('patient-utils', () => {
  describe('findPatientByCriteria', () => {
    // Mock patient data
    const mockPatient: Patient = {
      id: 'patient_123',
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: '1980-01-15',
      email: '<EMAIL>',
      phoneNumber: '**********',
      gender: 'male',
      address: {
        line1: '123 Main St',
        city: 'Chicago',
        state: 'IL',
        postalCode: '60601',
        country: 'US',
      },
      providerInfo: {
        provider: 'nextech',
        externalId: 'patient_123',
      },
    };

    // Mock provider
    const mockGetPatients = jest.fn();
    const mockProvider = {
      getPatientService: jest.fn().mockReturnValue({
        getPatients: mockGetPatients,
      }),
    };

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should find patient by phone number and date of birth', async () => {
      // Mock the getPatients to return a list with one patient when searched by phone and DOB
      mockGetPatients.mockResolvedValueOnce([mockPatient]);

      const result = await findPatientByCriteria(mockProvider, {
        phoneNumber: '**********',
        dateOfBirth: '1980-01-15',
      });

      expect(result).toEqual(mockPatient);
      expect(mockGetPatients).toHaveBeenCalledTimes(1);
      expect(mockGetPatients).toHaveBeenCalledWith({
        phoneNumber: '**********',
        dateOfBirth: '1980-01-15',
      });
    });

    it('should find patient by name and date of birth when phone+DOB search fails', async () => {
      // Mock the getPatients to return empty list for phone+DOB search
      mockGetPatients.mockResolvedValueOnce(null);
      // Mock the getPatients to return a list with one patient when searched by name and DOB
      mockGetPatients.mockResolvedValueOnce([mockPatient]);

      const result = await findPatientByCriteria(mockProvider, {
        phoneNumber: '**********', // Different from the patient's phone
        dateOfBirth: '1980-01-15',
        firstName: 'John',
        lastName: 'Doe',
      });

      expect(result).toEqual(mockPatient);
      expect(mockGetPatients).toHaveBeenCalledTimes(2);
      expect(mockGetPatients).toHaveBeenNthCalledWith(1, {
        phoneNumber: '**********',
        dateOfBirth: '1980-01-15',
      });
      expect(mockGetPatients).toHaveBeenNthCalledWith(2, {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
      });
    });

    it('should find patient by name and date of birth when phone is not provided', async () => {
      // Mock the getPatients to return a list with one patient when searched by name and DOB
      mockGetPatients.mockResolvedValueOnce([mockPatient]);

      const result = await findPatientByCriteria(mockProvider, {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
      });

      expect(result).toEqual(mockPatient);
      expect(mockGetPatients).toHaveBeenCalledTimes(1);
      expect(mockGetPatients).toHaveBeenCalledWith({
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
      });
    });

    it('should return null when patient is not found by either method', async () => {
      // Mock the getPatients to return empty lists for both searches
      mockGetPatients.mockResolvedValueOnce(null);
      mockGetPatients.mockResolvedValueOnce(null);

      const result = await findPatientByCriteria(mockProvider, {
        phoneNumber: '**********',
        dateOfBirth: '1990-05-20',
        firstName: 'Jane',
        lastName: 'Smith',
      });

      expect(result).toBeNull();
      expect(mockGetPatients).toHaveBeenCalledTimes(2);
    });

    it('should handle paginated response format', async () => {
      // Mock the getPatients to return a paginated result
      mockGetPatients.mockResolvedValueOnce({
        items: [mockPatient],
        total: 1,
        page: 1,
        limit: 10,
      });

      const result = await findPatientByCriteria(mockProvider, {
        phoneNumber: '**********',
        dateOfBirth: '1980-01-15',
      });

      expect(result).toEqual(mockPatient);
    });

    it('should handle data property response format', async () => {
      // Mock the getPatients to return a response with data property
      mockGetPatients.mockResolvedValueOnce({
        data: [mockPatient],
        success: true,
      });

      const result = await findPatientByCriteria(mockProvider, {
        phoneNumber: '**********',
        dateOfBirth: '1980-01-15',
      });

      expect(result).toEqual(mockPatient);
    });

    it('should return null when criteria is insufficient', async () => {
      const result = await findPatientByCriteria(mockProvider, {
        // No phone number and date of birth combination, incomplete name/DOB data
        firstName: 'John',
      });

      expect(result).toBeNull();
      expect(mockGetPatients).not.toHaveBeenCalled();
    });

    it('should handle errors from the patient service', async () => {
      const testError = new Error('API error');
      mockGetPatients.mockRejectedValueOnce(testError);

      await expect(
        findPatientByCriteria(mockProvider, {
          phoneNumber: '**********',
          dateOfBirth: '1980-01-15',
        }),
      ).rejects.toThrow(testError);
    });
  });
});
