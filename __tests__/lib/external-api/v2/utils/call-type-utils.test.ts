// Mock Firebase before importing anything else
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    updateCallSession: jest.fn(),
    getCallSession: jest.fn(),
  },
  callsService: {
    updateCall: jest.fn(),
    getCall: jest.fn(),
  },
}));

import { CallType } from '@/models/CallTypes';
import {
  parseDurationToSeconds,
  shouldDisplayCallInPortal,
  shouldMarkAsDisconnected,
} from '@/utils/call-duration-utils';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';

describe('Call Type Utils', () => {
  describe('parseDurationToSeconds', () => {
    it('should parse HH:MM:SS format correctly', () => {
      expect(parseDurationToSeconds('1:30:45')).toBe(5445); // 1*3600 + 30*60 + 45
      expect(parseDurationToSeconds('0:05:30')).toBe(330); // 5*60 + 30
      expect(parseDurationToSeconds('0:00:03')).toBe(3); // 3 seconds
    });

    it('should parse MM:SS format correctly', () => {
      expect(parseDurationToSeconds('5:30')).toBe(330); // 5*60 + 30
      expect(parseDurationToSeconds('0:45')).toBe(45); // 45 seconds
      expect(parseDurationToSeconds('0:03')).toBe(3); // 3 seconds
    });

    it('should parse minute formats correctly', () => {
      expect(parseDurationToSeconds('5 min')).toBe(300); // 5*60
      expect(parseDurationToSeconds('2.5 min')).toBe(150); // 2.5*60
      expect(parseDurationToSeconds('1 mins')).toBe(60); // 1*60
      expect(parseDurationToSeconds('0.5 min')).toBe(30); // 0.5*60
    });

    it('should parse second formats correctly', () => {
      expect(parseDurationToSeconds('45 sec')).toBe(45);
      expect(parseDurationToSeconds('30 secs')).toBe(30);
      expect(parseDurationToSeconds('3.5 sec')).toBe(4); // Rounded to 4
    });

    it('should parse plain numbers as seconds', () => {
      expect(parseDurationToSeconds('120')).toBe(120);
      expect(parseDurationToSeconds('5')).toBe(5);
      expect(parseDurationToSeconds('0')).toBe(0);
    });

    it('should handle invalid inputs', () => {
      expect(parseDurationToSeconds('')).toBe(0);
      expect(parseDurationToSeconds(null)).toBe(0);
      expect(parseDurationToSeconds(undefined)).toBe(0);
      expect(parseDurationToSeconds('invalid')).toBe(0);
    });
  });

  describe('shouldDisplayCallInPortal', () => {
    it('should display calls 5 seconds or longer', () => {
      expect(shouldDisplayCallInPortal({ duration: '0:00:05' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '0:00:10' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '5 sec' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '1 min' })).toBe(true);
    });

    it('should hide calls under 5 seconds', () => {
      expect(shouldDisplayCallInPortal({ duration: '0:00:04' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '0:00:03' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '3 sec' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '1' })).toBe(false);
    });

    it('should display calls with no duration (unknown duration)', () => {
      expect(shouldDisplayCallInPortal({ duration: null })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: undefined })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '' })).toBe(false);
    });
  });

  describe('shouldMarkAsDisconnected', () => {
    it('should mark calls over 20 seconds with no types as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:30',
          type: null,
          callTypes: null,
        }),
      ).toBe(true);

      expect(
        shouldMarkAsDisconnected({
          duration: '1 min',
          type: undefined,
          callTypes: [],
        }),
      ).toBe(true);
    });

    it('should mark calls over 20 seconds with only OTHER type as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:30',
          type: CallType.OTHER,
          callTypes: [CallType.OTHER],
        }),
      ).toBe(true);

      expect(
        shouldMarkAsDisconnected({
          duration: '1 min',
          type: CallType.OTHER,
          callTypes: null,
        }),
      ).toBe(true);
    });

    it('should not mark calls under 20 seconds as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:15',
          type: null,
          callTypes: null,
        }),
      ).toBe(false);

      expect(
        shouldMarkAsDisconnected({
          duration: '19 sec',
          type: undefined,
          callTypes: [],
        }),
      ).toBe(false);
    });

    it('should not mark calls with meaningful types as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:30',
          type: CallType.VOICEMAIL,
          callTypes: [CallType.VOICEMAIL],
        }),
      ).toBe(false);

      expect(
        shouldMarkAsDisconnected({
          duration: '1 min',
          type: CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
          callTypes: [CallType.OTHER, CallType.NEW_APPOINTMENT_EXISTING_PATIENT],
        }),
      ).toBe(false);
    });

    it('should mark calls with mixed types containing only OTHER as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:30',
          type: CallType.OTHER,
          callTypes: [CallType.OTHER, CallType.OTHER],
        }),
      ).toBe(true);
    });

    it('should not mark calls with mixed types containing meaningful types as disconnected', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '0:00:30',
          type: CallType.OTHER,
          callTypes: [CallType.OTHER, CallType.VOICEMAIL],
        }),
      ).toBe(false);
    });
  });

  describe('mergeCallTypes', () => {
    it('should handle adding DISCONNECTED type', () => {
      const result = mergeCallTypes([CallType.OTHER], CallType.DISCONNECTED);
      expect(result).toEqual([CallType.OTHER, CallType.DISCONNECTED]);
    });

    it('should replace types in same category', () => {
      const result = mergeCallTypes([CallType.OTHER], CallType.GENERAL_INFO);
      expect(result).toEqual([CallType.GENERAL_INFO]); // Both are 'misc' category
    });

    it('should not duplicate same type', () => {
      const result = mergeCallTypes([CallType.DISCONNECTED], CallType.DISCONNECTED);
      expect(result).toEqual([CallType.DISCONNECTED]);
    });
  });
});
