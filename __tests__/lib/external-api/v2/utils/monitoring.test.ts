import { NextApiRequest } from 'next';
import {
  recordMetrics,
  getMetrics,
  resetMetrics,
  logMetrics,
} from '../../../../../lib/external-api/v2/utils/monitoring';
import logger from '../../../../../lib/external-api/v2/utils/logger';

// Mock the logger
jest.mock('../../../../../lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('monitoring utilities', () => {
  // Reset metrics before each test
  beforeEach(() => {
    resetMetrics();
    jest.clearAllMocks();
  });

  it('should record metrics with no errors', () => {
    // Create mock request
    const mockReq = {
      method: 'GET',
      url: '/api/external-api/v2/test',
    } as NextApiRequest;

    // Record a metric
    recordMetrics(mockReq, 100, false);

    // Check that metrics were recorded
    const metrics = getMetrics();
    expect(metrics['GET /api/external-api/v2/test'].totalCalls).toBe(1);
    expect(metrics['GET /api/external-api/v2/test'].errors).toBe(0);
    expect(metrics['GET /api/external-api/v2/test'].avgLatencyMs).toBe(100);
    expect(metrics['GET /api/external-api/v2/test'].errorRate).toBe(0);
  });

  it('should record metrics with errors', () => {
    // Create mock request
    const mockReq = {
      method: 'GET',
      url: '/api/external-api/v2/test',
    } as NextApiRequest;

    // Record a metric with an error
    recordMetrics(mockReq, 200, true);

    // Check that metrics were recorded
    const metrics = getMetrics();
    expect(metrics['GET /api/external-api/v2/test'].totalCalls).toBe(1);
    expect(metrics['GET /api/external-api/v2/test'].errors).toBe(1);
    expect(metrics['GET /api/external-api/v2/test'].avgLatencyMs).toBe(200);
    expect(metrics['GET /api/external-api/v2/test'].errorRate).toBe(1);
  });

  it('should accumulate metrics across multiple calls', () => {
    // Create mock request
    const mockReq = {
      method: 'GET',
      url: '/api/external-api/v2/test',
    } as NextApiRequest;

    // Record multiple metrics
    recordMetrics(mockReq, 100, false);
    recordMetrics(mockReq, 200, true);
    recordMetrics(mockReq, 300, false);

    // Check that metrics were accumulated
    const metrics = getMetrics();
    expect(metrics['GET /api/external-api/v2/test'].totalCalls).toBe(3);
    expect(metrics['GET /api/external-api/v2/test'].errors).toBe(1);
    expect(metrics['GET /api/external-api/v2/test'].avgLatencyMs).toBe(200);
    expect(metrics['GET /api/external-api/v2/test'].errorRate).toBeCloseTo(0.333, 3);
  });

  it('should log metrics when called', () => {
    // Record some metrics
    const mockReq = {
      method: 'GET',
      url: '/api/external-api/v2/log-test',
    } as NextApiRequest;
    recordMetrics(mockReq, 150, false);

    // Call logMetrics
    logMetrics();

    // Check that logger was called
    expect(logger.info).toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalledWith(
      expect.objectContaining({ metrics: expect.any(Object) }),
      'API Metrics',
    );
  });

  it('should track multiple endpoints separately', () => {
    // Create mock requests for different endpoints
    const mockReqA = {
      method: 'GET',
      url: '/api/external-api/v2/endpoint-a',
    } as NextApiRequest;

    const mockReqB = {
      method: 'GET',
      url: '/api/external-api/v2/endpoint-b',
    } as NextApiRequest;

    // Record metrics for both endpoints
    recordMetrics(mockReqA, 100, false);
    recordMetrics(mockReqA, 200, false);
    recordMetrics(mockReqB, 300, true);

    // Check metrics were recorded correctly
    const metrics = getMetrics();

    // Check endpoint A
    expect(metrics['GET /api/external-api/v2/endpoint-a'].totalCalls).toBe(2);
    expect(metrics['GET /api/external-api/v2/endpoint-a'].errors).toBe(0);
    expect(metrics['GET /api/external-api/v2/endpoint-a'].avgLatencyMs).toBe(150);

    // Check endpoint B
    expect(metrics['GET /api/external-api/v2/endpoint-b'].totalCalls).toBe(1);
    expect(metrics['GET /api/external-api/v2/endpoint-b'].errors).toBe(1);
    expect(metrics['GET /api/external-api/v2/endpoint-b'].errorRate).toBe(1);
  });
});
