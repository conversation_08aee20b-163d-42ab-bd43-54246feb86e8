import {
  isChicagoDST,
  ensureChicagoTimezone,
  ensureAppointmentTimezones,
  formatSimpleDateTime,
} from '../../../../../lib/external-api/v2/utils/timezone';

describe('Timezone Utilities', () => {
  describe('isChicagoDST', () => {
    it('should return true for dates during DST', () => {
      // Test summer date (DST)
      const summerDate = new Date('2023-07-15T12:00:00');
      expect(isChicagoDST(summerDate)).toBe(true);
    });

    it('should return false for dates during standard time', () => {
      // Test winter date (standard time)
      const winterDate = new Date('2023-01-15T12:00:00');
      expect(isChicagoDST(winterDate)).toBe(false);
    });
  });

  describe('ensureChicagoTimezone', () => {
    it('should add Chicago DST timezone offset (-05:00) to date strings during DST', () => {
      // Mock the isChicagoDST function to always return true for testing
      jest
        .spyOn(require('../../../../../lib/external-api/v2/utils/timezone'), 'isChicagoDST')
        .mockImplementation(() => true);

      const dateStr = '2023-07-15T12:00:00';
      const result = ensureChicagoTimezone(dateStr);
      expect(result).toBe('2023-07-15T12:00:00-05:00');

      // Restore the original implementation
      jest.restoreAllMocks();
    });

    it('should add Chicago standard timezone offset (-06:00) to date strings during standard time', () => {
      // Mock the isChicagoDST function to always return false for testing
      jest
        .spyOn(require('../../../../../lib/external-api/v2/utils/timezone'), 'isChicagoDST')
        .mockImplementation(() => false);

      const dateStr = '2023-01-15T12:00:00';
      const result = ensureChicagoTimezone(dateStr);
      expect(result).toBe('2023-01-15T12:00:00-06:00');

      // Restore the original implementation
      jest.restoreAllMocks();
    });

    it('should not modify date strings that already have timezone information', () => {
      const dateWithZ = '2023-07-15T12:00:00Z';
      expect(ensureChicagoTimezone(dateWithZ)).toBe(dateWithZ);

      const dateWithOffset = '2023-07-15T12:00:00-04:00';
      expect(ensureChicagoTimezone(dateWithOffset)).toBe(dateWithOffset);
    });
  });

  describe('ensureAppointmentTimezones', () => {
    it('should ensure both start and end times have Chicago timezone', () => {
      // Mock the isChicagoDST function to always return true for testing
      jest
        .spyOn(require('../../../../../lib/external-api/v2/utils/timezone'), 'isChicagoDST')
        .mockImplementation(() => true);

      const startTime = '2023-07-15T09:00:00';
      const endTime = '2023-07-15T10:00:00';

      const result = ensureAppointmentTimezones(startTime, endTime);

      expect(result.startTime).toBe('2023-07-15T09:00:00-05:00');
      expect(result.endTime).toBe('2023-07-15T10:00:00-05:00');

      // Restore the original implementation
      jest.restoreAllMocks();
    });

    it('should handle empty strings', () => {
      const result = ensureAppointmentTimezones('', '');

      // Empty strings should be returned as-is
      expect(result.startTime).toBe('');
      expect(result.endTime).toBe('');
    });
  });

  describe('formatSimpleDateTime', () => {
    it('should format an ISO date string to DD/MM/YYYY HH:MM format', () => {
      const isoDateString = '2025-04-27T07:30:00-04:00';
      const result = formatSimpleDateTime(isoDateString);
      expect(result).toBe('27/04/2025 07:30');
    });

    it('should handle midnight time correctly', () => {
      const isoDateString = '2025-04-27T00:00:00-04:00';
      const result = formatSimpleDateTime(isoDateString);
      expect(result).toBe('27/04/2025 00:00');
    });

    it('should handle single-digit days and months with padding', () => {
      const isoDateString = '2025-01-05T14:05:00-04:00';
      const result = formatSimpleDateTime(isoDateString);
      expect(result).toBe('05/01/2025 14:05');
    });

    it('should handle empty input', () => {
      const result = formatSimpleDateTime('');
      expect(result).toBe('');
    });
  });
});
