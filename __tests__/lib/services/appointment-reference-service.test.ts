import { AppointmentReferenceService } from '../../../lib/services/appointment-reference-service';
import { AppointmentReference } from '../../../lib/models/appointment-reference';

// Create mock repository
const mockRepository = {
  create: jest.fn(),
  findByExternalId: jest.fn(),
  findByProviderId: jest.fn(),
  findByPatientId: jest.fn(),
  update: jest.fn(),
};

// Mock the repository
jest.mock('../../../lib/repositories', () => ({
  getAppointmentReferencesRepository: jest.fn(() => mockRepository),
}));

// Mock database initialization
jest.mock('../../../lib/middleware/db-init', () => ({
  ensureDbInitialized: jest.fn().mockResolvedValue(undefined),
}));

import { getAppointmentReferencesRepository } from '../../../lib/repositories';
import { ensureDbInitialized } from '../../../lib/middleware/db-init';

describe('AppointmentReferenceService', () => {
  let service: AppointmentReferenceService;

  beforeEach(() => {
    service = new AppointmentReferenceService();
    jest.clearAllMocks();

    // Set up default mock implementations
    mockRepository.create.mockResolvedValue(undefined);
    mockRepository.findByExternalId.mockResolvedValue(null);
    mockRepository.findByProviderId.mockResolvedValue(null);
    mockRepository.findByPatientId.mockResolvedValue([]);
    mockRepository.update.mockResolvedValue(undefined);
  });

  describe('storeNewAppointment', () => {
    it('should create appointment reference with dual-database support', async () => {
      const appointmentData = {
        provider: 'nextech',
        externalId: 'ext-123',
        providerId: 'ext-123',
        patientId: 'patient-456',
        patientName: 'John Doe',
        practitionerId: 'provider-789',
        practitionerName: 'Dr. Smith',
        locationId: 'location-101',
        locationName: 'Main Clinic',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
        type: 'consultation',
        status: 'booked',
        reason: 'Check-up',
        notes: 'Follow-up appointment',
      };

      const result = await service.storeNewAppointment(appointmentData);

      // Verify database initialization was called
      expect(ensureDbInitialized).toHaveBeenCalled();

      // Verify repository create was called with correct data
      expect(mockRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          ...appointmentData,
          id: expect.stringMatching(
            /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/,
          ), // UUID v4
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }),
      );

      // Verify result structure
      expect(result).toMatchObject({
        ...appointmentData,
        id: expect.any(String),
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should handle errors during creation', async () => {
      const appointmentData = {
        provider: 'nextech',
        externalId: 'ext-123',
        providerId: 'ext-123',
        patientId: 'patient-456',
        practitionerId: 'provider-789',
        locationId: 'location-101',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
        type: 'consultation',
        status: 'booked',
      };

      const error = new Error('Database connection failed');
      (mockRepository.create as jest.Mock).mockRejectedValue(error);

      await expect(service.storeNewAppointment(appointmentData)).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('findByExternalId', () => {
    it('should find appointment reference by external ID using repository', async () => {
      const mockAppointment: AppointmentReference = {
        id: 'ref-123',
        provider: 'nextech',
        externalId: 'ext-123',
        providerId: 'ext-123',
        patientId: 'patient-456',
        patientName: 'John Doe',
        practitionerId: 'provider-789',
        practitionerName: 'Dr. Smith',
        locationId: 'location-101',
        locationName: 'Main Clinic',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
        type: 'consultation',
        status: 'booked',
        reason: 'Check-up',
        notes: 'Follow-up appointment',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      (mockRepository.findByExternalId as jest.Mock).mockResolvedValue(mockAppointment);

      const result = await service.findByExternalId('nextech', 'ext-123');

      expect(ensureDbInitialized).toHaveBeenCalled();
      expect(mockRepository.findByExternalId).toHaveBeenCalledWith('nextech', 'ext-123');
      expect(result).toEqual(mockAppointment);
    });

    it('should return null when appointment reference not found', async () => {
      (mockRepository.findByExternalId as jest.Mock).mockResolvedValue(null);

      const result = await service.findByExternalId('nextech', 'non-existent');

      expect(result).toBeNull();
    });
  });

  describe('findByPatientId', () => {
    it('should find appointment references by patient ID using repository', async () => {
      const mockAppointments: AppointmentReference[] = [
        {
          id: 'ref-123',
          provider: 'nextech',
          externalId: 'ext-123',
          providerId: 'ext-123',
          patientId: 'patient-456',
          patientName: 'John Doe',
          practitionerId: 'provider-789',
          practitionerName: 'Dr. Smith',
          locationId: 'location-101',
          locationName: 'Main Clinic',
          startTime: '2024-01-15T10:00:00Z',
          endTime: '2024-01-15T10:30:00Z',
          type: 'consultation',
          status: 'booked',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      (mockRepository.findByPatientId as jest.Mock).mockResolvedValue(mockAppointments);

      const result = await service.findByPatientId('nextech', 'patient-456');

      expect(ensureDbInitialized).toHaveBeenCalled();
      expect(mockRepository.findByPatientId).toHaveBeenCalledWith('nextech', 'patient-456');
      expect(result).toEqual(mockAppointments);
    });
  });

  describe('updateAppointment', () => {
    it('should update appointment reference using repository', async () => {
      const appointmentToUpdate: AppointmentReference = {
        id: 'ref-123',
        provider: 'nextech',
        externalId: 'ext-123',
        providerId: 'ext-123',
        patientId: 'patient-456',
        patientName: 'John Doe',
        practitionerId: 'provider-789',
        practitionerName: 'Dr. Smith',
        locationId: 'location-101',
        locationName: 'Main Clinic',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
        type: 'consultation',
        status: 'completed', // Updated status
        createdAt: new Date('2024-01-10T09:00:00Z'),
        updatedAt: new Date('2024-01-10T09:00:00Z'),
      };

      const updatedAppointment = {
        ...appointmentToUpdate,
        updatedAt: new Date(),
      };

      (mockRepository.update as jest.Mock).mockResolvedValue(updatedAppointment);

      const result = await service.updateAppointment(appointmentToUpdate);

      expect(ensureDbInitialized).toHaveBeenCalled();
      expect(mockRepository.update).toHaveBeenCalledWith(
        'ref-123',
        expect.objectContaining({
          ...appointmentToUpdate,
          updatedAt: expect.any(Date),
        }),
      );
      expect(result).toEqual(updatedAppointment);
    });
  });
});
