import { PatientCoordinatorService } from '@/lib/services/patient-coordinator-service';
import { PatientReferenceService } from '@/lib/services/patient-reference-service';
import { PatientService } from '@/lib/services/patient-service';
import { PatientReference } from '@/lib/models/patient-reference';
import { Patient } from '@/lib/external-api/v2/models/types';
import { providerRegistry } from '@/lib/external-api/v2/providers';

// Mock Firebase Admin to prevent initialization errors
jest.mock('@/utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
    update: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock the PatientReferenceService
jest.mock('@/lib/services/patient-reference-service');

// Mock the PatientService
jest.mock('@/lib/services/patient-service');

// Mock the providerRegistry
jest.mock('@/lib/external-api/v2/providers', () => ({
  providerRegistry: {
    getProvider: jest.fn(),
  },
}));

describe('PatientCoordinatorService', () => {
  let service: PatientCoordinatorService;
  let mockPatientReferenceService: jest.Mocked<PatientReferenceService>;
  let mockPatientService: jest.Mocked<PatientService>;
  let mockProvider: any;
  let mockExternalPatientService: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock PatientReferenceService
    mockPatientReferenceService =
      new PatientReferenceService() as jest.Mocked<PatientReferenceService>;

    // Create mock PatientService
    mockPatientService = new PatientService() as jest.Mocked<PatientService>;

    // Create mock external patient service
    mockExternalPatientService = {
      createPatient: jest.fn(),
      getPatientById: jest.fn(),
    };

    // Create mock provider
    mockProvider = {
      name: 'nextech',
      getPatientService: jest.fn().mockReturnValue(mockExternalPatientService),
    };

    // Mock the providerRegistry.getProvider method
    (providerRegistry.getProvider as jest.Mock).mockReturnValue(mockProvider);

    // Create a new service instance with the mock services
    service = new PatientCoordinatorService(mockPatientReferenceService, mockPatientService);
  });

  describe('constructor', () => {
    it('should create service with default services when no parameters provided', () => {
      // Act
      const serviceWithDefaults = new PatientCoordinatorService();

      // Assert
      expect(serviceWithDefaults).toBeInstanceOf(PatientCoordinatorService);
    });

    it('should create service with provided services', () => {
      // Act
      const serviceWithMocks = new PatientCoordinatorService(
        mockPatientReferenceService,
        mockPatientService,
      );

      // Assert
      expect(serviceWithMocks).toBeInstanceOf(PatientCoordinatorService);
    });
  });

  describe('storePatientReference', () => {
    it('should store a patient reference', async () => {
      // Arrange
      const patientReferenceData = {
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
      };

      const patientReference: PatientReference = {
        id: 'mock-uuid',
        ...patientReferenceData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the PatientReferenceService's storeNewPatient method
      mockPatientReferenceService.storeNewPatient.mockResolvedValueOnce(patientReference);

      // Act
      const result = await service.storePatientReference(patientReferenceData);

      // Assert
      expect(mockPatientReferenceService.storeNewPatient).toHaveBeenCalledWith(
        patientReferenceData,
      );

      // Check that the result is the patient reference
      expect(result).toEqual(patientReference);
    });

    it('should handle errors when storing a patient reference', async () => {
      // Arrange
      const patientReferenceData = {
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
      };

      // Mock the PatientReferenceService's storeNewPatient method to throw an error
      const error = new Error('Firestore error');
      mockPatientReferenceService.storeNewPatient.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.storePatientReference(patientReferenceData)).rejects.toThrow(
        'Firestore error',
      );
    });
  });

  describe('storePatient', () => {
    it('should store a complete patient record', async () => {
      // Arrange
      const patientData: Omit<Patient, 'id'> = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
      };

      const storedPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid',
        ...patientData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the PatientService's storeNewPatient method
      mockPatientService.storeNewPatient.mockResolvedValueOnce(storedPatient);

      // Act
      const result = await service.storePatient(patientData);

      // Assert
      expect(mockPatientService.storeNewPatient).toHaveBeenCalledWith(patientData);

      // Check that the result is the stored patient
      expect(result).toEqual(storedPatient);
    });

    it('should handle errors when storing a patient', async () => {
      // Arrange
      const patientData: Omit<Patient, 'id'> = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
      };

      // Mock the PatientService's storeNewPatient method to throw an error
      const error = new Error('Firestore error');
      mockPatientService.storeNewPatient.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.storePatient(patientData)).rejects.toThrow('Firestore error');
    });
  });

  describe('getPatientByProviderId', () => {
    it('should get a patient by provider ID and update the reference if it exists', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const existingReference: PatientReference = {
        id: 'mock-uuid',
        provider,
        providerId,
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const externalPatient = {
        id: providerId,
        firstName: 'John',
        lastName: 'Smith',
        dateOfBirth: '1980-01-01',
        email: '<EMAIL>',
        phoneNumber: '************', // Different phone number
        providerInfo: {
          provider,
          externalId: providerId,
        },
      };

      const updatedReference: PatientReference = {
        ...existingReference,
        phoneNumber: '************',
        updatedAt: new Date(),
      };

      // Mock the PatientReferenceService's findByProviderId method
      mockPatientReferenceService.findByProviderId.mockResolvedValueOnce(existingReference);

      // Mock the external provider's getPatientById method
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(externalPatient);

      // Mock the PatientReferenceService's updateIfChanged method
      mockPatientReferenceService.updateIfChanged.mockResolvedValueOnce(updatedReference);

      // Act
      const result = await service.getPatientByProviderId(provider, providerId);

      // Assert
      expect(mockPatientReferenceService.findByProviderId).toHaveBeenCalledWith(
        provider,
        providerId,
      );
      expect(providerRegistry.getProvider).toHaveBeenCalledWith(provider);
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);

      expect(mockPatientReferenceService.updateIfChanged).toHaveBeenCalledWith({
        ...existingReference,
        phoneNumber: '************',
      });

      // Check that the result is the updated patient reference
      expect(result).toEqual(updatedReference);
    });

    it('should create a new reference if one does not exist', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const externalPatient = {
        id: providerId,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        providerInfo: {
          provider,
          externalId: providerId,
        },
      };

      const newReference: PatientReference = {
        id: 'mock-uuid',
        provider,
        providerId,
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the PatientReferenceService's findByProviderId method to return null
      mockPatientReferenceService.findByProviderId.mockResolvedValueOnce(null);

      // Mock the external provider's getPatientById method
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(externalPatient);

      // Mock the PatientReferenceService's storeNewPatient method
      mockPatientReferenceService.storeNewPatient.mockResolvedValueOnce(newReference);

      // Act
      const result = await service.getPatientByProviderId(provider, providerId);

      // Assert
      expect(mockPatientReferenceService.findByProviderId).toHaveBeenCalledWith(
        provider,
        providerId,
      );
      expect(providerRegistry.getProvider).toHaveBeenCalledWith(provider);
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);

      expect(mockPatientReferenceService.storeNewPatient).toHaveBeenCalledWith({
        provider,
        providerId,
        phoneNumber: '************',
      });

      // Check that the result is the new patient reference
      expect(result).toEqual(newReference);
    });

    it('should throw a NotFoundError if the patient is not found in the external provider', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the PatientReferenceService's findByProviderId method
      mockPatientReferenceService.findByProviderId.mockResolvedValueOnce(null);

      // Mock the external provider's getPatientById method to return null
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(null);

      // Act & Assert
      await expect(service.getPatientByProviderId(provider, providerId)).rejects.toThrow(
        'Patient not found in nextech with ID ext-123',
      );
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);
    });

    it('should handle errors when getting a patient by provider ID', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the PatientReferenceService's findByProviderId method to throw an error
      const error = new Error('Firestore error');
      mockPatientReferenceService.findByProviderId.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.getPatientByProviderId(provider, providerId)).rejects.toThrow(
        'Firestore error',
      );
    });
  });

  describe('getCompletePatientByProviderId', () => {
    it('should get a complete patient by provider ID and update the record if it exists', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const existingPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        providerInfo: {
          provider,
          externalId: providerId,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const externalPatient = {
        id: providerId,
        firstName: 'John',
        lastName: 'Smith', // Different last name
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>', // Different email
        phoneNumber: '************', // Different phone number
      };

      const updatedPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        ...existingPatient,
        lastName: externalPatient.lastName, // Different last name
        email: externalPatient.email, // Different email
        phoneNumber: externalPatient.phoneNumber, // Different phone number
        providerInfo: {
          provider,
          externalId: externalPatient.id,
        },
        updatedAt: new Date(),
      };

      // Mock the PatientService's findByProviderId method
      mockPatientService.findByProviderId.mockResolvedValueOnce(existingPatient);

      // Mock the external provider's getPatientById method
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(externalPatient);

      // Mock the PatientService's updateIfChanged method
      mockPatientService.updateIfChanged.mockResolvedValueOnce(updatedPatient);

      // Act
      const result = await service.getCompletePatientByProviderId(provider, providerId);

      // Assert
      expect(mockPatientService.findByProviderId).toHaveBeenCalledWith(provider, providerId);
      expect(providerRegistry.getProvider).toHaveBeenCalledWith(provider);
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);

      expect(mockPatientService.updateIfChanged).toHaveBeenCalledWith({
        ...existingPatient,
        id: externalPatient.id,
        lastName: externalPatient.lastName,
        email: externalPatient.email,
        phoneNumber: externalPatient.phoneNumber,
        providerInfo: {
          provider,
          externalId: externalPatient.id,
        },
      });

      // Check that the result is the updated patient
      expect(result).toEqual(updatedPatient);
    });

    it('should create a new complete patient record if one does not exist', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const externalPatient = {
        id: providerId,
        firstName: 'Jane',
        lastName: 'Doe',
        dateOfBirth: '1985-05-15',
        gender: 'female',
        email: '<EMAIL>',
        phoneNumber: '************',
      };

      const newPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid',
        firstName: externalPatient.firstName,
        lastName: externalPatient.lastName,
        dateOfBirth: externalPatient.dateOfBirth,
        gender: externalPatient.gender,
        email: externalPatient.email,
        phoneNumber: externalPatient.phoneNumber,
        providerInfo: {
          provider,
          externalId: externalPatient.id,
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the PatientService's findByProviderId method to return null
      mockPatientService.findByProviderId.mockResolvedValueOnce(null);

      // Mock the external provider's getPatientById method
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(externalPatient);

      // Mock the PatientService's storeNewPatient method
      mockPatientService.storeNewPatient.mockResolvedValueOnce(newPatient);

      // Act
      const result = await service.getCompletePatientByProviderId(provider, providerId);

      // Assert
      expect(mockPatientService.findByProviderId).toHaveBeenCalledWith(provider, providerId);
      expect(providerRegistry.getProvider).toHaveBeenCalledWith(provider);
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);

      expect(mockPatientService.storeNewPatient).toHaveBeenCalledWith({
        id: externalPatient.id,
        firstName: externalPatient.firstName,
        lastName: externalPatient.lastName,
        dateOfBirth: externalPatient.dateOfBirth,
        gender: externalPatient.gender,
        email: externalPatient.email,
        phoneNumber: externalPatient.phoneNumber,
        providerInfo: {
          provider,
          externalId: externalPatient.id,
        },
      });

      // Check that the result is the new patient
      expect(result).toEqual(newPatient);
    });

    it('should throw a NotFoundError if the patient is not found in the external provider', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the PatientService's findByProviderId method
      mockPatientService.findByProviderId.mockResolvedValueOnce(null);

      // Mock the external provider's getPatientById method to return null
      mockExternalPatientService.getPatientById.mockResolvedValueOnce(null);

      // Act & Assert
      await expect(service.getCompletePatientByProviderId(provider, providerId)).rejects.toThrow(
        'Patient not found in nextech with ID ext-123',
      );
      expect(mockExternalPatientService.getPatientById).toHaveBeenCalledWith(providerId);
    });

    it('should handle errors when getting a complete patient by provider ID', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the PatientService's findByProviderId method to throw an error
      const error = new Error('Firestore error');
      mockPatientService.findByProviderId.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.getCompletePatientByProviderId(provider, providerId)).rejects.toThrow(
        'Firestore error',
      );
    });
  });

  describe('getCompletePatientById', () => {
    it('should get a complete patient by internal ID', async () => {
      // Arrange
      const id = 'mock-uuid';

      const patient: Patient & { createdAt: Date; updatedAt: Date } = {
        id,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the PatientService's findById method
      mockPatientService.findById.mockResolvedValueOnce(patient);

      // Act
      const result = await service.getCompletePatientById(id);

      // Assert
      expect(mockPatientService.findById).toHaveBeenCalledWith(id);

      // Check that the result is the patient
      expect(result).toEqual(patient);
    });

    it('should return null if no patient is found by internal ID', async () => {
      // Arrange
      const id = 'mock-uuid';

      // Mock the PatientService's findById method to return null
      mockPatientService.findById.mockResolvedValueOnce(null);

      // Act
      const result = await service.getCompletePatientById(id);

      // Assert
      expect(mockPatientService.findById).toHaveBeenCalledWith(id);

      // Check that the result is null
      expect(result).toBeNull();
    });

    it('should handle errors when getting a complete patient by ID', async () => {
      // Arrange
      const id = 'mock-uuid';

      // Mock the PatientService's findById method to throw an error
      const error = new Error('Firestore error');
      mockPatientService.findById.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.getCompletePatientById(id)).rejects.toThrow('Firestore error');
    });
  });
});
