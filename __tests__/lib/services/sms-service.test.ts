import { SmsService } from '../../../lib/services/sms-service';
import twilio from 'twilio';

// Mock the twilio module
jest.mock('twilio', () => {
  const mockMessages = {
    create: jest.fn().mockResolvedValue({
      sid: 'test-message-sid',
      status: 'queued',
    }),
  };

  return jest.fn().mockImplementation(() => ({
    messages: mockMessages,
  }));
});

// Mock the logger
jest.mock('../../../lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock environment variables
const originalEnv = process.env;

describe('SmsService', () => {
  beforeEach(() => {
    // Setup environment variables for tests
    process.env = {
      ...originalEnv,
      TWILIO_ACCOUNT_SID: 'test-account-sid',
      TWILIO_AUTH_TOKEN: 'test-auth-token',
      TWILIO_PHONE_NUMBER: '+***********',
    };
  });

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv;
    jest.clearAllMocks();
  });

  it('should initialize with Twilio credentials in constructor', () => {
    new SmsService();
    expect(twilio).toHaveBeenCalledWith('test-account-sid', 'test-auth-token');
  });

  it('should not throw when credentials are missing in constructor', () => {
    // Remove required environment variables
    delete process.env.TWILIO_ACCOUNT_SID;
    delete process.env.TWILIO_AUTH_TOKEN;

    // Should not throw
    expect(() => new SmsService()).not.toThrow();
  });

  it('should send an SMS message successfully', async () => {
    // Reset the mock to ensure it's called again
    jest.clearAllMocks();

    const smsService = new SmsService();
    const to = '+***********';
    const message = 'Test message';

    const result = await smsService.sendSms(to, message);

    expect(result).toBe('test-message-sid');
    expect(twilio().messages.create).toHaveBeenCalledWith({
      body: message,
      from: '+***********',
      to: to,
    });
  });

  it('should throw an error for invalid phone numbers', async () => {
    const smsService = new SmsService();
    const invalidPhone = 'not-a-phone-number';
    const message = 'Test message';

    await expect(smsService.sendSms(invalidPhone, message)).rejects.toThrow(
      'Invalid phone number format',
    );
  });

  it('should send an appointment confirmation SMS', async () => {
    // Reset the mock to ensure it's called again
    jest.clearAllMocks();

    const smsService = new SmsService();
    const to = '+***********';
    const appointmentDetails = {
      patientName: 'John Doe',
      date: '2023-06-15',
      time: '10:30 AM',
      location: 'Main Clinic',
      provider: 'Dr. Smith',
    };

    const result = await smsService.sendAppointmentConfirmation(to, appointmentDetails);

    expect(result).toBe('test-message-sid');
    expect(twilio().messages.create).toHaveBeenCalledWith({
      body: expect.stringContaining('Hello John Doe'),
      from: '+***********',
      to: to,
    });
    expect(twilio().messages.create).toHaveBeenCalledWith({
      body: expect.stringContaining('with Dr. Smith'),
      from: '+***********',
      to: to,
    });
  });
});
