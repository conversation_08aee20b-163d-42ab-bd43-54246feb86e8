import {
  PatientService,
  normalizePhoneNumberForCaching,
} from '../../../lib/services/patient-service';
import { Patient } from '../../../lib/external-api/v2/models/types';

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid-123'),
}));

// Mock Firebase Admin
jest.mock('../../../utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock RepositoryManager to prevent dual-database initialization issues
const mockPatientsRepository = {
  create: jest.fn(),
  findByProviderId: jest.fn(),
  findByPhoneNumber: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
};

const mockRepositoryManager = {
  initialize: jest.fn().mockResolvedValue(undefined),
  patients: mockPatientsRepository,
};

jest.mock('../../../lib/repositories', () => ({
  RepositoryManager: {
    getInstance: jest.fn(() => mockRepositoryManager),
  },
}));

describe('PatientService', () => {
  let service: PatientService;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Set up default mock implementations
    mockPatientsRepository.create.mockResolvedValue(undefined);
    mockPatientsRepository.findByProviderId.mockResolvedValue(null);
    mockPatientsRepository.findByPhoneNumber.mockResolvedValue(null);
    mockPatientsRepository.findById.mockResolvedValue(null);
    mockPatientsRepository.update.mockResolvedValue(undefined);

    // Create a new service instance
    service = new PatientService();
  });

  describe('storeNewPatient', () => {
    it('should store a new patient successfully', async () => {
      // Arrange
      const patientData: Omit<Patient, 'id'> = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        address: {
          line1: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          postalCode: '12345',
          country: 'USA',
        },
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        notes: 'Test patient',
        insurances: [
          {
            companyName: 'Test Insurance',
            memberId: 'MEM123',
            isPrimary: true,
            patientId: 'mock-uuid-123',
          },
        ],
      };

      const expectedResult: Patient & { providerId: string; createdAt: Date; updatedAt: Date } = {
        ...patientData,
        id: 'mock-uuid-123',
        providerId: 'ext-123',
        phoneNumber: '**********', // normalized
        identifiers: [],
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      };

      // Mock the repository create method to return the expected result
      mockPatientsRepository.create.mockResolvedValueOnce(expectedResult);

      // Act
      const result = await service.storeNewPatient(patientData);

      // Assert
      expect(mockRepositoryManager.initialize).toHaveBeenCalled();
      expect(mockPatientsRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'mock-uuid-123',
          firstName: 'John',
          lastName: 'Doe',
          providerId: 'ext-123',
          phoneNumber: '**********', // normalized
          identifiers: [],
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }),
      );
      expect(result).toEqual(expectedResult);
    });

    it('should handle errors when storing a patient', async () => {
      // Arrange
      const patientData: Omit<Patient, 'id'> = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
      };

      // Mock the repository create method to throw an error
      const error = new Error('Database connection failed');
      mockPatientsRepository.create.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.storeNewPatient(patientData)).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('findByProviderId', () => {
    it('should find a patient by provider ID', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const expectedResult: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '**********',
        providerInfo: {
          provider,
          externalId: providerId,
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      // Mock the repository findByProviderId method
      mockPatientsRepository.findByProviderId.mockResolvedValueOnce(expectedResult);

      // Act
      const result = await service.findByProviderId(provider, providerId);

      // Assert
      expect(mockRepositoryManager.initialize).toHaveBeenCalled();
      expect(mockPatientsRepository.findByProviderId).toHaveBeenCalledWith(provider, providerId);
      expect(result).toEqual(expectedResult);
    });

    it('should return null if no patient is found', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'nonexistent';

      // Mock the repository to return null
      mockPatientsRepository.findByProviderId.mockResolvedValueOnce(null);

      // Act
      const result = await service.findByProviderId(provider, providerId);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle errors when finding a patient', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the repository method to throw an error
      const error = new Error('Database connection failed');
      mockPatientsRepository.findByProviderId.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.findByProviderId(provider, providerId)).rejects.toThrow(
        'Database connection failed',
      );
    });
  });

  describe('findByPhoneNumber', () => {
    it('should find a patient by phone number', async () => {
      // Arrange
      const phoneNumber = '(*************';
      const normalizedPhone = '**********';

      const expectedResult: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        phoneNumber: normalizedPhone,
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      // Mock the repository findByPhoneNumber method
      mockPatientsRepository.findByPhoneNumber.mockResolvedValueOnce(expectedResult);

      // Act
      const result = await service.findByPhoneNumber(phoneNumber);

      // Assert
      expect(mockRepositoryManager.initialize).toHaveBeenCalled();
      expect(mockPatientsRepository.findByPhoneNumber).toHaveBeenCalledWith(normalizedPhone);
      expect(result).toEqual(expectedResult);
    });

    it('should return null if no patient is found', async () => {
      // Arrange
      const phoneNumber = '(*************';

      // Mock the repository to return null
      mockPatientsRepository.findByPhoneNumber.mockResolvedValueOnce(null);

      // Act
      const result = await service.findByPhoneNumber(phoneNumber);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('updateIfChanged', () => {
    it('should update a patient if data has changed', async () => {
      // Arrange
      const patientId = 'mock-uuid-123';

      const currentData: Patient & { createdAt: Date; updatedAt: Date } = {
        id: patientId,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        phoneNumber: '**********',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      const updatedPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        ...currentData,
        firstName: 'Jane', // Changed name
        email: '<EMAIL>', // Changed email
      };

      // Mock the repository methods
      mockPatientsRepository.findById.mockResolvedValueOnce(currentData);
      mockPatientsRepository.update.mockResolvedValueOnce(undefined);

      // Act
      const result = await service.updateIfChanged(updatedPatient);

      // Assert
      expect(mockRepositoryManager.initialize).toHaveBeenCalled();
      expect(mockPatientsRepository.findById).toHaveBeenCalledWith(patientId);
      expect(mockPatientsRepository.update).toHaveBeenCalledWith(
        patientId,
        expect.objectContaining({
          firstName: 'Jane',
          email: '<EMAIL>',
          providerId: 'ext-123',
          phoneNumber: '**********',
          identifiers: undefined,
          updatedAt: expect.any(Date),
        }),
      );
      expect(result.firstName).toBe('Jane');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should not update a patient if data has not changed', async () => {
      // Arrange
      const patientData: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'mock-uuid-123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        phoneNumber: '**********',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      // Mock the repository to return the same data
      mockPatientsRepository.findById.mockResolvedValueOnce(patientData);

      // Act
      const result = await service.updateIfChanged(patientData);

      // Assert
      expect(mockPatientsRepository.findById).toHaveBeenCalledWith('mock-uuid-123');
      expect(mockPatientsRepository.update).not.toHaveBeenCalled();
      expect(result).toEqual(patientData);
    });

    it('should detect changes in complex fields like address', async () => {
      // Arrange
      const patientId = 'mock-uuid-123';

      const currentData: Patient & { createdAt: Date; updatedAt: Date } = {
        id: patientId,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        address: {
          line1: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          postalCode: '12345',
          country: 'USA',
        },
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      const updatedPatient: Patient & { createdAt: Date; updatedAt: Date } = {
        ...currentData,
        address: {
          line1: '456 Oak Ave', // Changed address
          city: 'Newtown',
          state: 'CA',
          postalCode: '54321',
          country: 'USA',
        },
      };

      // Mock the repository methods
      mockPatientsRepository.findById.mockResolvedValueOnce(currentData);
      mockPatientsRepository.update.mockResolvedValueOnce(undefined);

      // Act
      const result = await service.updateIfChanged(updatedPatient);

      // Assert
      expect(mockPatientsRepository.update).toHaveBeenCalled();
      expect(result.address?.line1).toBe('456 Oak Ave');
    });

    it('should throw an error if the patient is not found', async () => {
      // Arrange
      const patientData: Patient & { createdAt: Date; updatedAt: Date } = {
        id: 'nonexistent-id',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      // Mock the repository to return null
      mockPatientsRepository.findById.mockResolvedValueOnce(null);

      // Act & Assert
      await expect(service.updateIfChanged(patientData)).rejects.toThrow(
        'Patient with ID nonexistent-id not found',
      );
    });
  });

  describe('findById', () => {
    it('should find a patient by ID', async () => {
      // Arrange
      const id = 'mock-uuid-123';

      const expectedResult: Patient & { createdAt: Date; updatedAt: Date } = {
        id,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        phoneNumber: '**********',
        providerInfo: {
          provider: 'nextech',
          externalId: 'ext-123',
        },
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      };

      // Mock the repository findById method
      mockPatientsRepository.findById.mockResolvedValueOnce(expectedResult);

      // Act
      const result = await service.findById(id);

      // Assert
      expect(mockRepositoryManager.initialize).toHaveBeenCalled();
      expect(mockPatientsRepository.findById).toHaveBeenCalledWith(id);
      expect(result).toEqual(expectedResult);
    });

    it('should return null if no patient is found', async () => {
      // Arrange
      const id = 'nonexistent-id';

      // Mock the repository to return null
      mockPatientsRepository.findById.mockResolvedValueOnce(null);

      // Act
      const result = await service.findById(id);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle errors when finding a patient by ID', async () => {
      // Arrange
      const id = 'mock-uuid-123';

      // Mock the repository method to throw an error
      const error = new Error('Database connection failed');
      mockPatientsRepository.findById.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.findById(id)).rejects.toThrow('Database connection failed');
    });
  });

  describe('normalizePhoneNumberForCaching', () => {
    it('should normalize phone numbers correctly', () => {
      expect(normalizePhoneNumberForCaching('(*************')).toBe('**********');
      expect(normalizePhoneNumberForCaching('**************')).toBe('**********');
      expect(normalizePhoneNumberForCaching('1**********')).toBe('**********');
      expect(normalizePhoneNumberForCaching('************')).toBe('**********');
      expect(normalizePhoneNumberForCaching('************')).toBe('**********');
    });

    it('should handle edge cases', () => {
      expect(normalizePhoneNumberForCaching(undefined)).toBeUndefined();
      expect(normalizePhoneNumberForCaching('')).toBeUndefined(); // Empty string returns undefined
      expect(normalizePhoneNumberForCaching('abc')).toBe(''); // Non-digit string returns empty after replacement
    });
  });
});
