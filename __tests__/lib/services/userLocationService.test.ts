import { UserLocationService } from '../../../lib/services/userLocationService';
import { UserRole } from '../../../models/auth';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
  firestore: () => ({
    collection: jest.fn(),
  }),
  apps: [],
}));

// Setup mocks
jest.mock('../../../lib/services/userLocationService', () => {
  const actualModule = jest.requireActual('../../../lib/services/userLocationService');
  return {
    ...actualModule,
    UserLocationService: {
      ...actualModule.UserLocationService,
      getUserLocationContext: jest.fn(),
    },
  };
});

describe('UserLocationService - Admin Access', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(UserLocationService).toBeDefined();
    expect(UserLocationService.getUserLocationContext).toBeDefined();
  });

  it('should mock admin user getting all locations', async () => {
    // Mock all locations in clinic
    const mockAllLocations = [
      {
        id: 'location-1',
        name: 'Location 1',
        practiceId: 'practice-1',
        clinicId: 12,
        isActive: true,
      },
      {
        id: 'location-2',
        name: 'Location 2',
        practiceId: 'practice-2',
        clinicId: 12,
        isActive: true,
      },
      {
        id: 'location-3',
        name: 'Location 3',
        practiceId: 'practice-1',
        clinicId: 12,
        isActive: true,
      },
    ];

    // Mock practices
    const mockPractices = [
      {
        id: 'practice-1',
        name: 'Practice 1',
        clinicId: 12,
      },
      {
        id: 'practice-2',
        name: 'Practice 2',
        clinicId: 12,
      },
    ];

    // Mock the getUserLocationContext to return all locations for admin
    (UserLocationService.getUserLocationContext as jest.Mock).mockResolvedValue({
      currentLocation: mockAllLocations[0],
      availableLocations: mockAllLocations, // Admin gets all locations
      availablePractices: mockPractices,
    });

    const result = await UserLocationService.getUserLocationContext('admin-user-id');

    expect(result).toBeDefined();
    expect(result.availableLocations).toHaveLength(3); // Admin should see all 3 locations
    expect(result.availablePractices).toHaveLength(2); // Should see all practices
    expect(result.currentLocation).toBeDefined();
  });

  it('should mock regular user getting only assigned locations', async () => {
    // Mock assigned locations only
    const mockAssignedLocations = [
      {
        id: 'location-1',
        name: 'Location 1',
        practiceId: 'practice-1',
        clinicId: 12,
        isActive: true,
      },
    ];

    // Mock practices
    const mockPractices = [
      {
        id: 'practice-1',
        name: 'Practice 1',
        clinicId: 12,
      },
    ];

    // Mock the getUserLocationContext to return only assigned locations for regular user
    (UserLocationService.getUserLocationContext as jest.Mock).mockResolvedValue({
      currentLocation: mockAssignedLocations[0],
      availableLocations: mockAssignedLocations, // Regular user gets only assigned locations
      availablePractices: mockPractices,
    });

    const result = await UserLocationService.getUserLocationContext('regular-user-id');

    expect(result).toBeDefined();
    expect(result.availableLocations).toHaveLength(1); // Regular user should see only 1 assigned location
    expect(result.availablePractices).toHaveLength(1); // Should see only related practices
    expect(result.currentLocation).toBeDefined();
  });
});
