/**
 * @file Unit tests for OfficeHoursService
 */

import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

import { OfficeHoursService } from '../../../lib/services/office-hours';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

describe('OfficeHoursService', () => {
  const mockOfficeHours = {
    '1': { start: '09:00', end: '17:00' }, // Monday
    '2': { start: '09:00', end: '17:00' }, // Tuesday
    '3': { start: '09:00', end: '17:00' }, // Wednesday
    '4': { start: '09:00', end: '17:00' }, // Thursday
    '5': { start: '09:00', end: '17:00' }, // Friday
    '6': null, // Saturday - closed
    '7': null, // Sunday - closed
  };

  const timezone = 'America/Chicago';

  describe('checkOfficeHours', () => {
    it('should return open status during business hours', () => {
      // Monday 10:00 AM Chicago time
      const currentTime = dayjs.tz('2024-01-15 10:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(true);
      expect(result.currentStatus).toBe('open');
      expect(result.timezone).toBe(timezone);
      expect(result.todayHours).toEqual({ start: '09:00', end: '17:00' });
      expect(result.nextCloseTime).toBeDefined();
    });

    it('should return closed status outside business hours', () => {
      // Monday 8:00 AM Chicago time (before opening)
      const currentTime = dayjs.tz('2024-01-15 08:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(false);
      expect(result.currentStatus).toBe('closed');
      expect(result.timezone).toBe(timezone);
      expect(result.todayHours).toEqual({ start: '09:00', end: '17:00' });
      expect(result.nextOpenTime).toBeDefined();
    });

    it('should return closed status on weekends', () => {
      // Saturday 10:00 AM Chicago time
      const currentTime = dayjs.tz('2024-01-20 10:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(false);
      expect(result.currentStatus).toBe('closed');
      expect(result.timezone).toBe(timezone);
      expect(result.todayHours).toBeNull();
      expect(result.nextOpenTime).toBeDefined();
    });

    it('should handle timezone conversion correctly', () => {
      // 10:00 AM EST = 9:00 AM CST (opening time)
      const currentTime = dayjs.tz('2024-01-15 10:00', 'America/New_York').toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(true);
      expect(result.currentStatus).toBe('open');
    });

    it('should handle edge case at opening time', () => {
      // Exactly 9:00 AM Chicago time
      const currentTime = dayjs.tz('2024-01-15 09:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(true);
      expect(result.currentStatus).toBe('open');
    });

    it('should handle edge case at closing time', () => {
      // Exactly 5:00 PM Chicago time
      const currentTime = dayjs.tz('2024-01-15 17:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(false);
      expect(result.currentStatus).toBe('closed');
    });

    it('should return unknown status for invalid office hours', () => {
      const invalidOfficeHours = {};

      const currentTime = dayjs.tz('2024-01-15 10:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours(invalidOfficeHours, timezone, currentTime);

      expect(result.isOpen).toBe(false);
      expect(result.currentStatus).toBe('unknown');
      expect(result.timezone).toBe(timezone);
    });

    it('should use current time when not provided', () => {
      // Use fake timers to control current time
      jest.useFakeTimers();
      // Set time to Monday, January 15, 2024 at 10:00 AM Chicago time
      const mockTime = dayjs.tz('2024-01-15 10:00', timezone).toDate();
      jest.setSystemTime(mockTime);

      const result = OfficeHoursService.checkOfficeHours(mockOfficeHours, timezone);

      expect(result.isOpen).toBe(true);
      expect(result.currentStatus).toBe('open');

      jest.useRealTimers();
    });
  });

  describe('getNextBusinessDay', () => {
    it('should return next business day when closed on weekends', () => {
      // Saturday
      const currentTime = dayjs.tz('2024-01-20 10:00', timezone).toDate();

      const result = OfficeHoursService.getNextBusinessDay(mockOfficeHours, timezone, currentTime);

      expect(result).toBeDefined();
      expect(result?.date).toBe('2024-01-22'); // Monday
      expect(result?.hours).toEqual({ start: '09:00', end: '17:00' });
    });

    it('should return null when no business days found', () => {
      const noBusinessDays = {
        '1': null,
        '2': null,
        '3': null,
        '4': null,
        '5': null,
        '6': null,
        '7': null,
      };

      const currentTime = dayjs.tz('2024-01-15 10:00', timezone).toDate();

      const result = OfficeHoursService.getNextBusinessDay(noBusinessDays, timezone, currentTime);

      expect(result).toBeNull();
    });

    it('should handle invalid timezone gracefully', () => {
      const currentTime = new Date();

      const result = OfficeHoursService.getNextBusinessDay(
        mockOfficeHours,
        'Invalid/Timezone',
        currentTime,
      );

      expect(result).toBeNull();
    });
  });

  describe('validateOfficeHours', () => {
    it('should validate correct office hours format', () => {
      const isValid = OfficeHoursService.validateOfficeHours(mockOfficeHours);

      expect(isValid).toBe(true);
    });

    it('should reject invalid day keys', () => {
      const invalidHours = {
        '0': { start: '09:00', end: '17:00' }, // Invalid day (should be 1-7)
        '8': { start: '09:00', end: '17:00' }, // Invalid day (should be 1-7)
      };

      const isValid = OfficeHoursService.validateOfficeHours(invalidHours);

      expect(isValid).toBe(false);
    });

    it('should reject invalid time format', () => {
      const invalidHours = {
        '1': { start: '25:00', end: '17:00' }, // Invalid hour
        '2': { start: '09:00', end: '17:60' }, // Invalid minute
      };

      const isValid = OfficeHoursService.validateOfficeHours(invalidHours);

      expect(isValid).toBe(false);
    });

    it('should reject start time after end time', () => {
      const invalidHours = {
        '1': { start: '18:00', end: '17:00' }, // Start after end
      };

      const isValid = OfficeHoursService.validateOfficeHours(invalidHours);

      expect(isValid).toBe(false);
    });

    it('should accept null values for closed days', () => {
      const validHours = {
        '1': { start: '09:00', end: '17:00' },
        '6': null,
        '7': null,
      };

      const isValid = OfficeHoursService.validateOfficeHours(validHours);

      expect(isValid).toBe(true);
    });
  });

  describe('validateOfficeHoursWithErrors', () => {
    it('should return detailed validation errors', () => {
      const invalidHours = {
        '0': { start: '09:00', end: '17:00' }, // Invalid day
        '1': { start: '25:00', end: '17:00' }, // Invalid start time
        '2': { start: '09:00', end: '17:60' }, // Invalid end time
        '3': { start: '18:00', end: '17:00' }, // Start after end
        '4': { start: '09:00' } as any, // Missing end time
        '5': null, // Valid closed day
      };

      const result = OfficeHoursService.validateOfficeHoursWithErrors(invalidHours);

      expect(result.isValid).toBe(false);
      expect(result.errors).toEqual(
        expect.arrayContaining([
          'Invalid day: 0. Must be 1-7 (Monday-Sunday)',
          'Day 1: Invalid start time format. Use HH:MM (24-hour format)',
          'Day 2: Invalid end time format. Use HH:MM (24-hour format)',
          'Day 3: Start time must be before end time',
          'Day 4: Both start and end times required',
        ]),
      );
    });

    it('should return valid result for correct office hours', () => {
      const result = OfficeHoursService.validateOfficeHoursWithErrors(mockOfficeHours);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should handle malformed hours objects', () => {
      const malformedHours = {
        '1': 'invalid' as any, // Not an object
        '2': { start: '09:00' } as any, // Missing end
        '3': {} as any, // Empty object
      };

      const result = OfficeHoursService.validateOfficeHoursWithErrors(malformedHours);

      expect(result.isValid).toBe(false);
      expect(result.errors).toEqual(
        expect.arrayContaining([
          'Day 1: Hours must be an object with start and end times',
          'Day 2: Both start and end times required',
          'Day 3: Both start and end times required',
        ]),
      );
    });
  });

  describe('validateTimezone', () => {
    it('should validate correct timezone strings', () => {
      expect(OfficeHoursService.validateTimezone('America/Chicago')).toBe(true);
      expect(OfficeHoursService.validateTimezone('Europe/London')).toBe(true);
      expect(OfficeHoursService.validateTimezone('Asia/Tokyo')).toBe(true);
      expect(OfficeHoursService.validateTimezone('UTC')).toBe(true);
    });

    it('should reject invalid timezone strings', () => {
      expect(OfficeHoursService.validateTimezone('Invalid/Timezone')).toBe(false);
      expect(OfficeHoursService.validateTimezone('Not-A-Timezone')).toBe(false);
      expect(OfficeHoursService.validateTimezone('')).toBe(false);
    });
  });

  describe('getOfficeHoursForUI', () => {
    it('should return UI-formatted office hours data', () => {
      const result = OfficeHoursService.getOfficeHoursForUI(mockOfficeHours, timezone);

      expect(result.raw).toEqual(mockOfficeHours);
      expect(result.locationTimezone).toBe(timezone);
      expect(result.timezoneLabel).toContain('America/Chicago');
      expect(result.formatted.Monday).toBe('9:00 AM - 5:00 PM');
      expect(result.formatted.Saturday).toBe('Closed');
      expect(result.formatted.Sunday).toBe('Closed');
    });

    it('should handle invalid timezone gracefully', () => {
      const result = OfficeHoursService.getOfficeHoursForUI(mockOfficeHours, 'Invalid/Timezone');

      expect(result.raw).toEqual(mockOfficeHours);
      expect(result.locationTimezone).toBe('Invalid/Timezone');
      expect(result.timezoneLabel).toBe('Invalid/Timezone'); // Falls back to timezone string
    });
  });

  describe('formatOfficeHoursForDisplay', () => {
    it('should format office hours for display', () => {
      const formatted = OfficeHoursService.formatOfficeHoursForDisplay(mockOfficeHours, timezone);

      expect(formatted).toBeDefined();
      expect(formatted.Monday).toBe('9:00 AM - 5:00 PM');
      expect(formatted.Saturday).toBe('Closed');
      expect(formatted.Sunday).toBe('Closed');
    });

    it('should handle 24-hour format', () => {
      const hours24 = {
        '1': { start: '08:30', end: '16:30' },
      };

      const formatted = OfficeHoursService.formatOfficeHoursForDisplay(hours24, timezone);

      expect(formatted.Monday).toBe('8:30 AM - 4:30 PM');
    });

    it('should handle midnight hours', () => {
      const midnightHours = {
        '1': { start: '00:00', end: '23:59' },
      };

      const formatted = OfficeHoursService.formatOfficeHoursForDisplay(midnightHours, timezone);

      expect(formatted.Monday).toBe('12:00 AM - 11:59 PM');
    });
  });

  describe('edge cases and error handling', () => {
    it('should handle invalid timezone gracefully', () => {
      const currentTime = new Date();

      const result = OfficeHoursService.checkOfficeHours(
        mockOfficeHours,
        'Invalid/Timezone',
        currentTime,
      );

      expect(result.currentStatus).toBe('unknown');
      expect(result.isOpen).toBe(false);
    });

    it('should handle empty office hours object', () => {
      const currentTime = dayjs.tz('2024-01-15 10:00', timezone).toDate();

      const result = OfficeHoursService.checkOfficeHours({}, timezone, currentTime);

      expect(result.currentStatus).toBe('unknown');
      expect(result.isOpen).toBe(false);
    });

    it('should handle malformed time strings', () => {
      const malformedHours = {
        '1': { start: 'invalid', end: 'time' },
      };

      const isValid = OfficeHoursService.validateOfficeHours(malformedHours);

      expect(isValid).toBe(false);
    });
  });
});
