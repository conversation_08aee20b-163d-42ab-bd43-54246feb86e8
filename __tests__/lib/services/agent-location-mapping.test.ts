/**
 * @file Unit tests for AgentLocationMappingService
 */

// Mock Firebase Admin first
jest.mock('firebase-admin', () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(),
    Timestamp: {
      now: jest.fn(() => ({
        seconds: 1640995200,
        nanoseconds: 0,
        toDate: () => new Date(1640995200000),
      })),
    },
  })),
}));

// Mock firebase-admin/firestore
jest.mock('firebase-admin/firestore', () => ({
  Timestamp: {
    now: jest.fn(() => ({
      seconds: 1640995200,
      nanoseconds: 0,
      toDate: () => new Date(1640995200000),
    })),
  },
}));

import admin from 'firebase-admin';
import { AgentLocationMappingService } from '../../../lib/services/agent-location-mapping';
import { LocationService } from '../../../lib/services/locationService';

// Mock LocationService
jest.mock('../../../lib/services/locationService');

describe('AgentLocationMappingService', () => {
  // Global mocks for Firebase operations
  const mockGet = jest.fn();
  const mockSet = jest.fn();
  const mockUpdate = jest.fn();
  const mockDelete = jest.fn();
  const mockQueryGet = jest.fn();
  const mockLimit = jest.fn();
  const mockWhere = jest.fn();
  const mockWhere2 = jest.fn();
  const mockDoc = jest.fn();
  const mockCollection = jest.fn();

  // Mock Timestamp
  const mockTimestamp = {
    seconds: 1640995200,
    nanoseconds: 0,
    toDate: () => new Date(1640995200000),
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Reset mockGet to default behavior (important for tests that use mockImplementation)
    mockGet.mockReset();

    // Setup query chain mocks
    mockLimit.mockReturnValue({
      get: mockQueryGet,
    });

    mockWhere2.mockReturnValue({
      limit: mockLimit,
      get: mockQueryGet, // Add get method here too for queries without limit
    });

    mockWhere.mockReturnValue({
      where: mockWhere2,
    });

    mockDoc.mockReturnValue({
      get: mockGet,
      set: mockSet,
      update: mockUpdate,
      delete: mockDelete,
    });

    mockCollection.mockReturnValue({
      doc: mockDoc,
      where: mockWhere,
    });

    // Direct service property mocking
    (AgentLocationMappingService as any).db = {
      collection: mockCollection,
    };

    // Mock admin.firestore.Timestamp.now
    (admin.firestore as any).Timestamp = {
      now: () => mockTimestamp,
    };
  });

  describe('getLocationByAgentId', () => {
    it('should return location when agent mapping exists', async () => {
      const mockMapping = {
        agentId: 'agent-001',
        locationId: 'location-001',
        clinicId: 1,
        isActive: true,
      };

      const mockLocation = {
        id: 'location-001',
        name: 'Test Location',
        clinicId: 1,
      };

      // Mock the query result
      mockQueryGet.mockResolvedValue({
        empty: false,
        docs: [
          {
            data: () => mockMapping,
          },
        ],
      });

      (LocationService.getLocationById as jest.Mock).mockResolvedValue(mockLocation);

      const result = await AgentLocationMappingService.getLocationByAgentId('agent-001');

      expect(mockCollection).toHaveBeenCalledWith('agent-location-mappings');
      expect(mockWhere).toHaveBeenCalledWith('agentId', '==', 'agent-001');
      expect(mockWhere2).toHaveBeenCalledWith('isActive', '==', true);
      expect(LocationService.getLocationById).toHaveBeenCalledWith('location-001', 1);
      expect(result).toEqual(mockLocation);
    });

    it('should return null when agent mapping does not exist', async () => {
      // Mock empty query result
      mockQueryGet.mockResolvedValue({
        empty: true,
        docs: [],
      });

      const result = await AgentLocationMappingService.getLocationByAgentId('nonexistent-agent');

      expect(result).toBeNull();
      expect(LocationService.getLocationById).not.toHaveBeenCalled();
    });

    it('should return null when mapping is inactive', async () => {
      // Mock query for inactive mapping
      mockQueryGet.mockResolvedValue({
        empty: true,
        docs: [],
      });

      const result = await AgentLocationMappingService.getLocationByAgentId('agent-001');

      expect(result).toBeNull();
      expect(LocationService.getLocationById).not.toHaveBeenCalled();
    });

    it('should handle Firestore errors', async () => {
      const error = new Error('Firestore error');
      mockQueryGet.mockRejectedValue(error);

      await expect(AgentLocationMappingService.getLocationByAgentId('agent-001')).rejects.toThrow(
        'Firestore error',
      );
    });
  });

  describe('createMapping', () => {
    it('should create a new agent-location mapping', async () => {
      const mockLocation = {
        id: 'location-001',
        name: 'Test Location',
        clinicId: 1,
      };

      // Mock location document lookup
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({ clinicId: 1 }),
      });

      // Mock getMappingByAgentId (should return null for new mapping)
      mockGet.mockResolvedValueOnce({
        exists: false,
      });

      (LocationService.getLocationById as jest.Mock).mockResolvedValue(mockLocation);
      mockSet.mockResolvedValue(undefined);

      const result = await AgentLocationMappingService.createMapping('agent-001', 'location-001');

      expect(LocationService.getLocationById).toHaveBeenCalledWith('location-001', 1);
      expect(mockSet).toHaveBeenCalledWith({
        agentId: 'agent-001',
        locationId: 'location-001',
        clinicId: 1,
        isActive: true,
        createdAt: expect.any(Object),
        updatedAt: expect.any(Object),
      });
      expect(result).toEqual(
        expect.objectContaining({
          agentId: 'agent-001',
          locationId: 'location-001',
          clinicId: 1,
          isActive: true,
        }),
      );
    });

    it('should throw error when location does not exist', async () => {
      mockGet.mockResolvedValue({
        exists: false,
      });

      await expect(
        AgentLocationMappingService.createMapping('agent-001', 'nonexistent-location'),
      ).rejects.toThrow('Location nonexistent-location not found');
    });

    it('should throw error when agent already has an active mapping', async () => {
      // Mock location exists
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({ clinicId: 1 }),
      });

      // Mock existing active mapping
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          agentId: 'agent-001',
          locationId: 'existing-location',
          clinicId: 1,
          isActive: true,
          createdAt: mockTimestamp,
          updatedAt: mockTimestamp,
        }),
      });

      await expect(
        AgentLocationMappingService.createMapping('agent-001', 'location-001'),
      ).rejects.toThrow('Agent agent-001 is already mapped to location existing-location');
    });

    it('should handle Firestore errors during creation', async () => {
      // Mock location exists
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({ clinicId: 1 }),
      });

      // Mock no existing mapping
      mockGet.mockResolvedValueOnce({
        exists: false,
      });

      const error = new Error('Firestore error');
      mockSet.mockRejectedValue(error);

      (LocationService.getLocationById as jest.Mock).mockResolvedValue({
        id: 'location-001',
        name: 'Test Location',
        clinicId: 1,
      });

      await expect(
        AgentLocationMappingService.createMapping('agent-001', 'location-001'),
      ).rejects.toThrow('Firestore error');
    });
  });

  describe('updateMapping', () => {
    it('should update existing agent-location mapping', async () => {
      const mockUpdatedData = {
        agentId: 'agent-001',
        locationId: 'location-001',
        clinicId: 1,
        isActive: true,
        createdAt: mockTimestamp,
        updatedAt: mockTimestamp,
      };

      // Mock location exists
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({ clinicId: 1 }),
      });

      // Mock existing mapping
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          agentId: 'agent-001',
          locationId: 'old-location',
          clinicId: 1,
          isActive: true,
          createdAt: mockTimestamp,
          updatedAt: mockTimestamp,
        }),
      });

      // Mock updated document
      mockGet.mockResolvedValueOnce({
        exists: true,
        data: () => mockUpdatedData,
      });

      mockUpdate.mockResolvedValue(undefined);

      const result = await AgentLocationMappingService.updateMapping('agent-001', 'location-001');

      expect(mockUpdate).toHaveBeenCalledWith({
        locationId: 'location-001',
        clinicId: 1,
        updatedAt: expect.any(Object),
      });
      expect(result).toEqual(
        expect.objectContaining({
          agentId: 'agent-001',
          locationId: 'location-001',
          clinicId: 1,
          isActive: true,
        }),
      );
    });

    it('should throw error when mapping does not exist', async () => {
      // Setup mock to handle different collections
      let callCount = 0;
      mockGet.mockImplementation(() => {
        callCount++;
        if (callCount === 1) {
          // First call: location document (should exist)
          return Promise.resolve({
            exists: true,
            data: () => ({ clinicId: 1 }),
          });
        } else {
          // Second call: agent mapping document (should not exist)
          return Promise.resolve({
            exists: false,
          });
        }
      });

      (LocationService.getLocationById as jest.Mock).mockResolvedValue({
        id: 'location-001',
        name: 'Test Location',
        clinicId: 1,
      });

      await expect(
        AgentLocationMappingService.updateMapping('nonexistent-agent', 'location-001'),
      ).rejects.toThrow('No mapping found for agent nonexistent-agent');
    });
  });

  describe('deleteMapping', () => {
    it('should hard delete agent-location mapping', async () => {
      // Mock existing mapping
      mockGet.mockResolvedValue({
        exists: true,
        data: () => ({
          agentId: 'agent-001',
          locationId: 'location-001',
          isActive: true,
        }),
      });

      mockDelete.mockResolvedValue(undefined);

      await AgentLocationMappingService.deleteMapping('agent-001');

      expect(mockDelete).toHaveBeenCalled();
    });

    it('should throw error when mapping does not exist', async () => {
      mockGet.mockResolvedValue({
        exists: false,
      });

      await expect(AgentLocationMappingService.deleteMapping('nonexistent-agent')).rejects.toThrow(
        'No mapping found for agent nonexistent-agent',
      );
    });
  });

  describe('getMappingsByClinic', () => {
    it('should return mappings for a clinic', async () => {
      const mockMappings = [
        {
          agentId: 'agent-001',
          locationId: 'location-001',
          clinicId: 1,
          isActive: true,
          createdAt: mockTimestamp,
          updatedAt: mockTimestamp,
        },
        {
          agentId: 'agent-002',
          locationId: 'location-002',
          clinicId: 1,
          isActive: true,
          createdAt: mockTimestamp,
          updatedAt: mockTimestamp,
        },
      ];

      mockQueryGet.mockResolvedValue({
        empty: false,
        docs: mockMappings.map(mapping => ({
          data: () => mapping,
        })),
      });

      const result = await AgentLocationMappingService.getMappingsByClinic(1);

      expect(mockCollection).toHaveBeenCalledWith('agent-location-mappings');
      expect(mockWhere).toHaveBeenCalledWith('clinicId', '==', 1);
      expect(mockWhere2).toHaveBeenCalledWith('isActive', '==', true);
      expect(result).toEqual([
        {
          agentId: 'agent-001',
          locationId: 'location-001',
          clinicId: 1,
          isActive: true,
          createdAt: new Date(1640995200000),
          updatedAt: new Date(1640995200000),
        },
        {
          agentId: 'agent-002',
          locationId: 'location-002',
          clinicId: 1,
          isActive: true,
          createdAt: new Date(1640995200000),
          updatedAt: new Date(1640995200000),
        },
      ]);
    });

    it('should return empty array when no mappings found', async () => {
      mockQueryGet.mockResolvedValue({
        empty: true,
        docs: [],
      });

      const result = await AgentLocationMappingService.getMappingsByClinic(999);

      expect(result).toEqual([]);
    });
  });
});
