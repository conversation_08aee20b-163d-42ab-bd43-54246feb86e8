import { DashboardMetricsService } from '@/lib/services/dashboard-metrics-service';
import { CallsRepository } from '@/lib/repositories';

jest.mock('../../../lib/repositories/calls-repository');

describe('DashboardMetricsService', () => {
  const MockedCallsRepo = CallsRepository as jest.MockedClass<typeof CallsRepository>;

  beforeEach(() => {
    MockedCallsRepo.mockClear();
  });

  it('computes summary correctly', async () => {
    // Arrange mocks
    const countCalls = jest.fn().mockResolvedValue(10);
    const sumDurations = jest.fn().mockResolvedValue(600); // 10 min
    const appointmentCounts = jest
      .fn()
      .mockResolvedValue({ reschedule: 2, newAppointment: 5, cancel: 3 });
    const issuesCounts = jest
      .fn()
      .mockResolvedValue({ 'technical-issue': 1, 'billing-issue': 2, 'appointment-issue': 3 });

    const mockCallsRepo = {
      countCallsInRange: countCalls,
      sumDurationsInRange: sumDurations,
      countAppointmentsHandledInRange: appointmentCounts,
      countAllCallTypesInRange: issuesCounts,
    } as unknown as CallsRepository;

    const service = new DashboardMetricsService(mockCallsRepo);

    // Act
    const result = await service.getSummary('today');

    // Assert
    expect(result).toEqual({
      totalCalls: 10,
      totalTimeSavedSeconds: 600,
      avgTimeSavedSeconds: 60, // 600 / 10
      appointments: {
        reschedule: 2,
        newAppointment: 5,
        cancel: 3,
      },
      issues: {
        'technical-issue': 1,
        'billing-issue': 2,
        'appointment-issue': 3,
      },
    });

    expect(countCalls).toHaveBeenCalledTimes(1);
    expect(sumDurations).toHaveBeenCalledTimes(1);
    expect(appointmentCounts).toHaveBeenCalledTimes(1);
    expect(issuesCounts).toHaveBeenCalledTimes(1);
  });
});
