import { PatientReferenceService } from '@/lib/services/patient-reference-service';
import { PatientReference } from '@/lib/models/patient-reference';
import admin from '@/utils/firebase-admin';

// Mock Firebase Admin
jest.mock('@/utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
    update: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn().mockReturnValue('mock-uuid'),
}));

describe('PatientReferenceService', () => {
  let service: PatientReferenceService;
  let mockFirestore: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Get the mock Firestore instance
    mockFirestore = admin.firestore();

    // Create a new service instance
    service = new PatientReferenceService();
  });

  describe('storeNewPatient', () => {
    it('should store a new patient reference', async () => {
      // Arrange
      const patientData = {
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
      };

      // Mock the Firestore set method
      mockFirestore.set.mockResolvedValueOnce({});

      // Act
      const result = await service.storeNewPatient(patientData);

      // Assert
      expect(mockFirestore.collection).toHaveBeenCalledWith('patientReferences');
      expect(mockFirestore.doc).toHaveBeenCalledWith('mock-uuid');
      expect(mockFirestore.set).toHaveBeenCalled();

      // Check that the result has the expected properties
      expect(result).toEqual({
        ...patientData,
        id: 'mock-uuid',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should handle errors when storing a patient reference', async () => {
      // Arrange
      const patientData = {
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
      };

      // Mock the Firestore set method to throw an error
      const error = new Error('Firestore error');
      mockFirestore.set.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.storeNewPatient(patientData)).rejects.toThrow('Firestore error');
    });
  });

  describe('findByProviderId', () => {
    it('should find a patient reference by provider ID', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the Firestore query response
      const mockDoc = {
        id: 'mock-uuid',
        data: () => ({
          provider,
          providerId,
          phoneNumber: '************',
          createdAt: { toDate: () => new Date() },
          updatedAt: { toDate: () => new Date() },
        }),
      };

      mockFirestore.get.mockResolvedValueOnce({
        empty: false,
        docs: [mockDoc],
      });

      // Act
      const result = await service.findByProviderId(provider, providerId);

      // Assert
      expect(mockFirestore.collection).toHaveBeenCalledWith('patientReferences');
      expect(mockFirestore.where).toHaveBeenCalledWith('provider', '==', provider);
      expect(mockFirestore.where).toHaveBeenCalledWith('providerId', '==', providerId);
      expect(mockFirestore.limit).toHaveBeenCalledWith(1);
      expect(mockFirestore.get).toHaveBeenCalled();

      // Check that the result has the expected properties
      expect(result).toEqual({
        id: 'mock-uuid',
        provider,
        providerId,
        phoneNumber: '************',
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should return null if no patient reference is found', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the Firestore query response
      mockFirestore.get.mockResolvedValueOnce({
        empty: true,
        docs: [],
      });

      // Act
      const result = await service.findByProviderId(provider, providerId);

      // Assert
      expect(result).toBeNull();
    });

    it('should handle errors when finding a patient reference', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the Firestore get method to throw an error
      const error = new Error('Firestore error');
      mockFirestore.get.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.findByProviderId(provider, providerId)).rejects.toThrow(
        'Firestore error',
      );
    });
  });

  describe('updateIfChanged', () => {
    it('should update a patient reference if data has changed', async () => {
      // Arrange
      const patient: PatientReference = {
        id: 'mock-uuid',
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the Firestore get method
      mockFirestore.get.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          ...patient,
          phoneNumber: '************', // Different phone number to trigger update
        }),
      });

      // Mock the Firestore update method
      mockFirestore.update.mockResolvedValueOnce({});

      // Act
      const result = await service.updateIfChanged(patient);

      // Assert
      expect(mockFirestore.collection).toHaveBeenCalledWith('patientReferences');
      expect(mockFirestore.doc).toHaveBeenCalledWith('mock-uuid');
      expect(mockFirestore.get).toHaveBeenCalled();
      expect(mockFirestore.update).toHaveBeenCalled();

      // Check that the result has the expected properties
      expect(result).toEqual({
        ...patient,
        updatedAt: expect.any(Date),
      });
    });

    it('should not update a patient reference if data has not changed', async () => {
      // Arrange
      const patient: PatientReference = {
        id: 'mock-uuid',
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the Firestore get method
      mockFirestore.get.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          ...patient,
        }),
      });

      // Act
      const result = await service.updateIfChanged(patient);

      // Assert
      expect(mockFirestore.update).not.toHaveBeenCalled();
      expect(result).toEqual(patient);
    });

    it('should throw an error if the patient reference is not found', async () => {
      // Arrange
      const patient: PatientReference = {
        id: 'mock-uuid',
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the Firestore get method
      mockFirestore.get.mockResolvedValueOnce({
        exists: false,
      });

      // Act & Assert
      await expect(service.updateIfChanged(patient)).rejects.toThrow(
        'Patient reference with ID mock-uuid not found',
      );
    });

    it('should handle errors when updating a patient reference', async () => {
      // Arrange
      const patient: PatientReference = {
        id: 'mock-uuid',
        provider: 'nextech',
        providerId: 'ext-123',
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the Firestore get method
      mockFirestore.get.mockResolvedValueOnce({
        exists: true,
        data: () => ({
          ...patient,
          phoneNumber: '************', // Different phone number to trigger update
        }),
      });

      // Mock the Firestore update method to throw an error
      const error = new Error('Firestore error');
      mockFirestore.update.mockRejectedValueOnce(error);

      // Act & Assert
      await expect(service.updateIfChanged(patient)).rejects.toThrow('Firestore error');
    });
  });
});
