// Mock Firebase Admin before importing any modules that use it
jest.mock('firebase-admin', () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        id: 'mock-generated-id',
      })),
    })),
    doc: jest.fn(),
    batch: jest.fn(),
    runTransaction: jest.fn(),
  })),
  initializeApp: jest.fn(),
  apps: [{ name: 'test-app' }], // Mock that admin is initialized
}));

// Mock MySQL service to avoid database connection issues
jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    healthCheck: jest.fn().mockResolvedValue(true),
    testConnection: jest.fn().mockResolvedValue(true),
    checkConnection: jest.fn().mockResolvedValue(true),
    getKnex: jest.fn(),
    query: jest.fn(),
    queryWithVariables: jest.fn(),
  },
}));

import {
  BaseDualDatabaseService,
  BaseEntity,
  DualDatabaseError,
  DualDatabaseUtils,
} from '@/lib/database/dual-database-service';
import {
  getDatabaseConfig,
  updateDatabaseConfig,
  resetDatabaseConfig,
  DatabaseFeatures,
  validateDatabaseConfig,
  getReadStrategy,
  getWriteStrategy,
  DatabaseMetricsCollector,
} from '@/lib/database/database-config';
import { FindCriteria, PaginatedResult } from '@/lib/database/unit-of-work';
import { BaseRepository } from '@/lib/database/base-repository';

// Test entity interface
interface TestEntity extends BaseEntity {
  name: string;
  value: number;
  active: boolean;
}

// Mock service for testing without database dependencies
class MockDualDatabaseService extends BaseDualDatabaseService<TestEntity> {
  private mockMySQLData: Map<string, TestEntity> = new Map();
  private mockFirestoreData: Map<string, TestEntity> = new Map();
  public shouldFailMySQL = false;
  public shouldFailFirestore = false;

  constructor() {
    super('testEntities', 'test_entities');
  }

  protected async createInMySQL(entity: TestEntity): Promise<TestEntity> {
    if (this.shouldFailMySQL) throw new Error('MySQL create failed');
    this.mockMySQLData.set(entity.id, entity);
    return entity;
  }

  protected async createInFirestore(entity: TestEntity): Promise<TestEntity> {
    if (this.shouldFailFirestore) throw new Error('Firestore create failed');
    this.mockFirestoreData.set(entity.id, entity);
    return entity;
  }

  protected async updateInMySQL(id: string, updates: Partial<TestEntity>): Promise<TestEntity> {
    if (this.shouldFailMySQL) throw new Error('MySQL update failed');
    const existing = this.mockMySQLData.get(id);
    if (!existing) throw new Error('Entity not found');
    const updated = { ...existing, ...updates };
    this.mockMySQLData.set(id, updated);
    return updated;
  }

  protected async updateInFirestore(id: string, updates: Partial<TestEntity>): Promise<TestEntity> {
    if (this.shouldFailFirestore) throw new Error('Firestore update failed');
    const existing = this.mockFirestoreData.get(id);
    if (!existing) throw new Error('Entity not found');
    const updated = { ...existing, ...updates };
    this.mockFirestoreData.set(id, updated);
    return updated;
  }

  protected async deleteInMySQL(id: string): Promise<void> {
    if (this.shouldFailMySQL) throw new Error('MySQL delete failed');
    this.mockMySQLData.delete(id);
  }

  protected async deleteInFirestore(id: string): Promise<void> {
    if (this.shouldFailFirestore) throw new Error('Firestore delete failed');
    this.mockFirestoreData.delete(id);
  }

  protected async findByIdInMySQL(id: string): Promise<TestEntity | null> {
    if (this.shouldFailMySQL) throw new Error('MySQL findById failed');
    return this.mockMySQLData.get(id) || null;
  }

  protected async findByIdInFirestore(id: string): Promise<TestEntity | null> {
    if (this.shouldFailFirestore) throw new Error('Firestore findById failed');
    return this.mockFirestoreData.get(id) || null;
  }

  protected async findManyInMySQL(
    criteria: FindCriteria<TestEntity>,
  ): Promise<PaginatedResult<TestEntity>> {
    if (this.shouldFailMySQL) throw new Error('MySQL findMany failed');
    const items = Array.from(this.mockMySQLData.values()).slice(0, criteria.limit || 10);
    return {
      items,
      hasMore: false,
    };
  }

  protected async findManyInFirestore(
    criteria: FindCriteria<TestEntity>,
  ): Promise<PaginatedResult<TestEntity>> {
    if (this.shouldFailFirestore) throw new Error('Firestore findMany failed');
    const items = Array.from(this.mockFirestoreData.values()).slice(0, criteria.limit || 10);
    return {
      items,
      hasMore: false,
    };
  }

  // Helper methods for testing
  getMySQLData(): Map<string, TestEntity> {
    return this.mockMySQLData;
  }

  getFirestoreData(): Map<string, TestEntity> {
    return this.mockFirestoreData;
  }

  clearData(): void {
    this.mockMySQLData.clear();
    this.mockFirestoreData.clear();
  }

  setFailureFlags(mysql: boolean, firestore: boolean): void {
    this.shouldFailMySQL = mysql;
    this.shouldFailFirestore = firestore;
  }

  // Override validation to match test expectations
  validateEntity(entity: Partial<TestEntity>): boolean {
    if (!entity) return false;
    return entity.name !== undefined && entity.name !== '' && entity.name!.length > 0;
  }
}

describe('Dual Database Service', () => {
  let service: MockDualDatabaseService;
  const testEntity: Omit<TestEntity, 'id'> = {
    name: 'Test Entity',
    value: 42,
    active: true,
  };

  beforeEach(() => {
    service = new MockDualDatabaseService();
    service.clearData();
    service.setFailureFlags(false, false);
    resetDatabaseConfig();
    DatabaseMetricsCollector.clearMetrics();
  });

  afterEach(() => {
    resetDatabaseConfig();
  });

  describe('Database Configuration', () => {
    it('should have default configuration', () => {
      const config = getDatabaseConfig();
      expect(config.primaryDatabase).toBe('mysql');
      expect(config.enableFallback).toBe(true);
      expect(config.dualWriteEnabled).toBe(true);
    });

    it('should validate configuration correctly', () => {
      const validation = validateDatabaseConfig();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should update configuration at runtime', () => {
      updateDatabaseConfig({ primaryDatabase: 'firestore' });
      const config = getDatabaseConfig();
      expect(config.primaryDatabase).toBe('firestore');
    });

    it('should provide feature flag helpers', () => {
      expect(DatabaseFeatures.isMySQLPrimary()).toBe(true);
      expect(DatabaseFeatures.isDualWriteEnabled()).toBe(true);
      expect(DatabaseFeatures.isFallbackEnabled()).toBe(true);
      expect(DatabaseFeatures.isFirestoreOnly()).toBe(false);
    });
  });

  describe('Read Strategy', () => {
    it('should use MySQL first by default', () => {
      const strategy = getReadStrategy();
      expect(strategy.useMySQL).toBe(true);
      expect(strategy.useFirestore).toBe(true);
      expect(strategy.fallbackEnabled).toBe(true);
    });

    it('should force MySQL when specified', () => {
      const strategy = getReadStrategy({ forceMySQL: true });
      expect(strategy.useMySQL).toBe(true);
      expect(strategy.useFirestore).toBe(false);
      expect(strategy.fallbackEnabled).toBe(false);
    });

    it('should force Firestore when specified', () => {
      const strategy = getReadStrategy({ forceFirestore: true });
      expect(strategy.useMySQL).toBe(false);
      expect(strategy.useFirestore).toBe(true);
      expect(strategy.fallbackEnabled).toBe(false);
    });

    it('should handle Firestore-only mode', () => {
      updateDatabaseConfig({ firestoreOnly: true });
      const strategy = getReadStrategy();
      expect(strategy.useMySQL).toBe(false);
      expect(strategy.useFirestore).toBe(true);
      expect(strategy.fallbackEnabled).toBe(false);
    });
  });

  describe('Write Strategy', () => {
    it('should dual-write by default', () => {
      const strategy = getWriteStrategy();
      expect(strategy.writeToMySQL).toBe(true);
      expect(strategy.writeToFirestore).toBe(true);
      expect(strategy.useTransaction).toBe(true);
    });

    it('should skip MySQL when specified', () => {
      const strategy = getWriteStrategy({ skipMySQL: true });
      expect(strategy.writeToMySQL).toBe(false);
      expect(strategy.writeToFirestore).toBe(true);
      expect(strategy.useTransaction).toBe(false);
    });

    it('should skip Firestore when specified', () => {
      const strategy = getWriteStrategy({ skipFirestore: true });
      expect(strategy.writeToMySQL).toBe(true);
      expect(strategy.writeToFirestore).toBe(false);
      expect(strategy.useTransaction).toBe(false);
    });

    it('should handle Firestore-only mode', () => {
      updateDatabaseConfig({ firestoreOnly: true });
      const strategy = getWriteStrategy();
      expect(strategy.writeToMySQL).toBe(false);
      expect(strategy.writeToFirestore).toBe(true);
      expect(strategy.useTransaction).toBe(false);
    });
  });

  describe('Create Operations', () => {
    it('should dual-write by default', async () => {
      const result = await service.create(testEntity);

      expect(result).toBeDefined();
      expect(result.name).toBe(testEntity.name);
      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();

      // Check both databases
      expect(service.getMySQLData().has(result.id)).toBe(true);
      expect(service.getFirestoreData().has(result.id)).toBe(true);
    });

    it('should handle MySQL-only writes', async () => {
      const result = await service.create(testEntity, { skipFirestore: true });

      expect(service.getMySQLData().has(result.id)).toBe(true);
      expect(service.getFirestoreData().has(result.id)).toBe(false);
    });

    it('should handle Firestore-only writes', async () => {
      const result = await service.create(testEntity, { skipMySQL: true });

      expect(service.getMySQLData().has(result.id)).toBe(false);
      expect(service.getFirestoreData().has(result.id)).toBe(true);
    });

    it('should fail if MySQL write required but fails', async () => {
      service.setFailureFlags(true, false);

      await expect(service.create(testEntity)).rejects.toThrow(DualDatabaseError);
    });

    it('should fail if Firestore write required but fails', async () => {
      service.setFailureFlags(false, true);

      await expect(service.create(testEntity)).rejects.toThrow(DualDatabaseError);
    });

    it('should validate entity before creating', async () => {
      const invalidEntity = { ...testEntity, name: '' };

      await expect(service.create(invalidEntity)).rejects.toThrow(DualDatabaseError);
    });
  });

  describe('Read Operations', () => {
    beforeEach(async () => {
      // Set up test data in both databases
      const entity = await service.create(testEntity);
      service.setFailureFlags(false, false);
    });

    it('should read from MySQL first', async () => {
      const entities = Array.from(service.getMySQLData().values());
      const testId = entities[0].id;

      const result = await service.findById(testId);
      expect(result).toBeDefined();
      expect(result?.name).toBe(testEntity.name);
    });

    it('should fallback to Firestore if MySQL fails', async () => {
      const entities = Array.from(service.getFirestoreData().values());
      const testId = entities[0].id;

      // Clear MySQL data to simulate MySQL failure
      service.getMySQLData().clear();

      const result = await service.findById(testId);
      expect(result).toBeDefined();
      expect(result?.name).toBe(testEntity.name);
    });

    it('should handle force MySQL reads', async () => {
      const entities = Array.from(service.getMySQLData().values());
      const testId = entities[0].id;

      const result = await service.findById(testId, { forceMySQL: true });
      expect(result).toBeDefined();
    });

    it('should handle force Firestore reads', async () => {
      const entities = Array.from(service.getFirestoreData().values());
      const testId = entities[0].id;

      const result = await service.findById(testId, { forceFirestore: true });
      expect(result).toBeDefined();
    });

    it('should return null if entity not found', async () => {
      const result = await service.findById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('Update Operations', () => {
    let entityId: string;

    beforeEach(async () => {
      const entity = await service.create(testEntity);
      entityId = entity.id;
      service.setFailureFlags(false, false);
    });

    it('should dual-update by default', async () => {
      const updates = { name: 'Updated Name', value: 99 };
      const result = await service.update(entityId, updates);

      expect(result.name).toBe('Updated Name');
      expect(result.value).toBe(99);
      expect(result.updatedAt).toBeDefined();

      // Check both databases
      const mysqlEntity = service.getMySQLData().get(entityId);
      const firestoreEntity = service.getFirestoreData().get(entityId);

      expect(mysqlEntity?.name).toBe('Updated Name');
      expect(firestoreEntity?.name).toBe('Updated Name');
    });

    it('should handle MySQL-only updates', async () => {
      const updates = { name: 'MySQL Only' };
      await service.update(entityId, updates, { skipFirestore: true });

      const mysqlEntity = service.getMySQLData().get(entityId);
      const firestoreEntity = service.getFirestoreData().get(entityId);

      expect(mysqlEntity?.name).toBe('MySQL Only');
      expect(firestoreEntity?.name).toBe(testEntity.name); // Unchanged
    });
  });

  describe('Delete Operations', () => {
    let entityId: string;

    beforeEach(async () => {
      const entity = await service.create(testEntity);
      entityId = entity.id;
      service.setFailureFlags(false, false);
    });

    it('should dual-delete by default', async () => {
      await service.delete(entityId);

      expect(service.getMySQLData().has(entityId)).toBe(false);
      expect(service.getFirestoreData().has(entityId)).toBe(false);
    });

    it('should handle MySQL-only deletes', async () => {
      await service.delete(entityId, { skipFirestore: true });

      expect(service.getMySQLData().has(entityId)).toBe(false);
      expect(service.getFirestoreData().has(entityId)).toBe(true);
    });
  });

  describe('Health Check', () => {
    it('should check health of both databases', async () => {
      const health = await service.healthCheck();

      expect(health).toHaveProperty('mysql');
      expect(health).toHaveProperty('firestore');
      expect(typeof health.mysql).toBe('boolean');
      expect(typeof health.firestore).toBe('boolean');
    });
  });

  describe('Metrics Collection', () => {
    beforeEach(() => {
      updateDatabaseConfig({ performanceLogging: true });
    });

    it('should collect metrics for operations', async () => {
      await service.create(testEntity);

      const metrics = DatabaseMetricsCollector.getMetrics();
      expect(metrics.length).toBeGreaterThan(0);

      const summary = DatabaseMetricsCollector.getMetricsSummary();
      expect(summary.totalOperations).toBeGreaterThan(0);
    });

    it('should track MySQL and Firestore operations separately', async () => {
      await service.create(testEntity);

      const summary = DatabaseMetricsCollector.getMetricsSummary();
      expect(summary.mysqlOperations).toBeGreaterThan(0);
      expect(summary.firestoreOperations).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should throw DualDatabaseError for operation failures', async () => {
      service.setFailureFlags(true, true);

      await expect(service.create(testEntity)).rejects.toThrow(DualDatabaseError);
    });

    it('should include original error information', async () => {
      service.setFailureFlags(true, false);

      try {
        await service.create(testEntity);
      } catch (error) {
        expect(error).toBeInstanceOf(DualDatabaseError);
        const dbError = error as DualDatabaseError;
        expect(dbError.operation).toBe('create');
        expect(dbError.database).toBe('mysql');
      }
    });
  });

  describe('Utility Functions', () => {
    it('should convert timestamps to dates', () => {
      const now = new Date();
      const result = DualDatabaseUtils.timestampToDate(now);
      expect(result).toEqual(now);
    });

    it('should convert dates to MySQL format', () => {
      const date = new Date('2023-01-01T12:00:00Z');
      const result = DualDatabaseUtils.dateToMySQLFormat(date);
      expect(result).toBe('2023-01-01 12:00:00');
    });

    it('should handle retry logic', async () => {
      let attempts = 0;
      const operation = jest.fn().mockImplementation(() => {
        attempts++;
        if (attempts < 3) throw new Error('Temporary failure');
        return 'success';
      });

      const result = await DualDatabaseUtils.retry(operation, 3, 10);
      expect(result).toBe('success');
      expect(attempts).toBe(3);
    });
  });
});
