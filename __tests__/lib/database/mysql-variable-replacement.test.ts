import { replaceScriptVariables, formatQuery } from '../../../lib/database/utils';

describe('MySQL Variable Replacement', () => {
  describe('replaceScriptVariables', () => {
    it('should replace simple variables', () => {
      const query = 'SELECT * FROM users WHERE id = :userId AND name = :userName';
      const variables = { userId: 123, userName: 'John Doe' };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe("SELECT * FROM users WHERE id = 123 AND name = 'John Doe'");
    });

    it('should handle ValueObject instances', () => {
      class UserId {
        constructor(private value: number) {}
        toString(): string {
          return this.value.toString();
        }
      }

      const query = 'SELECT * FROM users WHERE id = :userId';
      const variables = { userId: new UserId(456) };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe("SELECT * FROM users WHERE id = '456'");
    });

    it('should handle literal replacements', () => {
      const query = 'SELECT * FROM :tableName WHERE id = :userId';
      const variables = {
        userId: 123,
        'tableName.literal': 'users',
      };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE id = 123');
    });

    it('should handle null and undefined values', () => {
      const query = 'SELECT * FROM users WHERE name = :name AND email = :email';
      const variables = { name: null, email: undefined };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE name = NULL AND email = NULL');
    });

    it('should handle arrays', () => {
      const query = 'SELECT * FROM users WHERE id IN (:userIds)';
      const variables = { userIds: [1, 2, 3] };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE id IN (1,2,3)');
    });

    it('should handle boolean values', () => {
      const query = 'SELECT * FROM users WHERE active = :isActive';
      const variables = { isActive: true };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE active = true');
    });

    it('should escape special characters', () => {
      const query = 'SELECT * FROM users WHERE notes = :notes';
      const variables = { notes: "John's notes with 'quotes'" };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe("SELECT * FROM users WHERE notes = 'John\\'s notes with \\'quotes\\''");
    });

    it('should leave unknown variables unchanged', () => {
      const query = 'SELECT * FROM users WHERE id = :userId AND name = :unknownVar';
      const variables = { userId: 123 };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE id = 123 AND name = :unknownVar');
    });

    it('should handle nested literal replacements', () => {
      const query = 'SELECT :columns FROM :tableName WHERE :whereClause';
      const variables = {
        'columns.literal': 'id, name, email',
        'tableName.literal': 'users',
        'whereClause.literal': 'active = :isActive',
      };

      const result = replaceScriptVariables(query, variables);

      expect(result).toBe('SELECT id, name, email FROM users WHERE active = :isActive');
    });
  });

  describe('formatQuery', () => {
    it('should format query with variables', () => {
      const query = 'SELECT * FROM users WHERE id = :userId';
      const variables = { userId: 123 };

      const result = formatQuery(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE id = 123');
    });

    it('should return original query when no variables provided', () => {
      const query = 'SELECT * FROM users';

      const result = formatQuery(query);

      expect(result).toBe('SELECT * FROM users');
    });

    it('should return original query when variables is empty', () => {
      const query = 'SELECT * FROM users WHERE id = :userId';
      const variables = {};

      const result = formatQuery(query, variables);

      expect(result).toBe('SELECT * FROM users WHERE id = :userId');
    });
  });

  describe('Complex scenarios', () => {
    it('should handle complex query with multiple variable types', () => {
      class ClientId {
        constructor(private value: string) {}
        toString(): string {
          return this.value;
        }
      }

      const query = `
        SELECT :selectFields
        FROM :tableName u
        WHERE u.client_id = :clientId
          AND u.created_at > :startDate
          AND u.active = :isActive
          AND u.type IN (:userTypes)
        ORDER BY :orderBy
        LIMIT :limit
      `;

      const variables = {
        'selectFields.literal': 'u.id, u.name, u.email, u.created_at',
        'tableName.literal': 'users',
        clientId: new ClientId('client_123'),
        startDate: '2024-01-01',
        isActive: true,
        userTypes: ['admin', 'user', 'moderator'],
        'orderBy.literal': 'u.created_at DESC',
        limit: 50,
      };

      const result = formatQuery(query, variables);

      expect(result).toContain('SELECT u.id, u.name, u.email, u.created_at');
      expect(result).toContain('FROM users u');
      expect(result).toContain("WHERE u.client_id = 'client_123'");
      expect(result).toContain("AND u.created_at > '2024-01-01'");
      expect(result).toContain('AND u.active = true');
      expect(result).toContain("AND u.type IN ('admin','user','moderator')");
      expect(result).toContain('ORDER BY u.created_at DESC');
      expect(result).toContain('LIMIT 50');
    });
  });
});
