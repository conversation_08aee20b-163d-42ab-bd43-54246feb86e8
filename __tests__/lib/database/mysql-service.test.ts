import { MySQLService, mysqlService } from '../../../lib/database/mysql-service';
import { getDatabaseConfig } from '../../../lib/database/config';

// Set test environment
(process.env as any).NODE_ENV = 'test';
(process.env as any).MYSQL_DATABASE = 'frontdesk_ai_test';

describe('MySQLService', () => {
  beforeAll(async () => {
    // Skip these tests if MySQL credentials are not available
    const config = getDatabaseConfig();
    if (!config.host || !config.user) {
      console.log('⚠️ Skipping MySQL tests - database credentials not configured');
      return;
    }
  });

  afterAll(async () => {
    try {
      await mysqlService.close();
    } catch (error) {
      // Ignore cleanup errors in tests
    }
  });

  describe('Configuration', () => {
    it('should load database configuration from environment variables', () => {
      const config = getDatabaseConfig();

      expect(config).toHaveProperty('host');
      expect(config).toHaveProperty('port');
      expect(config).toHaveProperty('user');
      expect(config).toHaveProperty('database');
      expect(config.database).toBe('frontdesk_ai_test');
    });

    it('should set proper defaults for configuration values', () => {
      const config = getDatabaseConfig();

      expect(config.port).toBe(3306);
      expect(config.connectionLimit).toBe(10);
      expect(config.acquireTimeoutMillis).toBe(60000);
    });
  });

  describe('Service Initialization', () => {
    it('should create singleton instance', () => {
      const instance1 = MySQLService.getInstance();
      const instance2 = MySQLService.getInstance();

      expect(instance1).toBe(instance2);
    });

    it('should throw error when accessing pool before initialization', () => {
      const freshService = new (MySQLService as any)();

      expect(() => freshService.getPool()).toThrow('Database pool not initialized');
    });

    it('should throw error when accessing knex before initialization', () => {
      const freshService = new (MySQLService as any)();

      expect(() => freshService.getKnex()).toThrow('Knex instance not initialized');
    });
  });

  describe('Health Check', () => {
    it('should return unhealthy status when not initialized', async () => {
      const freshService = new (MySQLService as any)();
      const healthCheck = await freshService.healthCheck();

      expect(healthCheck.status).toBe('unhealthy');
      expect(healthCheck.details.connectionPool).toBe(false);
      expect(healthCheck.details.knexInstance).toBe(false);
      expect(healthCheck.details.connectivity).toBe(false);
    });
  });

  // Integration tests - only run if database is available
  describe('Database Integration', () => {
    const config = getDatabaseConfig();
    const hasValidDatabaseConfig = config.host && config.host !== 'localhost';

    if (!hasValidDatabaseConfig) {
      it.skip('should initialize database connection successfully - database not configured', () => {});
      it.skip('should pass health check after initialization - database not configured', () => {});
      it.skip('should execute simple queries - database not configured', () => {});
      it.skip('should execute queryFirst method - database not configured', () => {});
      it.skip('should return null for queryFirst with no results - database not configured', () => {});
      it.skip('should handle transactions - database not configured', () => {});
    } else {
      it('should initialize database connection successfully', async () => {
        await expect(mysqlService.initialize()).resolves.not.toThrow();
      });

      it('should pass health check after initialization', async () => {
        await mysqlService.initialize();
        const healthCheck = await mysqlService.healthCheck();

        expect(healthCheck.status).toBe('healthy');
        expect(healthCheck.details.connectionPool).toBe(true);
        expect(healthCheck.details.knexInstance).toBe(true);
        expect(healthCheck.details.connectivity).toBe(true);
      });

      it('should execute simple queries', async () => {
        await mysqlService.initialize();

        const result = await mysqlService.query('SELECT 1 as test');
        expect(result).toHaveLength(1);
        expect(result[0]).toHaveProperty('test', 1);
      });

      it('should execute queryFirst method', async () => {
        await mysqlService.initialize();

        const result = await mysqlService.queryFirst('SELECT 1 as test');
        expect(result).toHaveProperty('test', 1);
      });

      it('should return null for queryFirst with no results', async () => {
        await mysqlService.initialize();

        const result = await mysqlService.queryFirst('SELECT 1 as test WHERE 1 = 0');
        expect(result).toBeNull();
      });

      it('should handle transactions', async () => {
        await mysqlService.initialize();

        const result = await mysqlService.transaction(async trx => {
          const testResult = await trx.raw('SELECT 1 as test');
          return testResult[0][0];
        });

        expect(result).toHaveProperty('test', 1);
      });
    }
  });
});

describe('Database Configuration', () => {
  it('should handle SSL configuration for production', () => {
    const originalEnv = process.env.NODE_ENV;
    (process.env as any).NODE_ENV = 'production';

    const config = getDatabaseConfig();
    expect(config.ssl).toBeDefined();
    expect(config.ssl?.rejectUnauthorized).toBe(false);

    (process.env as any).NODE_ENV = originalEnv;
  });

  it('should handle SSL configuration when MYSQL_SSL is true', () => {
    const originalSsl = process.env.MYSQL_SSL;
    (process.env as any).MYSQL_SSL = 'true';

    const config = getDatabaseConfig();
    expect(config.ssl).toBeDefined();
    expect(config.ssl?.rejectUnauthorized).toBe(false);

    if (originalSsl) {
      (process.env as any).MYSQL_SSL = originalSsl;
    } else {
      delete (process.env as any).MYSQL_SSL;
    }
  });
});
