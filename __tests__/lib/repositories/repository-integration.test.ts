/**
 * Integration tests for repository functionality
 * Tests the repository layer with mock database services
 */

import { AppointmentsRepository } from '../../../lib/repositories/appointments-repository';
import { LocationsRepository } from '../../../lib/repositories/locations-repository';
import { getRepositories, RepositoryManager } from '../../../lib/repositories';
import { Appointment } from '../../../models/Appointment';
import { Location } from '../../../models/Location';

// Mock the database services
jest.mock('../../../lib/database/mysql-service', () => ({
  MySQLService: {
    getInstance: jest.fn(() => ({
      isConnected: jest.fn(() => true),
      query: jest.fn(() => Promise.resolve({ results: [], fields: [] })),
      execute: jest.fn(() => Promise.resolve({ results: { insertId: 1, affectedRows: 1 } })),
      close: jest.fn(() => Promise.resolve()),
    })),
  },
}));

jest.mock('firebase-admin', () => {
  const mockFirestoreInstance = {
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(() =>
          Promise.resolve({
            exists: false,
            id: 'test-id',
            data: () => null,
          }),
        ),
        set: jest.fn(() => Promise.resolve()),
        update: jest.fn(() => Promise.resolve()),
        delete: jest.fn(() => Promise.resolve()),
      })),
      where: jest.fn(() => ({
        get: jest.fn(() => Promise.resolve({ docs: [], size: 0 })),
      })),
      get: jest.fn(() => Promise.resolve({ docs: [], size: 0 })),
    })),
    batch: jest.fn(() => ({
      set: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      commit: jest.fn(() => Promise.resolve()),
    })),
  };

  return {
    firestore: Object.assign(
      jest.fn(() => mockFirestoreInstance),
      {
        Timestamp: {
          now: jest.fn(() => ({ seconds: 1234567890, nanoseconds: 0 })),
          fromDate: jest.fn((date: Date) => ({
            seconds: Math.floor(date.getTime() / 1000),
            nanoseconds: 0,
          })),
        },
      },
    ),
    apps: [],
    initializeApp: jest.fn(),
  };
});

// Firebase admin mock above handles firestore initialization

describe('Repository Integration Tests', () => {
  let appointmentsRepo: AppointmentsRepository;
  let locationsRepo: LocationsRepository;
  let repositoryManager: RepositoryManager;

  beforeEach(() => {
    appointmentsRepo = new AppointmentsRepository();
    locationsRepo = new LocationsRepository();
    repositoryManager = getRepositories();

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('AppointmentsRepository', () => {
    const mockAppointment: Appointment = {
      id: 'test-appointment-1',
      userId: 'user-123',
      clientId: 'client-456',
      clientName: 'John Doe',
      slotId: 'slot-789',
      callId: 'call-101',
      date: '2024-01-15',
      time: '14:30',
      status: 'active',
      createdAt: new Date('2024-01-01T10:00:00Z'),
      updatedAt: new Date('2024-01-01T10:00:00Z'),
    };

    it('should create appointments repository instance', () => {
      expect(appointmentsRepo).toBeInstanceOf(AppointmentsRepository);
    });

    it('should validate appointment entities correctly', () => {
      // This should not throw
      expect(() => {
        // Use protected method through type assertion for testing
        (appointmentsRepo as any).customValidateEntity(mockAppointment);
      }).not.toThrow();
    });

    it('should reject invalid appointment entities', () => {
      const invalidAppointment = {
        id: 'test-invalid',
        // Missing required fields
      };

      expect(() => {
        (appointmentsRepo as any).customValidateEntity(invalidAppointment);
      }).toThrow('Appointment validation failed');
    });

    it('should convert entity to Firestore format', () => {
      const firestoreData = (appointmentsRepo as any).entityToFirestoreData(mockAppointment);

      expect(firestoreData).toMatchObject({
        userId: mockAppointment.userId,
        clientId: mockAppointment.clientId,
        clientName: mockAppointment.clientName,
        slotId: mockAppointment.slotId,
        callId: mockAppointment.callId,
        date: mockAppointment.date,
        time: mockAppointment.time,
        status: mockAppointment.status,
      });
      expect(firestoreData.createdAt).toBeDefined();
      expect(firestoreData.updatedAt).toBeDefined();
    });

    it('should convert MySQL data to entity format', () => {
      const mysqlRow = {
        uuid: 'test-appointment-1',
        user_id: 'user-123',
        client_id: 'client-456',
        client_name: 'John Doe',
        slot_id: 'slot-789',
        call_id: 'call-101',
        appointment_date: '2024-01-15',
        appointment_time: '14:30',
        status: 'active',
        created_at: new Date('2024-01-01T10:00:00Z'),
        updated_at: new Date('2024-01-01T10:00:00Z'),
      };

      const entity = (appointmentsRepo as any).mysqlDataToEntity(mysqlRow);

      expect(entity).toMatchObject({
        id: 'test-appointment-1',
        userId: 'user-123',
        clientId: 'client-456',
        clientName: 'John Doe',
        slotId: 'slot-789',
        callId: 'call-101',
        date: '2024-01-15',
        time: '14:30',
        status: 'active',
      });
      expect(entity.createdAt).toBeInstanceOf(Date);
      expect(entity.updatedAt).toBeInstanceOf(Date);
    });

    it('should handle appointment statistics', async () => {
      // Mock the findByStatus method to return empty arrays
      jest.spyOn(appointmentsRepo, 'findByStatus').mockResolvedValue([]);

      const stats = await appointmentsRepo.getAppointmentStats();

      expect(stats).toEqual({
        totalActive: 0,
        totalCompleted: 0,
        totalCancelled: 0,
        upcomingToday: 0,
      });
    });
  });

  describe('LocationsRepository', () => {
    const mockLocation: Location = {
      id: 'test-location-1',
      clinicId: 123,
      practiceId: 'practice-456',
      name: 'Main Office',
      address: '123 Main St, City, State',
      phone: '******-123-4567',
      timeZone: 'America/New_York',
      isActive: true,
      practiceName: 'Family Practice',
      officeHours: {
        monday: { start: '09:00', end: '17:00' },
        tuesday: { start: '09:00', end: '17:00' },
        wednesday: { start: '09:00', end: '17:00' },
        thursday: { start: '09:00', end: '17:00' },
        friday: { start: '09:00', end: '17:00' },
        saturday: null,
        sunday: null,
      },
      createdAt: new Date('2024-01-01T10:00:00Z'),
      updatedAt: new Date('2024-01-01T10:00:00Z'),
    };

    it('should create locations repository instance', () => {
      expect(locationsRepo).toBeInstanceOf(LocationsRepository);
    });

    it('should validate location entities correctly', () => {
      expect(() => {
        (locationsRepo as any).customValidateEntity(mockLocation);
      }).not.toThrow();
    });

    it('should reject invalid location entities', () => {
      const invalidLocation = {
        id: 'test-invalid',
        // Missing required fields
      };

      expect(() => {
        (locationsRepo as any).customValidateEntity(invalidLocation);
      }).toThrow('Location validation failed');
    });

    it('should convert entity to Firestore format', () => {
      const firestoreData = (locationsRepo as any).entityToFirestoreData(mockLocation);

      expect(firestoreData).toMatchObject({
        clinicId: mockLocation.clinicId,
        practiceId: mockLocation.practiceId,
        name: mockLocation.name,
        address: mockLocation.address,
        phone: mockLocation.phone,
        timeZone: mockLocation.timeZone,
        isActive: mockLocation.isActive,
        practiceName: mockLocation.practiceName,
        officeHours: mockLocation.officeHours,
      });
      expect(firestoreData.createdAt).toBeDefined();
      expect(firestoreData.updatedAt).toBeDefined();
    });

    it('should convert MySQL data to entity format', () => {
      const mysqlRow = {
        uuid: 'test-location-1',
        clinic_id: 123,
        practice_id: 'practice-456',
        name: 'Main Office',
        address: '123 Main St, City, State',
        phone: '******-123-4567',
        time_zone: 'America/New_York',
        is_active: 1,
        practice_name: 'Family Practice',
        office_hours: JSON.stringify(mockLocation.officeHours),
        created_at: new Date('2024-01-01T10:00:00Z'),
        updated_at: new Date('2024-01-01T10:00:00Z'),
      };

      const entity = (locationsRepo as any).mysqlDataToEntity(mysqlRow);

      expect(entity).toMatchObject({
        id: 'test-location-1',
        clinicId: 123,
        practiceId: 'practice-456',
        name: 'Main Office',
        address: '123 Main St, City, State',
        phone: '******-123-4567',
        timeZone: 'America/New_York',
        isActive: true,
        practiceName: 'Family Practice',
        officeHours: mockLocation.officeHours,
      });
      expect(entity.createdAt).toBeInstanceOf(Date);
      expect(entity.updatedAt).toBeInstanceOf(Date);
    });

    it('should handle location statistics', async () => {
      // Mock the findMany method to return empty result
      jest.spyOn(locationsRepo, 'findMany').mockResolvedValue({
        items: [],
        total: 0,
        hasMore: false,
        nextCursor: undefined,
      });

      const stats = await locationsRepo.getLocationStats();

      expect(stats).toEqual({
        totalLocations: 0,
        activeLocations: 0,
        inactiveLocations: 0,
        locationsByPractice: {},
        locationsByTimeZone: {},
      });
    });
  });

  describe('RepositoryManager', () => {
    it('should provide singleton access to repositories', () => {
      const manager1 = getRepositories();
      const manager2 = getRepositories();

      expect(manager1).toBe(manager2);
      expect(manager1).toBeInstanceOf(RepositoryManager);
    });

    it('should provide access to individual repositories', () => {
      const manager = getRepositories();

      expect(manager.appointments).toBeInstanceOf(AppointmentsRepository);
      expect(manager.locations).toBeInstanceOf(LocationsRepository);
    });

    it('should handle repository statistics', async () => {
      // Mock the stats methods
      jest.spyOn(repositoryManager.appointments, 'getAppointmentStats').mockResolvedValue({
        totalActive: 5,
        totalCompleted: 10,
        totalCancelled: 2,
        upcomingToday: 3,
      });

      jest.spyOn(repositoryManager.locations, 'getLocationStats').mockResolvedValue({
        totalLocations: 8,
        activeLocations: 7,
        inactiveLocations: 1,
        locationsByPractice: { 'Practice A': 5, 'Practice B': 3 },
        locationsByTimeZone: { 'America/New_York': 8 },
      });

      const stats = await repositoryManager.getStats();

      expect(stats).toEqual({
        appointments: {
          total: 17, // 5 + 10 + 2
          byStatus: {
            active: 5,
            completed: 10,
            cancelled: 2,
          },
        },
        locations: {
          total: 8,
          active: 7,
          inactive: 1,
        },
      });
    });

    it('should handle health checks', async () => {
      // Mock health check methods
      jest
        .spyOn(repositoryManager.appointments, 'healthCheck')
        .mockResolvedValue({ mysql: true, firestore: true });
      jest
        .spyOn(repositoryManager.locations, 'healthCheck')
        .mockResolvedValue({ mysql: true, firestore: true });

      const healthCheck = await repositoryManager.healthCheck();

      expect(healthCheck).toEqual({
        appointments: true,
        locations: true,
        overall: true,
      });
    });

    it('should handle health check failures', async () => {
      // Mock health check methods with failures
      jest
        .spyOn(repositoryManager.appointments, 'healthCheck')
        .mockRejectedValue(new Error('Connection failed'));
      jest
        .spyOn(repositoryManager.locations, 'healthCheck')
        .mockResolvedValue({ mysql: true, firestore: true });

      const healthCheck = await repositoryManager.healthCheck();

      expect(healthCheck).toEqual({
        appointments: false,
        locations: true,
        overall: false,
      });
    });

    it('should reset repository instances', () => {
      const manager = getRepositories();
      const originalAppointments = manager.appointments;
      const originalLocations = manager.locations;

      manager.reset();

      expect(manager.appointments).not.toBe(originalAppointments);
      expect(manager.locations).not.toBe(originalLocations);
      expect(manager.appointments).toBeInstanceOf(AppointmentsRepository);
      expect(manager.locations).toBeInstanceOf(LocationsRepository);
    });
  });

  describe('Database Configuration Integration', () => {
    it('should use database configuration for repository operations', () => {
      // Test that repositories use the database configuration
      expect(appointmentsRepo).toBeDefined();
      expect(locationsRepo).toBeDefined();

      // Verify the repositories are properly configured with table/collection names
      expect((appointmentsRepo as any).tableName).toBe('appointments');
      expect((appointmentsRepo as any).collectionName).toBe('appointments');
      expect((locationsRepo as any).tableName).toBe('locations');
      expect((locationsRepo as any).collectionName).toBe('locations');
    });

    it('should handle dual-database operations', async () => {
      // Mock the findMany method to simulate dual-database behavior
      const mockResult = {
        items: [],
        total: 0,
        hasMore: false,
        nextCursor: undefined,
      };

      jest.spyOn(appointmentsRepo, 'findMany').mockResolvedValue(mockResult);

      const result = await appointmentsRepo.findMany({
        where: { status: 'active' },
        limit: 10,
      });

      expect(result).toEqual(mockResult);
      expect(appointmentsRepo.findMany).toHaveBeenCalledWith({
        where: { status: 'active' },
        limit: 10,
      });
    });
  });
});
