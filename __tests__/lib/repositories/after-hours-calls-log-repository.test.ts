import { AfterHoursCallsLogRepository } from '@/lib/repositories/after-hours-calls-log-repository';
import { mysqlService } from '@/lib/database/mysql-service';

// Mock the mysql service
jest.mock('@/lib/database/mysql-service');

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      })),
      where: jest.fn(() => ({
        orderBy: jest.fn(() => ({
          limit: jest.fn(() => ({
            get: jest.fn(),
          })),
        })),
      })),
    })),
  })),
}));

describe('AfterHoursCallsLogRepository', () => {
  let repository: AfterHoursCallsLogRepository;
  const mockMysqlService = mysqlService as jest.Mocked<typeof mysqlService>;

  beforeEach(() => {
    repository = new AfterHoursCallsLogRepository();
    jest.clearAllMocks();
  });

  describe('findByCallIdWithUserNames', () => {
    it('should return after-hours call logs with user names by call_id', async () => {
      const mockResults = [
        {
          id: 'log-1',
          after_hours_call_id: 'ahc-1',
          viewed_by: 'user-1',
          contacted_by: null,
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z',
          viewed_by_user_name: 'Dr Jane Smith',
          contacted_by_user_name: null,
        },
        {
          id: 'log-2',
          after_hours_call_id: 'ahc-1',
          viewed_by: 'user-2',
          contacted_by: null,
          created_at: '2024-01-15T11:00:00Z',
          updated_at: '2024-01-15T11:00:00Z',
          viewed_by_user_name: 'Dr John Abbot',
          contacted_by_user_name: null,
        },
      ];

      mockMysqlService.query.mockResolvedValue(mockResults);

      const result = await repository.findByCallIdWithUserNames('call-123');

      expect(mockMysqlService.query).toHaveBeenCalledWith(expect.stringContaining('SELECT'), [
        'call-123',
      ]);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 'log-1',
        afterHoursCallId: 'ahc-1',
        viewedBy: 'user-1',
        contactedBy: undefined,
        createdAt: new Date('2024-01-15T10:30:00Z'),
        updatedAt: new Date('2024-01-15T10:30:00Z'),
        viewedByUserName: 'Dr Jane Smith',
        contactedByUserName: undefined,
      });

      expect(result[1]).toEqual({
        id: 'log-2',
        afterHoursCallId: 'ahc-1',
        viewedBy: 'user-2',
        contactedBy: undefined,
        createdAt: new Date('2024-01-15T11:00:00Z'),
        updatedAt: new Date('2024-01-15T11:00:00Z'),
        viewedByUserName: 'Dr John Abbot',
        contactedByUserName: undefined,
      });
    });

    it('should handle empty results', async () => {
      mockMysqlService.query.mockResolvedValue([]);

      const result = await repository.findByCallIdWithUserNames('call-123');

      expect(result).toHaveLength(0);
    });

    it('should handle database errors', async () => {
      const error = new Error('Database connection failed');
      mockMysqlService.query.mockRejectedValue(error);

      await expect(repository.findByCallIdWithUserNames('call-123')).rejects.toThrow(
        'Failed to find after-hours call logs by call ID: Database connection failed',
      );
    });

    it('should handle logs with contacted users', async () => {
      const mockResults = [
        {
          id: 'log-1',
          after_hours_call_id: 'ahc-1',
          viewed_by: 'user-1',
          contacted_by: 'user-2',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z',
          viewed_by_user_name: 'Dr Jane Smith',
          contacted_by_user_name: 'Dr John Abbot',
        },
      ];

      mockMysqlService.query.mockResolvedValue(mockResults);

      const result = await repository.findByCallIdWithUserNames('call-123');

      expect(result[0]).toEqual({
        id: 'log-1',
        afterHoursCallId: 'ahc-1',
        viewedBy: 'user-1',
        contactedBy: 'user-2',
        createdAt: new Date('2024-01-15T10:30:00Z'),
        updatedAt: new Date('2024-01-15T10:30:00Z'),
        viewedByUserName: 'Dr Jane Smith',
        contactedByUserName: 'Dr John Abbot',
      });
    });
  });
});
