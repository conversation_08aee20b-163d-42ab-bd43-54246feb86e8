import { CallsRepository } from '@/lib/repositories/calls-repository';
import admin from 'firebase-admin';

// Mock Firebase Admin
jest.mock('firebase-admin', () => ({
  apps: [],
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(),
      get: jest.fn(),
      add: jest.fn(),
      where: jest.fn(() => ({
        get: jest.fn(),
      })),
    })),
  })),
}));

// Mock database config
jest.mock('@/lib/database/database-config', () => ({
  getDatabaseConfig: jest.fn(() => ({
    host: 'localhost',
    port: 3306,
    user: 'test',
    password: 'test',
    database: 'test_db',
  })),
}));

// Mock MySQL service with proper exports
const mockQuery = jest.fn().mockResolvedValue([]);
const mockQueryFirst = jest.fn().mockResolvedValue(null);

jest.mock('@/lib/database/mysql-service', () => ({
  mysqlService: {
    query: mockQuery,
    queryFirst: mockQueryFirst,
  },
  getInstance: jest.fn(() => ({
    query: mockQuery,
    queryFirst: mockQueryFirst,
  })),
}));

describe('CallsRepository', () => {
  let repository: CallsRepository;

  beforeEach(() => {
    // Initialize mock Firebase app
    if (!admin.apps.length) {
      (admin.initializeApp as jest.Mock).mockReturnValue({
        name: '[DEFAULT]',
      });
    }

    repository = new CallsRepository();
  });

  describe('buildCallsQuery', () => {
    test('should build query with agent ID filter correctly', () => {
      // Access the private method for testing
      const buildQuery = (repository as any).buildCallsQuery.bind(repository);

      const params = {
        limit: 10,
        offset: 0,
        configuredAgentId: 'test-agent-123',
      };

      const result = buildQuery(params);

      // Verify SQL contains the correct agent ID filter
      expect(result.sql).toContain('(agent_id IS NULL OR agent_id = ? OR agent_id = ?)');

      // Verify parameters are in correct order: empty string first, then agent ID
      const agentParams = result.params.slice(-3, -1); // Get the last 2 params before limit
      expect(agentParams[0]).toBe(''); // Empty string for legacy calls
      expect(agentParams[1]).toBe('test-agent-123'); // Configured agent ID

      // Verify total parameter count matches SQL placeholders
      const placeholderCount = (result.sql.match(/\?/g) || []).length;
      expect(result.params.length).toBe(placeholderCount);
    });

    test('should build query without agent ID filter when not provided', () => {
      const buildQuery = (repository as any).buildCallsQuery.bind(repository);

      const params = {
        limit: 10,
        offset: 0,
        clinicId: 1,
      };

      const result = buildQuery(params);

      // Verify SQL does not contain agent ID filter condition
      expect(result.sql).not.toContain('agent_id IS NULL OR agent_id = ?');

      // Verify parameter count matches SQL placeholders
      const placeholderCount = (result.sql.match(/\?/g) || []).length;
      expect(result.params.length).toBe(placeholderCount);

      // Should have clinic_id and limit parameters
      expect(result.params).toContain(1); // clinic_id
      expect(result.params).toContain(10); // limit
    });

    test('should handle complex query with multiple filters', () => {
      const buildQuery = (repository as any).buildCallsQuery.bind(repository);

      const params = {
        limit: 20,
        offset: 0,
        clinicId: 123,
        locationId: 456,
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-12-31'),
        callType: 1,
        callDirection: 'inbound' as const,
        minPriority: 1,
        maxPriority: 10,
        configuredAgentId: 'agent-xyz',
        phoneBlacklist: ['555-0000', '555-1111'],
        includeUnknownPhoneNumbers: false,
      };

      const result = buildQuery(params);

      // Verify all expected conditions are present
      expect(result.sql).toContain('clinic_id = ?');
      expect(result.sql).toContain('location_id = ?');
      expect(result.sql).toContain('call_date >= ?');
      expect(result.sql).toContain('call_date <= ?');
      expect(result.sql).toContain('call_type = ?');
      expect(result.sql).toContain('(is_outbound_call = 0 OR is_outbound_call IS NULL)');
      expect(result.sql).toContain('priority_score >= ?');
      expect(result.sql).toContain('priority_score <= ?');
      expect(result.sql).toContain('(agent_id IS NULL OR agent_id = ? OR agent_id = ?)');
      expect(result.sql).toContain('phone_number NOT LIKE ?');
      expect(result.sql).toContain("phone_number != 'Unknown'");

      // Verify parameter count matches SQL placeholders
      const placeholderCount = (result.sql.match(/\?/g) || []).length;
      expect(result.params.length).toBe(placeholderCount);

      // Verify some key parameters are present
      expect(result.params).toContain(123); // clinic_id
      expect(result.params).toContain(456); // location_id
      expect(result.params).toContain(1); // call_type
      // Note: is_outbound_call filter is now hardcoded in SQL, not parameterized
      expect(result.params).toContain(1); // min_priority
      expect(result.params).toContain(10); // max_priority
      expect(result.params).toContain(''); // empty agent_id
      expect(result.params).toContain('agent-xyz'); // configured agent_id
      expect(result.params).toContain('%555-0000%'); // blacklisted phone
      expect(result.params).toContain('%555-1111%'); // blacklisted phone
      expect(result.params).toContain(20); // limit
    });

    test('should handle phone search tokens correctly', () => {
      const buildQuery = (repository as any).buildCallsQuery.bind(repository);

      const phoneSearch = {
        searchTerm: '555-1234',
        normalizedSearchTerm: '5551234',
        searchTokens: ['5551234', '1234'],
      };

      const params = {
        limit: 10,
        offset: 0,
        phoneSearch,
      };

      const result = buildQuery(params);

      // Verify phone search conditions
      expect(result.sql).toContain('REPLACE(phone_number, "-", "") LIKE ?');

      // Verify phone search parameters
      expect(result.params).toContain('%5551234%');
      expect(result.params).toContain('%1234%');

      // Verify parameter count matches SQL placeholders
      const placeholderCount = (result.sql.match(/\?/g) || []).length;
      expect(result.params.length).toBe(placeholderCount);
    });

    // NEW TEST: API-style parameters that might be causing the issue
    test('should handle API-style parameters without errors', () => {
      const buildQuery = (repository as any).buildCallsQuery.bind(repository);

      // These are the exact parameters that would come from the API
      const params = {
        limit: 10,
        offset: 0,
        clinicId: undefined, // API often sends undefined values
        locationId: undefined,
        startDate: undefined,
        endDate: undefined,
        callType: undefined,
        callDirection: 'both' as const, // Default API value
        minPriority: undefined,
        maxPriority: undefined,
        phoneSearch: null,
        includeUnknownPhoneNumbers: false,
        phoneBlacklist: ['9178582585', '9082740595', '9176268074', '7328229112'],
        configuredAgentId: process.env.GCP_AGENT_ID || 'test-agent', // From environment
        afterId: undefined,
      };

      const result = buildQuery(params);

      // Verify parameter count matches question marks
      const questionMarks = (result.sql.match(/\?/g) || []).length;
      expect(result.params.length).toBe(questionMarks);

      // Verify no undefined parameters
      expect(result.params).not.toContain(undefined);

      // Log the actual query for debugging
      console.log('API-style query SQL:', result.sql);
      console.log('API-style query params:', result.params);
      console.log('Question marks:', questionMarks, 'Params:', result.params.length);
    });
  });

  describe('fetchAndFilterCalls', () => {
    test.skip('should handle API-style parameters in full method', async () => {
      // Set environment variable like in production
      const originalAgentId = process.env.GCP_AGENT_ID;
      process.env.GCP_AGENT_ID = 'test-production-agent';

      // Use the already mocked MySQL service
      mockQuery.mockClear();

      const params = {
        limit: 10,
        offset: 0,
        clinicId: undefined,
        locationId: undefined,
        startDate: undefined,
        endDate: undefined,
        searchTerm: undefined,
        callType: undefined,
        minPriority: undefined,
        maxPriority: undefined,
        callDirection: 'both' as const,
        agentId: undefined,
        includeUnknownPhoneNumbers: false,
        afterId: undefined,
      };

      const result = await repository.fetchAndFilterCalls(params);

      expect(result).toBeDefined();
      expect(result.calls).toEqual([]);
      expect(result.hasMore).toBe(false);

      // Verify the query was called with valid parameters
      expect(mockQuery).toHaveBeenCalledTimes(1);
      const [sql, sqlParams] = mockQuery.mock.calls[0];

      // Verify parameter count matches question marks
      const questionMarks = (sql.match(/\?/g) || []).length;
      expect(sqlParams.length).toBe(questionMarks);

      console.log('Full method SQL:', sql);
      console.log('Full method params:', sqlParams);
      console.log('Question marks:', questionMarks, 'Params:', sqlParams.length);

      // Restore original environment variable
      if (originalAgentId) {
        process.env.GCP_AGENT_ID = originalAgentId;
      } else {
        delete process.env.GCP_AGENT_ID;
      }
    });
  });

  describe('parseDurationToSeconds', () => {
    test('should parse various duration formats correctly', () => {
      // Access the private method for testing
      const parseDuration = (repository as any).parseDurationToSeconds.bind(repository);

      // Test cases for different duration formats
      const testCases = [
        // HH:MM:SS format
        { input: '01:30:45', expected: 5445 }, // 1 hour 30 minutes 45 seconds
        { input: '0:05:30', expected: 330 }, // 5 minutes 30 seconds

        // MM:SS format
        { input: '05:30', expected: 330 }, // 5 minutes 30 seconds
        { input: '1:45', expected: 105 }, // 1 minute 45 seconds

        // Minutes with decimal
        { input: '3.0 min', expected: 180 }, // 3 minutes
        { input: '1.7 min', expected: 102 }, // 1.7 minutes = 102 seconds
        { input: '5.5 mins', expected: 330 }, // 5.5 minutes = 330 seconds
        { input: '2.25 min', expected: 135 }, // 2.25 minutes = 135 seconds

        // Integer minutes
        { input: '5 min', expected: 300 }, // 5 minutes
        { input: '10 mins', expected: 600 }, // 10 minutes
        { input: '1 min', expected: 60 }, // 1 minute

        // Seconds with decimal
        { input: '13 sec', expected: 13 }, // 13 seconds
        { input: '45.5 sec', expected: 46 }, // 45.5 seconds (rounded)
        { input: '30 secs', expected: 30 }, // 30 seconds
        { input: '1.2 secs', expected: 1 }, // 1.2 seconds (rounded)

        // Plain numeric seconds
        { input: '120', expected: 120 }, // 120 seconds
        { input: '60', expected: 60 }, // 60 seconds

        // Edge cases
        { input: null, expected: 0 }, // null input
        { input: '', expected: 0 }, // empty string
        { input: '   ', expected: 0 }, // whitespace only
        { input: 'invalid', expected: 0 }, // invalid format
        { input: 'abc min', expected: 0 }, // invalid number
      ];

      testCases.forEach(({ input, expected }) => {
        const result = parseDuration(input);
        expect(result).toBe(expected);
      });
    });

    test('should handle case-insensitive units', () => {
      const parseDuration = (repository as any).parseDurationToSeconds.bind(repository);

      // Test case insensitivity
      expect(parseDuration('5 MIN')).toBe(300);
      expect(parseDuration('5 Min')).toBe(300);
      expect(parseDuration('5 MiN')).toBe(300);
      expect(parseDuration('13 SEC')).toBe(13);
      expect(parseDuration('13 Sec')).toBe(13);
      expect(parseDuration('13 SeC')).toBe(13);
    });

    test('should handle whitespace variations', () => {
      const parseDuration = (repository as any).parseDurationToSeconds.bind(repository);

      // Test different whitespace patterns
      expect(parseDuration('5min')).toBe(300); // no space
      expect(parseDuration('5 min')).toBe(300); // single space
      expect(parseDuration('5  min')).toBe(300); // multiple spaces
      expect(parseDuration('  5 min  ')).toBe(300); // leading/trailing spaces
      expect(parseDuration('13sec')).toBe(13); // no space
      expect(parseDuration('13 sec')).toBe(13); // single space
      expect(parseDuration('13  sec')).toBe(13); // multiple spaces
    });
  });
});
