import { createMockApiContext } from '../../utils/testHelpers';
import handler from '@/pages/api/storage/session-audio';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getGcpStorageService } from '@/utils/gcp-storage';

// Mock the firebase-admin utility
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

// Mock the gcp-storage utility
jest.mock('@/utils/gcp-storage', () => ({
  getGcpStorageService: jest.fn(),
}));

describe('/api/storage/session-audio', () => {
  // Mock environment variables
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv, GCP_AUDIO_BUCKET_NAME: 'test-audio-bucket' };

    // Mock the GCP storage service with default implementation
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockResolvedValue(null),
      getSignedUrl: jest.fn().mockResolvedValue('https://example.com/signed-url'),
      listFiles: jest.fn().mockResolvedValue([]),
      fileExists: jest.fn().mockResolvedValue(false),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  // Authentication is now enabled
  it('should return 401 if user is not authenticated', async () => {
    // Mock authentication failure
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue(null);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Unauthorized' });
  });

  it('should return 405 for non-GET methods', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'POST',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Method not allowed' });
  });

  it('should return 400 if sessionId is not provided', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Session ID is required' });
  });

  it('should return 400 if bucket name is not provided and not in env', async () => {
    // Remove bucket name from env
    delete process.env.GCP_AUDIO_BUCKET_NAME;

    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
  });

  it('should return the latest audio file for a session', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockResolvedValue({
        fileName: 'audio/test-session-id_1609545600000.mp3',
        url: 'https://signed-url.example.com',
      }),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      fileName: 'audio/test-session-id_1609545600000.mp3',
      url: 'https://signed-url.example.com',
    });
    expect(mockStorageService.getLatestAudioFileForSession).toHaveBeenCalledWith(
      'test-audio-bucket',
      'test-session-id',
      undefined,
    );
  });

  it('should pass minTimestamp to getLatestAudioFileForSession', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockResolvedValue({
        fileName: 'audio/test-session-id_1609545600000.mp3',
        url: 'https://signed-url.example.com',
      }),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
        minTimestamp: '1609459200000',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockStorageService.getLatestAudioFileForSession).toHaveBeenCalledWith(
      'test-audio-bucket',
      'test-session-id',
      1609459200000,
    );
  });

  it('should return 404 if no audio file is found for the session', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockResolvedValue(null),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'nonexistent-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
  });

  it('should handle errors gracefully', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service with error
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockRejectedValue(new Error('Storage error')),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
    expect(JSON.parse(res._getData())).toHaveProperty('message');
  });

  it('should use custom bucket name from query if provided', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getLatestAudioFileForSession: jest.fn().mockResolvedValue({
        fileName: 'audio/test-session-id_1609545600000.mp3',
        url: 'https://signed-url.example.com',
      }),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
        bucket: 'custom-bucket',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockStorageService.getLatestAudioFileForSession).toHaveBeenCalledWith(
      'custom-bucket',
      'test-session-id',
      undefined,
    );
  });
});
