import { createMockApiContext } from '../../utils/testHelpers';
import handler from '@/pages/api/storage/audio-files';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getGcpStorageService } from '@/utils/gcp-storage';

// Mock the firebase-admin utility
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

// Mock the gcp-storage utility
jest.mock('@/utils/gcp-storage', () => ({
  getGcpStorageService: jest.fn(),
}));

describe('/api/storage/audio-files', () => {
  // Mock environment variables
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv, GCP_AUDIO_BUCKET_NAME: 'test-audio-bucket' };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should return 401 if user is not authenticated', async () => {
    // Mock authentication failure
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue(null);

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Unauthorized' });
  });

  it('should return 400 if bucket name is not provided and not in env', async () => {
    // Remove bucket name from env
    delete process.env.GCP_AUDIO_BUCKET_NAME;

    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
  });

  it('should return a list of files', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      listFiles: jest.fn().mockResolvedValue(['audio/file1.mp3', 'audio/file2.mp3']),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      files: ['audio/file1.mp3', 'audio/file2.mp3'],
    });
    expect(mockStorageService.listFiles).toHaveBeenCalledWith('test-audio-bucket', {});
  });

  it('should filter files by prefix', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      listFiles: jest.fn().mockResolvedValue(['audio/2023/file1.mp3', 'audio/2023/file2.mp3']),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        prefix: 'audio/2023/',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      files: ['audio/2023/file1.mp3', 'audio/2023/file2.mp3'],
    });
    expect(mockStorageService.listFiles).toHaveBeenCalledWith('test-audio-bucket', {
      prefix: 'audio/2023/',
    });
  });

  it('should return a signed URL for a specific file', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      fileExists: jest.fn().mockResolvedValue(true),
      getSignedUrl: jest.fn().mockResolvedValue('https://signed-url.example.com'),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        fileName: 'audio/file1.mp3',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      url: 'https://signed-url.example.com',
    });
    expect(mockStorageService.fileExists).toHaveBeenCalledWith(
      'test-audio-bucket',
      'audio/file1.mp3',
    );
    expect(mockStorageService.getSignedUrl).toHaveBeenCalled();
  });

  it('should return 404 if the requested file does not exist', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      fileExists: jest.fn().mockResolvedValue(false),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        fileName: 'audio/nonexistent.mp3',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
  });

  it('should filter for audio files only when audioOnly=true', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      listFiles: jest
        .fn()
        .mockResolvedValue([
          'audio/file1.mp3',
          'audio/file2.wav',
          'audio/document.pdf',
          'audio/image.jpg',
        ]),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        audioOnly: 'true',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      files: ['audio/file1.mp3', 'audio/file2.wav'],
    });
  });

  it('should handle errors gracefully', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service with error
    const mockStorageService = {
      listFiles: jest.fn().mockRejectedValue(new Error('Storage error')),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('error');
    expect(JSON.parse(res._getData())).toHaveProperty('message');
  });

  it('should return 405 for non-GET methods', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Method not allowed' });
  });
});
