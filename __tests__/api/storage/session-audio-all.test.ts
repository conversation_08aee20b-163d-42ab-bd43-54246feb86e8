import { createMockApiContext } from '../../utils/testHelpers';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getGcpStorageService } from '@/utils/gcp-storage';
import handler from '@/pages/api/storage/session-audio-all';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';

// Mock the firebase-admin utility
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

// Mock the gcp-storage utility
jest.mock('@/utils/gcp-storage', () => ({
  getGcpStorageService: jest.fn(),
}));

// Mock the dialogflow utility
jest.mock('@/lib/dialogflow', () => ({
  getDialogflowConversation: jest.fn(),
  extractTranscriptFromConversation: jest.fn(),
}));

// Mock the dialogflow auth utility
jest.mock('@/lib/dialogflow/auth', () => ({
  DialogflowAuthService: {
    getAccessToken: jest.fn(),
  },
}));

describe('Session Audio All API', () => {
  // Mock environment variables
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = {
      ...originalEnv,
      GCP_AUDIO_BUCKET_NAME: 'test-audio-bucket',
      GCP_PROJECT_ID: 'test-project-id',
      GCP_LOCATION_ID: 'global',
      GCP_AGENT_ID: 'test-agent-id',
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  it('should return all audio files for a session', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock DialogflowAuthService
    (DialogflowAuthService.getAccessToken as jest.Mock).mockResolvedValue('test-access-token');

    // Mock Dialogflow conversation data
    const mockConversation = {
      interactions: [
        {
          startTime: new Date(1609545600000).toISOString(),
          request: { text: 'I need to schedule an appointment' },
          response: {
            queryResult: {
              responseMessages: [{ text: { text: ['Hello, how can I help you today?'] } }],
            },
          },
        },
        {
          startTime: new Date(1609545660000).toISOString(),
          request: { text: 'I would like to schedule an appointment' },
          response: {
            queryResult: {
              responseMessages: [
                { text: { text: ['I can help you with that. What day works for you?'] } },
              ],
            },
          },
        },
      ],
    };
    (getDialogflowConversation as jest.Mock).mockResolvedValue(mockConversation);

    // Mock extractTranscriptFromConversation function
    (extractTranscriptFromConversation as jest.Mock).mockReturnValue(
      'Patient: I need to schedule an appointment\nHeather: Hello, how can I help you today?',
    );

    // Mock GCP storage service
    const mockStorageService = {
      getTranscriptWithAudioRecords: jest.fn().mockResolvedValue([
        {
          text: 'Patient: I need to schedule an appointment',
          recordUrl: 'gcp://test-audio-bucket/audio/test-session-id_1609545660000.mp3',
        },
        {
          text: 'Heather: Hello, how can I help you today?',
          recordUrl: 'gcp://test-audio-bucket/audio/test-session-id_1609545600000.mp3',
        },
      ]),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual([
      {
        text: 'Patient: I need to schedule an appointment',
        recordUrl: 'gcp://test-audio-bucket/audio/test-session-id_1609545660000.mp3',
      },
      {
        text: 'Heather: Hello, how can I help you today?',
        recordUrl: 'gcp://test-audio-bucket/audio/test-session-id_1609545600000.mp3',
      },
    ]);
    expect(mockStorageService.getTranscriptWithAudioRecords).toHaveBeenCalledWith(
      expect.any(String),
      'test-session-id',
      undefined,
    );
    // We're not calling getDialogflowConversation directly anymore
    // expect(getDialogflowConversation).toHaveBeenCalledWith({
    // projectId: 'test-project-id',
    // locationId: 'global',
    // agentId: 'test-agent-id',
    // sessionId: 'test-session-id',
    // accessToken: 'test-access-token'
    // });
  });

  it('should return 401 when user is not authenticated', async () => {
    // Mock failed authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue(null);

    // Mock storage service to ensure it is NOT called in this scenario
    const mockStorageService = {
      getTranscriptWithAudioRecords: jest.fn(),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({
      error: 'Unauthorized',
    });

    // Storage service should NOT be called when unauthorized
    expect(mockStorageService.getTranscriptWithAudioRecords).not.toHaveBeenCalled();
  });

  it('should return 400 if session ID is missing', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Session ID is required' });
  });

  it('should return 404 if no audio files are found', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getTranscriptWithAudioRecords: jest.fn().mockResolvedValue([]),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      error: 'No audio files found for session test-session-id',
    });
  });

  it('should return 405 for non-GET requests', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    const { req, res } = createMockApiContext({
      method: 'POST',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ error: 'Method not allowed' });
  });

  it('should handle errors gracefully', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service with error
    const mockStorageService = {
      getTranscriptWithAudioRecords: jest.fn().mockRejectedValue(new Error('Test error')),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toEqual({
      error: 'Internal server error',
      message: 'Test error',
    });
  });

  it('should apply minTimestamp filter if provided', async () => {
    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({ uid: 'test-user-id' });

    // Mock GCP storage service
    const mockStorageService = {
      getTranscriptWithAudioRecords: jest.fn().mockResolvedValue([
        {
          text: 'Patient: I would like to schedule an appointment',
          recordUrl: 'gcp://test-audio-bucket/audio/test-session-id_1609545660000.mp3',
        },
      ]),
    };
    (getGcpStorageService as jest.Mock).mockReturnValue(mockStorageService);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        sessionId: 'test-session-id',
        minTimestamp: '1609545650000',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(mockStorageService.getTranscriptWithAudioRecords).toHaveBeenCalledWith(
      expect.any(String),
      'test-session-id',
      1609545650000,
    );
  });
});
