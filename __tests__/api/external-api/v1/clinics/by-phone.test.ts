import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/clinics/by-phone';
import admin from '@/utils/firebase-admin';

// Mock firestore services
jest.mock('@/utils/firebase-admin', () => ({
  firestore: jest.fn(),
}));

describe('Clinics By Phone API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-GET requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without phone number', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {}, // Empty query params
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Valid phone number is required',
    });
  });

  it('should return clinic information when found', async () => {
    const createdDate = new Date('2023-01-01');
    const updatedDate = new Date('2023-01-02');

    const mockClinicData = {
      id: '1',
      data: () => ({
        name: 'Test Clinic',
        logoUrl: 'https://example.com/logo.png',
        address: '123 Test St',
        phone: '************',
        createdAt: {
          toDate: () => createdDate,
        },
        updatedAt: {
          toDate: () => updatedDate,
        },
      }),
    };

    // Mock Firestore query implementation
    const mockGet = jest.fn().mockResolvedValue({
      empty: false,
      docs: [mockClinicData],
    });

    const mockLimit = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockWhere = jest.fn().mockReturnValue({
      limit: mockLimit,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        phone: '************',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('clinic');
    expect(data.clinic).toEqual({
      id: 1,
      name: 'Test Clinic',
      logoUrl: 'https://example.com/logo.png',
      address: '123 Test St',
      phone: '************',
      createdAt: createdDate.toISOString(),
      updatedAt: updatedDate.toISOString(),
    });

    // Verify Firestore calls
    expect(mockCollection).toHaveBeenCalledWith('clinics');
    expect(mockWhere).toHaveBeenCalledWith('phone', '==', '************');
    expect(mockLimit).toHaveBeenCalledWith(1);
  });

  it('should return 404 when clinic is not found', async () => {
    // Mock Firestore query to return empty results
    const mockGet = jest.fn().mockResolvedValue({
      empty: true,
      docs: [],
    });

    const mockLimit = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockWhere = jest.fn().mockReturnValue({
      limit: mockLimit,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        phone: '************',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'No clinic found with the provided phone number',
    });
  });

  it('should handle errors gracefully', async () => {
    // Mock Firestore query to throw an error
    const mockGet = jest.fn().mockRejectedValue(new Error('Database error'));

    const mockLimit = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockWhere = jest.fn().mockReturnValue({
      limit: mockLimit,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        phone: '************',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
