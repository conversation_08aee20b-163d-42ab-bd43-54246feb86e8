import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/clinics/locations';
import { locationsService } from '@/utils/firestore';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  locationsService: {
    getLocationsByClinicId: jest.fn(),
  },
}));

describe('Clinics Locations API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-GET requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without clinicId', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {}, // Empty query params
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'clinicId is required',
    });
  });

  it('should reject requests with invalid clinicId', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: 'not-a-number',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'clinicId must be a valid number',
    });
  });

  it('should return 404 when no locations are found', async () => {
    // Mock implementation to return empty array (no locations found)
    (locationsService.getLocationsByClinicId as jest.Mock).mockResolvedValue([]);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '42',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'No locations found for the specified clinic',
    });
    expect(locationsService.getLocationsByClinicId).toHaveBeenCalledWith(42);
  });

  it('should return locations when found', async () => {
    // Create dates for testing
    const createdAt1 = new Date();
    const updatedAt1 = new Date();
    const createdAt2 = new Date();
    const updatedAt2 = new Date();

    // Mock implementation to return locations
    const mockLocations = [
      {
        id: 'location1',
        clinicId: 42,
        name: 'Downtown Office',
        address: '123 Main St',
        phone: '+15551234567',
        createdAt: createdAt1,
        updatedAt: updatedAt1,
      },
      {
        id: 'location2',
        clinicId: 42,
        name: 'Uptown Office',
        address: '456 Oak St',
        phone: '+15559876543',
        createdAt: createdAt2,
        updatedAt: updatedAt2,
      },
    ];

    // Expected response after JSON serialization
    const expectedResponse = {
      locations: [
        {
          id: 'location1',
          clinicId: 42,
          name: 'Downtown Office',
          address: '123 Main St',
          phone: '+15551234567',
          createdAt: createdAt1.toISOString(),
          updatedAt: updatedAt1.toISOString(),
        },
        {
          id: 'location2',
          clinicId: 42,
          name: 'Uptown Office',
          address: '456 Oak St',
          phone: '+15559876543',
          createdAt: createdAt2.toISOString(),
          updatedAt: updatedAt2.toISOString(),
        },
      ],
    };

    (locationsService.getLocationsByClinicId as jest.Mock).mockResolvedValue(mockLocations);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '42',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(expectedResponse);
    expect(locationsService.getLocationsByClinicId).toHaveBeenCalledWith(42);
  });

  it('should handle errors gracefully', async () => {
    // Mock implementation to throw an error
    (locationsService.getLocationsByClinicId as jest.Mock).mockRejectedValue(
      new Error('Database error'),
    );

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '42',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
