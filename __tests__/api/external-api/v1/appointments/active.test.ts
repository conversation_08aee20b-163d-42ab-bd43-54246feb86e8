import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/appointments/active';
import { appointmentsService, userService } from '@/utils/firestore';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  appointmentsService: {
    getActiveAppointmentsByClient: jest.fn(),
  },
  userService: {
    getUserById: jest.fn(),
  },
}));

describe('Active Appointments API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-POST requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without fullName', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {}, // Empty body
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'fullName is required',
    });
  });

  it('should return active appointments', async () => {
    // Create mock appointments with timestamps
    const mockAppointments = [
      {
        id: 'appointment1',
        userId: 'user123',
        clientId: 'client123',
        slotId: 'slot123',
        callId: 'call123',
        date: '2023-01-01',
        time: '12:00',
        status: 'active',
        createdAt: new Date('2023-01-01T10:00:00Z'),
        updatedAt: new Date('2023-01-01T10:00:00Z'),
      },
      {
        id: 'appointment2',
        userId: 'user123',
        clientId: 'client123',
        slotId: 'slot124',
        callId: 'call124',
        date: '2023-01-02',
        time: '13:00',
        status: 'active',
        createdAt: new Date('2023-01-01T11:00:00Z'),
        updatedAt: new Date('2023-01-01T11:00:00Z'),
      },
    ];

    // Mock implementation to return user
    (userService.getUserById as jest.Mock).mockResolvedValue({
      id: 'user123',
      name: 'Test User',
      email: '<EMAIL>',
      // Add other required user properties
    });

    // Mock implementation to return active appointments
    (appointmentsService.getActiveAppointmentsByClient as jest.Mock).mockResolvedValue(
      mockAppointments,
    );

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        fullName: 'Test Client',
        dateOfBirth: '1990-01-01',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('appointments');
    expect(data.appointments).toHaveLength(2);
    expect(data.appointments[0]).toHaveProperty('id', 'appointment1');
    expect(data.appointments[0]).toHaveProperty('createdAt');
    expect(data.appointments[0]).toHaveProperty('updatedAt');

    // Verify service call
    expect(appointmentsService.getActiveAppointmentsByClient).toHaveBeenCalledWith(
      'Test Client',
      '1990-01-01',
    );
  });

  it('should handle empty results', async () => {
    // Mock implementation to return empty array
    (appointmentsService.getActiveAppointmentsByClient as jest.Mock).mockResolvedValue([]);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        fullName: 'Nonexistent Client',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('appointments');
    expect(data.appointments).toHaveLength(0);
  });

  it('should handle errors gracefully', async () => {
    // Mock implementation to throw an error
    (appointmentsService.getActiveAppointmentsByClient as jest.Mock).mockRejectedValue(
      new Error('Database error'),
    );

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        fullName: 'Test Client',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
