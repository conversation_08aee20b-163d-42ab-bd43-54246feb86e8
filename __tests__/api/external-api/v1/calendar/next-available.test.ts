import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/calendar/next-available';
import { calendarSlotsService } from '@/utils/firestore';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  calendarSlotsService: {
    getSlotsByUserId: jest.fn(),
  },
}));

describe('Calendar Next Available API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock the current date to ensure consistent test behavior
    jest.useFakeTimers().setSystemTime(new Date('2023-01-01'));
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    jest.useRealTimers();
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-GET requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without userId', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {}, // Empty query params
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'userId is required',
    });
  });

  it('should reject requests with invalid date format', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        userId: 'user123',
        date: 'invalid-date', // Invalid date format
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Invalid date format. Use YYYY-MM-DD',
    });
  });

  it('should return 404 when no slots are found', async () => {
    // Mock implementation to return empty array (no slots found)
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockResolvedValue([]);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        userId: 'user123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'No available slots found for this user',
    });
    expect(calendarSlotsService.getSlotsByUserId).toHaveBeenCalledWith('user123');
  });

  it('should return the next available slot', async () => {
    // Mock implementation to return slots with available time slots
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockResolvedValue([
      {
        id: 'calendar123',
        userId: 'user123',
        date: '2023-01-01',
        timeSlots: [
          {
            id: 'slot123',
            time: '12:00',
            isAvailable: true,
          },
          {
            id: 'slot124',
            time: '13:00',
            isAvailable: false,
          },
        ],
      },
      {
        id: 'calendar124',
        userId: 'user123',
        date: '2023-01-02',
        timeSlots: [
          {
            id: 'slot125',
            time: '10:00',
            isAvailable: true,
          },
        ],
      },
    ]);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        userId: 'user123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('slot');
    expect(data.slot).toHaveProperty('userId', 'user123');
    expect(data.slot).toHaveProperty('slotId', 'slot123');
    expect(data.slot).toHaveProperty('date', '2023-01-01');
    expect(data.slot).toHaveProperty('time', '12:00');
  });

  it('should handle errors gracefully', async () => {
    // Mock implementation to throw an error
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockRejectedValue(
      new Error('Database error'),
    );

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        userId: 'user123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
