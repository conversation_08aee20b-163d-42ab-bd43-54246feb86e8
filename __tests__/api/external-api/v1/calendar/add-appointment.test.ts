import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/calendar/add-appointment';
import {
  appointmentsService,
  calendarSlotsService,
  clientsService,
  callsService,
} from '@/utils/firestore';
import { v4 as uuidv4 } from 'uuid';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  appointmentsService: {
    createAppointment: jest.fn(),
  },
  calendarSlotsService: {
    getSlotsByUserId: jest.fn(),
    updateSlotAvailability: jest.fn(),
  },
  clientsService: {
    getClientById: jest.fn(),
    createClient: jest.fn(),
    updateClient: jest.fn(),
    findClientByNameAndBirthday: jest.fn(),
  },
  callsService: {
    createCall: jest.fn(),
  },
}));

// Mock uuid generation
jest.mock('uuid', () => ({
  v4: jest.fn(),
}));

describe('Calendar Add Appointment API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
    (uuidv4 as jest.Mock).mockReturnValue('mocked-uuid');
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-POST requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without required fields', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {}, // Empty body
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'userId is required',
    });
  });

  it('should reject requests with invalid slot ID', async () => {
    // Mock implementation to return empty array (no slots found)
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockResolvedValue([]);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        userId: 'user123',
        slotId: 'slot123',
        client: {
          fullName: 'Test Client',
          birthday: '1990-01-01',
          phoneNumber: '************',
          insuranceCompany: 'Test Insurance',
          insuranceGroupNumber: '12345',
          subscriberName: 'Test Subscriber',
          clinicId: 1,
        },
        call: {
          userId: 'user123',
          clinicId: 1,
          date: '2023-01-01T12:00:00Z',
          summary: 'Test summary',
          transcription: 'Test transcription',
          recordingUrl: 'https://example.com/recording',
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Slot not found' });
    expect(calendarSlotsService.getSlotsByUserId).toHaveBeenCalledWith('user123');
  });

  it('should reject booking when slot is unavailable', async () => {
    // Mock implementation to return a slot that is unavailable
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockResolvedValue([
      {
        id: 'calendar123',
        date: '2023-01-01',
        timeSlots: [
          {
            id: 'slot123',
            time: '12:00',
            isAvailable: false, // Slot is not available
          },
        ],
      },
    ]);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        userId: 'user123',
        slotId: 'slot123',
        client: {
          fullName: 'Test Client',
          birthday: '1990-01-01',
          phoneNumber: '************',
          insuranceCompany: 'Test Insurance',
          insuranceGroupNumber: '12345',
          subscriberName: 'Test Subscriber',
          clinicId: 1,
        },
        call: {
          userId: 'user123',
          clinicId: 1,
          date: '2023-01-01T12:00:00Z',
          summary: 'Test summary',
          transcription: 'Test transcription',
          recordingUrl: 'https://example.com/recording',
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Slot is not available',
    });
  });

  it('should successfully create an appointment for a new client', async () => {
    // Mock implementation to return an available slot
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockResolvedValue([
      {
        id: 'calendar123',
        date: '2023-01-01',
        timeSlots: [
          {
            id: 'slot123',
            time: '12:00',
            isAvailable: true, // Slot is available
          },
        ],
      },
    ]);

    // Mock client search to return no existing clients
    (clientsService.findClientByNameAndBirthday as jest.Mock).mockResolvedValue(null);

    // Mock successful client creation
    (clientsService.createClient as jest.Mock).mockResolvedValue({
      id: 'client123',
    });

    // Mock successful call creation
    (callsService.createCall as jest.Mock).mockResolvedValue({ id: 'call123' });

    // Mock successful appointment creation
    (appointmentsService.createAppointment as jest.Mock).mockResolvedValue({
      id: 'appt123',
    });

    // Mock successful calendar slot update
    (calendarSlotsService.updateSlotAvailability as jest.Mock).mockResolvedValue(undefined);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        userId: 'user123',
        slotId: 'slot123',
        client: {
          fullName: 'Test Client',
          birthday: '1990-01-01',
          phoneNumber: '************',
          insuranceCompany: 'Test Insurance',
          insuranceGroupNumber: '12345',
          subscriberName: 'Test Subscriber',
          clinicId: 1,
        },
        call: {
          userId: 'user123',
          clinicId: 1,
          date: '2023-01-01T12:00:00Z',
          summary: 'Test summary',
          transcription: 'Test transcription',
          recordingUrl: 'https://example.com/recording',
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(201);
    expect(JSON.parse(res._getData())).toEqual({
      appointmentId: 'appt123',
      message: 'Appointment created successfully',
    });

    // Verify the correct sequence of service calls
    expect(clientsService.createClient).toHaveBeenCalled();
    expect(callsService.createCall).toHaveBeenCalled();
    expect(appointmentsService.createAppointment).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user123',
        clientId: expect.any(String),
        clientName: 'Test Client',
        slotId: 'slot123',
        date: '2023-01-01',
        time: '12:00',
        status: 'active',
      }),
    );
    expect(calendarSlotsService.updateSlotAvailability).toHaveBeenCalledWith(
      'user123',
      '2023-01-01',
      'slot123',
      false,
    );
  });

  it('should handle errors gracefully', async () => {
    // Mock implementation to throw an error
    (calendarSlotsService.getSlotsByUserId as jest.Mock).mockRejectedValue(
      new Error('Database error'),
    );

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        userId: 'user123',
        slotId: 'slot123',
        client: {
          fullName: 'Test Client',
          birthday: '1990-01-01',
          phoneNumber: '************',
          insuranceCompany: 'Test Insurance',
          insuranceGroupNumber: '12345',
          subscriberName: 'Test Subscriber',
          clinicId: 1,
        },
        call: {
          userId: 'user123',
          clinicId: 1,
          date: '2023-01-01T12:00:00Z',
          summary: 'Test summary',
          transcription: 'Test transcription',
          recordingUrl: 'https://example.com/recording',
        },
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
