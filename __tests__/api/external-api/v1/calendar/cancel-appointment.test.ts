import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/calendar/cancel-appointment';
import { appointmentsService } from '@/utils/firestore';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  appointmentsService: {
    getAppointmentById: jest.fn(),
    cancelAppointment: jest.fn(),
  },
}));

describe('Cancel Appointment API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-POST requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without appointmentId', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {}, // Empty body
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'appointmentId is required',
    });
  });

  it('should reject when appointment is not found', async () => {
    // Mock getAppointmentById to return null (not found)
    (appointmentsService.getAppointmentById as jest.Mock).mockResolvedValue(null);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        appointmentId: 'nonexistent-id',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Appointment not found',
    });
  });

  it('should reject when appointment is already cancelled', async () => {
    // Mock getAppointmentById to return a cancelled appointment
    (appointmentsService.getAppointmentById as jest.Mock).mockResolvedValue({
      id: 'appt123',
      status: 'cancelled',
    });

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        appointmentId: 'appt123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Appointment is already cancelled',
    });
  });

  it('should successfully cancel an active appointment', async () => {
    // Mock getAppointmentById to return an active appointment
    (appointmentsService.getAppointmentById as jest.Mock).mockResolvedValue({
      id: 'appt123',
      status: 'active',
    });

    // Mock successful cancellation
    (appointmentsService.cancelAppointment as jest.Mock).mockResolvedValue(undefined);

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        appointmentId: 'appt123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
      message: 'Appointment cancelled successfully',
    });

    // Verify service calls
    expect(appointmentsService.getAppointmentById).toHaveBeenCalledWith('appt123');
    expect(appointmentsService.cancelAppointment).toHaveBeenCalledWith('appt123');
  });

  it('should handle errors gracefully', async () => {
    // Mock getAppointmentById to throw an error
    (appointmentsService.getAppointmentById as jest.Mock).mockRejectedValue(
      new Error('Database error'),
    );

    const { req, res } = createMockApiContext({
      method: 'POST',
      body: {
        appointmentId: 'appt123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
