import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/calls/add-or-update-call-session';
import { callSessionsService } from '@/utils/firestore';

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    addOrUpdateCallSession: jest.fn().mockImplementation((sessionId, data) => {
      return Promise.resolve({
        id: 'test-uuid',
        sessionId,
        ...data,
        createdAt: new Date('2023-01-01T00:00:00Z'),
        updatedAt: new Date('2023-01-01T00:00:00Z'),
      });
    }),
  },
}));

// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('Add or Update Call Session API Endpoint', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return 401 for requests without API key', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {}, // No API key
      body: {
        sessionId: 'test-session-id',
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'Method not allowed',
    });
  });

  it('should return 400 if sessionId is missing', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        // Missing sessionId
        hasVoiceMail: true,
        callType: 1,
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'sessionId is required',
    });
  });

  it('should return 400 if hasVoiceMail is not a boolean', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionId: 'test-session-id',
        hasVoiceMail: 'not-a-boolean', // Invalid type
        callType: 1,
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'hasVoiceMail must be a boolean',
    });
  });

  it('should return 400 if callType is not a number', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionId: 'test-session-id',
        hasVoiceMail: true,
        callType: 'not-a-number', // Invalid type
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'callType must be a number',
    });
  });

  it('should create a new call session successfully', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionId: 'test-session-id',
        hasVoiceMail: true,
        callType: 1,
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData.success).toBe(true);
    expect(responseData.message).toBe('Call session added or updated successfully');
    expect(responseData.data).toEqual({
      id: 'test-uuid',
      sessionId: 'test-session-id',
      hasVoiceMail: true,
      callType: 1,
      createdAt: '2023-01-01T00:00:00.000Z',
      updatedAt: '2023-01-01T00:00:00.000Z',
    });

    expect(callSessionsService.addOrUpdateCallSession).toHaveBeenCalledWith('test-session-id', {
      hasVoiceMail: true,
      callType: 1,
    });
  });

  it('should handle server errors', async () => {
    (callSessionsService.addOrUpdateCallSession as jest.Mock).mockRejectedValueOnce(
      new Error('Test error'),
    );

    const { req, res } = createMockApiContext({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        sessionId: 'test-session-id',
        hasVoiceMail: true,
        callType: 1,
      },
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'Internal server error',
      error: 'Test error',
    });
  });
});
