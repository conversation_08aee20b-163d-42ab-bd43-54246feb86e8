import { createMockApiContext } from '../../../../utils/testHelpers';
import handler from '@/pages/api/external-api/v1/user-specialties';
import admin from '@/utils/firebase-admin';

// Mock firestore services
jest.mock('@/utils/firebase-admin', () => ({
  firestore: jest.fn(),
}));

describe('User Specialties API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;
  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-GET requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject requests without clinicId', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {}, // Empty query params
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'clinicId is required',
    });
  });

  it('should return specialties for a clinic', async () => {
    // Mock data for the test
    const mockUsers = [
      {
        id: 'user1',
        data: () => ({
          name: 'Dr. Smith',
          specialty: 'Cardiology',
          clinicId: 1,
        }),
      },
      {
        id: 'user2',
        data: () => ({
          name: 'Dr. Jones',
          specialty: 'Neurology',
          clinicId: 1,
        }),
      },
    ];

    // Mock Firestore query implementation
    const mockGet = jest.fn().mockResolvedValue({
      docs: mockUsers,
    });

    const mockWhere = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '1',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('specialties');
    expect(data.specialties).toHaveLength(2);
    expect(data.specialties[0]).toEqual({
      userId: 'user1',
      name: 'Dr. Smith',
      specialty: 'Cardiology',
    });
    expect(data.specialties[1]).toEqual({
      userId: 'user2',
      name: 'Dr. Jones',
      specialty: 'Neurology',
    });

    // Verify Firestore calls
    expect(mockCollection).toHaveBeenCalledWith('users');
    expect(mockWhere).toHaveBeenCalledWith('clinicId', '==', 1);
  });

  it('should handle empty results', async () => {
    // Mock Firestore query to return empty results
    const mockGet = jest.fn().mockResolvedValue({
      docs: [],
    });

    const mockWhere = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '1',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('specialties');
    expect(data.specialties).toHaveLength(0);
  });

  it('should handle errors gracefully', async () => {
    // Mock Firestore query to throw an error
    const mockGet = jest.fn().mockRejectedValue(new Error('Database error'));

    const mockWhere = jest.fn().mockReturnValue({
      get: mockGet,
    });

    const mockCollection = jest.fn().mockReturnValue({
      where: mockWhere,
    });

    const mockFirestore = jest.fn().mockReturnValue({
      collection: mockCollection,
    });

    (admin.firestore as unknown as jest.Mock).mockImplementation(mockFirestore);

    const { req, res } = createMockApiContext({
      method: 'GET',
      query: {
        clinicId: '1',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Internal server error');
    expect(JSON.parse(res._getData())).toHaveProperty('error', 'Database error');
  });
});
