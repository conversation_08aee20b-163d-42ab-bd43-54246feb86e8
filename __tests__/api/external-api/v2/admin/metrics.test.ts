import { createMocks } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/admin/metrics';

// Mock the logger to prevent actual logging during tests
jest.mock('../../../../../lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  generateRequestId: jest.fn().mockReturnValue('test-id'),
}));

// Mock the monitoring module
jest.mock('../../../../../lib/external-api/v2/utils/monitoring', () => ({
  getMetrics: jest.fn().mockReturnValue({
    'GET /api/test': {
      totalCalls: 100,
      errors: 5,
      errorRate: 0.05,
      avgLatencyMs: 150,
      p95LatencyMs: 300,
    },
  }),
  logMetrics: jest.fn(),
  recordMetrics: jest.fn(),
}));

// Save original environment
const originalEnv = process.env;

describe('Admin Metrics API Endpoint', () => {
  // Setup environment before each test
  beforeEach(() => {
    process.env = { ...originalEnv, ADMIN_API_KEY: 'test-admin-key' };
  });

  // Restore environment after each test
  afterEach(() => {
    process.env = originalEnv;
  });

  it('should return 401 when no admin token is provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api/v2/admin/metrics',
      headers: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);

    const responseData = JSON.parse(res._getData());
    expect(responseData).toHaveProperty('code', 'UNAUTHORIZED');
  });

  it('should return 401 when invalid admin token is provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api/v2/admin/metrics',
      headers: {
        'x-admin-token': 'invalid-token',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
  });

  it('should return 200 and metrics when valid admin token is provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api/v2/admin/metrics',
      headers: {
        'x-admin-token': 'test-admin-key',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData).toHaveProperty('timestamp');
    expect(responseData).toHaveProperty('metrics');
    expect(responseData.metrics).toHaveProperty('GET /api/test');
    expect(responseData.metrics['GET /api/test']).toHaveProperty('totalCalls', 100);
  });

  it('should return 405 for non-GET methods', async () => {
    const methods = ['POST', 'PUT', 'DELETE', 'PATCH'] as const;

    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method,
        url: '/api/external-api/v2/admin/metrics',
        headers: {
          'x-admin-token': 'test-admin-key',
        },
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toHaveProperty('message', 'Method not allowed');
    }
  });
});
