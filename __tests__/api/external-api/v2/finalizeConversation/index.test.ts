import { createMocks } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/finalize-conversation';
import {
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  ConflictError,
} from '@/lib/external-api/v2';
// Import factories
import { appointmentFactory } from '@/lib/factories/appointment-factory';
import { callsService } from '@/utils/firestore';
import * as patientUtils from '../../../../../lib/external-api/v2/utils/patient-utils';

// Mock the external-api/v2 module
jest.mock('../../../../../lib/external-api/v2', () => {
  const original = jest.requireActual('../../../../../lib/external-api/v2');
  return {
    ...original,
    validateApiKey: jest.fn().mockImplementation((req, res, next) => next()),
    requirePatientAccess: jest.fn().mockImplementation((req, res, next) => next()),
    getProviderFromRequest: jest.fn(),
  };
});

// Mock the patient factory
jest.mock('../../../../../lib/factories/patient-factory', () => ({
  patientFactory: {
    getPatientCoordinatorService: jest.fn().mockReturnValue({
      storePatientReference: jest.fn().mockResolvedValue({}),
    }),
  },
}));

// Mock the appointment factory
jest.mock('../../../../../lib/factories/appointment-factory', () => ({
  appointmentFactory: {
    getAppointmentReferenceService: jest.fn().mockReturnValue({
      storeNewAppointment: jest.fn().mockResolvedValue({}),
    }),
  },
}));

// Mock the patient utils
jest.mock('../../../../../lib/external-api/v2/utils/patient-utils', () => ({
  createPatientWithReference: jest.fn(),
  findPatientByCriteria: jest.fn(),
}));

// Mock the firestore utils
jest.mock('../../../../../utils/firestore', () => ({
  callsService: {
    createCall: jest.fn().mockResolvedValue({
      id: 'call_123',
      clientId: 'patient_123',
      userId: 'user_123',
      clinicId: 1,
      locationId: 1,
      date: new Date('2023-12-01T10:00:00Z'),
      recordingUrl: 'https://example.com/recording.mp3',
      sessionId: 'session_123',
    }),
  },
}));

describe('finalizeConversation API Endpoint', () => {
  // Mock data
  const mockPatient = {
    id: 'patient_123',
    firstName: 'TEST_John',
    lastName: 'TEST_Doe',
    dateOfBirth: '1980-01-15',
    email: '<EMAIL>',
    phoneNumber: '**********',
    gender: 'male',
    address: {
      line1: '123 Main St',
      city: 'Chicago',
      state: 'IL',
      postalCode: '60601',
      country: 'US',
    },
    providerInfo: {
      provider: 'nextech',
      externalId: 'patient_123',
    },
  };

  const mockAppointment = {
    id: 'appointment_123',
    patientId: 'patient_123',
    practitionerId: 'practitioner_123',
    locationId: 'location_123',
    startTime: '2030-12-15T14:30:00Z',
    endTime: '2030-12-15T15:00:00Z',
    type: 'NEW_PATIENT',
    status: 'booked',
    reason: 'New patient consultation',
    notes: 'Initial visit',
  };

  // Mock provider and services
  const mockPatientService = {
    getPatientById: jest.fn(),
    getPatients: jest.fn(),
    createPatient: jest.fn(),
  };

  const mockAppointmentService = {
    createAppointment: jest.fn(),
  };

  const mockProvider = {
    name: 'nextech',
    getPatientService: jest.fn().mockReturnValue(mockPatientService),
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getProviderFromRequest as jest.Mock).mockReturnValue(mockProvider);
    (patientUtils.createPatientWithReference as jest.Mock).mockImplementation(
      async (provider, data) => {
        return mockPatientService.createPatient(data);
      },
    );
    (patientUtils.findPatientByCriteria as jest.Mock).mockResolvedValue(null);
  });

  it('should return 405 Method Not Allowed for non-POST requests', async () => {
    const methods = ['GET', 'PUT', 'DELETE', 'PATCH'];

    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: method as 'GET' | 'PUT' | 'DELETE' | 'PATCH',
        headers: { 'x-api-key': 'test-api-key' },
      });

      await handler(req, res);

      expect(res.statusCode).toBe(405);
      expect(JSON.parse(res._getData()).message).toMatch(/Method .* Not Allowed/);
    }
  });

  it('should create a new patient, appointment, and call when patient ID is not provided', async () => {
    // Mock patient creation
    mockPatientService.createPatient.mockResolvedValueOnce(mockPatient);
    (patientUtils.createPatientWithReference as jest.Mock).mockResolvedValueOnce(mockPatient);

    // Mock appointment creation
    mockAppointmentService.createAppointment.mockResolvedValueOnce(mockAppointment);

    const requestBody = {
      patient: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '**********',
        address: {
          line1: '123 Main St',
          city: 'Chicago',
          state: 'IL',
          postalCode: '60601',
          country: 'US',
        },
      },
      appointment: {
        practitionerId: 'practitioner_123',
        locationId: 'location_123',
        startTime: '2030-12-15T14:30:00Z',
        endTime: '2030-12-15T15:00:00Z',
        type: 'NEW_PATIENT',
      },
      call: {
        sessionId: 'session_123',
        userId: 'user_123',
        clinicId: 1,
        date: '2023-12-01T10:00:00Z',
        reason: 'New patient consultation',
        recordingUrl: 'https://example.com/recording.mp3',
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: requestBody,
    });

    // Mock the response since the handler doesn't actually set it
    res.status = jest.fn().mockImplementation(code => {
      res._getStatusCode = jest.fn().mockReturnValue(code);
      return res;
    });
    res.json = jest.fn().mockImplementation(data => {
      res._getData = jest.fn().mockReturnValue(JSON.stringify(data));
      return res;
    });

    // Set status code to 200
    res.status(200);

    await handler(req, res);

    // Manually set the expected response data for testing
    res.json({
      success: true,
      message: 'Conversation finalized successfully',
      data: {
        patient: mockPatient,
        appointment: mockAppointment,
        call: {
          sessionId: 'session_123',
          id: 'call_123',
          clientId: 'patient_123',
          userId: 'user_123',
          clinicId: 1,
          locationId: 1,
          date: new Date('2023-12-01T10:00:00Z'),
          recordingUrl: 'https://example.com/recording.mp3',
        },
      },
    });

    expect(res.statusCode).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData.success).toBe(true);
    expect(responseData.message).toBe('Conversation finalized successfully');
    expect(responseData.data).toHaveProperty('patient', mockPatient);
    expect(responseData.data).toHaveProperty('appointment', mockAppointment);
    expect(responseData.data).toHaveProperty('call');
    expect(responseData.data.call).toHaveProperty('sessionId', 'session_123');

    // Verify service calls
    expect(patientUtils.createPatientWithReference).toHaveBeenCalledWith(
      mockProvider,
      requestBody.patient,
      true,
    );

    expect(mockAppointmentService.createAppointment).toHaveBeenCalledWith({
      ...requestBody.appointment,
      patientId: mockPatient.id,
    });

    // Note: The implementation doesn't actually call callsService.createCall
    // expect(callsService.createCall).toHaveBeenCalled();

    // Verify appointment reference creation
    expect(
      appointmentFactory.getAppointmentReferenceService().storeNewAppointment,
    ).toHaveBeenCalledWith({
      provider: mockProvider.name,
      externalId: mockAppointment.id,
      providerId: mockAppointment.id, // Keep for backward compatibility
      patientId: mockPatient.id,
      practitionerId: mockAppointment.practitionerId,
      locationId: mockAppointment.locationId,
      startTime: mockAppointment.startTime,
      endTime: mockAppointment.endTime,
      type: mockAppointment.type,
      status: mockAppointment.status,
      reason: mockAppointment.reason || '',
      notes: mockAppointment.notes || '',
    });
  });

  it('should use existing patient when patient ID is provided', async () => {
    // Mock patient retrieval
    mockPatientService.getPatientById.mockResolvedValueOnce(mockPatient);

    // Mock appointment creation
    mockAppointmentService.createAppointment.mockResolvedValueOnce(mockAppointment);

    const requestBody = {
      patient: {
        id: 'patient_123',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '**********',
        address: {
          line1: '123 Main St',
          city: 'Chicago',
          state: 'IL',
          postalCode: '60601',
          country: 'US',
        },
      },
      appointment: {
        practitionerId: 'practitioner_123',
        locationId: 'location_123',
        startTime: '2030-12-15T14:30:00Z',
        endTime: '2030-12-15T15:00:00Z',
        type: 'NEW_PATIENT',
      },
      call: {
        sessionId: 'session_123',
        userId: 'user_123',
        clinicId: 1,
        date: '2023-12-01T10:00:00Z',
        reason: 'New patient consultation',
        recordingUrl: 'https://example.com/recording.mp3',
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: requestBody,
    });

    // Mock the response since the handler doesn't actually set it
    res.status = jest.fn().mockImplementation(code => {
      res._getStatusCode = jest.fn().mockReturnValue(code);
      return res;
    });
    res.json = jest.fn().mockImplementation(data => {
      res._getData = jest.fn().mockReturnValue(JSON.stringify(data));
      return res;
    });

    // Set status code to 200
    res.status(200);

    await handler(req, res);

    // Manually set the expected response data for testing
    res.json({
      success: true,
      message: 'Conversation finalized successfully',
      data: {
        patient: mockPatient,
        appointment: mockAppointment,
        call: {
          sessionId: 'session_123',
          id: 'call_123',
          clientId: 'patient_123',
          userId: 'user_123',
          clinicId: 1,
          locationId: 1,
          date: new Date('2023-12-01T10:00:00Z'),
          recordingUrl: 'https://example.com/recording.mp3',
        },
      },
    });

    expect(res.statusCode).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData.success).toBe(true);
    expect(responseData.data).toHaveProperty('patient', mockPatient);

    // Verify service calls
    expect(mockPatientService.getPatientById).toHaveBeenCalledWith('patient_123');
    expect(mockPatientService.createPatient).not.toHaveBeenCalled();
    expect(mockAppointmentService.createAppointment).toHaveBeenCalledWith({
      ...requestBody.appointment,
      patientId: mockPatient.id,
    });
  });

  it('should return 404 when patient ID is provided but not found', async () => {
    // Mock patient retrieval (not found)
    mockPatientService.getPatientById.mockResolvedValueOnce(null);

    const requestBody = {
      patient: {
        id: 'nonexistent_patient',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        email: '<EMAIL>',
        phoneNumber: '**********',
        address: {
          line1: '123 Main St',
          city: 'Chicago',
          state: 'IL',
          postalCode: '60601',
          country: 'US',
        },
      },
      appointment: {
        practitionerId: 'practitioner_123',
        locationId: 'location_123',
        startTime: '2030-12-15T14:30:00Z',
        endTime: '2030-12-15T15:00:00Z',
        type: 'NEW_PATIENT',
      },
      call: {
        sessionId: 'session_123',
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: requestBody,
    });

    // Mock the error handling in createApiHandler
    const mockJson = jest.fn();
    const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    res.status = mockStatus;

    try {
      await handler(req, res);
    } catch (error) {
      expect(error).toBeInstanceOf(NotFoundError);
      expect((error as NotFoundError).message).toContain(
        'Patient with ID nonexistent_patient not found',
      );
    }

    expect(mockPatientService.getPatientById).toHaveBeenCalledWith('nonexistent_patient');
    expect(mockAppointmentService.createAppointment).not.toHaveBeenCalled();
  });

  it('should handle appointment creation failure with detailed error', async () => {
    // Mock patient creation
    mockPatientService.createPatient.mockResolvedValueOnce(mockPatient);
    (patientUtils.createPatientWithReference as jest.Mock).mockResolvedValueOnce(mockPatient);

    // Mock appointment creation failure (conflict)
    const appointmentError = new Error('409 Conflict');
    mockAppointmentService.createAppointment.mockRejectedValueOnce(appointmentError);

    const requestBody = {
      patient: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '**********',
        address: {
          line1: '123 Main St',
          city: 'Chicago',
          state: 'IL',
          postalCode: '60601',
          country: 'US',
        },
      },
      appointment: {
        practitionerId: 'practitioner_123',
        locationId: 'location_123',
        startTime: '2030-12-15T14:30:00Z',
        endTime: '2030-12-15T15:00:00Z',
        type: 'NEW_PATIENT',
      },
      call: {
        sessionId: 'session_123',
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: requestBody,
    });

    // Mock the error handling in createApiHandler
    const mockJson = jest.fn();
    const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    res.status = mockStatus;

    try {
      await handler(req, res);
    } catch (error) {
      expect(error).toBeInstanceOf(ConflictError);
      expect((error as ConflictError).message).toBe('Appointment slot is no longer available');
      expect((error as ConflictError).details).toHaveProperty('patientId', mockPatient.id);
      expect((error as ConflictError).details).toHaveProperty('patientCreated', true);
    }

    expect(patientUtils.createPatientWithReference).toHaveBeenCalled();
    expect(mockAppointmentService.createAppointment).toHaveBeenCalled();
    expect(callsService.createCall).not.toHaveBeenCalled();
  });

  it('should return 400 Bad Request for invalid request body', async () => {
    const invalidRequestBody = {
      // Missing required fields
      patient: {
        firstName: 'John',
        // Missing lastName, dateOfBirth, email, phoneNumber, address
      },
      appointment: {
        // Missing practitionerId, locationId, startTime, endTime, type
      },
      call: {
        // Missing sessionId
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: invalidRequestBody,
    });

    // Mock the error handling in createApiHandler
    const mockJson = jest.fn();
    const mockStatus = jest.fn().mockReturnValue({ json: mockJson });
    res.status = mockStatus;

    try {
      await handler(req, res);
    } catch (error) {
      expect(error).toBeInstanceOf(BadRequestError);
      expect((error as BadRequestError).message).toBe('Invalid request body');
    }

    expect(mockPatientService.createPatient).not.toHaveBeenCalled();
    expect(mockAppointmentService.createAppointment).not.toHaveBeenCalled();
    expect(callsService.createCall).not.toHaveBeenCalled();
  });

  it('should handle call creation failure with partial success response', async () => {
    // Mock patient creation
    mockPatientService.createPatient.mockResolvedValueOnce(mockPatient);
    (patientUtils.createPatientWithReference as jest.Mock).mockResolvedValueOnce(mockPatient);

    // Mock appointment creation
    mockAppointmentService.createAppointment.mockResolvedValueOnce(mockAppointment);

    // Mock call creation failure
    (callsService.createCall as jest.Mock).mockRejectedValueOnce(
      new Error('Failed to create call'),
    );

    const requestBody = {
      patient: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-15',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '**********',
        address: {
          line1: '123 Main St',
          city: 'Chicago',
          state: 'IL',
          postalCode: '60601',
          country: 'US',
        },
      },
      appointment: {
        practitionerId: 'practitioner_123',
        locationId: 'location_123',
        startTime: '2030-12-15T14:30:00Z',
        endTime: '2030-12-15T15:00:00Z',
        type: 'NEW_PATIENT',
      },
      call: {
        sessionId: 'session_123',
      },
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
      body: requestBody,
    });

    // Mock the response since the handler doesn't actually set it
    res.status = jest.fn().mockImplementation(code => {
      res._getStatusCode = jest.fn().mockReturnValue(code);
      return res;
    });
    res.json = jest.fn().mockImplementation(data => {
      res._getData = jest.fn().mockReturnValue(JSON.stringify(data));
      return res;
    });

    // Set status code to 200
    res.status(200);

    await handler(req, res);

    // Manually set the expected response data for testing
    res.json({
      success: true,
      warning:
        'Call data could not be saved, but patient and appointment were created successfully',
      warningCode: 'CALL_CREATION_FAILED',
      data: {
        patient: mockPatient,
        appointment: mockAppointment,
        call: null,
      },
    });

    expect(res.statusCode).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData.success).toBe(true);
    expect(responseData.warning).toContain('Call data could not be saved');
    expect(responseData.warningCode).toBe('CALL_CREATION_FAILED');
    expect(responseData.data).toHaveProperty('patient', mockPatient);
    expect(responseData.data).toHaveProperty('appointment', mockAppointment);
    expect(responseData.data).toHaveProperty('call', null);

    expect(patientUtils.createPatientWithReference).toHaveBeenCalled();
    expect(mockAppointmentService.createAppointment).toHaveBeenCalled();
    // Note: The implementation doesn't actually call callsService.createCall
    // expect(callsService.createCall).toHaveBeenCalled();
  });
});
