import { createMocks } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../pages/api/external-api/v2/health';

// Mock the logger to prevent actual logging during tests
jest.mock('../../../../lib/external-api/v2/utils/logger', () => ({
  __esModule: true,
  default: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  generateRequestId: jest.fn().mockReturnValue('test-id'),
}));

// Mock the monitoring module
jest.mock('../../../../lib/external-api/v2/utils/monitoring', () => ({
  recordMetrics: jest.fn(),
}));

describe('Health Check API Endpoint', () => {
  it('should return 200 and health information', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api/v2/health',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);

    const responseData = JSON.parse(res._getData());
    expect(responseData).toHaveProperty('status', 'healthy');
    expect(responseData).toHaveProperty('timestamp');
    expect(responseData).toHaveProperty('version', '2.0');
    expect(responseData).toHaveProperty('environment');
  });

  it('should return 405 for non-GET methods', async () => {
    const methods = ['POST', 'PUT', 'DELETE', 'PATCH'] as const;

    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method,
        url: '/api/external-api/v2/health',
      });

      await handler(req, res);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toHaveProperty('message', 'Method not allowed');
    }
  });
});
