import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/patients'; // Adjust path if needed
import { providerRegistry } from '@/lib/external-api/v2';
import { MockProvider } from '@/lib/external-api/v2/providers/mock';
import { ApiError } from '@/lib/external-api/v2';
// Mock Firebase before importing anything else
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    updateCallSession: jest.fn(),
    getCallSession: jest.fn(),
  },
  callsService: {
    updateCall: jest.fn(),
    getCall: jest.fn(),
  },
}));

import { Patient } from '@/lib/external-api/v2';
import { PaginatedResult } from '@/lib/external-api/v2';

// Mock Firebase Admin to prevent initialization errors
jest.mock('@/utils/firebase-admin', () => {
  const mockFirestore = {
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    get: jest.fn(),
    set: jest.fn(),
    update: jest.fn(),
  };

  return {
    firestore: jest.fn(() => mockFirestore),
    apps: [],
    initializeApp: jest.fn(),
    default: {
      firestore: jest.fn(() => mockFirestore),
      apps: [],
      initializeApp: jest.fn(),
    },
  };
});

// Mock dependencies
jest.mock('../../../../../lib/external-api/v2/providers');
jest.mock('../../../../../lib/external-api/v2/utils/logger');

describe('/api/external-api/v2/patients API Endpoint', () => {
  let mockProvider: MockProvider;

  // Define empty paginated result for reuse
  const emptyPaginatedResult: PaginatedResult<Patient> = {
    items: [],
    pagination: {
      totalCount: 0,
      limit: 10,
      offset: 0,
      hasMore: false,
      links: {},
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockProvider = new MockProvider();
    (providerRegistry.getProvider as jest.Mock).mockReturnValue(mockProvider);
  });

  it('should return 405 Method Not Allowed for non-GET/POST requests', async () => {
    const methods: ('DELETE' | 'PATCH')[] = ['DELETE', 'PATCH'];
    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: method,
        headers: { 'x-api-key': 'test-api-key' },
        env: {},
      });
      await handler(req, res);
      expect(res.statusCode).toBe(405);
      expect(JSON.parse(res._getData()).message).toBe('Method not allowed');
    }
  });

  // Basic success case
  it('should return 200 OK and patients on successful GET request', async () => {
    const mockPatients: Patient[] = [
      {
        id: 'patient1',
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        gender: 'male',
        email: '<EMAIL>',
        phoneNumber: '************',
        address: {
          line1: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          postalCode: '12345',
          country: 'USA',
        },
        providerInfo: { provider: 'mock', externalId: 'mock-patient1' },
      },
    ];

    const mockPaginatedResult: PaginatedResult<Patient> = {
      items: mockPatients,
      pagination: {
        totalCount: mockPatients.length,
        limit: 10,
        offset: 0,
        hasMore: false,
        links: {},
      },
    };

    mockProvider.getPatients.mockResolvedValue(mockPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockPaginatedResult);
    expect(mockProvider.getPatients).toHaveBeenCalledWith(
      { clinicId: 'clinic123' },
      { limit: 10, offset: 0 },
    );
  });

  // Error case from provider
  it('should handle errors from the provider', async () => {
    const errorMessage = 'Failed to fetch patients';
    const providerError = new ApiError(500, 'PROVIDER_ERROR', errorMessage);
    mockProvider.getPatients.mockRejectedValue(providerError);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData()).message).toBe(errorMessage);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_ERROR');
  });

  // Test with different provider
  it('should use the provider specified in x-provider header', async () => {
    const specificProvider = new MockProvider();
    specificProvider.name = 'specific-mock';

    specificProvider.getPatients.mockResolvedValue(emptyPaginatedResult);

    (providerRegistry.getProvider as jest.Mock).mockImplementation((providerName?: string) => {
      if (providerName === 'specific-mock') {
        return specificProvider;
      }
      return mockProvider;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
        'x-provider': 'specific-mock',
      },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(providerRegistry.getProvider).toHaveBeenCalledWith('specific-mock');
    expect(specificProvider.getPatients).toHaveBeenCalledWith(
      { clinicId: 'clinic123' },
      { limit: 10, offset: 0 },
    );
    expect(mockProvider.getPatients).not.toHaveBeenCalled();
  });

  // Tests for different query parameters/filters
  it('should pass firstName filter to provider', async () => {
    mockProvider.getPatients.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', firstName: 'John' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getPatients).toHaveBeenCalledWith(
      { clinicId: 'clinic123', firstName: 'John' },
      { limit: 10, offset: 0 },
    );
  });

  it('should pass multiple filters to provider', async () => {
    mockProvider.getPatients.mockResolvedValue(emptyPaginatedResult);

    const queryParams = {
      clinicId: 'clinic123',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      dateOfBirth: '1980-01-01',
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: queryParams,
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getPatients).toHaveBeenCalledWith(queryParams, {
      limit: 10,
      offset: 0,
    });
  });

  // Validation error tests
  it('should return 400 when clinicId is missing', async () => {
    // Mock provider to throw a validation error
    mockProvider.getPatients.mockImplementationOnce(() => {
      const error = new Error('Clinic ID is required');
      (error as any).status = 400;
      (error as any).code = 'VALIDATION_ERROR';
      throw error;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {}, // No clinicId provided
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('VALIDATION_ERROR');
    expect(mockProvider.getPatients).toHaveBeenCalled();
  });

  it('should return 400 for invalid dateOfBirth format', async () => {
    // Mock provider to throw a validation error
    mockProvider.getPatients.mockImplementationOnce(() => {
      const error = new Error('Invalid date format');
      (error as any).status = 400;
      (error as any).code = 'VALIDATION_ERROR';
      throw error;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', dateOfBirth: 'invalid-date' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('VALIDATION_ERROR');
  });

  // Authentication error tests
  it('should return 401 when API key is missing', async () => {
    mockProvider.getPatients.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {}, // No x-api-key header
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('API key is required');
  });

  it('should return 401 when API key is invalid', async () => {
    (providerRegistry.getProvider as jest.Mock).mockImplementation(() => {
      throw new ApiError(401, 'UNAUTHORIZED', 'Invalid API key');
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'invalid-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('Invalid API key');
  });

  // Provider-specific error test
  it('should handle provider-specific validation errors', async () => {
    mockProvider.getPatients.mockRejectedValue(
      new ApiError(400, 'PROVIDER_VALIDATION_ERROR', 'Provider-specific validation failed'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_VALIDATION_ERROR');
  });

  it('should handle rate limiting errors from provider', async () => {
    mockProvider.getPatients.mockRejectedValue(
      new ApiError(429, 'RATE_LIMIT_EXCEEDED', 'Too many requests'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(429);
    expect(JSON.parse(res._getData()).code).toBe('RATE_LIMIT_EXCEEDED');
  });

  it('should handle validation errors', async () => {
    mockProvider.getPatients.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {}, // Missing clinicId or other required filters
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
  });

  it('should handle provider service errors', async () => {
    const errorMessage = 'Failed to fetch patients';
    mockProvider.getPatients.mockRejectedValue(new ApiError(500, 'PROVIDER_ERROR', errorMessage));

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData()).message).toBe(errorMessage);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_ERROR');
  });
});
