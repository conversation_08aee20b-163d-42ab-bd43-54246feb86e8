import { ProviderFactory } from '../../../../../lib/external-api/v2/providers/factory';
import type { ProviderConfig } from '../../../../../lib/external-api/v2/providers/factory';
import type {
  IClinicService,
  ILocationService,
  IPatientService,
  IProvider,
  IUserService,
  IAppointmentService,
} from '../../../../../lib/external-api/v2/providers/types';

// Mock provider for testing
class MockProvider implements IProvider {
  name: string;

  constructor(config: ProviderConfig) {
    this.name = config.name;
  }

  getClinicService(): IClinicService {
    return {} as IClinicService;
  }

  getLocationService(): ILocationService {
    return {} as ILocationService;
  }

  getPatientService(): IPatientService {
    return {} as IPatientService;
  }

  getUserService(): IUserService {
    return {} as IUserService;
  }

  getAppointmentService(): IAppointmentService {
    return {} as IAppointmentService;
  }
}

describe('ProviderFactory', () => {
  let factory: ProviderFactory;

  beforeEach(() => {
    factory = new ProviderFactory();
  });

  test('should register a provider constructor', () => {
    factory.registerProviderConstructor('mock', MockProvider);
    const provider = factory.createProvider({ name: 'mock' });
    expect(provider.name).toBe('mock');
    expect(provider).toBeInstanceOf(MockProvider);
  });

  test('should throw an error when provider constructor is not registered', () => {
    expect(() => factory.createProvider({ name: 'nonexistent' })).toThrow(
      'Provider constructor for nonexistent not found',
    );
  });

  test('should pass config to provider constructor', () => {
    factory.registerProviderConstructor('mock', MockProvider);
    const config = { name: 'mock', apiUrl: 'https://example.com' };
    const provider = factory.createProvider(config);
    expect(provider.name).toBe('mock');
  });
});
