import { ProviderRegistry } from '../../../../../lib/external-api/v2/providers/registry';
import type {
  IProvider,
  IClinicService,
  ILocationService,
  IPatientService,
  IUserService,
  IAppointmentService,
} from '../../../../../lib/external-api/v2/providers/types';

// Mock provider for testing
class MockProvider implements IProvider {
  name: string;

  constructor(name: string) {
    this.name = name;
  }

  getClinicService(): IClinicService {
    return {} as IClinicService;
  }

  getLocationService(): ILocationService {
    return {} as ILocationService;
  }

  getPatientService(): IPatientService {
    return {} as IPatientService;
  }

  getUserService(): IUserService {
    return {} as IUserService;
  }

  getAppointmentService(): IAppointmentService {
    return {} as IAppointmentService;
  }
}

describe('ProviderRegistry', () => {
  let registry: ProviderRegistry;
  let mockProvider1: IProvider;
  let mockProvider2: IProvider;

  beforeEach(() => {
    registry = new ProviderRegistry('provider1');
    mockProvider1 = new MockProvider('provider1');
    mockProvider2 = new MockProvider('provider2');
  });

  test('should register a provider', () => {
    registry.registerProvider(mockProvider1);
    const provider = registry.getProvider('provider1');
    expect(provider).toBe(mockProvider1);
  });

  test('should return the default provider when no name is provided', () => {
    registry.registerProvider(mockProvider1);
    const provider = registry.getProvider();
    expect(provider).toBe(mockProvider1);
  });

  test('should return all available providers', () => {
    registry.registerProvider(mockProvider1);
    registry.registerProvider(mockProvider2);
    const providers = registry.getAvailableProviders();
    expect(providers).toEqual(['provider1', 'provider2']);
  });

  test('should throw an error when provider is not found', () => {
    expect(() => registry.getProvider('nonexistent')).toThrow('Provider nonexistent not found');
  });
});
