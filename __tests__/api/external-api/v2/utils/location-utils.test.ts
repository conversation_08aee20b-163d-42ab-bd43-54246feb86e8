import { compactFormatLocationHours } from '@/lib/external-api/v2/utils/location-utils';

const FIVE_DAY_WEEK = {
  expected: ['M-F 9am-5pm'],
  input: {
    '1': { start: '09:00', end: '17:00' },
    '2': { start: '09:00', end: '17:00' },
    '3': { start: '09:00', end: '17:00' },
    '4': { start: '09:00', end: '17:00' },
    '5': { start: '09:00', end: '17:00' },
    '6': null,
    '7': null,
  },
};

const SIX_DAY_WEEK = {
  expected: ['M-F 9am-5pm', 'Sa 11am-2pm'],
  input: {
    '1': { start: '09:00', end: '17:00' },
    '2': { start: '09:00', end: '17:00' },
    '3': { start: '09:00', end: '17:00' },
    '4': { start: '09:00', end: '17:00' },
    '5': { start: '09:00', end: '17:00' },
    '6': { start: '11:00', end: '14:00' },
    '7': null,
  },
};

const SEVEN_DAY_WEEK_ODD = {
  expected: ['M-F 9am-5pm', 'Sa 9am-12pm', 'Su 10am-1pm'],
  input: {
    '1': { start: '09:00', end: '17:00' },
    '2': { start: '09:00', end: '17:00' },
    '3': { start: '09:00', end: '17:00' },
    '4': { start: '09:00', end: '17:00' },
    '5': { start: '09:00', end: '17:00' },
    '6': { start: '09:00', end: '12:00' },
    '7': { start: '10:00', end: '13:00' },
  },
};

const SEVEN_DAY_WEEK_EVEN = {
  expected: ['M-F 9am-5pm', 'Sa-Su 10am-1pm'],
  input: {
    '1': { start: '09:00', end: '17:00' },
    '2': { start: '09:00', end: '17:00' },
    '3': { start: '09:00', end: '17:00' },
    '4': { start: '09:00', end: '17:00' },
    '5': { start: '09:00', end: '17:00' },
    '6': { start: '10:00', end: '13:00' },
    '7': { start: '10:00', end: '13:00' },
  },
};

const SEVEN_DAY_WEEK_EVEN_WITH_GAP = {
  expected: ['M 9am-5pm', 'W-F 9am-5pm', 'Sa-Su 10am-1pm'],
  input: {
    '1': { start: '09:00', end: '17:00' },
    '2': null,
    '3': { start: '09:00', end: '17:00' },
    '4': { start: '09:00', end: '17:00' },
    '5': { start: '09:00', end: '17:00' },
    '6': { start: '10:00', end: '13:00' },
    '7': { start: '10:00', end: '13:00' },
  },
};

const WORKING_WEEK_END = {
  expected: ['F-Su 8am-7:30pm'],
  input: {
    '5': { start: '08:00', end: '19:30' },
    '6': { start: '08:00', end: '19:30' },
    '7': { start: '08:00', end: '19:30' },
  },
};

const TEST_CASES = [
  FIVE_DAY_WEEK,
  SIX_DAY_WEEK,
  SEVEN_DAY_WEEK_ODD,
  SEVEN_DAY_WEEK_EVEN,
  SEVEN_DAY_WEEK_EVEN_WITH_GAP,
  WORKING_WEEK_END,
];

describe('Location Utils', () => {
  describe('compactFormatLocationHours', () => {
    it.each(TEST_CASES)('should return the correct compact format', ({ expected, input }) => {
      const result = compactFormatLocationHours(input);
      expect(result).toEqual(expected);
    });
  });
});
