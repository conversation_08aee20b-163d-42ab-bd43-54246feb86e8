import {
  ApiError,
  BadRequestError,
  NotFoundError,
  handleError,
} from '../../../../../lib/external-api/v2/utils/errors';
import { NextApiResponse } from 'next';

describe('Error utilities', () => {
  describe('ApiError', () => {
    test('should create an error with the correct properties', () => {
      const error = new ApiError(400, 'BAD_REQUEST', 'Bad request', {
        field: 'value',
      });
      expect(error.status).toBe(400);
      expect(error.code).toBe('BAD_REQUEST');
      expect(error.message).toBe('Bad request');
      expect(error.details).toEqual({ field: 'value' });
    });

    test('should format as a response object', () => {
      const error = new ApiError(400, 'BAD_REQUEST', 'Bad request', {
        field: 'value',
      });
      const response = {
        status: error.status,
        code: error.code,
        message: error.message,
        details: error.details,
      };
      expect(response).toEqual({
        status: 400,
        code: 'BAD_REQUEST',
        message: 'Bad request',
        details: { field: 'value' },
      });
    });
  });

  describe('Error subclasses', () => {
    test('BadRequestError should have status 400', () => {
      const error = new BadRequestError('Invalid input');
      expect(error.status).toBe(400);
      expect(error.code).toBe('BAD_REQUEST');
    });

    test('NotFoundError should have status 404', () => {
      const error = new NotFoundError('Resource not found');
      expect(error.status).toBe(404);
      expect(error.code).toBe('NOT_FOUND');
    });
  });

  describe('handleError', () => {
    let mockResponse: jest.Mocked<NextApiResponse>;

    beforeEach(() => {
      mockResponse = {
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as jest.Mocked<NextApiResponse>;

      // Mock console.error to avoid cluttering test output
      jest.spyOn(console, 'error').mockImplementation(() => {});
    });

    afterEach(() => {
      jest.restoreAllMocks();
    });

    test('should handle ApiError', () => {
      const error = new BadRequestError('Invalid input');
      handleError(error, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 400,
          code: 'BAD_REQUEST',
          message: 'Invalid input',
        }),
      );
    });

    test('should handle standard Error', () => {
      const error = new Error('Standard error');
      handleError(error, mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 500,
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Standard error',
        }),
      );
    });

    test('should handle unknown error types', () => {
      handleError('Not an error', mockResponse);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 500,
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Unknown error',
        }),
      );
    });
  });
});
