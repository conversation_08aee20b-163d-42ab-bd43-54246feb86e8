import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/appointment-purposes';
import { providerRegistry } from '../../../../../lib/external-api/v2/providers';
import { MockProvider } from '../../../../../lib/external-api/v2/providers/mock';
import { ApiError } from '../../../../../lib/external-api/v2/utils/errors';
import { AppointmentPurpose } from '../../../../../lib/external-api/v2/models/types';
import { PaginatedResult } from '../../../../../lib/external-api/v2/models/pagination';

// Mock dependencies
jest.mock('../../../../../lib/external-api/v2/providers');
jest.mock('../../../../../lib/external-api/v2/utils/logger');

describe('/api/external-api/v2/appointment-purposes API Endpoint', () => {
  let mockProvider: MockProvider;

  beforeEach(() => {
    jest.clearAllMocks();
    mockProvider = new MockProvider();
    (providerRegistry.getProvider as jest.Mock).mockReturnValue(mockProvider);
  });

  it('should return 200 for non-GET requests', async () => {
    const methods: ('POST' | 'PUT' | 'DELETE' | 'PATCH')[] = ['POST', 'PUT', 'DELETE', 'PATCH'];
    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: method,
        headers: { 'x-api-key': 'test-api-key' },
        env: {},
      });
      await handler(req, res);
      expect(res.statusCode).toBe(200);
    }
  });

  it('should return 200 OK and appointment purposes on successful GET request', async () => {
    const mockAppointmentPurposes: AppointmentPurpose[] = [
      {
        id: 'purpose1',
        name: 'Annual Check-up',
        description: 'Regular annual check-up',
        isActive: true,
        providerInfo: { provider: 'mock', externalId: 'mock-purpose1' },
      },
    ];

    const mockPaginatedResult: PaginatedResult<AppointmentPurpose> = {
      items: mockAppointmentPurposes,
      pagination: {
        totalCount: mockAppointmentPurposes.length,
        limit: 10,
        offset: 0,
        hasMore: false,
        links: {},
      },
    };

    mockProvider.appointments.getAppointmentPurposes.mockResolvedValue(mockPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockPaginatedResult);
    expect(mockProvider.appointments.getAppointmentPurposes).toHaveBeenCalledWith({
      clinicId: 'clinic123',
    });
  });

  it('should return 200 when clinicId is missing', async () => {
    mockProvider.appointments.getAppointmentPurposes.mockResolvedValue({
      items: [],
      pagination: {
        totalCount: 0,
        limit: 10,
        offset: 0,
        hasMore: false,
        links: {},
      },
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {},
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.appointments.getAppointmentPurposes).toHaveBeenCalled();
  });

  it('should handle errors from the provider', async () => {
    const errorMessage = 'Failed to fetch appointment purposes';
    mockProvider.appointments.getAppointmentPurposes.mockRejectedValue(
      new ApiError(500, 'PROVIDER_ERROR', errorMessage),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData()).message).toBe(errorMessage);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_ERROR');
  });

  it('should use the provider specified in x-provider header', async () => {
    const specificProvider = new MockProvider();
    specificProvider.name = 'specific-mock';

    const emptyPaginatedResult: PaginatedResult<AppointmentPurpose> = {
      items: [],
      pagination: {
        totalCount: 0,
        limit: 10,
        offset: 0,
        hasMore: false,
        links: {},
      },
    };

    specificProvider.appointments.getAppointmentPurposes.mockResolvedValue(emptyPaginatedResult);

    (providerRegistry.getProvider as jest.Mock).mockImplementation((providerName?: string) => {
      if (providerName === 'specific-mock') {
        return specificProvider;
      }
      return mockProvider;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
        'x-provider': 'specific-mock',
      },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(providerRegistry.getProvider).toHaveBeenCalledWith('specific-mock');
    expect(specificProvider.appointments.getAppointmentPurposes).toHaveBeenCalledWith({
      clinicId: 'clinic123',
    });
    expect(mockProvider.appointments.getAppointmentPurposes).not.toHaveBeenCalled();
  });

  it('should return 401 when API key is missing', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {},
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('API key is required');
  });

  it('should return 401 when API key is invalid', async () => {
    (providerRegistry.getProvider as jest.Mock).mockImplementation(() => {
      throw new ApiError(401, 'UNAUTHORIZED', 'Invalid API key');
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'invalid-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('Invalid API key');
  });
});
