import { createMocks } from 'node-mocks-http';
import type { NextApiRequest, NextApiResponse } from 'next';

// Import the wrapped handler and the wrapper/middleware directly
import confirmHandler from '../../../../../../pages/api/external-api/v2/appointments/[id]/confirm';
import {
  // createApiHandler,
  // validate<PERSON><PERSON><PERSON><PERSON> as actualValidate<PERSON>pi<PERSON>ey,
  UnauthorizedError as ActualUnauthorizedError,
  BadRequestError as ActualBadRequestError,
  NotFoundError as ActualNotFoundError,
  // getProviderFromRequest as actualGetProviderFromRequest,
} from '../../../../../../lib/external-api/v2';
import { AppointmentStatus } from '../../../../../../lib/external-api/v2/models/types';
import { CallType } from '../../../../../../models/CallTypes';

// Mock the entire module containing the middleware and provider logic
jest.mock('../../../../../../lib/external-api/v2', () => {
  const originalModule = jest.requireActual('../../../../../../lib/external-api/v2');
  const mockAppointmentService = {
    getAppointmentById: jest.fn(),
    confirmAppointment: jest.fn(),
  };
  const mockProvider = {
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
  };
  return {
    ...originalModule,
    validateApiKey: jest.fn(),
    getProviderFromRequest: jest.fn().mockReturnValue(mockProvider),
    providerRegistry: {
      ...originalModule.providerRegistry,
      getProvider: jest.fn().mockReturnValue(mockProvider),
    },
    UnauthorizedError: originalModule.UnauthorizedError,
    BadRequestError: originalModule.BadRequestError,
    NotFoundError: originalModule.NotFoundError,
    createApiHandler: jest.fn().mockImplementation((handler, options = {}) => {
      return async (req: NextApiRequest, res: NextApiResponse) => {
        try {
          if (options.middleware) {
            for (const middleware of options.middleware) {
              await new Promise<void>((resolve, reject) => {
                try {
                  middleware(req, res, () => resolve());
                } catch (error) {
                  reject(error);
                }
              });
            }
          }
          return await handler(req, res);
        } catch (error: any) {
          if (
            error instanceof ActualUnauthorizedError ||
            (error.message && error.message.toLowerCase().includes('api key'))
          ) {
            res.status(401).json({
              status: 401,
              code: 'UNAUTHORIZED',
              message: error.message,
            });
          } else if (
            error instanceof ActualBadRequestError ||
            (error.message &&
              (error.message.toLowerCase().includes('invalid') ||
                error.message.toLowerCase().includes('missing')))
          ) {
            res.status(400).json({
              status: 400,
              code: 'BAD_REQUEST',
              message: error.message,
              details: error.details,
            });
          } else if (
            error instanceof ActualNotFoundError ||
            (error.message && error.message.toLowerCase().includes('not found'))
          ) {
            res.status(404).json({
              status: 404,
              code: 'NOT_FOUND',
              message: error.message,
            });
          } else {
            res.status(500).json({
              status: 500,
              code: 'INTERNAL_SERVER_ERROR',
              message: error.message,
            });
          }
        }
      };
    }),
  };
});

// Mock logger
jest.mock('../../../../../../lib/external-api/v2/utils/logger');

// Mock the call-type-utils module
jest.mock('../../../../../../lib/external-api/v2/utils/call-type-utils', () => ({
  updateCallSessionType: jest.fn().mockResolvedValue({}),
}));

// Get mock functions AFTER mocking the module
import {
  providerRegistry as mockedRegistry,
  validateApiKey,
  getProviderFromRequest,
} from '../../../../../../lib/external-api/v2';
import { updateCallSessionType } from '../../../../../../lib/external-api/v2/utils/call-type-utils';

// Cast mocks for type safety
const mockedValidateApiKey = validateApiKey as jest.Mock;
const mockedGetProviderFromRequest = getProviderFromRequest as jest.Mock;
const mockedUpdateCallSessionType = updateCallSessionType as jest.Mock;

describe('API Route: /api/external-api/v2/appointments/[id]/confirm', () => {
  // --- Mock Data Setup ---
  const mockAppointment = {
    id: 'appointment1',
    patientId: 'patient1',
    providerId: 'provider1',
    locationId: 'location1',
    clinicId: 'clinic1',
    startTime: '2023-04-01T10:00:00Z',
    endTime: '2023-04-01T11:00:00Z',
    status: AppointmentStatus.PENDING,
    type: 'Check-up',
    reason: 'Annual check-up',
    notes: 'Patient requested morning appointment',
    createdAt: '2023-03-15T08:00:00Z',
    updatedAt: '2023-03-15T08:00:00Z',
    providerInfo: {
      provider: 'nextech',
      externalId: 'ext1',
    },
  };

  const confirmedAppointment = {
    ...mockAppointment,
    status: AppointmentStatus.BOOKED,
  };

  // --- End Mock Data Setup ---

  let mockProvider: any;
  let mockAppointmentService: any;
  let testHandler: (req: NextApiRequest, res: NextApiResponse) => Promise<void>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockProvider = mockedRegistry.getProvider();
    mockAppointmentService = mockProvider.getAppointmentService();
    mockedValidateApiKey.mockImplementation((req, res, next) => next());
    mockedGetProviderFromRequest.mockReturnValue(mockProvider);
    mockedUpdateCallSessionType.mockResolvedValue({});

    testHandler = confirmHandler;
  });

  // --- POST Tests ---
  describe('POST', () => {
    it('should confirm an appointment successfully', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
      expect(mockAppointmentService.getAppointmentById).toHaveBeenCalledWith('appointment1');
      expect(mockAppointmentService.confirmAppointment).toHaveBeenCalledWith('appointment1');
    });

    it('should confirm an appointment with session ID in query params', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1', sessionId: 'session123' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
      expect(mockedUpdateCallSessionType).toHaveBeenCalledWith(
        'session123',
        CallType.CONFIRM_APPOINTMENT,
        'Appointment Confirmation',
      );
    });

    it('should confirm an appointment with session ID in body', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
        body: { sessionId: 'session456' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
      expect(mockedUpdateCallSessionType).toHaveBeenCalledWith(
        'session456',
        CallType.CONFIRM_APPOINTMENT,
        'Appointment Confirmation',
      );
    });

    it('should prioritize session ID from query params over body', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1', sessionId: 'session123' },
        body: { sessionId: 'session456' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(mockedUpdateCallSessionType).toHaveBeenCalledWith(
        'session123',
        CallType.CONFIRM_APPOINTMENT,
        'Appointment Confirmation',
      );
    });

    it('should handle session update failure gracefully', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);
      mockedUpdateCallSessionType.mockRejectedValueOnce(new Error('Session update failed'));

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1', sessionId: 'session123' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
      expect(mockAppointmentService.confirmAppointment).toHaveBeenCalledWith('appointment1');
    });

    it('should return 404 if appointment not found', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(null);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'not-found' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData()).code).toBe('NOT_FOUND');
      expect(JSON.parse(res._getData()).message).toBe('Appointment not found');
    });

    it('should return 400 if appointment ID is missing', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: {},
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).code).toBe('BAD_REQUEST');
      expect(JSON.parse(res._getData()).message).toBe('Invalid or missing appointment ID');
    });

    it('should return 400 if appointment ID is empty', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: '' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).code).toBe('BAD_REQUEST');
      expect(JSON.parse(res._getData()).message).toBe('Invalid or missing appointment ID');
    });

    it('should return 400 if appointment ID is whitespace only', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: '   ' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).code).toBe('BAD_REQUEST');
      expect(JSON.parse(res._getData()).message).toBe('Invalid or missing appointment ID');
    });

    it('should return 401 if API key is missing', async () => {
      mockedValidateApiKey.mockImplementation(() => {
        throw new ActualUnauthorizedError('API key is required');
      });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData()).code).toBe('UNAUTHORIZED');
    });

    it('should return 401 if API key is invalid', async () => {
      mockedValidateApiKey.mockImplementation(() => {
        throw new ActualUnauthorizedError('Invalid API key');
      });

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'invalid-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData()).code).toBe('UNAUTHORIZED');
    });

    it('should work without session ID', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
      expect(mockedUpdateCallSessionType).not.toHaveBeenCalled();
    });

    it('should handle provider parameter in query', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockResolvedValueOnce(true);

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1', provider: 'nextech' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual(confirmedAppointment);
    });
  });

  // --- Unsupported Method Tests ---
  describe('Unsupported Methods', () => {
    it('should return 405 for GET method', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'GET',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        message: 'Method GET Not Allowed',
      });
    });

    it('should return 405 for PUT method', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'PUT',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        message: 'Method PUT Not Allowed',
      });
    });

    it('should return 405 for PATCH method', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'PATCH',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        message: 'Method PATCH Not Allowed',
      });
    });

    it('should return 405 for DELETE method', async () => {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'DELETE',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toEqual({
        message: 'Method DELETE Not Allowed',
      });
    });
  });

  // --- Error Handling Tests ---
  describe('Error Handling', () => {
    it('should handle appointment service errors', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockRejectedValueOnce(
        new Error('Database connection failed'),
      );

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(500);
      expect(JSON.parse(res._getData()).code).toBe('INTERNAL_SERVER_ERROR');
    });

    it('should handle confirm appointment service errors', async () => {
      (mockAppointmentService.getAppointmentById as jest.Mock).mockResolvedValueOnce(
        mockAppointment,
      );
      (mockAppointmentService.confirmAppointment as jest.Mock).mockRejectedValueOnce(
        new Error('Confirmation failed'),
      );

      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: 'POST',
        headers: { 'x-api-key': 'test-key' },
        query: { id: 'appointment1' },
      });

      await testHandler(req as unknown as NextApiRequest, res as NextApiResponse);

      expect(res._getStatusCode()).toBe(500);
      expect(JSON.parse(res._getData()).code).toBe('INTERNAL_SERVER_ERROR');
    });
  });
});
