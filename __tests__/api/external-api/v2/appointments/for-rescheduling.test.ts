import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';
// Mock Firebase before importing anything else
jest.mock('@/utils/firestore', () => ({
  callSessionsService: {
    updateCallSession: jest.fn(),
    getCallSession: jest.fn(),
  },
  callsService: {
    updateCall: jest.fn(),
    getCall: jest.fn(),
  },
}));

import handler from '@/pages/api/external-api/v2/appointments/for-rescheduling';
import { AppointmentStatus } from '@/lib/external-api/v2/models/types';

// Mock the validateApiKey middleware
jest.mock('@/lib/external-api/v2/middleware/auth', () => ({
  validateApiKey: jest.fn((req: any, res: any, next: any) => next(req, res)),
  applyMiddleware: jest.fn(
    (middleware: any) => (req: any, res: any, handler: any) => handler(req, res),
  ),
}));

// Mock the ensureProvidersInitialized middleware and initialization
jest.mock('@/lib/external-api/v2/init', () => ({
  ensureProvidersInitialized: jest.fn((req: any, res: any, next: any) => next(req, res)),
  initializeExternalApi: jest.fn(),
}));

// Mock the provider registry and appointment service
jest.mock('@/lib/external-api/v2/providers', () => {
  const mockAppointmentService = {
    getAppointmentsForRescheduling: jest.fn(),
  };

  const mockProvider = {
    name: 'nextech',
    getAppointmentService: jest.fn(() => mockAppointmentService),
  };

  return {
    providerRegistry: {
      getProvider: jest.fn(() => mockProvider),
      getAvailableProviders: jest.fn(() => ['nextech']),
    },
  };
});

// Import the mocked modules
import { providerRegistry } from '@/lib/external-api/v2/providers';

describe('GET /api/external-api/v2/appointments/for-rescheduling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Mock appointment data
  const mockAppointments = {
    items: [
      {
        id: 'appt-123',
        patientId: 'patient-123',
        providerId: 'provider-123',
        locationId: 'location-123',
        clinicId: 'clinic-123',
        startTime: '2099-05-15T10:00:00Z',
        endTime: '2099-05-15T11:00:00Z',
        status: AppointmentStatus.BOOKED,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
        providerInfo: {
          provider: 'nextech',
          externalId: 'appt-123',
        },
      },
    ],
    pagination: {
      totalCount: 1,
      limit: 30,
      offset: 0,
      hasMore: false,
      links: {},
    },
  };

  it('should return 405 for non-GET requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: { 'x-api-key': 'test-api-key' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({ message: 'Method POST Not Allowed' });
  });

  it('should return 400 if patientId is missing', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { isForRescheduling: 'true' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request parameters');
  });

  it('should return appointments for a patient with isForRescheduling=false', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: 'false' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      false,
    );
  });

  it('should return appointments for a patient with isForRescheduling=true', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: 'true' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      true,
    );
  });

  it('should handle numeric values for isForRescheduling', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123', isForRescheduling: '1' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      true,
    );
  });

  it('should use default value for isForRescheduling if not provided', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockResolvedValueOnce(
      mockAppointments,
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockAppointments);
    expect(mockAppointmentService.getAppointmentsForRescheduling).toHaveBeenCalledWith(
      'patient-123',
      false,
    );
  });

  it('should handle errors gracefully', async () => {
    // Get the mock appointment service
    const mockProvider = providerRegistry.getProvider();
    const mockAppointmentService = mockProvider.getAppointmentService();

    // Mock the getAppointmentsForRescheduling method to throw an error
    (mockAppointmentService.getAppointmentsForRescheduling as jest.Mock).mockRejectedValueOnce(
      new Error('API Error'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { patientId: 'patient-123' },
    });

    await handler(req as unknown as NextApiRequest, res as NextApiResponse);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'API Error');
  });
});
