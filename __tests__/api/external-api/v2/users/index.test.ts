import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/users'; // Adjust path if needed
import { providerRegistry } from '../../../../../lib/external-api/v2/providers';
import { MockProvider } from '../../../../../lib/external-api/v2/providers/mock';
import { ApiError } from '../../../../../lib/external-api/v2/utils/errors';
import { User } from '../../../../../lib/external-api/v2/models/types';
import { PaginatedResult } from '../../../../../lib/external-api/v2/models/pagination';

// Mock dependencies
jest.mock('../../../../../lib/external-api/v2/providers');
jest.mock('../../../../../lib/external-api/v2/utils/logger');

describe('/api/external-api/v2/users API Endpoint', () => {
  let mockProvider: MockProvider;

  // Define empty paginated result for reuse
  const emptyPaginatedResult: PaginatedResult<User> = {
    items: [],
    pagination: {
      totalCount: 0,
      limit: 10,
      offset: 0,
      hasMore: false,
      links: {},
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockProvider = new MockProvider();
    (providerRegistry.getProvider as jest.Mock).mockReturnValue(mockProvider);
  });

  it('should return 405 Method Not Allowed for non-GET requests', async () => {
    const methods: ('POST' | 'PUT' | 'DELETE' | 'PATCH')[] = ['POST', 'PUT', 'DELETE', 'PATCH'];
    for (const method of methods) {
      const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
        method: method,
        headers: { 'x-api-key': 'test-api-key' },
        env: {},
      });
      await handler(req, res);
      expect(res.statusCode).toBe(405);
      expect(JSON.parse(res._getData()).message).toBe('Method not allowed');
    }
  });

  it('should return 200 OK and users on successful GET request', async () => {
    const mockUsers: User[] = [
      {
        id: 'user1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        speciality: 'Dermatology',
        phoneNumber: '************',
        role: 'doctor',
        providerInfo: { provider: 'mock', externalId: 'mock-user1' },
      },
    ];

    const mockPaginatedResult: PaginatedResult<User> = {
      items: mockUsers,
      pagination: {
        totalCount: mockUsers.length,
        limit: 10,
        offset: 0,
        hasMore: false,
        links: {},
      },
    };

    mockProvider.getUsers.mockResolvedValue(mockPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockPaginatedResult);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
    });
  });

  it('should use the provider specified in x-provider header', async () => {
    const specificProvider = new MockProvider();
    specificProvider.name = 'specific-mock';

    specificProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    (providerRegistry.getProvider as jest.Mock).mockImplementation((providerName?: string) => {
      if (providerName === 'specific-mock') {
        return specificProvider;
      }
      return mockProvider;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
        'x-provider': 'specific-mock',
      },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(providerRegistry.getProvider).toHaveBeenCalledWith('specific-mock');
    expect(specificProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
    });
    expect(mockProvider.getUsers).not.toHaveBeenCalled();
  });

  it('should handle validation errors', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {}, // Missing clinicId or other required filters
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
  });

  it('should handle provider service errors', async () => {
    const errorMessage = 'Failed to fetch users';
    mockProvider.getUsers.mockRejectedValue(new ApiError(500, 'PROVIDER_ERROR', errorMessage));

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData()).message).toBe(errorMessage);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_ERROR');
  });

  it('should handle authentication errors - missing API key', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {}, // No API key
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('API key is required');
  });

  it('should handle authentication errors - invalid API key', async () => {
    (providerRegistry.getProvider as jest.Mock).mockImplementation(() => {
      throw new ApiError(401, 'UNAUTHORIZED', 'Invalid API key');
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'invalid-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).message).toContain('Invalid API key');
  });

  // Tests for different query parameters/filters
  it('should pass firstName filter to provider', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', firstName: 'Doctor' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      firstName: 'Doctor',
    });
  });

  it('should pass lastName filter to provider', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', lastName: 'Smith' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      lastName: 'Smith',
    });
  });

  it('should pass role filter to provider', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', role: 'physician' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      role: 'physician',
    });
  });

  it('should pass multiple filters to provider', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const queryParams = {
      clinicId: 'clinic123',
      firstName: 'Doctor',
      lastName: 'Smith',
      role: 'physician',
      email: '<EMAIL>',
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: queryParams,
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith(queryParams);
  });

  // Test pagination parameters
  it('should pass limit and offset filters to provider', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const queryParams = {
      clinicId: 'clinic123',
      limit: '10',
      offset: '20',
    };

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: queryParams,
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    // Expect numbers to be passed after parsing
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      limit: 10,
      offset: 20,
    });
  });

  // Test empty result
  it('should return 200 OK with an empty array when provider returns no users', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult); // Explicitly return empty paginated result

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(emptyPaginatedResult);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
    });
  });

  // Validation error tests (Handler-level)
  it('should return 400 if limit parameter is not a number', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', limit: 'not-a-number' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('BAD_REQUEST');
    expect(JSON.parse(res._getData()).message).toContain('Invalid request parameters');
    expect(mockProvider.getUsers).not.toHaveBeenCalled();
  });

  it('should return 400 if offset parameter is not a number', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', offset: 'invalid' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('BAD_REQUEST');
    expect(JSON.parse(res._getData()).message).toContain('Invalid request parameters');
    expect(mockProvider.getUsers).not.toHaveBeenCalled();
  });

  // Validation error tests (Provider-level - existing)
  it('should return 400 when clinicId is missing (simulated provider error)', async () => {
    // Mock provider to throw a validation error
    mockProvider.getUsers.mockImplementationOnce(() => {
      const error = new Error('Clinic ID is required');
      (error as any).status = 400;
      (error as any).code = 'VALIDATION_ERROR';
      throw error;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {}, // No clinicId provided
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toEqual(expect.any(String));
    expect(mockProvider.getUsers).toHaveBeenCalled();
  });

  it('should return 400 for invalid role format (simulated provider error)', async () => {
    // Mock provider to throw a validation error
    mockProvider.getUsers.mockImplementationOnce(() => {
      const error = new Error('Invalid role');
      (error as any).status = 400;
      (error as any).code = 'VALIDATION_ERROR';
      throw error;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123', role: 'invalid-role' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('VALIDATION_ERROR');
  });

  // Provider-specific error test
  it('should handle provider-specific validation errors', async () => {
    mockProvider.getUsers.mockRejectedValue(
      new ApiError(400, 'PROVIDER_VALIDATION_ERROR', 'Provider-specific validation failed'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(400);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_VALIDATION_ERROR');
  });

  it('should handle rate limiting errors from provider', async () => {
    mockProvider.getUsers.mockRejectedValue(
      new ApiError(429, 'RATE_LIMIT_EXCEEDED', 'Too many requests'),
    );

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { clinicId: 'clinic123' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(429);
    expect(JSON.parse(res._getData()).code).toBe('RATE_LIMIT_EXCEEDED');
  });

  it('should return 400 Bad Request when clinicId is missing', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {}, // No query params
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
  });

  it('should handle filter parameters correctly', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {
        clinicId: 'clinic123',
        name: 'Smith',
        role: 'doctor',
      },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      name: 'Smith',
      role: 'doctor',
    });
  });

  it('should handle pagination parameters correctly', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {
        clinicId: 'clinic123',
        limit: '20',
        offset: '10',
      },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      limit: 20,
      offset: 10,
    });
  });

  it('should handle sorting parameters correctly', async () => {
    mockProvider.getUsers.mockResolvedValue(emptyPaginatedResult);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: {
        clinicId: 'clinic123',
        sort: 'lastName',
        order: 'desc',
      },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockProvider.getUsers).toHaveBeenCalledWith({
      clinicId: 'clinic123',
      sort: 'lastName',
      order: 'desc',
    });
  });
});
