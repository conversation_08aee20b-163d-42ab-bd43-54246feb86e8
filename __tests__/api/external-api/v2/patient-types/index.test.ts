import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../pages/api/external-api/v2/patient-types';
import { providerRegistry } from '../../../../../lib/external-api/v2/providers';
import { ApiError } from '../../../../../lib/external-api/v2/utils/errors';
import { PatientType } from '../../../../../lib/external-api/v2/models/types';

// Mock dependencies
jest.mock('../../../../../lib/external-api/v2/providers');
jest.mock('../../../../../lib/external-api/v2/utils/logger');

describe('/api/external-api/v2/patient-types API Endpoint', () => {
  // Create mock patient service
  const mockPatientService = {
    getPatientTypes: jest.fn().mockResolvedValue([]),
    getPatientTypeById: jest.fn().mockResolvedValue(null),
  };

  // Create mock provider
  const mockProvider = {
    name: 'mock',
    getPatientService: jest.fn().mockReturnValue(mockPatientService),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Set up the mock provider registry
    (providerRegistry.getProvider as jest.Mock).mockReturnValue(mockProvider);
  });

  // Basic success case
  it('should return 200 OK and patient types on successful GET request', async () => {
    const mockPatientTypes: PatientType[] = [
      {
        id: 'type1',
        name: 'Adult',
        description: 'Adult patient',
        isActive: true,
        providerInfo: { provider: 'mock', externalId: 'mock-type1' },
      },
      {
        id: 'type2',
        name: 'Pediatric',
        description: 'Pediatric patient',
        isActive: true,
        providerInfo: { provider: 'mock', externalId: 'mock-type2' },
      },
    ];
    mockPatientService.getPatientTypes.mockResolvedValue(mockPatientTypes);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(JSON.parse(res._getData())).toEqual(mockPatientTypes);
    expect(mockPatientService.getPatientTypes).toHaveBeenCalledWith({});
  });

  // Test with query parameters
  it('should pass query parameters to the provider service', async () => {
    mockPatientService.getPatientTypes.mockResolvedValue([]);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      query: { name: 'Adult', isActive: 'true' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(mockPatientService.getPatientTypes).toHaveBeenCalledWith({
      name: 'Adult',
      isActive: 'true',
    });
  });

  // Error case
  it('should handle errors from the provider', async () => {
    const errorMessage = 'Failed to fetch patient types';
    const providerError = new ApiError(500, 'PROVIDER_ERROR', errorMessage);
    mockPatientService.getPatientTypes.mockRejectedValue(providerError);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: { 'x-api-key': 'test-api-key' },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(500);
    expect(JSON.parse(res._getData()).message).toBe(errorMessage);
    expect(JSON.parse(res._getData()).code).toBe('PROVIDER_ERROR');
  });

  // Test with different provider
  it('should use the provider specified in x-provider header', async () => {
    const specificPatientService = {
      getPatientTypes: jest.fn().mockResolvedValue([]),
      getPatientTypeById: jest.fn().mockResolvedValue(null),
    };

    const specificProvider = {
      name: 'specific-mock',
      getPatientService: jest.fn().mockReturnValue(specificPatientService),
    };

    (providerRegistry.getProvider as jest.Mock).mockImplementation((providerName?: string) => {
      if (providerName === 'specific-mock') {
        return specificProvider;
      }
      return mockProvider;
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
        'x-provider': 'specific-mock',
      },
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(200);
    expect(providerRegistry.getProvider).toHaveBeenCalledWith('specific-mock');
    expect(specificPatientService.getPatientTypes).toHaveBeenCalledTimes(1);
    expect(mockPatientService.getPatientTypes).not.toHaveBeenCalled();
  });

  // Authorization error
  it('should return 401 when API key is missing', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {}, // No API key
      env: {},
    });

    await handler(req, res);

    expect(res.statusCode).toBe(401);
    expect(JSON.parse(res._getData()).code).toBe('UNAUTHORIZED');
  });
});
