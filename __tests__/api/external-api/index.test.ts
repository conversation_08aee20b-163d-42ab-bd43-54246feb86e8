import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/external-api';

describe('External API Root Endpoint', () => {
  it('should return API information and available endpoints', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);

    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('message', 'Front Desk Portal External API');
    expect(data).toHaveProperty('documentation', '/api/swagger');
    expect(data).toHaveProperty('versions.v1.baseEndpoint', '/api/external-api/v1');
    expect(data.versions.v1.availableEndpoints).toEqual(
      expect.arrayContaining([
        '/api/external-api/v1/user-specialties',
        '/api/external-api/v1/appointments/active',
        '/api/external-api/v1/calendar/next-available',
        '/api/external-api/v1/calendar/add-appointment',
        '/api/external-api/v1/calendar/cancel-appointment',
        '/api/external-api/v1/clinics/by-phone',
      ]),
    );
    // Also test v2 endpoints are present
    expect(data).toHaveProperty('versions.v2.baseEndpoint', '/api/external-api/v2');
    expect(data.versions.v2.availableEndpoints).toEqual(
      expect.arrayContaining(['/api/external-api/v2/clinics', '/api/external-api/v2/locations']),
    );
  });

  it('should redirect to v1 endpoint for other requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      url: '/api/external-api/some-endpoint',
    });

    // Mock the redirect method
    res.redirect = jest.fn();

    await handler(req, res);

    expect(res.redirect).toHaveBeenCalledWith(308, '/api/external-api/v1/some-endpoint');
  });
});
