// Import only what we need
import { createMockApiContext } from '../../utils/testHelpers';
import handler from '@/pages/api/calls/index';
import { callsService } from '@/utils/firestore';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

// Mock firebase-admin
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

// Mock firestore services
jest.mock('@/utils/firestore', () => ({
  callsService: {
    paginateCalls: jest.fn(),
  },
}));

// Mock the location middleware
jest.mock('@/lib/middleware/locationAware', () => ({
  withLocationContext: (handler: any) => handler,
  addLocationFilter: jest.fn((req, params) => ({
    ...params,
    locationId: req.currentLocation?.id,
  })),
}));

describe('Calls API Endpoint', () => {
  // Mock console.error to prevent error output during tests
  const originalConsoleError = console.error;

  beforeEach(() => {
    jest.clearAllMocks();
    // Silence console.error during tests
    console.error = jest.fn();

    // Mock successful authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({
      id: 'user123',
      role: 'STAFF',
      clinicId: 12,
      locationIds: ['loc1', 'loc2'],
      currentLocationId: 'loc1',
    });

    // Mock callsService.paginateCalls
    (callsService.paginateCalls as jest.Mock).mockResolvedValue({
      calls: [],
      lastDocId: undefined,
      isLastPage: true,
    });
  });

  afterEach(() => {
    // Restore original console.error
    console.error = originalConsoleError;
  });

  it('should reject non-GET requests', async () => {
    const { req, res } = createMockApiContext({
      method: 'POST',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should reject unauthenticated requests', async () => {
    // Mock failed authentication
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue(null);

    const { req, res } = createMockApiContext({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Unauthorized',
    });
  });

  it('should return calls for authenticated user', async () => {
    const { req, res } = createMockApiContext({
      method: 'GET',
      query: { limit: '10' },
    });

    // Mock the request to have location context (simulating middleware)
    req.user = {
      id: 'user123',
      role: 'STAFF',
      clinicId: 12,
      locationIds: ['loc1', 'loc2'],
      currentLocationId: 'loc1',
    };
    req.currentLocation = {
      id: 'loc1',
      name: 'Main Location',
      practiceId: 'practice1',
    };

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(callsService.paginateCalls).toHaveBeenCalledWith({
      limit: 10,
      startAfterId: undefined,
      clinicId: 12,
      locationId: 'loc1',
      callDirection: 'inbound',
      startDate: undefined,
      endDate: undefined,
      searchTerm: undefined,
      callType: undefined,
      minPriority: undefined,
      maxPriority: undefined,
      officeHoursOnly: false,
    });
  });
});
