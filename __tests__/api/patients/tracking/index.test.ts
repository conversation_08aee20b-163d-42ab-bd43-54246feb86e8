import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '@/pages/api/patients/tracking/index';
import { patientFactory } from '@/lib/factories/patient-factory';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { NotFoundError } from '@/lib/external-api/v2/utils/errors';

// Helper function to create mocks with the required env property
function createNextApiMocks(options: any) {
  return createMocks<NextApiRequest, NextApiResponse>({
    ...options,
    env: {},
  });
}

// Mock Firebase Admin
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

// Mock the patient factory
jest.mock('@/lib/factories/patient-factory', () => ({
  patientFactory: {
    getPatientCoordinatorService: jest.fn(),
  },
}));

describe('/api/patients/tracking', () => {
  let mockPatientCoordinatorService: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Mock authenticated user
    (verifyAuthAndGetUser as jest.Mock).mockResolvedValue({
      id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'staff',
    });

    // Create mock PatientCoordinatorService
    mockPatientCoordinatorService = {
      getPatientByProviderId: jest.fn(),
    };

    // Mock the patientFactory.getPatientCoordinatorService method
    (patientFactory.getPatientCoordinatorService as jest.Mock).mockReturnValue(
      mockPatientCoordinatorService,
    );
  });

  describe('POST /api/patients/tracking', () => {
    it('should return 405 Method Not Allowed', async () => {
      // Arrange
      const patientData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        email: '<EMAIL>',
        phoneNumber: '************',
        address: {
          line1: '123 Main St',
          city: 'Anytown',
          state: 'CA',
          postalCode: '12345',
          country: 'US',
        },
      };

      // Create mock request and response
      const { req, res } = createNextApiMocks({
        method: 'POST',
        body: patientData,
      });

      // Act
      await handler(req, res);

      // Assert
      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData())).toHaveProperty(
        'message',
        'Method not allowed. Use the external API to create patients.',
      );
      // No createPatient method should be called
    });
  });

  describe('GET /api/patients/tracking', () => {
    it('should get a patient by provider ID', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      const patientReference = {
        id: 'ref-123',
        provider,
        providerId,
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1980-01-01',
        email: '<EMAIL>',
        phoneNumber: '************',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Mock the getPatientByProviderId method
      mockPatientCoordinatorService.getPatientByProviderId.mockResolvedValueOnce(patientReference);

      // Create mock request and response
      const { req, res } = createNextApiMocks({
        method: 'GET',
        query: {
          provider,
          providerId,
        },
      });

      // Act
      await handler(req, res);

      // Assert
      expect(res._getStatusCode()).toBe(200);

      // Parse the response and handle date serialization
      const responseData = JSON.parse(res._getData());
      expect(responseData).toEqual({
        ...patientReference,
        createdAt: patientReference.createdAt.toISOString(),
        updatedAt: patientReference.updatedAt.toISOString(),
      });
      expect(mockPatientCoordinatorService.getPatientByProviderId).toHaveBeenCalledWith(
        provider,
        providerId,
      );
    });

    it('should return 400 for invalid query parameters', async () => {
      // Arrange
      // Missing providerId
      const query = {
        provider: 'nextech',
      };

      // Create mock request and response
      const { req, res } = createNextApiMocks({
        method: 'GET',
        query,
      });

      // Act
      await handler(req, res);

      // Assert
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid query parameters');
      expect(JSON.parse(res._getData())).toHaveProperty('errors');
      expect(mockPatientCoordinatorService.getPatientByProviderId).not.toHaveBeenCalled();
    });

    it('should return 404 if patient is not found', async () => {
      // Arrange
      const provider = 'nextech';
      const providerId = 'ext-123';

      // Mock the getPatientByProviderId method to throw a NotFoundError
      mockPatientCoordinatorService.getPatientByProviderId.mockRejectedValueOnce(
        new NotFoundError(`Patient not found in ${provider} with ID ${providerId}`),
      );

      // Create mock request and response
      const { req, res } = createNextApiMocks({
        method: 'GET',
        query: {
          provider,
          providerId,
        },
      });

      // Act
      await handler(req, res);

      // Assert
      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData())).toHaveProperty(
        'message',
        `Patient not found in ${provider} with ID ${providerId}`,
      );
    });

    it('should return 401 if not authenticated', async () => {
      // Arrange
      (verifyAuthAndGetUser as jest.Mock).mockResolvedValueOnce(null);

      const provider = 'nextech';
      const providerId = 'ext-123';

      // Create mock request and response
      const { req, res } = createNextApiMocks({
        method: 'GET',
        query: {
          provider,
          providerId,
        },
      });

      // Act
      await handler(req, res);

      // Assert
      expect(res._getStatusCode()).toBe(401);
      expect(JSON.parse(res._getData())).toHaveProperty('message', 'Unauthorized');
      expect(mockPatientCoordinatorService.getPatientByProviderId).not.toHaveBeenCalled();
    });
  });

  it('should return 405 for unsupported methods', async () => {
    // Arrange
    const { req, res } = createNextApiMocks({
      method: 'PUT',
    });

    // Act
    await handler(req, res);

    // Assert
    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Method not allowed');
  });
});
