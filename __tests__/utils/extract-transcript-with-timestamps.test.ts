import { DialogflowConversation } from '@/lib/dialogflow';
import { TranscriptEntry } from '@/utils/gcp-storage';
import { GcpStorageService } from '@/utils/gcp-storage';

// Mock the conversation example
const mockConversation: DialogflowConversation = {
  name: 'test-conversation',
  interactions: [
    {
      createTime: '2023-01-01T12:00:00Z',
      request: {
        queryInput: {
          text: {
            text: 'Hello. I need a doctor.',
          },
        },
      },
      response: {
        queryResult: {
          responseMessages: [
            {
              text: {
                text: ['How can I help you?'],
              },
            },
          ],
        },
      },
      partialResponses: [
        {
          queryResult: {
            responseMessages: [
              {
                outputAudioText: {
                  text: 'Just a moment',
                },
              },
            ],
          },
        },
      ],
    },
    {
      createTime: '2023-01-01T12:01:00Z',
      request: {
        queryInput: {
          text: {
            text: "No, I don't have a vehicle. I need an appointment.",
          },
        },
      },
      response: {
        queryResult: {
          responseMessages: [
            {
              text: {
                text: ['Okay, I can help you with that!'],
              },
            },
          ],
        },
      },
      partialResponses: [
        {
          queryResult: {
            responseMessages: [
              {
                text: {
                  text: ['Thanks for your patience.'],
                },
              },
            ],
          },
        },
      ],
    },
  ],
};

describe('extractTranscriptWithTimestamps', () => {
  it('should extract transcript with timestamps including partial responses', async () => {
    // Create an instance of GcpStorageService
    const storageService = new GcpStorageService();

    // Access the private method using type assertion and unknown as intermediate step
    const extractTranscriptWithTimestamps = (
      storageService as unknown as {
        extractTranscriptWithTimestamps: (
          conversation: DialogflowConversation,
        ) => TranscriptEntry[];
      }
    ).extractTranscriptWithTimestamps.bind(storageService);

    // Call the method with the mock conversation
    const result = extractTranscriptWithTimestamps(mockConversation) as TranscriptEntry[];

    // Verify the result includes both user messages, agent final responses, and agent partial responses
    expect(result).toHaveLength(6); // 2 user messages + 2 agent final responses + 2 agent partial responses

    // Verify the transcript entries have the correct structure and content
    expect(result.map((entry: TranscriptEntry) => entry.text)).toEqual([
      'Hello. I need a doctor.',
      'Just a moment',
      'How can I help you?',
      "No, I don't have a vehicle. I need an appointment.",
      'Thanks for your patience.',
      'Okay, I can help you with that!',
    ]);

    // Verify the speakers are correctly assigned
    expect(result.filter((entry: TranscriptEntry) => entry.speaker === 'Patient')).toHaveLength(2);
    expect(result.filter((entry: TranscriptEntry) => entry.speaker === 'Heather')).toHaveLength(4);

    // Verify the timestamps are in chronological order
    const timestamps = result.map((entry: TranscriptEntry) => entry.timestamp);
    expect(timestamps).toEqual([...timestamps].sort((a, b) => a - b));
  });
});
