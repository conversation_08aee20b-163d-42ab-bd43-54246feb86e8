import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';

describe('External API Authentication Middleware', () => {
  beforeEach(() => {
    // Set up environment variables for testing
    process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    // Clean up environment
    jest.resetModules();
  });

  it('should allow requests with valid API key', async () => {
    // Create a mock handler that will be wrapped by the middleware
    const mockHandler = jest.fn((req, res) => {
      return res.status(200).json({ success: true });
    });

    // Create mocked request and response
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
    });

    // Wrap the mock handler with the auth middleware
    const wrappedHandler = withExternalApiAuth(mockHandler);

    // Call the wrapped handler
    await wrappedHandler(req, res);

    // Expect the mock handler to have been called
    expect(mockHandler).toHaveBeenCalled();
    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({ success: true });
  });

  it('should reject requests with missing API key', async () => {
    // Create a mock handler that will be wrapped by the middleware
    const mockHandler = jest.fn((req, res) => {
      return res.status(200).json({ success: true });
    });

    // Create mocked request and response with no API key
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {},
    });

    // Wrap the mock handler with the auth middleware
    const wrappedHandler = withExternalApiAuth(mockHandler);

    // Call the wrapped handler
    await wrappedHandler(req, res);

    // Expect the mock handler to NOT have been called
    expect(mockHandler).not.toHaveBeenCalled();
    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Unauthorized - Invalid or missing API key',
    });
  });

  it('should reject requests with invalid API key', async () => {
    // Create a mock handler that will be wrapped by the middleware
    const mockHandler = jest.fn((req, res) => {
      return res.status(200).json({ success: true });
    });

    // Create mocked request and response with invalid API key
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'invalid-key',
      },
    });

    // Wrap the mock handler with the auth middleware
    const wrappedHandler = withExternalApiAuth(mockHandler);

    // Call the wrapped handler
    await wrappedHandler(req, res);

    // Expect the mock handler to NOT have been called
    expect(mockHandler).not.toHaveBeenCalled();
    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Unauthorized - Invalid or missing API key',
    });
  });
});
