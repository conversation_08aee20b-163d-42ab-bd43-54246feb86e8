import { NextApiRequest, NextApiResponse } from 'next';
import { createMocks } from 'node-mocks-http';

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS';

/**
 * <PERSON>reate mocked Next.js API request and response objects
 */
export function createMockApiContext(options: {
  method?: RequestMethod;
  body?: Record<string, unknown>;
  query?: Record<string, string | string[]>;
  headers?: Record<string, string>;
}) {
  const {
    method = 'GET',
    body = {},
    query = {},
    headers = {
      'x-api-key': process.env.EXTERNAL_SERVICE_API_KEY || 'test-api-key',
    },
  } = options;

  return createMocks<NextApiRequest, NextApiResponse>({
    method,
    body,
    query,
    headers,
  });
}

/**
 * Mock Firestore services
 */
export const mockFirestoreServices = () => {
  // Mock implementation of firestore services
  jest.mock('@/utils/firestore', () => ({
    appointmentsService: {
      createAppointment: jest.fn(),
      getAppointmentById: jest.fn(),
      getActiveAppointmentsByClient: jest.fn(),
      updateAppointment: jest.fn(),
      deleteAppointment: jest.fn(),
      cancelAppointment: jest.fn(),
    },
    calendarSlotsService: {
      getSlotsByUserId: jest.fn(),
      getSlotById: jest.fn(),
      updateSlot: jest.fn(),
      markSlotAsUnavailable: jest.fn(),
      markSlotAsAvailable: jest.fn(),
    },
    clientsService: {
      createClient: jest.fn(),
      getClientById: jest.fn(),
      findClientsByName: jest.fn(),
      updateClient: jest.fn(),
      getAllClients: jest.fn(),
    },
    callsService: {
      createCall: jest.fn(),
      getCallById: jest.fn(),
      updateCall: jest.fn(),
    },
    userSpecialtiesService: {
      getUserSpecialties: jest.fn(),
    },
    clinicsService: {
      getClinicByPhone: jest.fn(),
    },
    callSessionsService: {
      getCallSessionBySessionId: jest.fn(),
      getCallSessionById: jest.fn(),
      createCallSession: jest.fn(),
      updateCallSession: jest.fn(),
      addOrUpdateCallSession: jest.fn(),
      deleteCallSession: jest.fn(),
    },
  }));
};

/**
 * Reset all mocks between tests
 */
export const resetAllMocks = () => {
  jest.resetAllMocks();
  jest.restoreAllMocks();
};
