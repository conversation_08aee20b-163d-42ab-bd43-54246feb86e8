import { getRoleDisplayName, getRoleDescription } from '@/utils/role-utils';
import { UserRole } from '@/models/auth';

describe('Role Utils', () => {
  describe('getRoleDisplayName', () => {
    it('should return "Super Admin" for SUPER_ADMIN role', () => {
      expect(getRoleDisplayName(UserRole.SUPER_ADMIN)).toBe('Super Admin');
    });

    it('should return "Clinic Admin" for CLINIC_ADMIN role', () => {
      expect(getRoleDisplayName(UserRole.CLINIC_ADMIN)).toBe('Clinic Admin');
    });

    it('should return "Staff" for STAFF role', () => {
      expect(getRoleDisplayName(UserRole.STAFF)).toBe('Staff');
    });

    it('should return the original value for unknown roles', () => {
      const unknownRole = 'UNKNOWN_ROLE' as UserRole;
      expect(getRoleDisplayName(unknownRole)).toBe('UNKNOWN_ROLE');
    });
  });

  describe('getRoleDescription', () => {
    it('should return correct description for SUPER_ADMIN', () => {
      expect(getRoleDescription(UserRole.SUPER_ADMIN)).toBe(
        'Full system access across all clinics',
      );
    });

    it('should return correct description for CLINIC_ADMIN', () => {
      expect(getRoleDescription(UserRole.CLINIC_ADMIN)).toBe(
        'Manage staff and settings for their clinic',
      );
    });

    it('should return correct description for STAFF', () => {
      expect(getRoleDescription(UserRole.STAFF)).toBe('Handle patient calls and manage records');
    });

    it('should return default description for unknown roles', () => {
      const unknownRole = 'UNKNOWN_ROLE' as UserRole;
      expect(getRoleDescription(unknownRole)).toBe('Standard user privileges');
    });
  });
});
