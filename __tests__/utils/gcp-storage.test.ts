import { GcpStorageService } from '@/utils/gcp-storage';
import { Storage } from '@google-cloud/storage';

// Mock the @google-cloud/storage package
jest.mock('@google-cloud/storage');

describe('GcpStorageService', () => {
  let storageService: GcpStorageService;
  let mockStorage: jest.Mocked<Storage>;
  let mockBucket: any;
  let mockFile: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock implementations
    mockFile = {
      getSignedUrl: jest.fn().mockResolvedValue(['https://signed-url.example.com']),
      download: jest.fn().mockResolvedValue([Buffer.from('test file content')]),
      exists: jest.fn().mockResolvedValue([true]),
      getMetadata: jest.fn().mockResolvedValue([{ size: 1024, contentType: 'audio/mp3' }]),
    };

    mockBucket = {
      file: jest.fn().mockReturnValue(mockFile),
      getFiles: jest
        .fn()
        .mockResolvedValue([
          [
            { name: 'audio/test1.mp3' },
            { name: 'audio/test2.mp3' },
            { name: 'audio/session123_1234567890123.mp3' },
            { name: 'audio/session456_1234567890456.mp3' },
          ],
        ]),
    };

    // Mock the Storage constructor and its methods
    mockStorage = {
      bucket: jest.fn().mockReturnValue(mockBucket),
    } as unknown as jest.Mocked<Storage>;

    (Storage as unknown as jest.Mock).mockImplementation(() => mockStorage);

    // Create the service instance
    storageService = new GcpStorageService();
  });

  describe('listFiles', () => {
    it('should list files from a bucket', async () => {
      const files = await storageService.listFiles('test-bucket');

      expect(mockStorage.bucket).toHaveBeenCalledWith('test-bucket');
      expect(mockBucket.getFiles).toHaveBeenCalled();
      expect(files).toEqual([
        'audio/test1.mp3',
        'audio/test2.mp3',
        'audio/session123_1234567890123.mp3',
        'audio/session456_1234567890456.mp3',
      ]);
    });

    it('should pass options to getFiles', async () => {
      await storageService.listFiles('test-bucket', { prefix: 'audio/' });

      expect(mockBucket.getFiles).toHaveBeenCalledWith({ prefix: 'audio/' });
    });

    it('should handle errors', async () => {
      mockBucket.getFiles.mockRejectedValue(new Error('Bucket not found'));

      await expect(storageService.listFiles('test-bucket')).rejects.toThrow(
        'Failed to list files from bucket test-bucket: Bucket not found',
      );
    });
  });

  describe('getSignedUrl', () => {
    it('should generate a signed URL for a file', async () => {
      const url = await storageService.getSignedUrl('test-bucket', 'audio/test1.mp3');

      expect(mockStorage.bucket).toHaveBeenCalledWith('test-bucket');
      expect(mockBucket.file).toHaveBeenCalledWith('audio/test1.mp3');
      expect(mockFile.getSignedUrl).toHaveBeenCalled();
      expect(url).toBe('https://signed-url.example.com');
    });

    it('should pass options to getSignedUrl', async () => {
      const expires = Date.now() + 60 * 60 * 1000; // 1 hour
      await storageService.getSignedUrl('test-bucket', 'audio/test1.mp3', { expires });

      expect(mockFile.getSignedUrl).toHaveBeenCalledWith({
        action: 'read',
        expires,
      });
    });

    it('should handle errors', async () => {
      mockFile.getSignedUrl.mockRejectedValue(new Error('File not found'));

      await expect(storageService.getSignedUrl('test-bucket', 'audio/test1.mp3')).rejects.toThrow(
        'Failed to generate signed URL for file audio/test1.mp3 in bucket test-bucket: File not found',
      );
    });
  });

  describe('fileExists', () => {
    it('should check if a file exists', async () => {
      const exists = await storageService.fileExists('test-bucket', 'audio/test1.mp3');

      expect(mockStorage.bucket).toHaveBeenCalledWith('test-bucket');
      expect(mockBucket.file).toHaveBeenCalledWith('audio/test1.mp3');
      expect(mockFile.exists).toHaveBeenCalled();
      expect(exists).toBe(true);
    });

    it('should handle errors', async () => {
      mockFile.exists.mockRejectedValue(new Error('Bucket not found'));

      await expect(storageService.fileExists('test-bucket', 'audio/test1.mp3')).rejects.toThrow(
        'Failed to check if file audio/test1.mp3 exists in bucket test-bucket: Bucket not found',
      );
    });
  });

  describe('getLatestAudioFileForSession', () => {
    beforeEach(() => {
      // Mock files with timestamps for session audio testing
      mockBucket.getFiles.mockResolvedValue([
        [
          {
            name: 'audio/session123_1609459200000.mp3',
            getSignedUrl: jest.fn().mockResolvedValue(['https://signed-url-1.example.com']),
          },
          {
            name: 'audio/session123_1609545600000.mp3',
            getSignedUrl: jest.fn().mockResolvedValue(['https://signed-url-2.example.com']),
          },
          {
            name: 'audio/session456_1609632000000.mp3',
            getSignedUrl: jest.fn().mockResolvedValue(['https://signed-url-3.example.com']),
          },
        ],
      ]);
    });

    it('should return null when no patient audio records are found', async () => {
      // Mock getTranscriptWithAudioRecords to return empty array
      storageService.getTranscriptWithAudioRecords = jest.fn().mockResolvedValue([]);

      const result = await storageService.getLatestAudioFileForSession('test-bucket', 'session123');

      expect(storageService.getTranscriptWithAudioRecords).toHaveBeenCalledWith(
        'test-bucket',
        'session123',
        undefined,
      );
      expect(result).toBeNull();
    });

    it('should return patient audio record when found', async () => {
      // Mock getTranscriptWithAudioRecords to return patient records
      storageService.getTranscriptWithAudioRecords = jest.fn().mockResolvedValue([
        {
          text: 'Patient: Hello, I need help',
          recordUrl: 'gcp://test-bucket/audio/session123_1609545600000.mp3',
          transcriptTimestamp: 1609545600000,
          audioTimestamp: 1609545600000,
        },
      ]);

      // Mock bucket and file operations for URL generation
      const mockFile = {
        getSignedUrl: jest.fn().mockResolvedValue(['https://signed-url-2.example.com']),
      };
      mockBucket.file.mockReturnValue(mockFile);

      const result = await storageService.getLatestAudioFileForSession(
        'test-bucket',
        'session123',
        1609500000000, // Minimum timestamp
      );

      expect(storageService.getTranscriptWithAudioRecords).toHaveBeenCalledWith(
        'test-bucket',
        'session123',
        1609500000000,
      );
      // The implementation doesn't call bucket.file() directly, so we don't test for that call
      expect(result).toEqual({
        fileName: 'audio/session123_1609545600000.mp3',
        url: 'gcp://test-bucket/audio/session123_1609545600000.mp3',
      });
    });

    it('should return null if no matching files are found', async () => {
      const result = await storageService.getLatestAudioFileForSession(
        'test-bucket',
        'nonexistent',
      );

      expect(result).toBeNull();
    });

    it('should return null if no files match the minimum timestamp', async () => {
      const result = await storageService.getLatestAudioFileForSession(
        'test-bucket',
        'session123',
        1609632000000, // After both session123 files
      );

      expect(result).toBeNull();
    });

    it('should throw errors from the outer catch block', async () => {
      // Mock getTranscriptWithAudioRecords to throw an error
      storageService.getTranscriptWithAudioRecords = jest.fn().mockImplementation(() => {
        throw new Error('Inner error');
      });

      // Mock console functions to avoid test output noise
      console.error = jest.fn();

      // The function should throw an error
      await expect(
        storageService.getLatestAudioFileForSession('test-bucket', 'session123'),
      ).rejects.toThrow('Failed to get latest audio file for session session123: Inner error');

      // Verify that the error was logged
      expect(console.error).toHaveBeenCalled();
    });
  });
});
