import {
  parseDurationToSeconds,
  calculateDurationFromTranscript,
  getEffectiveCallDuration,
  shouldDisplayCallInPortal,
  shouldMarkAsDisconnected,
  formatDialogflowDuration,
} from '@/utils/call-duration-utils';
import { CallType } from '@/models/CallTypes';

describe('call-duration-utils', () => {
  describe('parseDurationToSeconds', () => {
    it('should parse HH:MM:SS format correctly', () => {
      expect(parseDurationToSeconds('1:23:45')).toBe(5025);
      expect(parseDurationToSeconds('0:05:30')).toBe(330);
      expect(parseDurationToSeconds('10:00:00')).toBe(36000);
    });

    it('should parse MM:SS format correctly', () => {
      expect(parseDurationToSeconds('5:30')).toBe(330);
      expect(parseDurationToSeconds('0:45')).toBe(45);
      expect(parseDurationToSeconds('12:00')).toBe(720);
    });

    it('should parse minute format correctly', () => {
      expect(parseDurationToSeconds('5 min')).toBe(300);
      expect(parseDurationToSeconds('5 mins')).toBe(300);
      expect(parseDurationToSeconds('3.5 min')).toBe(210);
      expect(parseDurationToSeconds('1.7 min')).toBe(102);
    });

    it('should parse second format correctly', () => {
      expect(parseDurationToSeconds('30 sec')).toBe(30);
      expect(parseDurationToSeconds('30 secs')).toBe(30);
      expect(parseDurationToSeconds('45.5 sec')).toBe(46);
    });

    it('should parse Dialogflow duration format correctly', () => {
      expect(parseDurationToSeconds('393.473262801s')).toBe(393);
      expect(parseDurationToSeconds('49.221030099s')).toBe(49);
      expect(parseDurationToSeconds('120.5s')).toBe(121);
      expect(parseDurationToSeconds('0s')).toBe(0);
    });

    it('should parse plain numeric seconds', () => {
      expect(parseDurationToSeconds('120')).toBe(120);
      expect(parseDurationToSeconds('45')).toBe(45);
      expect(parseDurationToSeconds('0')).toBe(0);
    });

    it('should return 0 for invalid input', () => {
      expect(parseDurationToSeconds(null)).toBe(0);
      expect(parseDurationToSeconds(undefined)).toBe(0);
      expect(parseDurationToSeconds('')).toBe(0);
      expect(parseDurationToSeconds('invalid')).toBe(0);
      expect(parseDurationToSeconds('NaN')).toBe(0);
    });
  });

  describe('calculateDurationFromTranscript', () => {
    const mockTranscriptBothSides = JSON.stringify([
      { text: 'Patient: I need to schedule an appointment for next week please.' },
      { text: 'Heather: I can help you with that. What day works best for you?' },
      { text: 'Patient: How about Tuesday morning if you have any availability?' },
      { text: 'Agent: Let me check our schedule for Tuesday morning appointments.' },
    ]);

    const mockTranscriptPatientOnly = JSON.stringify([
      { text: 'Patient: Hello, I need to schedule an appointment.' },
      { text: 'Patient: Are you there? I really need help with this.' },
    ]);

    const mockTranscriptAgentOnly = JSON.stringify([
      { text: 'Heather: Hello, how can I help you today?' },
      { text: 'Agent: I can assist you with scheduling.' },
    ]);

    const mockTranscriptShort = JSON.stringify([
      { text: 'Patient: Hi' },
      { text: 'Heather: Hello' },
    ]);

    const mockTranscriptLong = JSON.stringify([
      {
        text: 'Patient: I need to schedule an appointment for next week and I was wondering if you have any availability on Tuesday morning or perhaps Wednesday afternoon would work better for my schedule since I have some flexibility with my work hours and would prefer to come in during a time that works well for both of us and allows me to get the care I need without having to rush or worry about being late for other commitments.',
      },
      {
        text: 'Heather: I can definitely help you with scheduling an appointment and let me check our availability for both Tuesday morning and Wednesday afternoon to see what options we have available and find a time that works best for your schedule while also ensuring that you get the appropriate amount of time with the doctor to address all of your concerns and questions.',
      },
    ]);

    it('should calculate duration for transcript with both patient and agent interactions', () => {
      const duration = calculateDurationFromTranscript(mockTranscriptBothSides);
      expect(duration).toBeGreaterThan(10); // Should be at least minimum call time
      expect(duration).toBeLessThan(600); // Should be less than maximum
    });

    it('should return 0 for transcript with only patient interactions', () => {
      const duration = calculateDurationFromTranscript(mockTranscriptPatientOnly);
      expect(duration).toBe(0);
    });

    it('should return 0 for transcript with only agent interactions', () => {
      const duration = calculateDurationFromTranscript(mockTranscriptAgentOnly);
      expect(duration).toBe(0);
    });

    it('should return minimum duration for very short conversations', () => {
      const duration = calculateDurationFromTranscript(mockTranscriptShort);
      expect(duration).toBe(10); // Should return minimum call time
    });

    it('should cap duration at maximum for very long conversations', () => {
      const duration = calculateDurationFromTranscript(mockTranscriptLong);
      expect(duration).toBeLessThanOrEqual(600); // Should not exceed 10 minutes
    });

    it('should return 0 for invalid JSON', () => {
      expect(calculateDurationFromTranscript('invalid json')).toBe(0);
      expect(calculateDurationFromTranscript('{"invalid": "format"}')).toBe(0);
    });

    it('should return 0 for empty or null input', () => {
      expect(calculateDurationFromTranscript(null)).toBe(0);
      expect(calculateDurationFromTranscript(undefined)).toBe(0);
      expect(calculateDurationFromTranscript('')).toBe(0);
      expect(calculateDurationFromTranscript('[]')).toBe(0);
    });

    it('should handle different speaker prefixes correctly', () => {
      const mixedSpeakers = JSON.stringify([
        { text: 'Patient: Hello there' },
        { text: 'Heather: Hi, how can I help?' },
        { text: 'Patient: I need help' },
        { text: 'Agent: I can assist you' },
      ]);

      const duration = calculateDurationFromTranscript(mixedSpeakers);
      expect(duration).toBeGreaterThan(10);
    });

    it('should calculate duration proportional to word count', () => {
      const shortConversation = JSON.stringify([
        { text: 'Patient: Hi' },
        { text: 'Heather: Hello' },
      ]);

      const longConversation = JSON.stringify([
        {
          text: 'Patient: I need to schedule an appointment for next week and I have some questions about the process',
        },
        {
          text: 'Heather: I can help you with scheduling and will answer all your questions about the appointment process',
        },
      ]);

      const shortDuration = calculateDurationFromTranscript(shortConversation);
      const longDuration = calculateDurationFromTranscript(longConversation);

      expect(longDuration).toBeGreaterThan(shortDuration);
    });
  });

  describe('getEffectiveCallDuration', () => {
    it('should return original duration when greater than 0', () => {
      const call = {
        duration: '2 min',
        transcriptionWithAudio: JSON.stringify([
          { text: 'Patient: Hello' },
          { text: 'Heather: Hi' },
        ]),
      };

      expect(getEffectiveCallDuration(call)).toBe(120);
    });

    it('should calculate from transcript when duration is 0', () => {
      const call = {
        duration: '0 min',
        transcriptionWithAudio: JSON.stringify([
          { text: 'Patient: I need to schedule an appointment.' },
          { text: 'Heather: I can help you with that.' },
        ]),
      };

      const duration = getEffectiveCallDuration(call);
      expect(duration).toBeGreaterThan(0);
    });

    it('should calculate from transcript when duration is null', () => {
      const call = {
        duration: null,
        transcriptionWithAudio: JSON.stringify([
          { text: 'Patient: I need help.' },
          { text: 'Heather: I can help you.' },
        ]),
      };

      const duration = getEffectiveCallDuration(call);
      expect(duration).toBeGreaterThan(0);
    });

    it('should return 0 when both duration and transcript are unavailable', () => {
      const call = {
        duration: '0 min',
        transcriptionWithAudio: null,
      };

      expect(getEffectiveCallDuration(call)).toBe(0);
    });

    it('should return 0 when transcript has only one-sided conversation', () => {
      const call = {
        duration: '0 min',
        transcriptionWithAudio: JSON.stringify([
          { text: 'Patient: Hello' },
          { text: 'Patient: Anyone there?' },
        ]),
      };

      expect(getEffectiveCallDuration(call)).toBe(0);
    });
  });

  describe('shouldDisplayCallInPortal', () => {
    it('should return true for calls 5 seconds or longer', () => {
      expect(shouldDisplayCallInPortal({ duration: '5 sec' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '10 sec' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '1 min' })).toBe(true);
      expect(shouldDisplayCallInPortal({ duration: '2:30' })).toBe(true);
    });

    it('should return false for calls under 5 seconds', () => {
      expect(shouldDisplayCallInPortal({ duration: '4 sec' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '1 sec' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '0 min' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '0:04' })).toBe(false);
    });

    it('should return false for invalid or null duration', () => {
      expect(shouldDisplayCallInPortal({ duration: null })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: undefined })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: '' })).toBe(false);
      expect(shouldDisplayCallInPortal({ duration: 'invalid' })).toBe(false);
    });
  });

  describe('shouldMarkAsDisconnected', () => {
    it('should return false for calls 20 seconds or under', () => {
      expect(shouldMarkAsDisconnected({ duration: '20 sec' })).toBe(false);
      expect(shouldMarkAsDisconnected({ duration: '15 sec' })).toBe(false);
      expect(shouldMarkAsDisconnected({ duration: '0:19' })).toBe(false);
    });

    it('should return true for calls over 20 seconds with no types', () => {
      expect(shouldMarkAsDisconnected({ duration: '25 sec' })).toBe(true);
      expect(shouldMarkAsDisconnected({ duration: '1 min' })).toBe(true);
      expect(shouldMarkAsDisconnected({ duration: '0:30' })).toBe(true);
    });

    it('should return true for calls over 20 seconds with only OTHER type', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '25 sec',
          type: CallType.OTHER,
        }),
      ).toBe(true);

      expect(
        shouldMarkAsDisconnected({
          duration: '1 min',
          callTypes: [CallType.OTHER],
        }),
      ).toBe(true);
    });

    it('should return false for calls over 20 seconds with meaningful types', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: '25 sec',
          type: CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
        }),
      ).toBe(false);

      expect(
        shouldMarkAsDisconnected({
          duration: '1 min',
          callTypes: [CallType.NEW_APPOINTMENT_EXISTING_PATIENT, CallType.RESCHEDULE],
        }),
      ).toBe(false);

      expect(
        shouldMarkAsDisconnected({
          duration: '30 sec',
          callTypes: [CallType.OTHER, CallType.NEW_APPOINTMENT_EXISTING_PATIENT],
        }),
      ).toBe(false);
    });

    it('should return false for calls with invalid duration', () => {
      expect(
        shouldMarkAsDisconnected({
          duration: null,
          type: CallType.OTHER,
        }),
      ).toBe(false);

      expect(
        shouldMarkAsDisconnected({
          duration: 'invalid',
          callTypes: [],
        }),
      ).toBe(false);
    });
  });

  describe('formatDialogflowDuration', () => {
    it('should format duration in seconds for calls under 60 seconds', () => {
      expect(formatDialogflowDuration('45.123456789s')).toBe('45 sec');
      expect(formatDialogflowDuration('59.999s')).toBe('60 sec');
      expect(formatDialogflowDuration('1.5s')).toBe('2 sec');
      expect(formatDialogflowDuration('0.5s')).toBe('1 sec');
    });

    it('should format duration in minutes for calls 60 seconds or longer', () => {
      expect(formatDialogflowDuration('393.473262801s')).toBe('6.6 min');
      expect(formatDialogflowDuration('60.0s')).toBe('1.0 min');
      expect(formatDialogflowDuration('90.0s')).toBe('1.5 min');
      expect(formatDialogflowDuration('120.0s')).toBe('2.0 min');
      expect(formatDialogflowDuration('150.0s')).toBe('2.5 min');
    });

    it('should handle edge cases and invalid input', () => {
      expect(formatDialogflowDuration('')).toBe('');
      expect(formatDialogflowDuration('invalid')).toBe('');
      expect(formatDialogflowDuration('s')).toBe('');
      expect(formatDialogflowDuration('abc.123s')).toBe('');
      expect(formatDialogflowDuration('123')).toBe('');
      expect(formatDialogflowDuration('123.456')).toBe('');
    });

    it('should handle zero duration', () => {
      expect(formatDialogflowDuration('0s')).toBe('0 sec');
      expect(formatDialogflowDuration('0.0s')).toBe('0 sec');
    });
  });
});
