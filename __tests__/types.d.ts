// Type definitions for test files

import { AppointmentPurpose, PatientType } from '../lib/external-api/v2/models/types';

declare module '@jest/globals' {
  namespace jest {
    interface Mock<T = any, Y extends any[] = any[]> {
      mockResolvedValue(val: T): this;
      mockRejectedValue(val: any): this;
    }
  }
}

// Extend Je<PERSON>'s Mock interface to accommodate mock functions for our API
declare global {
  namespace jest {
    interface Mock<T = any, Y extends any[] = any[]> {
      mockResolvedValue(val: T): this;
      mockRejectedValue(val: any): this;
    }
  }
}

// Enhanced mock provider types for testing
declare class EnhancedAppointmentService {
  getAppointmentPurposes: jest.Mock<Promise<AppointmentPurpose[]>>;
}

declare class EnhancedPatientService {
  getPatientTypes: jest.Mock<Promise<PatientType[]>>;
}

declare class EnhancedMockProvider {
  appointments: EnhancedAppointmentService;
  patients: EnhancedPatientService;
}
