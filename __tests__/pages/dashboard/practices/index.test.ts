import PracticeManagement from '../../../../pages/dashboard/practices/index';
import { getToken } from '../../../../utils/auth';

// Mock the auth utility
jest.mock('../../../../utils/auth', () => ({
  getToken: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

const mockPracticesResponse = {
  success: true,
  practices: [
    {
      id: 'practice1',
      name: 'Main Practice',
      description: 'Primary practice location',
      clinicId: 1,
      isActive: true,
      locationCount: 2,
      userCount: 5,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'practice2',
      name: 'Branch Practice',
      description: 'Secondary practice location',
      clinicId: 1,
      isActive: true,
      locationCount: 1,
      userCount: 3,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
};

const mockCurrentUser = {
  id: 'user1',
  name: 'Admin User',
  role: 'CLINIC_ADMIN',
  clinicId: 1,
};

describe('PracticeManagement', () => {
  beforeEach(() => {
    (getToken as jest.Mock).mockReturnValue('mock-token');
    (fetch as jest.Mock).mockClear();
  });

  it('should be defined', () => {
    expect(PracticeManagement).toBeDefined();
  });

  it('should fetch practices and current user on mount', () => {
    (fetch as jest.Mock)
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCurrentUser),
      })
      .mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockPracticesResponse),
      });

    // Component instantiation would trigger useEffect
    expect(PracticeManagement).toBeDefined();
  });

  it('should handle practice creation', async () => {
    const newPractice = {
      id: 'practice3',
      name: 'New Practice',
      description: 'Newly created practice',
      clinicId: 1,
      isActive: true,
      locationCount: 0,
      userCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true, practice: newPractice }),
    });

    // Test would involve simulating form submission
    expect(fetch).toHaveBeenCalledTimes(0); // Not called yet since we're just testing the mock
  });

  it('should handle practice editing', async () => {
    const updatedPractice = {
      ...mockPracticesResponse.practices[0],
      name: 'Updated Practice Name',
      description: 'Updated description',
    };

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true, practice: updatedPractice }),
    });

    // Test would involve simulating edit form submission
    expect(fetch).toHaveBeenCalledTimes(0); // Not called yet since we're just testing the mock
  });
});
