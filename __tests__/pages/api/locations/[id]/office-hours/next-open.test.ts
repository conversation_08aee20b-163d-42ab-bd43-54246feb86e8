import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '@/pages/api/locations/[id]/office-hours/next-open';

// Mock the dependencies
jest.mock('@/utils/firebase-admin', () => ({
  verifyAuthAndGetUser: jest.fn(),
}));

jest.mock('@/lib/services/locationService', () => ({
  LocationService: {
    getLocationsByClinicId: jest.fn(),
  },
}));

jest.mock('@/lib/services/office-hours', () => ({
  OfficeHoursService: {
    getNextBusinessDay: jest.fn(),
  },
}));

describe('/api/locations/[id]/office-hours/next-open', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return next open time for authenticated user', async () => {
    const { verifyAuthAndGetUser } = require('@/utils/firebase-admin');
    const { LocationService } = require('@/lib/services/locationService');
    const { OfficeHoursService } = require('@/lib/services/office-hours');

    // Mock authenticated user
    verifyAuthAndGetUser.mockResolvedValue({
      uid: 'test-user',
      clinicId: 12,
      role: 'CLINIC_ADMIN',
    });

    // Mock location response
    LocationService.getLocationsByClinicId.mockResolvedValue({
      locations: [
        {
          id: 'test-location',
          clinicId: 12,
          officeHours: {
            '1': { start: '09:00', end: '17:00' },
            '2': { start: '09:00', end: '17:00' },
          },
          timeZone: 'America/Chicago',
        },
      ],
    });

    // Mock next business day
    OfficeHoursService.getNextBusinessDay.mockReturnValue({
      date: '2024-01-16',
      hours: { start: '09:00', end: '17:00' },
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      query: { id: 'test-location' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.nextBusinessDay).toEqual({
      date: '2024-01-16',
      hours: { start: '09:00', end: '17:00' },
    });
    expect(data.locationTimezone).toBe('America/Chicago');
  });

  it('should return null when no next business day found', async () => {
    const { verifyAuthAndGetUser } = require('@/utils/firebase-admin');
    const { LocationService } = require('@/lib/services/locationService');
    const { OfficeHoursService } = require('@/lib/services/office-hours');

    verifyAuthAndGetUser.mockResolvedValue({
      uid: 'test-user',
      clinicId: 12,
    });

    LocationService.getLocationsByClinicId.mockResolvedValue({
      locations: [
        {
          id: 'test-location',
          clinicId: 12,
          officeHours: {}, // No office hours defined
          timeZone: 'America/Chicago',
        },
      ],
    });

    OfficeHoursService.getNextBusinessDay.mockReturnValue(null);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      query: { id: 'test-location' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.nextBusinessDay).toBeNull();
    expect(data.locationTimezone).toBe('America/Chicago');
  });

  it('should return 401 for unauthenticated user', async () => {
    const { verifyAuthAndGetUser } = require('@/utils/firebase-admin');

    verifyAuthAndGetUser.mockResolvedValue(null);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      query: { id: 'test-location' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
    const data = JSON.parse(res._getData());
    expect(data.message).toBe('Unauthorized: Authentication required');
  });

  it('should return 404 for non-existent location', async () => {
    const { verifyAuthAndGetUser } = require('@/utils/firebase-admin');
    const { LocationService } = require('@/lib/services/locationService');

    verifyAuthAndGetUser.mockResolvedValue({
      uid: 'test-user',
      clinicId: 12,
    });

    LocationService.getLocationsByClinicId.mockResolvedValue({
      locations: [], // Empty locations array
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      query: { id: 'non-existent' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    const data = JSON.parse(res._getData());
    expect(data.message).toBe('Location not found');
  });

  it('should return 405 for unsupported methods', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      query: { id: 'test-location' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    const data = JSON.parse(res._getData());
    expect(data.message).toBe('Method POST not allowed');
  });

  it('should handle different timezones correctly', async () => {
    const { verifyAuthAndGetUser } = require('@/utils/firebase-admin');
    const { LocationService } = require('@/lib/services/locationService');
    const { OfficeHoursService } = require('@/lib/services/office-hours');

    verifyAuthAndGetUser.mockResolvedValue({
      uid: 'test-user',
      clinicId: 12,
    });

    LocationService.getLocationsByClinicId.mockResolvedValue({
      locations: [
        {
          id: 'test-location',
          clinicId: 12,
          officeHours: {
            '1': { start: '09:00', end: '17:00' },
          },
          timeZone: 'America/New_York',
        },
      ],
    });

    OfficeHoursService.getNextBusinessDay.mockReturnValue({
      date: '2024-01-16',
      hours: { start: '09:00', end: '17:00' },
    });

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      query: { id: 'test-location' },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data.locationTimezone).toBe('America/New_York');
    expect(OfficeHoursService.getNextBusinessDay).toHaveBeenCalledWith(
      expect.any(Object),
      'America/New_York',
      undefined,
    );
  });
});
