import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '@/pages/api/calls/after-hours/contact';
import { afterHoursCallsLogService, afterHoursCallsService } from '@/utils/firestore';

// Mock the services
jest.mock('@/utils/firestore', () => ({
  afterHoursCallsLogService: {
    createAfterHoursCallLog: jest.fn(),
  },
  afterHoursCallsService: {
    getAfterHoursCallByCallId: jest.fn(),
  },
}));

describe('/api/calls/after-hours/contact', () => {
  const mockAfterHoursCallsLogService = afterHoursCallsLogService as jest.Mocked<
    typeof afterHoursCallsLogService
  >;
  const mockAfterHoursCallsService = afterHoursCallsService as jest.Mocked<
    typeof afterHoursCallsService
  >;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create contact log successfully', async () => {
    const mockAfterHoursCall = {
      id: 'ahc-123',
      callId: 'call-456',
      patientFullName: '<PERSON> Doe',
      patientPhoneNumber: '555-1234',
      callReason: 'Emergency',
      patientBirthday: new Date('1990-01-01'),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const mockCreatedLog = {
      id: 'log-789',
      afterHoursCallId: 'ahc-123',
      contactedBy: 'doctor-123',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    mockAfterHoursCallsService.getAfterHoursCallByCallId.mockResolvedValue(mockAfterHoursCall);
    mockAfterHoursCallsLogService.createAfterHoursCallLog.mockResolvedValue(mockCreatedLog);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      body: {
        callId: 'call-456',
        doctorId: 'doctor-123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(201);
    const response = JSON.parse(res._getData());
    expect(response).toEqual({
      success: true,
      message: 'Contact log created successfully',
      data: {
        logId: 'log-789',
        afterHoursCallId: 'ahc-123',
        contactedBy: 'doctor-123',
        createdAt: mockCreatedLog.createdAt.toISOString(),
      },
    });

    expect(mockAfterHoursCallsService.getAfterHoursCallByCallId).toHaveBeenCalledWith('call-456');
    expect(mockAfterHoursCallsLogService.createAfterHoursCallLog).toHaveBeenCalledWith({
      afterHoursCallId: 'ahc-123',
      contactedBy: 'doctor-123',
    });
  });

  it('should return 404 when after-hours call not found', async () => {
    mockAfterHoursCallsService.getAfterHoursCallByCallId.mockResolvedValue(null);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      body: {
        callId: 'call-456',
        doctorId: 'doctor-123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      success: false,
      message: 'After-hours call with call ID call-456 not found',
    });

    expect(mockAfterHoursCallsService.getAfterHoursCallByCallId).toHaveBeenCalledWith('call-456');
    expect(mockAfterHoursCallsLogService.createAfterHoursCallLog).not.toHaveBeenCalled();
  });

  it('should return 400 when validation fails', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      body: {
        // Missing required fields
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    const response = JSON.parse(res._getData());
    expect(response.message).toBe('Validation failed');
    expect(response.errors).toHaveProperty('callId');
    expect(response.errors).toHaveProperty('doctorId');

    expect(mockAfterHoursCallsService.getAfterHoursCallByCallId).not.toHaveBeenCalled();
    expect(mockAfterHoursCallsLogService.createAfterHoursCallLog).not.toHaveBeenCalled();
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method GET Not Allowed',
    });

    expect(mockAfterHoursCallsService.getAfterHoursCallByCallId).not.toHaveBeenCalled();
    expect(mockAfterHoursCallsLogService.createAfterHoursCallLog).not.toHaveBeenCalled();
  });

  it('should return 500 when service throws error', async () => {
    const error = new Error('Database connection failed');
    mockAfterHoursCallsService.getAfterHoursCallByCallId.mockRejectedValue(error);

    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      body: {
        callId: 'call-456',
        doctorId: 'doctor-123',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    const response = JSON.parse(res._getData());
    expect(response.success).toBe(false);
    expect(response.message).toBe('Internal server error');
    expect(response.error).toBe('Database connection failed');

    expect(mockAfterHoursCallsService.getAfterHoursCallByCallId).toHaveBeenCalledWith('call-456');
    expect(mockAfterHoursCallsLogService.createAfterHoursCallLog).not.toHaveBeenCalled();
  });
});
