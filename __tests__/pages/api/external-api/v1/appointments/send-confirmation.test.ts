import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../../pages/api/external-api/v1/appointments/send-confirmation';
import { smsService } from '../../../../../../lib/services/sms-service';
import { appointmentsService } from '../../../../../../utils/firestore';

// Mock the SMS service
jest.mock('../../../../../../lib/services/sms-service', () => ({
  smsService: {
    sendSms: jest.fn().mockResolvedValue('test-message-sid'),
    sendAppointmentConfirmation: jest.fn().mockResolvedValue('test-message-sid'),
  },
}));

// Mock the appointments service
jest.mock('../../../../../../utils/firestore', () => ({
  appointmentsService: {
    getAppointmentById: jest.fn().mockImplementation(id => {
      if (id === 'valid-appointment-id') {
        return Promise.resolve({
          id: 'valid-appointment-id',
          userId: 'user-123',
          clientId: 'client-456',
          clientName: 'John Doe',
          date: '2023-06-15',
          time: '10:30 AM',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
      return Promise.resolve(null);
    }),
  },
}));

// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('Send Confirmation API Endpoint (v1)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method not allowed',
    });
  });

  it('should return 401 for requests without API key', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
  });

  it('should return 400 for missing appointmentId', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        // Missing appointmentId
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'appointmentId is required',
    });
  });

  it('should return 400 for missing phoneNumber', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        // Missing phoneNumber
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'phoneNumber is required',
    });
  });

  it('should return 404 if appointment is not found', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'non-existent-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(404);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Appointment not found',
    });
  });

  it('should send SMS confirmation successfully with default template', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
        provider: 'Dr. Smith',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
      message: 'Confirmation SMS sent successfully',
      appointmentId: 'valid-appointment-id',
      messageSid: 'test-message-sid',
    });

    expect(smsService.sendAppointmentConfirmation).toHaveBeenCalledWith('+15551234567', {
      patientName: 'John Doe',
      date: '2023-06-15',
      time: '10:30 AM',
      location: 'Main Clinic',
      provider: 'Dr. Smith',
    });
  });

  it('should send custom SMS message if provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
        customMessage: 'This is a custom confirmation message.',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(smsService.sendSms).toHaveBeenCalledWith(
      '+15551234567',
      'This is a custom confirmation message.',
    );
  });
});
