import { createMocks } from 'node-mocks-http';
import { NextApiRequest, NextApiResponse } from 'next';
import handler from '../../../../../../pages/api/external-api/v2/appointments/send-confirmation';
import { smsService } from '@/lib/services/sms-service';

// Mock the SMS service
jest.mock('../../../../../../lib/services/sms-service', () => ({
  smsService: {
    sendSms: jest.fn().mockResolvedValue('test-message-sid'),
    sendAppointmentConfirmation: jest.fn().mockResolvedValue('test-message-sid'),
    sendAppointmentCancellationConfirmation: jest.fn().mockResolvedValue('test-message-sid'),
  },
}));

// Mock the provider registry and appointment service
jest.mock('../../../../../../lib/external-api/v2/providers', () => {
  const mockAppointmentService = {
    getAppointmentById: jest.fn().mockImplementation(id => {
      if (id === 'valid-appointment-id') {
        return Promise.resolve({
          id: 'valid-appointment-id',
          patientId: 'patient-123',
          providerId: 'provider-456',
          startTime: '2023-06-15T10:30:00Z',
          endTime: '2023-06-15T11:00:00Z',
          status: 'scheduled',
          patientName: 'Patient',
          locationName: 'Location',
          locationId: 'location-789',
          practitionerName: undefined,
        });
      }
      return Promise.resolve(null);
    }),
  };

  const mockLocationService = {
    getLocationById: jest.fn().mockImplementation(id => {
      if (id === 'location-789') {
        return Promise.resolve({
          id: 'location-789',
          name: 'Location',
          address: {
            line1: '123 Main St',
            city: 'Anytown',
            state: 'ST',
            postalCode: '12345',
          },
          phoneNumber: '************',
        });
      }
      return Promise.resolve(null);
    }),
  };

  const mockProvider = {
    getAppointmentService: jest.fn().mockReturnValue(mockAppointmentService),
    getLocationService: jest.fn().mockReturnValue(mockLocationService),
  };

  return {
    providerRegistry: {
      getDefaultProvider: jest.fn().mockReturnValue(mockProvider),
      getProvider: jest.fn().mockReturnValue(mockProvider),
      getAvailableProviders: jest.fn(() => ['nextech']),
    },
  };
});

// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';

describe('Send Confirmation API Endpoint', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return 405 for non-POST requests', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'GET',
      headers: {
        'x-api-key': 'test-api-key',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(405);
    expect(JSON.parse(res._getData())).toEqual({
      message: 'Method GET Not Allowed',
    });
  });

  it('should return 401 for requests without API key', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(401);
  });

  it('should return 400 for invalid request body', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        // Missing required fields
        appointmentId: 'valid-appointment-id',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData())).toHaveProperty('message', 'Invalid request body');
  });

  it('should return 404 if appointment is not found', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'non-existent-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    expect(JSON.parse(res._getData()).message).toContain(
      'Appointment with ID non-existent-id not found',
    );
  });

  it('should send SMS confirmation successfully with default template', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      success: true,
      message: 'Confirmation SMS sent successfully',
      appointmentId: 'valid-appointment-id',
      messageSid: 'test-message-sid',
    });

    expect(smsService.sendAppointmentConfirmation).toHaveBeenCalledWith(
      '+15551234567',
      expect.objectContaining({
        patientName: 'Patient',
        date: 'June 15, 2023',
        time: '10:30 AM',
        location: 'Location',
        provider: undefined,
      }),
    );
  });

  it('should send custom SMS message if provided', async () => {
    const { req, res } = createMocks<NextApiRequest, NextApiResponse>({
      method: 'POST',
      headers: {
        'x-api-key': 'test-api-key',
      },
      body: {
        appointmentId: 'valid-appointment-id',
        phoneNumber: '+15551234567',
        patientName: 'John Doe',
        date: '2023-06-15',
        time: '10:30 AM',
        location: 'Main Clinic',
        customMessage: 'This is a custom confirmation message.',
      },
      env: {},
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(smsService.sendSms).toHaveBeenCalledWith(
      '+15551234567',
      'This is a custom confirmation message.',
    );
  });
});
