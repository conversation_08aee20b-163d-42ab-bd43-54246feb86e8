// Add global test setup
// Mock environment variables
process.env.EXTERNAL_SERVICE_API_KEY = 'test-api-key';
// Add Nextech provider environment variables for tests
process.env.NEXTECH_BASE_URL = 'http://api.test';
process.env.NEXTECH_CLIENT_ID = 'test-client-id';
process.env.NEXTECH_CLIENT_SECRET = 'test-client-secret';

// Add Firebase environment variables for tests
process.env.FIREBASE_PROJECT_ID = 'test-project-id';
process.env.FIREBASE_CLIENT_EMAIL = '<EMAIL>';
process.env.FIREBASE_PRIVATE_KEY =
  '-----BEGIN PRIVATE KEY-----\nTEST_PRIVATE_KEY\n-----END PRIVATE KEY-----\n';

// Mock Firebase Admin to prevent real initialization
jest.mock('firebase-admin', () => ({
  firestore: jest.fn(() => ({
    collection: jest.fn(),
    doc: jest.fn(),
  })),
  initializeApp: jest.fn(),
  getApps: jest.fn(() => []),
  cert: jest.fn(),
}));

jest.mock('firebase-admin/app', () => ({
  getApps: jest.fn(() => []),
  initializeApp: jest.fn(),
  cert: jest.fn(),
}));

jest.mock('firebase-admin/firestore', () => ({
  getFirestore: jest.fn(() => ({
    collection: jest.fn(),
    doc: jest.fn(),
  })),
}));

// Set up global mocks and spies
jest.setTimeout(30000); // Set timeout to 30 seconds

// Add global afterAll hook to ensure all timers are cleaned up
afterAll(() => {
  jest.useRealTimers();
});

// Mock console methods to reduce test noise
global.console = {
  ...console,
  // Uncomment the following lines to silence console during tests
  // error: jest.fn(),
  // warn: jest.fn(),
  // log: jest.fn(),
};

// Mock the external-api v2 module to prevent real initialization
jest.mock(
  '../lib/external-api/v2/index',
  () => {
    // Import the real module but don't execute the initialization
    const actual = jest.requireActual('../lib/external-api/v2/providers');

    // Return the provider classes and factories without the initialization
    return {
      ...actual,
      // We're not executing initializeExternalApi() here
    };
  },
  { virtual: true },
);

// Mock the initialization module
jest.mock('../lib/external-api/v2/init', () => {
  return {
    initializeExternalApi: jest.fn(),
    ensureProvidersInitialized: jest.fn((req, res, next) => next()),
  };
});
