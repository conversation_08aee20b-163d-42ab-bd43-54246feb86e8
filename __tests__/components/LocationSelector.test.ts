import LocationSelector from '../../components/LocationSelector';
import { getToken } from '../../utils/auth';

// Mock the auth utility
jest.mock('../../utils/auth', () => ({
  getToken: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn();

const mockLocationContext = {
  currentLocation: {
    id: 'loc1',
    name: 'Main Office',
    practiceId: 'practice1',
    address: '123 Main St',
    clinicId: 1,
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  availableLocations: [
    {
      id: 'loc1',
      name: 'Main Office',
      practiceId: 'practice1',
      address: '123 Main St',
      clinicId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'loc2',
      name: 'Branch Office',
      practiceId: 'practice1',
      address: '456 Oak Ave',
      clinicId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  allLocations: [
    {
      id: 'loc1',
      name: 'Main Office',
      practiceId: 'practice1',
      address: '123 Main St',
      clinicId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'loc2',
      name: 'Branch Office',
      practiceId: 'practice1',
      address: '456 Oak Ave',
      clinicId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'loc3',
      name: 'Restricted Office',
      practiceId: 'practice1',
      address: '789 Pine Rd',
      clinicId: 1,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
  availablePractices: [
    {
      id: 'practice1',
      name: 'General Practice',
      clinicId: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ],
};

describe('LocationSelector', () => {
  beforeEach(() => {
    (getToken as jest.Mock).mockReturnValue('mock-token');
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockLocationContext),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(LocationSelector).toBeDefined();
  });

  it('should call getToken when component is used', () => {
    // This test verifies the component can be imported and the auth utility is available
    expect(getToken).toBeDefined();
    expect(typeof LocationSelector).toBe('function');
  });

  it('should have correct mock data structure with all locations', () => {
    expect(mockLocationContext.currentLocation).toBeDefined();
    expect(mockLocationContext.availableLocations).toHaveLength(2);
    expect(mockLocationContext.allLocations).toHaveLength(3);
    expect(mockLocationContext.availablePractices).toHaveLength(1);

    // Verify that restricted location is in allLocations but not in availableLocations
    const restrictedLocation = mockLocationContext.allLocations.find(loc => loc.id === 'loc3');
    const isInAvailable = mockLocationContext.availableLocations.some(loc => loc.id === 'loc3');
    expect(restrictedLocation).toBeDefined();
    expect(isInAvailable).toBe(false);
  });
});
