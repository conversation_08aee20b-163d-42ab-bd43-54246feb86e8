rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isStaff() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'staff';
    }

    function isDoctor() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'doctor';
    }

    function isAdmin() {
      return isAuthenticated() &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }

    function belongsToSameClinic(resourceId, collection) {
      let userClinicId = get(/databases/$(database)/documents/users/$(request.auth.uid)).data.clinicId;
      let resourceClinicId = get(/databases/$(database)/documents/$(collection)/$(resourceId)).data.clinicId;
      return userClinicId == resourceClinicId;
    }

    // Users collection
    match /users/{userId} {
      // Admin can read and write all user documents
      // Users can only read their own documents
      // Users can only update limited fields in their own document (e.g. name, contact info)
      allow read: if isAuthenticated() && (userId == request.auth.uid || isAdmin());
      allow update: if isAuthenticated() && (
        (userId == request.auth.uid &&
         request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['name', 'email', 'phone', 'photoUrl'])) ||
        isAdmin()
      );
      allow create, delete: if isAdmin();
    }

    // Staff collection (legacy, keeping for compatibility)
    match /staff/{userId} {
      // Same rules as users collection
      allow read: if isAuthenticated() && (userId == request.auth.uid || isAdmin());
      allow update: if isAuthenticated() && (
        (userId == request.auth.uid &&
         request.resource.data.diff(resource.data).affectedKeys()
          .hasOnly(['name', 'email', 'phone', 'photoUrl'])) ||
        isAdmin()
      );
      allow create, delete: if isAdmin();
    }

    // Calls collection
    match /calls/{callId} {
      // Doctor and admin can read all calls for their clinic
      // Staff can read calls but not update sensitive information
      allow read: if isAuthenticated() && belongsToSameClinic(callId, 'calls');
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isDoctor() || isAdmin()) && belongsToSameClinic(callId, 'calls');
      allow delete: if isAuthenticated() && isAdmin() && belongsToSameClinic(callId, 'calls');
    }

    // Call Details collection
    match /callDetails/{detailId} {
      // Access to call details follows the same rules as the parent call
      function getCallId() {
        return resource.data.callId;
      }

      function hasAccessToParentCall() {
        let callId = getCallId();
        return belongsToSameClinic(callId, 'calls');
      }

      allow read: if isAuthenticated() && hasAccessToParentCall();
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isDoctor() || isAdmin()) && hasAccessToParentCall();
      allow delete: if isAuthenticated() && isAdmin() && hasAccessToParentCall();
    }

    // Locations collection
    match /locations/{locationId} {
      // Locations must have a clinic ID
      // All authenticated users can read locations for their clinic
      // Only admins can create/update/delete locations
      allow read: if isAuthenticated() && belongsToSameClinic(locationId, 'locations');
      allow create: if isAuthenticated() && isAdmin();
      allow update: if isAuthenticated() && isAdmin() && belongsToSameClinic(locationId, 'locations');
      allow delete: if isAuthenticated() && isAdmin() && belongsToSameClinic(locationId, 'locations');
    }

    // Agent-Location Mappings collection
    match /agent-location-mappings/{agentId} {
      // Only admins can create, update, or delete mappings
      allow create, update, delete: if isAuthenticated() && isAdmin();
      // All authenticated users can read mappings for their clinic
      allow read: if isAuthenticated() && get(/databases/$(database)/documents/agent-location-mappings/$(agentId)).data.clinicId == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.clinicId;
    }

    // Clients collection
    match /clients/{clientId} {
      // Clients must have a clinic ID
      // Doctors can read and update all client data for their clinic
      // Staff can read basic info but not medical data for clients in their clinic
      allow read: if isAuthenticated() && belongsToSameClinic(clientId, 'clients');
      allow create: if isAuthenticated();
      allow update: if isAuthenticated() && (isDoctor() || isAdmin()) && belongsToSameClinic(clientId, 'clients');
      allow delete: if isAuthenticated() && isAdmin() && belongsToSameClinic(clientId, 'clients');
    }

    // Patient References collection
    match /patientReferences/{patientRefId} {
      // All authenticated users can read patient references
      // Only external API can create/update patient references
      allow read: if isAuthenticated();
      // No client-side create/update allowed - only through server API
      allow create, update: if false; // Restricted to server-side API only
      allow delete: if isAuthenticated() && isAdmin();
    }

    // On-call schedules - Clinic admins can manage their clinic's schedules
    match /on-call-schedules/{scheduleId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || belongsToSameClinic(scheduleId, 'on-call-schedules'));
      allow create: if isAuthenticated() && 
        isAdmin() && 
        request.resource.data.clinicId == get(/databases/$(database)/documents/users/$(request.auth.uid)).data.clinicId;
      allow update, delete: if isAuthenticated() && 
        isAdmin() && 
        belongsToSameClinic(scheduleId, 'on-call-schedules');
    }

    // On-call notifications - Read-only for clinic admins, system writes only
    match /on-call-notifications/{notificationId} {
      allow read: if isAuthenticated() && 
        (isAdmin() || belongsToSameClinic(notificationId, 'on-call-notifications'));
      // No client-side writes - server-side only
      allow create, update, delete: if false;
    }

    // Settings collection (for clinic-wide settings)
    match /settings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isAdmin();
    }
  }
}