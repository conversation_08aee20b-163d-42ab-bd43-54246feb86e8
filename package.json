{"name": "doctor-staff-portal", "version": "0.1.0", "private": true, "description": "Doctor Staff Portal", "scripts": {"dev": "DOTENV_CONFIG_PATH=./.env.dev.local node -r dotenv/config node_modules/next/dist/bin/next dev", "prd": "DOTENV_CONFIG_PATH=./.env.prd.local node -r dotenv/config node_modules/next/dist/bin/next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx", "lint:ci": "eslint . --ext .ts,.tsx --quiet", "prepare": "husky", "type": "tsc --noEmit", "test": "jest", "test:ci": "jest --silent", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:detect-leaks": "jest --<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "git-pre-commit": "lint-staged --shell", "format": "prettier lib models pages utils scripts components types __tests__ !**/*.{yml,yaml} --write", "db:init": "tsx scripts/init-database.ts", "db:migrate": "knex migrate:latest", "db:sync": "knex migrate:latest && node scripts/migrate-firestore-to-mysql.js", "db:rollback": "knex migrate:rollback", "db:reset": "knex migrate:rollback --all && knex migrate:latest"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "@google-cloud/scheduler": "^5.1.0", "@google-cloud/storage": "^7.16.0", "@google/genai": "^0.12.0", "axios": "^1.8.4", "dayjs": "^1.11.13", "dotenv": "^16.4.1", "firebase": "^11.5.0", "firebase-admin": "^13.2.0", "flowbite": "^2.2.0", "flowbite-react": "^0.10.2", "fluent-ffmpeg": "^2.1.3", "jose": "4.15.9", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "mysql2": "^3.14.1", "next": "^14.2.25", "next-swagger-doc": "^0.4.1", "node-fetch": "^2.7.0", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.11.0", "react-markdown": "^10.1.0", "react-select": "^5.10.1", "recharts": "^3.0.2", "redoc": "^2.4.0", "swagger-ui-react": "^5.20.8", "swr": "^2.3.4", "tailwind-merge": "^3.2.0", "twilio": "^5.5.1", "uuid": "^9.0.1", "zod": "^3.24.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.0", "@types/react": "^18.2.39", "@types/react-dom": "^18.2.17", "@types/recharts": "^2.0.1", "@types/supertest": "^6.0.3", "@types/swagger-ui-react": "^5.18.0", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.16", "axios-mock-adapter": "^2.1.0", "eslint": "^8.54.0", "eslint-config-next": "^14.2.25", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.0", "msw": "^2.7.3", "nock": "^14.0.1", "node-mocks-http": "^1.16.2", "postcss": "^8.4.31", "prettier": "^3.5.3", "supertest": "^7.1.0", "tailwindcss": "^3.3.5", "ts-jest": "^29.3.0", "tsx": "^4.7.0", "typescript": "^5.3.2"}}