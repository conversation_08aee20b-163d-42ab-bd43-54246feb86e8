import { v4 as uuidv4 } from 'uuid';

export interface TimeSlot {
  id: string;
  time: string; // Format: "HH:MM" (24-hour)
  isAvailable: boolean;
}

export interface CalendarSlot {
  id: string;
  userId: string; // UUID v4 of the user with role "doctor"
  locationId: string; // UUID v4 of the location
  date: string; // ISO format, e.g., "2025-01-30"
  timeSlots: TimeSlot[];
}

// Helper function to create default time slots (30-minute intervals)
export function createDefaultTimeSlots(startHour = 9, endHour = 17): TimeSlot[] {
  const slots: TimeSlot[] = [];

  for (let hour = startHour; hour < endHour; hour++) {
    for (const minutes of ['00', '30']) {
      slots.push({
        id: uuidv4(),
        time: `${hour.toString().padStart(2, '0')}:${minutes}`,
        isAvailable: true,
      });
    }
  }

  return slots;
}
