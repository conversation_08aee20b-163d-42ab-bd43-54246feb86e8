/**
 * @swagger
 * components:
 *   schemas:
 *     CallSession:
 *       type: object
 *       required:
 *         - id
 *         - sessionId
 *       properties:
 *         id:
 *           type: string
 *           description: UUID v4 format
 *         sessionId:
 *           type: string
 *           description: Unique identifier for the call session
 *         agentId:
 *           type: string
 *           description: Unique identifier for the agent
 *         hasVoiceMail:
 *           type: boolean
 *           description: Indicates if the call has a voicemail
 *         callType:
 *           type: number
 *           description: Enum value representing the type of call
 *         callTypes:
 *           type: array
 *           description: Ordered list of call types encountered during the session (latest last)
 *         callerPhone:
 *           type: string
 *           description: Phone number of the caller
 *         patientId:
 *           type: string
 *           description: UUID of the associated patient
 *         appointmentId:
 *           type: string
 *           description: UUID of the associated appointment
 *         isRedirected:
 *           type: boolean
 *           description: Indicates if the call is redirected
 *         callId:
 *           type: string
 *           description: UUID of the associated call record
 *         sendNewPatientForm:
 *           type: boolean
 *           description: Indicates if new patient form SMS should be sent
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * Represents a call session in the system
 */
export interface CallSession {
  id: string; // UUID v4 format
  status?: string; // 'active', 'completed', 'cancelled'
  sessionId: string; // Unique identifier for the call session
  agentId?: string; // Unique identifier for the agent
  triggerEvent?: string; // Trigger event for the call 'no-show'
  hasVoiceMail?: boolean; // Indicates if the call has a voicemail
  callType?: number; // Enum value representing the type of call
  /** Ordered list of call types encountered during the session (latest last). */
  callTypes?: number[];
  callerPhone?: string; // Phone number of the caller
  patientId?: string; // UUID of the associated patient
  appointmentId?: string; // UUID of the associated appointment
  isRedirected?: boolean; // Indicates if the call is redirected
  callId?: string; // UUID of the associated call record
  sendNewPatientForm?: boolean; // Indicates if new patient form SMS should be sent
  createdAt: Date;
  updatedAt: Date;
}
