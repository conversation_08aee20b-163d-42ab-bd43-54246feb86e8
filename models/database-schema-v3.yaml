Project multi_location_practice_schema {
  database_type: "PostgreSQL"
  note: "Updated schema v3: Added Practice entity and multi-location user access"
}

// --------------------------------------------------
// Enum definitions
// --------------------------------------------------
Enum UserRole {
  SUPER_ADMIN
  CLINIC_ADMIN
  STAFF
}

// --------------------------------------------------
// Table definitions
// --------------------------------------------------
Table Clinics {
  id int [pk, note: "Primary key for Clinics"]
  name varchar [not null]
  logoUrl varchar
  address varchar
  phone varchar
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table Practice {
  id varchar [pk, note: "UUID v4 format"]
  clinicId int [not null, note: "References Clinics.id"]
  name varchar [not null, note: "Name of the practice (e.g., 'Cardiology Practice')"]
  description varchar [note: "Optional description of the practice"]
  isActive boolean [not null, default: "true", note: "Whether the practice is currently active"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table Location {
  id varchar [pk, note: "UUID v4 format"]
  clinicId int [not null, note: "References Clinics.id"]
  practiceId varchar [not null, note: "References Practice.id"]
  name varchar [not null, note: "E.g., 'Downtown Office'"]
  address varchar [not null]
  phone varchar
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table User {
  id varchar [pk, note: "UUID v4 format"]
  email varchar [unique, not null]
  phone varchar
  name varchar [not null]
  role UserRole [not null]
  specialty varchar
  clinicId int [note: "References Clinics.id; can be null for SUPER_ADMIN if desired"]
  profilePicture varchar
  canTakeAppointments boolean [not null, default: "false"]
  locationIds varchar[] [note: "Array of accessible location IDs"]
  currentLocationId varchar [note: "Currently selected location ID"]
  practiceIds varchar[] [note: "Optional array of practice IDs (derived from locations)"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table Client {
  id varchar [pk, note: "UUID v4 format"]
  fullName varchar [not null]
  birthday date
  phoneNumber varchar [not null]
  email varchar [unique]
  medicalHistory text
  recentNotes text
  insuranceCompany varchar
  insuranceGroupNumber varchar
  subscriberName varchar
  clinicId int [not null, note: "References Clinics.id"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table Call {
  id varchar [pk, note: "UUID v4 format"]
  clientId varchar [not null, note: "References Client.id"]
  userId varchar [not null, note: "References User.id (staff)"]
  clinicId int [not null, note: "References Clinics.id"]
  date timestamp [not null, note: "Date/time of the call"]
  reason varchar
  // (summary and transcription have been moved to CallDetail)
  recordingUrl varchar [not null]
  notes text
  priorityScore int
  urgent boolean [default: "false"]
  tags varchar[] [note: "Array of tags, might be JSONB in some DBs"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table CallDetail {
  id varchar [pk, note: "UUID v4 format"]
  callId varchar [not null, note: "References Call.id"]
  summary text [not null]
  transcription text [not null]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table CalendarSlot {
  id varchar [pk, note: "UUID v4 format"]
  userId varchar [not null, note: "References User.id"]
  locationId varchar [not null, note: "References Location.id"]
  date date [not null, note: "Date of availability"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]

  Indexes {
    (userId, locationId, date) [unique, name: "UniqueUserLocationDate"]
  }
}

Table TimeSlot {
  id varchar [pk, note: "UUID v4 format"]
  calendarSlotId varchar [not null, note: "References CalendarSlot.id"]
  time time [not null, note: "Specific time (e.g., 09:30)"]
  isAvailable boolean [not null, default: "true"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]

  Indexes {
    (calendarSlotId, time) [unique, name: "UniqueCalendarSlotTime"]
  }
}

Table Appointment {
  id varchar [pk, note: "UUID v4 format"]
  userId varchar [not null, note: "References User.id (provider)"]
  clientId varchar [not null, note: "References Client.id (patient)"]
  slotId varchar [unique, not null, note: "References TimeSlot.id"]
  callId varchar [note: "Optional, references Call.id"]
  status varchar [not null, default: "active"] // e.g. 'active','completed','cancelled'
  // Denormalized client name for quick display:
  clientName varchar [note: "Copied from Client.fullName to avoid an extra lookup"]
  createdAt timestamp [not null, default: "now()"]
  updatedAt timestamp [not null, default: "now()"]
}

Table StaffInviteCode {
  id varchar [pk, note: "UUID v4 format"]
  code varchar [unique, not null]
  clinicId int [not null, note: "References Clinics.id"]
  used boolean [not null, default: "false"]
  expiresAt timestamp [not null]
  createdBy varchar [not null, note: "References User.id (clinic admin)"]
  createdAt timestamp [not null, default: "now()"]
  usedAt timestamp
}

// --------------------------------------------------
// Relationships (Refs)
// --------------------------------------------------
Ref: Practice.clinicId > Clinics.id
Ref: Location.clinicId > Clinics.id
Ref: Location.practiceId > Practice.id
Ref: User.clinicId > Clinics.id
Ref: User.currentLocationId > Location.id
Ref: Client.clinicId > Clinics.id
Ref: Call.clientId > Client.id
Ref: Call.userId > User.id
Ref: Call.clinicId > Clinics.id
Ref: CallDetail.callId > Call.id
Ref: CalendarSlot.userId > User.id
Ref: CalendarSlot.locationId > Location.id
Ref: TimeSlot.calendarSlotId > CalendarSlot.id
Ref: Appointment.userId > User.id
Ref: Appointment.clientId > Client.id
Ref: Appointment.slotId > TimeSlot.id
Ref: Appointment.callId > Call.id
Ref: StaffInviteCode.clinicId > Clinics.id
Ref: StaffInviteCode.createdBy > User.id

// --------------------------------------------------
// Notes
// --------------------------------------------------
Note: "Practice entity added to support Clinic -> Practice -> Location hierarchy"
Note: "User.locationIds array allows multi-location access within clinic scope"
Note: "User.practiceIds is optional and derived from accessible locations"
Note: "Backward compatibility maintained with existing clinic structure" 