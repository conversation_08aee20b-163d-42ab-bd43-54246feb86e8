/**
 * @swagger
 * components:
 *   schemas:
 *     Call:
 *       type: object
 *       required:
 *         - id
 *         - clientId
 *         - userId
 *         - clinicId
 *         - date
 *         - recordingUrl
 *       properties:
 *         id:
 *           type: string
 *           description: UUID v4 format
 *         clientId:
 *           type: string
 *           description: UUID v4 of the client who made the call
 *         clientName:
 *           type: string
 *           description: Full name of the client who made the call
 *         userId:
 *           type: string
 *           description: ID of staff member who handled the call
 *         clinicId:
 *           type: number
 *           description: ID of the clinic this call belongs to
 *         locationId:
 *           type: number
 *           description: ID of the location this call belongs to
 *         transferToLocationId:
 *           type: number
 *           description: ID of the location where the call was transferred to
 *         date:
 *           type: string
 *           format: date-time
 *           description: Date and time of the call
 *         reason:
 *           type: string
 *           description: Reason for the call
 *         recordingUrl:
 *           type: string
 *           description: URL to the call recording
 *         notes:
 *           type: string
 *           description: Additional notes about the call
 *         priorityScore:
 *           type: number
 *           description: Priority score (higher means higher priority)
 *         urgent:
 *           type: boolean
 *           description: Indicates if the call is urgent
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           description: List of tags associated with the call
 *         phoneNumber:
 *           type: string
 *           description: Phone number of the caller
 *         sessionId:
 *           type: string
 *           description: Dialogflow session ID for the call
 *         agentId:
 *           type: string
 *           description: Dialogflow agent ID for the call
 *         hasVoiceMail:
 *           type: boolean
 *           description: Indicates if the call has a voice mail
 *         voicemailUrl:
 *           type: string
 *           description: URL to the voice mail
 *         transcriptionWithAudio:
 *           type: string
 *           description: JSON string containing transcript with audio records
 *         duration:
 *           type: string
 *           description: Duration of the call (e.g., "5 min")
 *         type:
 *           type: number
 *           description: Type of call (enum value from CallType)
 *         callTypes:
 *           type: array
 *           items:
 *             type: number
 *           description: Ordered list of call types encountered during the call lifecycle
 *         lastAppointmentId:
 *           type: string
 *           description: ID of the last fulfilled appointment
 *         lastAppointmentDate:
 *           type: string
 *           format: date-time
 *           description: Date of the last fulfilled appointment
 *         lastAppointmentPractitionerId:
 *           type: string
 *           description: Practitioner ID of the last fulfilled appointment
 *         lastAppointmentPractitionerName:
 *           type: string
 *           description: Practitioner name of the last fulfilled appointment
 */

import { CallType } from './CallTypes';

/**
 * Represents a call record in the system
 */
export interface Call {
  id: string; // UUID v4 format
  clientId: string; // UUID v4 format of the client who made the call
  clientName?: string; // Full name of the client who made the call
  userId: string; // ID of staff member who handled the call
  clinicId: number; // ID of the clinic this call belongs to
  locationId: number; // ID of the location this call belongs to
  transferToLocationId?: number; // ID of the location where the call was transferred to
  date: Date; // Contains both date and time information
  reason?: string; // Renamed from 'purpose'
  summary?: string; // Summary of the call (now optional, moved to CallDetail)
  voicemailSummary?: string; // Summary of the voicemail (now optional, moved to CallDetail)
  transcription?: string; // Full transcription of the call (now optional, moved to CallDetail)
  recordingUrl: string; // URL to the call recording (required)
  notes?: string;
  priorityScore?: number; // Higher score means higher priority
  urgent?: boolean; // Indicates if the call is urgent
  tags?: string[]; // List of tags associated with the call
  phoneNumber?: string; // Phone number of the caller
  sessionId?: string; // Dialogflow session ID for the call
  agentId?: string; // Dialogflow agent ID for the call
  hasVoiceMail?: boolean; // Indicates if the call has a voice mail
  isOutboundCall?: boolean; // Indicates if the call is an outbound call
  voicemailUrl?: string; // URL to the voice mail
  transcriptionWithAudio?: string; // JSON string containing transcript with audio records
  duration?: string; // Duration of the call (e.g., "5 min")
  type?: CallType; // Type of call (enum value from CallType)
  /** Ordered list of call types encountered during the call lifecycle */
  callTypes?: CallType[];
  lastAppointmentId?: string; // ID of the last fulfilled appointment
  lastAppointmentDate?: Date | null; // Date of the last fulfilled appointment
  lastAppointmentPractitionerId?: string; // Practitioner ID of the last fulfilled appointment
  lastAppointmentPractitionerName?: string; // Practitioner name of the last fulfilled appointment
}
