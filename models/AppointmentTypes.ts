/**
 * Enum for Nextech appointment types
 * Maps appointment type names to their externalId values in the Nextech system
 */
export enum NextechAppointmentTypes {
  // Consultation types
  CONSULT_NEW_PATIENT = '1', // Consult New Patient
  CONSULT_EXISTING_PATIENT = '2', // Consult Existing Patient

  // Follow up types
  FOLLOW_UP_OLD = '3', // Follow Up_OLD
  FOLLOW_UP = '18', // Follow Up

  // Surgery types
  SURGERY_OLD = '4', // Surgery_OLD
  SURGERY = '22', // Surgery
  SURGERY_COSMETIC = '130', // Surgery Cosmetic
  SURGERY_FUNC_COS = '131', // Surgery Func-Cos

  // Procedure types
  EXAM_ROOM_PROCEDURE = '6', // Exam Room Procedure
  IN_OFFICE_PROCEDURE = '21', // In Office Procedure

  // Pre/Post op types
  PRE_OP = '7', // Pre-Op
  PRE_OP_APPT = '45', // Pre Op Appt
  POST_OP_VISIT = '15', // Post-op Visit
  ONE_DAY_POST_OP = '88', // 1 Day Post Op
  SAME_DAY_POST_OP = '112', // Same Day Post Op

  // Non-clinical types
  NON_CLINICAL = '8', // Non-clinical

  // Nurse visit types
  NURSE_VISIT = '9', // Nurse Visit

  // Block time
  BLOCK_TIME = '10', // Block Time

  // Patient types
  NEW_PATIENT = '11', // New Patient
  NEW_PATIENT_SHORT = '52', // New Patient Short
  NEW_PATIENT_EMERGENCY_WORK_IN = '27', // New Patient Emergency Work-In
  ESTABLISHED_PATIENT = '82', // Established Patient
  ESTABLISHED_PATIENT_SHORT = '53', // Established Patient Short

  // Referral types
  REFER_BACK = '12', // Refer Back
  REFERRAL = '16', // REFERRAL

  // Treatment types
  INJECTION = '13', // Injection
  LASER_TREATMENT = '14', // Laser Treatment

  // Retina types
  RETINA_BABY = '17', // RETINA BABY
  RETINA_EVALUATION = '57', // Retina Evaluation
  RETINA_REVISIT = '58', // Retina Revisit

  // Routine types
  ROUTINE_NEW = '19', // ROUTINE NEW
  ROUTINE_OLD = '20', // ROUTINE OLD
  ROUTINE_EXAM = '86', // Routine Exam

  // Telemedicine types
  TELEMEDICINE = '23', // Telemedicine
  VIRTUAL_CONSULT = '24', // VIRTUAL CONSULT

  // Emergency types
  EMERGENCY_WORK_IN = '25', // Emergency Work-In
  WORK_IN = '36', // WORK-IN

  // Testing
  TESTING = '26', // Testing

  // Cosmetic
  COSMETIC = '28', // Cosmetic

  // Study/Research
  STUDY_RESEARCH = '29', // Study-Research

  // Pediatric types
  PEDIATRIC_NON_DILATION = '30', // Pediatric Non-dilation
  PEDIATRIC_DILATION = '31', // Pediatric Dilation
  PREEMIE_EVALUATION = '32', // Preemie Evaluation
  PREEMIE_FOLLOW_UP = '33', // Preemie Follow-up

  // Archived
  ARCHIVED_TYPE = '34', // Archived Type

  // Contact lens types
  CONTACT_LENS_EXAM = '35', // Contact Lens Exam
  ESTABLISHED_CONTACTS = '60', // Established Contacts
  NEW_PATIENT_CONTACTS = '61', // New Patient Contacts
  HARD_LENS_CEX = '62', // Hard Lens CEX
  CONTACT_LENS_FIT = '63', // Contact Lens Fit
  HARD_LENS_FIT = '64', // Hard Lens Fit
  CONTACT_LENS_RECHECK = '65', // Contact Lens Recheck
  HARD_LENS_RECHECK = '66', // Hard Lens Recheck
  SCLERAL = '95', // Scleral

  // Glaucoma
  GLAUCOMA_CHECK = '37', // Glaucoma Check

  // Revisit types
  REVISIT_OLD = '38', // ReVisit_OLD
  REVISIT = '39', // REVISIT
  REVISIT_ALT = '73', // ReVisit (alternative spelling)

  // Cataract types
  CATARACT_EVALUATION = '40', // Cataract Evaluation
  CATARACT_EVAL_2 = '107', // Cataract Eval 2
  CATARACT_SX_UPGRADE_66984 = '92', // Cataract Sx Upgrade 66984
  CATARACT_SX_COMPLEX_66982 = '132', // Cataract Sx Complex 66982
  CATARACT_MIGS_SX = '133', // Cataract - MIGS sx
  CATARACT_SURGERY_66984 = '114', // Cataract Surgery 66984

  // Registration types
  PRE_REGISTRATION = '41', // Pre Registration

  // Conversion types
  CONVERTED = '42', // Converted
  CONVERTED_NON_CLINICAL = '43', // Converted (non-clinical)

  // Release types
  RELEASED = '44', // Released

  // Evaluation types
  LASIK_EVALUATION = '46', // Lasik Evaluation
  BLEPH_EVALUATION = '47', // Bleph Evaluation
  LESION_EVAL = '83', // Lesion Eval
  ENTROPION_OR_ECTROPION_EVAL = '84', // Entropion or Ectropion Eval
  TEAR_DUCT_EVAL = '85', // Tear Duct Eval
  DRY_EYE_EVAL = '67', // Dry Eye Eval
  LASER_EVAL = '101', // Laser Eval

  // Baseline
  BASELINE_EXAM = '48', // Baseline Exam

  // Rx types
  RX_CHECK = '49', // Rx Check

  // Diagnostics
  DIAGNOSTICS = '50', // Diagnostics

  // Vision types
  DOUBLE_VISION = '51', // Double Vision

  // Treatment types
  LIPIFLOW = '54', // Lipiflow
  LIPEVIEW = '55', // Lipeview
  DRY_EYE_TREATMENT = '99', // Dry Eye Treatment

  // YAG types
  YAG_OUTPATIENT = '56', // YAG- Outpatient

  // Tonometry
  SERIAL_TONOMETRY = '59', // Serial Tonometry

  // Refractive surgery
  REFRACTIVE_SURGERY = '68', // Refractive Surgery

  // Visual field
  VISUAL_FIELD_1 = '69', // Visual Field 1
  VISUAL_FIELD_2 = '70', // Visual Field 2

  // Exam types
  EXAM_TREAT = '71', // Exam Treat
  COMPLETE_EXAM_OLD = '74', // Complete Exam_OLD
  COMPLETE_EXAM = '75', // Complete Exam
  SHORT_EXAM = '76', // Short Exam
  INTERMEDIATE_EXAM = '77', // Intermediate Exam
  LONG_EXAM = '78', // Long Exam
  INTERMEDIATE = '79', // Intermediate
  SHORT = '80', // Short
  LONG = '81', // Long

  // Day-specific
  ON_DAY_ONE_DAY_PO = '72', // On Day-One Day PO

  // Surgery upgrade
  SURGERY_UPGRADE_LENSX = '87', // Surgery Upgrade (LenSx)

  // ASCAN types
  ASCAN = '89', // ASCAN
  ASCAN_BT = '90', // ASCAN BT
  ASCAN_RA = '91', // ASCAN RA

  // Surgery return
  SURGERY_RETURN = '93', // Surgery Return
  RETURN_SURGICAL_EVAL = '110', // Return Surgical Eval

  // Doctor approval
  DOCTOR_APPROVAL_ONLY = '94', // Doctor Approval Only

  // Laser outpatient
  LASER_OUTPATIENT = '96', // Laser Outpatient

  // Test
  TESTXTWE = '97', // tesxtwe

  // Lunch
  LUNCH = '98', // LUNCH

  // Event types
  COSMETIC_EVENT = '100', // Cosmetic Event

  // Cancer
  CANCER_PROVEN_LESION = '102', // Cancer Proven Lesion

  // Skinvive
  SKINVIVE = '103', // Skinvive

  // Consult types
  OUTSIDE_CONSULT = '109', // Outside Consult
  LASER_CONSULT = '116', // Laser Consult
  HOSPITAL_CONSULT = '118', // Hospital Consult
  ER_INPATIENT_CONSULT = '119', // ER-Inpatient Consult

  // Medicaid
  MEDICAID = '111', // Medicaid

  // CLAREON
  CLAREON_1MO_PO_UPSTAIR = '113', // CLAREON 1MO PO (UPSTAIR)

  // IOL
  IOL_CALCULATIONS = '115', // IOL Calculations

  // Lasik
  LASIK_SCREENING = '117', // Lasik Screening

  // Eylea
  EYLEA_PAP = '120', // Eylea PAP

  // Charity
  CHARITY = '121', // Charity

  // Follow up with test
  FOLLOW_UP_WITH_TEST = '122', // Follow Up with Test

  // PTO types
  PTO_HALF_AM = '123', // PTO Half AM
  PTO_FULL = '124', // PTO Full
  PTO_HALF_PM = '127', // PTO Half PM

  // Scheduled off types
  SCHEDULED_OFF_HALF_AM = '125', // Scheduled Off Half AM
  SCHEDULED_OFF_FULL = '126', // Scheduled Off Full
  SCHEDULED_OFF_HALF_PM = '128', // Scheduled Off Half PM

  // Admin
  PROVIDER_ADMIN = '129', // Provider Admin

  // MIGS
  MIGS_STAND_ALONE = '134', // MIGS Stand Alone

  // Diamond glow
  DIAMOND_GLOW = '135', // Diamond Glow

  // Unexpected conditions
  UNEXPECTED_CONDITIONS_CLOSURE = '136', // Unexpected Conditions-Closure
}

/**
 * Interface for appointment type information
 */
export interface AppointmentTypeInfo {
  id: string;
  name: string;
  externalId: string;
}

/**
 * Get the appointment type name from the enum value (externalId)
 * @param externalId The external ID of the appointment type
 * @returns The name of the appointment type or undefined if not found
 */
export function getAppointmentTypeName(externalId: string): string | undefined {
  const entry = Object.entries(NextechAppointmentTypes).find(([, value]) => value === externalId);

  if (!entry) return undefined;

  // Get the comment that contains the original name
  const enumKey = entry[0];
  const enumDefinition = NextechAppointmentTypes.toString();
  const regex = new RegExp(`${enumKey} = "${externalId}", // (.+)`);
  const match = enumDefinition.match(regex);

  return match ? match[1] : formatEnumKey(enumKey);
}

/**
 * Get the enum key from the externalId
 * @param externalId The external ID of the appointment type
 * @returns The enum key or undefined if not found
 */
export function getAppointmentTypeKey(externalId: string): string | undefined {
  const entry = Object.entries(NextechAppointmentTypes).find(([, value]) => value === externalId);

  return entry ? entry[0] : undefined;
}

/**
 * Format an enum key to a more readable string
 * @param key The enum key to format
 * @returns The formatted string
 */
function formatEnumKey(key: string): string {
  return key
    .split('_')
    .map(word => word.charAt(0) + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Get all appointment types as an array of AppointmentTypeInfo objects
 * @returns Array of appointment type information
 */
export function getAllAppointmentTypes(): AppointmentTypeInfo[] {
  return Object.entries(NextechAppointmentTypes).map(([key, value]) => {
    const name = getAppointmentTypeName(value) || formatEnumKey(key);
    return {
      id: key,
      name,
      externalId: value,
    };
  });
}
