/**
 * @swagger
 * components:
 *   schemas:
 *     CallDetail:
 *       type: object
 *       required:
 *         - id
 *         - callId
 *         - summary
 *         - transcription
 *       properties:
 *         id:
 *           type: string
 *           description: UUID v4 format
 *         callId:
 *           type: string
 *           description: References Call.id
 *         summary:
 *           type: string
 *           description: Summary of the call
 *         transcription:
 *           type: string
 *           description: Full transcription of the call
 *         transcriptionWithAudio:
 *           type: string
 *           description: JSON string containing transcript with audio records
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * Represents detailed data for a call that is expensive to retrieve
 */
export interface CallDetail {
  id: string; // UUID v4 format
  callId: string; // References the parent Call
  summary: string; // Summary of the call
  transcription: string; // Full transcription of the call
  transcriptionWithAudio?: string; // JSON string containing transcript with audio records
  voicemailSummary?: string;
  createdAt: Date;
  updatedAt: Date;
}
