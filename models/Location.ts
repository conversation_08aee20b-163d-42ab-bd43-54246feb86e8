/**
 * @swagger
 * components:
 *   schemas:
 *     Location:
 *       type: object
 *       required:
 *         - id
 *         - clinicId
 *         - practiceId
 *         - name
 *         - address
 *       properties:
 *         id:
 *           type: string
 *           description: Location ID (can be string or number)
 *         clinicId:
 *           type: number
 *           description: References Clinic.id
 *         practiceId:
 *           type: string
 *           description: References Practice.id
 *         name:
 *           type: string
 *           description: Name of the location (e.g., 'Downtown Office')
 *         address:
 *           type: string
 *           description: Physical address of the location
 *         phone:
 *           type: string
 *           description: Contact phone for this location
 *         timeZone:
 *           type: string
 *           description: Time zone for the location
 *         isActive:
 *           type: boolean
 *           description: Whether the location is active
 *         practiceName:
 *           type: string
 *           description: Name of the practice this location belongs to
 *         officeHours:
 *           type: object
 *           description: Office hours for the location
 *           properties:
 *             day:
 *               type: string
 *               description: Day of the week (1-7, where 1 is Monday)
 *             start:
 *               type: string
 *               description: Start time of the office hours (e.g., '09:00')
 *             end:
 *               type: string
 *               description: End time of the office hours (e.g., '17:00')
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * Represents a physical location of a clinic within a practice
 */
export interface Location {
  /** Location ID (can be string or number for compatibility) */
  id: string;
  /** References the parent Clinic */
  clinicId: number;
  /** References the parent Practice */
  practiceId: string;
  /** Name of the location (e.g., 'Downtown Office') */
  name: string;
  /** Physical address of the location */
  address: string;
  /** Contact phone for this location */
  phone?: string;
  /** Time zone for the location */
  timeZone: string;
  /** Whether the location is active */
  isActive: boolean;
  /** Name of the practice this location belongs to */
  practiceName: string;
  /** Office hours for the location */
  officeHours?: Record<string, { start: string; end: string } | null>;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
}
