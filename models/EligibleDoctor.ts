/**
 * @swagger
 * components:
 *   schemas:
 *     EligibleDoctor:
 *       type: object
 *       required:
 *         - id
 *         - name
 *         - email
 *         - canTakeAppointments
 *         - hasConflicts
 *       properties:
 *         id:
 *           type: string
 *           description: Doctor unique identifier
 *         name:
 *           type: string
 *           description: Doctor full name
 *         email:
 *           type: string
 *           description: Doctor email address
 *         phone:
 *           type: string
 *           description: Doctor contact phone number
 *         specialty:
 *           type: string
 *           description: Doctor medical specialty
 *         canTakeAppointments:
 *           type: boolean
 *           description: Whether the doctor is authorized to take appointments
 *         currentSchedules:
 *           type: array
 *           description: Current schedules for the doctor showing availability
 *           items:
 *             type: object
 *             properties:
 *               date:
 *                 type: string
 *                 format: date
 *                 description: Schedule date in ISO format (YYYY-MM-DD)
 *               startTime:
 *                 type: string
 *                 description: Schedule start time (HH:MM, 24-hour)
 *               endTime:
 *                 type: string
 *                 description: Schedule end time (HH:MM, 24-hour)
 *         hasConflicts:
 *           type: boolean
 *           description: Whether the doctor has scheduling conflicts
 */

/**
 * Represents a doctor eligible for scheduling at a location
 * Used to determine which doctors can be assigned to on-call schedules or take appointments
 */
export interface EligibleDoctor {
  /** Doctor unique identifier */
  id: string;
  /** Doctor full name */
  name: string;
  /** Doctor email address */
  email: string;
  /** Doctor contact phone number */
  phone?: string;
  /** Doctor medical specialty */
  specialty?: string;
  /** Whether the doctor is authorized to take appointments */
  canTakeAppointments: boolean;
  /** Current schedules for the doctor showing availability */
  currentSchedules?: {
    /** Schedule date in ISO format (YYYY-MM-DD) */
    date: string;
    /** Schedule start time (HH:MM, 24-hour) */
    startTime: string;
    /** Schedule end time (HH:MM, 24-hour) */
    endTime: string;
  }[];
  /** Whether the doctor has scheduling conflicts */
  hasConflicts: boolean;
}
