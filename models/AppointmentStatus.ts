/**
 * Enum for Nextech appointment statuses
 * Based on FHIR appointment status values
 */
export enum AppointmentStatus {
  // None of the participant(s) have finalized their acceptance of the appointment request
  PROPOSED = 'proposed',

  // Some or all of the participant(s) have not finalized their acceptance of the appointment request
  PENDING = 'pending',

  // All participant(s) have been considered and the appointment is confirmed
  BOOKED = 'booked',

  // The patient/patients has/have arrived and is/are waiting to be seen
  ARRIVED = 'arrived',

  // The planning stages of the appointment are now complete
  FULFILLED = 'fulfilled',

  // The appointment has been cancelled
  CANCELLED = 'cancelled',

  // Some or all of the participant(s) have not/did not appear for the appointment
  NOSHOW = 'noshow',

  // This instance should not have been part of this patient's medical record
  ENTERED_IN_ERROR = 'entered-in-error',

  // When checked in, all pre-encounter administrative work is complete
  CHECKED_IN = 'checked-in',

  // The appointment has been placed on a waitlist
  WAITLIST = 'waitlist',
}
