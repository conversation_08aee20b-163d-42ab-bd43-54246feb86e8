/**
 * @swagger
 * components:
 *   schemas:
 *     AfterHoursCall:
 *       type: object
 *       required:
 *         - id
 *         - patientBirthday
 *         - patientFullName
 *         - patientPhoneNumber
 *         - callReason
 *         - callId
 *       properties:
 *         id:
 *           type: number
 *           description: Auto-increment primary key
 *         patientBirthday:
 *           type: string
 *           format: date
 *           description: Pat<PERSON>'s date of birth
 *         patientFullName:
 *           type: string
 *           description: Full name of the patient
 *         patientPhoneNumber:
 *           type: string
 *           description: Pat<PERSON>'s phone number
 *         callReason:
 *           type: string
 *           description: Reason for the after-hours call
 *         callId:
 *           type: string
 *           description: UUID v4 reference to the original call
 *         sessionId:
 *           type: string
 *           description: Session ID for the call
 *         doctorId:
 *           type: string
 *           description: UUID v4 of the primary on-call doctor
 *         backupDoctorId:
 *           type: string
 *           description: UUID v4 of the backup on-call doctor
 *         doctorPhone:
 *           type: string
 *           description: Phone number of the primary on-call doctor
 *         backupDoctorPhone:
 *           type: string
 *           description: Phone number of the backup on-call doctor
 *         isReviewedByDoctor:
 *           type: boolean
 *           description: Whether the call has been reviewed by a doctor
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

/**
 * Represents an after-hours call record in the system
 */
export interface AfterHoursCall {
  id: string; // String ID for BaseEntity compatibility (converted from MySQL auto-increment)
  patientBirthday: Date; // Patient's date of birth
  patientFullName: string; // Full name of the patient
  patientPhoneNumber: string; // Patient's phone number
  callReason: string; // Reason for the after-hours call
  callId: string; // UUID v4 reference to the original call
  sessionId?: string; // Session ID for the call
  doctorId?: string; // UUID v4 of the primary on-call doctor
  backupDoctorId?: string; // UUID v4 of the backup on-call doctor
  doctorPhone?: string; // Phone number of the primary on-call doctor
  backupDoctorPhone?: string; // Phone number of the backup on-call doctor
  isReviewedByDoctor?: boolean; // Whether the call has been reviewed by a doctor
  createdAt?: Date; // When the record was created
  updatedAt?: Date; // When the record was last updated
}
