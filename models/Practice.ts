/**
 * @swagger
 * components:
 *   schemas:
 *     Practice:
 *       type: object
 *       required:
 *         - id
 *         - clinicId
 *         - name
 *         - isActive
 *       properties:
 *         id:
 *           type: string
 *           description: UUID v4 format
 *         clinicId:
 *           type: number
 *           description: References Clinic.id
 *         name:
 *           type: string
 *           description: Name of the practice (e.g., 'Cardiology Practice')
 *         description:
 *           type: string
 *           description: Optional description of the practice
 *         isActive:
 *           type: boolean
 *           description: Whether the practice is currently active
 *           default: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * Represents a practice within a clinic
 * A practice is a logical grouping of locations and users within a clinic
 */
export interface Practice {
  /** UUID v4 format */
  id: string;
  /** References the parent Clinic */
  clinicId: number;
  /** Name of the practice (e.g., 'Cardiology Practice') */
  name: string;
  /** Optional description of the practice */
  description?: string;
  /** Whether the practice is currently active */
  isActive: boolean;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
}
