/**
 * @swagger
 * components:
 *   schemas:
 *     CallType:
 *       type: object
 *       properties:
 *         id:
 *           type: number
 *           description: Numeric identifier for the call type
 *         name:
 *           type: string
 *           description: Display name of the call type
 *         description:
 *           type: string
 *           description: Detailed description of the call type
 */

/**
 * Enum for call types
 * Represents different types of calls in the system
 */
export enum CallType {
  OTHER = 0,
  VOICEMAIL = 1,
  TRANSFER_TO_HUMAN = 2, // Keep for backward compatibility
  NEW_PATIENT_NEW_APPOINTMENT = 3,
  NEW_APPOINTMENT_EXISTING_PATIENT = 4,
  RESCHEDULE = 5,
  CANCELLATION = 6,
  VOICEMAIL_SYSTEM_ERROR = 7,
  GENERAL_INFO = 8,
  LOOKUP = 9,
  TRANSFER_TO_CLINIC = 10,
  AFTER_HOURS = 11,
  CONFIRM_APPOINTMENT = 12,
  DISCONNECTED = 13, // >20 seconds, abrupt end
  // New transfer subtypes
  IMMEDIATE_TRANSFER = 14, // ≤35 seconds
  TRANSFER_DUE_TO_SCHEDULING = 15, // Unable to find appointment slot
  TRANSFER_DUE_TO_UNABLE_TO_ASSIST = 16, // Multiple turns, can't help
  IMMEDIATE_HANGUP = 17, // ≤3 conversation turns, no significant interaction
}

/**
 * Call type information with display names and descriptions
 */
export const CallTypeInfo: Record<CallType, { name: string; description: string }> = {
  [CallType.OTHER]: {
    name: 'Other',
    description: 'Call type not specified or other',
  },
  [CallType.VOICEMAIL]: {
    name: 'Voicemail',
    description: 'Caller left a voicemail message',
  },
  [CallType.TRANSFER_TO_HUMAN]: {
    name: 'Transfer to Human',
    description: 'Call was transferred to a human agent',
  },
  [CallType.NEW_PATIENT_NEW_APPOINTMENT]: {
    name: 'New Patient New Appointment',
    description: 'New patient scheduling their first appointment',
  },
  [CallType.NEW_APPOINTMENT_EXISTING_PATIENT]: {
    name: 'New Appointment Existing Patient',
    description: 'Existing patient scheduling a new appointment',
  },
  [CallType.RESCHEDULE]: {
    name: 'Reschedule',
    description: 'Patient rescheduling an existing appointment',
  },
  [CallType.CANCELLATION]: {
    name: 'Cancellation',
    description: 'Patient cancelling an existing appointment',
  },
  [CallType.VOICEMAIL_SYSTEM_ERROR]: {
    name: 'Voicemail after System Error',
    description: 'Voicemail left after a system error occurred',
  },
  [CallType.GENERAL_INFO]: {
    name: 'General Info',
    description: 'Caller requesting general information',
  },
  [CallType.LOOKUP]: {
    name: 'Lookup',
    description: 'Lookup action such as appointment or patient search',
  },
  [CallType.TRANSFER_TO_CLINIC]: {
    name: 'Transfer to Clinic',
    description: 'Call was transferred to a clinic',
  },
  [CallType.AFTER_HOURS]: {
    name: 'After Hours',
    description: 'Call was made during after hours',
  },
  [CallType.CONFIRM_APPOINTMENT]: {
    name: 'Confirm Appointment',
    description: 'Appointment confirmation',
  },
  [CallType.DISCONNECTED]: {
    name: 'Disconnected',
    description: 'Call was disconnected without clear purpose or type',
  },
  [CallType.IMMEDIATE_TRANSFER]: {
    name: 'Immediate Transfer',
    description: 'Call transferred to human within 35 seconds',
  },
  [CallType.TRANSFER_DUE_TO_SCHEDULING]: {
    name: 'Transfer - Scheduling Issue',
    description: 'Call transferred after being unable to schedule appointment',
  },
  [CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST]: {
    name: 'Transfer - Unable to Assist',
    description: 'Call transferred after multiple attempts to assist',
  },
  [CallType.IMMEDIATE_HANGUP]: {
    name: 'Immediate Hangup',
    description: 'Call ended within 3 conversation turns without significant interaction',
  },
};

/**
 * Get call type display name
 * @param type Call type enum value
 * @returns Display name for the call type
 */
export function getCallTypeName(type: CallType | undefined | null): string {
  if (type === undefined || type === null) {
    return CallTypeInfo[CallType.OTHER].name;
  }
  return CallTypeInfo[type]?.name || CallTypeInfo[CallType.OTHER].name;
}

/**
 * Get call type description
 * @param type Call type enum value
 * @returns Description for the call type
 */
export function getCallTypeDescription(type: CallType | undefined | null): string {
  if (type === undefined || type === null) {
    return CallTypeInfo[CallType.OTHER].description;
  }
  return CallTypeInfo[type]?.description || CallTypeInfo[CallType.OTHER].description;
}
