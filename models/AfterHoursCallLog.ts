/**
 * @swagger
 * components:
 *   schemas:
 *     AfterHoursCallLog:
 *       type: object
 *       required:
 *         - id
 *         - afterHoursCallId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: UUID v4 primary key
 *         afterHoursCallId:
 *           type: string
 *           format: uuid
 *           description: UUID v4 reference to the after hours call
 *         viewedBy:
 *           type: string
 *           format: uuid
 *           description: UUID v4 of the user who viewed the call
 *         contactedBy:
 *           type: string
 *           format: uuid
 *           description: UUID v4 of the user who contacted the patient
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: When the record was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: When the record was last updated
 */

/**
 * Represents an after-hours call log record in the system
 */
export interface AfterHoursCallLog {
  id: string; // UUID v4 primary key
  afterHoursCallId: string; // UUID v4 reference to the after hours call
  viewedBy?: string; // UUID v4 of the user who viewed the call
  contactedBy?: string; // UUID v4 of the user who contacted the patient
  contactedByInfo?: string; // Fallback field for contact info when FK constraint fails
  createdAt?: Date; // When the record was created
  updatedAt?: Date; // When the record was last updated
}
