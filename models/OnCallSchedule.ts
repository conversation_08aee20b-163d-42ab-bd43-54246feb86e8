/**
 * On-Call Schedule Model
 * Represents doctor on-call schedules for specific locations and time periods
 */

/**
 * Represents an on-call schedule for a specific location and doctor
 */
export interface OnCallSchedule {
  /** Schedule ID (UUID string) */
  id: string;
  /** Doctor (staff) unique identifier */
  doctorId: string;
  /** Doctor display name */
  doctorName: string;
  /** Doctor contact phone number */
  doctorPhone?: string;
  /** References the Location the schedule belongs to */
  locationId: string;
  /** References the Clinic the schedule belongs to */
  clinicId: number;
  /** Schedule calendar date in ISO format (YYYY-MM-DD) */
  date: string;
  /** Schedule start time (HH:MM, 24-hour) */
  startTime: string;
  /** Schedule end time (HH:MM, 24-hour) */
  endTime: string;
  /** Whether the schedule is currently active */
  isActive: boolean;
  /** IANA timezone identifier (e.g. 'America/Chicago') */
  timezone: string;
  /** Optional free-text notes */
  notes?: string;
  /** Whether this is the primary on-call doctor for the shift */
  isPrimary?: boolean;
  /** User ID who created the schedule */
  createdBy: string;
  /** Creation timestamp */
  createdAt: Date;
  /** Last update timestamp */
  updatedAt: Date;
}

export interface OnCallScheduleCreateRequest {
  doctorId: string;
  locationId: string;
  date: string;
  startTime: string;
  endTime: string;
  notes?: string;
  isPrimary?: boolean;
}

export interface OnCallScheduleUpdateRequest {
  doctorId?: string;
  doctorName?: string;
  doctorPhone?: string;
  date?: string;
  startTime?: string;
  endTime?: string;
  notes?: string;
  isActive?: boolean;
  isPrimary?: boolean;
}

/**
 * Schedule validation result interface
 */
export interface ScheduleValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Schedule conflict interface
 */
export interface ScheduleConflict {
  conflictingSchedule: OnCallSchedule;
  overlapStart: string;
  overlapEnd: string;
  conflictType: 'complete_overlap' | 'partial_overlap' | 'adjacent';
}

/**
 * On-call schedule query filters
 */
export interface OnCallScheduleFilters {
  locationId?: string;
  doctorId?: string;
  clinicId?: number;
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
  isPrimary?: boolean;
}

/**
 * Extended schedule information with additional context
 */
export interface OnCallScheduleWithDetails extends OnCallSchedule {
  locationName?: string;
  doctorEmail?: string;
  conflicts?: ScheduleConflict[];
}
