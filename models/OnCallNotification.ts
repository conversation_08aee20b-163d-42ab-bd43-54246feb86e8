/**
 * On-Call Notification Model
 * Represents SMS notifications sent to on-call doctors
 */

export interface OnCallNotification {
  id: string; // Auto-generated document ID
  scheduleId: string; // References OnCallSchedule.id
  callSessionId: string; // References callSessions collection
  doctorId: string; // References User.id
  clinicId: number; // References clinic (for security isolation)
  notificationTime: Date; // When SMS was sent
  smsMessageId?: string; // Twilio message SID
  status: NotificationStatus; // SMS delivery status
  callType?: string; // Type of call that triggered notification
  callerPhone?: string; // Phone number of caller
  errorMessage?: string; // Error details if status is 'failed'
  retryCount: number; // Number of retry attempts
  createdAt: Date; // Auto-generated
}

export type NotificationStatus = 'sent' | 'failed' | 'delivered' | 'pending' | 'retrying';

export interface OnCallNotificationCreateRequest {
  scheduleId: string;
  callSessionId: string;
  callType?: string;
  callerPhone?: string;
}

/**
 * Notification delivery configuration
 */
export interface NotificationConfig {
  enabled: boolean;
  delayMinutes: number;
  maxRetries: number;
  retryDelayMinutes: number;
  cooldownMinutes: number;
}

/**
 * Notification history query filters
 */
export interface NotificationFilters {
  scheduleId?: string;
  doctorId?: string;
  clinicId?: number;
  callSessionId?: string;
  status?: NotificationStatus;
  startDate?: Date;
  endDate?: Date;
}

/**
 * Notification statistics interface
 */
export interface NotificationStats {
  totalSent: number;
  delivered: number;
  failed: number;
  pending: number;
  retrying: number;
  deliveryRate: number;
}

/**
 * Extended notification information with additional context
 */
export interface OnCallNotificationWithDetails extends OnCallNotification {
  doctorName?: string;
  locationName?: string;
  scheduleDate?: string;
  scheduleTime?: string;
}
