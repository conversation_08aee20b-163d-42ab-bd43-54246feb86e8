# Front Desk Portal Database Schema

# Enum definitions
UserRole:
  type: enum
  values:
    - SUPER_ADMIN
    - CLINIC_ADMIN
    - STAFF

# Table definitions
Clinic:
  id: int [pk]
  name: varchar [not null]
  logoUrl: varchar
  address: varchar
  phone: varchar
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]

User:
  id: varchar [pk] # UUID v4 format
  email: varchar [unique, not null]
  phone: varchar
  name: varchar [not null]
  role: UserRole [not null]
  specialty: varchar
  clinicId: int [ref: > Clinic.id, null] # null for super-admin
  profilePicture: varchar
  canTakeAppointments: boolean [not null, default: false]
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]

Client:
  id: varchar [pk] # UUID v4 format
  fullName: varchar [not null]
  birthday: date [not null]
  phoneNumber: varchar [not null]
  email: varchar [unique]
  medicalHistory: text
  recentNotes: text
  insuranceCompany: varchar [not null]
  insuranceGroupNumber: varchar [not null]
  subscriberName: varchar [not null]
  clinicId: int [not null, ref: > Clinic.id]
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]

Call:
  id: varchar [pk] # UUID v4 format
  clientId: varchar [not null, ref: > Client.id]
  userId: varchar [not null, ref: > User.id] # staff member who handled the call
  clinicId: int [not null, ref: > Clinic.id]
  date: timestamp [not null] # Date and time of the call
  reason: varchar
  summary: text [not null]
  transcription: text [not null]
  recordingUrl: varchar [not null]
  notes: text
  priorityScore: int
  urgent: boolean [default: false]
  tags: varchar[] # Array of tags
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]

CalendarSlot:
  id: varchar [pk] # UUID
  userId: varchar [not null, ref: > User.id] # doctor/provider
  date: date [not null] # Date for which slots are defined
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]
  indexes:
    - name: user_date_unique
      columns: [userId, date]
      unique: true

TimeSlot:
  id: varchar [pk] # UUID
  calendarSlotId: varchar [not null, ref: > CalendarSlot.id]
  time: time [not null] # Specific time (e.g., "09:30")
  isAvailable: boolean [not null, default: true]
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]
  indexes:
    - name: calendar_time_unique
      columns: [calendarSlotId, time]
      unique: true

Appointment:
  id: varchar [pk] # UUID
  userId: varchar [not null, ref: > User.id] # provider
  clientId: varchar [not null, ref: > Client.id] # patient
  slotId: varchar [unique, not null, ref: > TimeSlot.id] # specific TimeSlot
  callId: varchar [ref: > Call.id] # optional originating Call
  status: varchar [not null, default: 'active'] # 'active', 'completed', 'cancelled'
  createdAt: timestamp [not null, default: `now()`]
  updatedAt: timestamp [not null, default: `now()`]

StaffInviteCode:
  id: varchar [pk] # UUID
  code: varchar [unique, not null] # unique invite code string
  clinicId: int [not null, ref: > Clinic.id]
  used: boolean [not null, default: false]
  expiresAt: timestamp [not null]
  createdBy: varchar [not null, ref: > User.id] # creator (clinic admin)
  createdAt: timestamp [not null, default: `now()`]
  usedAt: timestamp # When the code was used