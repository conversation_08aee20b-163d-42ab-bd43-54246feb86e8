/**
 * @swagger
 * components:
 *   schemas:
 *     Client:
 *       type: object
 *       required:
 *         - id
 *         - fullName
 *         - birthday
 *         - phoneNumber
 *         - insuranceCompany
 *         - insuranceGroupNumber
 *         - subscriberName
 *         - listOfCalls
 *         - clinicId
 *       properties:
 *         id:
 *           type: string
 *           description: UUID v4 format
 *         fullName:
 *           type: string
 *           description: Full name of the client
 *         birthday:
 *           type: string
 *           format: date
 *           description: Date of birth of the client
 *         phoneNumber:
 *           type: string
 *           description: Contact phone number
 *         email:
 *           type: string
 *           format: email
 *           description: Email address
 *         medicalHistory:
 *           type: string
 *           description: Medical history notes
 *         recentNotes:
 *           type: string
 *           description: Recent notes about the client
 *         insuranceCompany:
 *           type: string
 *           description: Name of insurance company
 *         insuranceGroupNumber:
 *           type: string
 *           description: Insurance group number
 *         subscriberName:
 *           type: string
 *           description: Name of the insurance subscriber
 *         listOfCalls:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of Call UUIDs
 *         clinicId:
 *           type: number
 *           description: For clinic-specific scoping
 */

/**
 * Represents a client/patient in the system
 */
export interface Client {
  id: string; // UUID v4 format
  fullName: string;
  birthday: Date | string;
  phoneNumber: string;
  email?: string;
  medicalHistory?: string;
  recentNotes?: string;
  insuranceCompany: string;
  insuranceGroupNumber: string;
  subscriberName: string;
  listOfCalls: string[]; // Array of Call UUIDs
  clinicId: number; // For clinic-specific scoping
}
