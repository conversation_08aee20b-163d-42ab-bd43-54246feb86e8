{"extends": ["prettier", "next/core-web-vitals", "next/typescript", "plugin:prettier/recommended"], "rules": {"@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/no-unused-vars": "error", "prettier/prettier": "warn"}, "overrides": [{"files": ["scripts/**/*.js"], "rules": {"@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-unused-vars": "warn"}}, {"files": ["**/__tests__/**/*.ts", "**/__tests__/**/*.tsx", "**/*.test.ts", "**/*.test.tsx"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-require-imports": "warn"}}]}