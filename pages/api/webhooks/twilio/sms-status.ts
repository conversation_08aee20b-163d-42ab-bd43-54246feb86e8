import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallNotificationService } from '@/lib/services/on-call-notification';
import { NotificationStatus } from '@/models/OnCallNotification';
import logger from '@/lib/external-api/v2/utils/logger';

interface TwilioStatusWebhook {
  MessageSid: string;
  MessageStatus: string;
  SmsSid?: string;
  SmsStatus?: string;
  AccountSid: string;
  From: string;
  To: string;
  ErrorCode?: string;
  ErrorMessage?: string;
}

/**
 * @swagger
 * /api/webhooks/twilio/sms-status:
 *   post:
 *     summary: Twilio SMS status webhook
 *     description: Receives SMS delivery status updates from Twilio
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               MessageSid:
 *                 type: string
 *                 description: Twilio message SID
 *               MessageStatus:
 *                 type: string
 *                 description: Message delivery status
 *               SmsSid:
 *                 type: string
 *                 description: SMS SID (legacy)
 *               SmsStatus:
 *                 type: string
 *                 description: SMS status (legacy)
 *               ErrorCode:
 *                 type: string
 *                 description: Error code if delivery failed
 *               ErrorMessage:
 *                 type: string
 *                 description: Error message if delivery failed
 *     responses:
 *       200:
 *         description: Status update processed successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    const data = req.body as TwilioStatusWebhook;

    // Get message SID (try both fields for compatibility)
    const messageSid = data.MessageSid || data.SmsSid;
    const messageStatus = data.MessageStatus || data.SmsStatus;

    if (!messageSid || !messageStatus) {
      logger.warn({ data }, 'Missing required fields in Twilio webhook');
      return res.status(400).json({ error: 'Missing MessageSid or MessageStatus' });
    }

    // Map Twilio status to our notification status
    const notificationStatus = mapTwilioStatus(messageStatus);

    logger.info(
      {
        messageSid,
        twilioStatus: messageStatus,
        notificationStatus,
        errorCode: data.ErrorCode,
        errorMessage: data.ErrorMessage,
      },
      'Received Twilio SMS status update',
    );

    // Update notification status in database
    await OnCallNotificationService.updateNotificationStatus(messageSid, notificationStatus);

    // Log errors if any
    if (data.ErrorCode || data.ErrorMessage) {
      logger.warn(
        {
          messageSid,
          errorCode: data.ErrorCode,
          errorMessage: data.ErrorMessage,
          twilioStatus: messageStatus,
        },
        'SMS delivery error reported by Twilio',
      );
    }

    res.status(200).json({ success: true });
  } catch (error) {
    logger.error({ error, body: req.body }, 'Error processing Twilio SMS status webhook');
    res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Map Twilio status to our notification status
 */
function mapTwilioStatus(twilioStatus: string): NotificationStatus {
  switch (twilioStatus.toLowerCase()) {
    case 'queued':
    case 'accepted':
      return 'pending';
    case 'sending':
      return 'pending';
    case 'sent':
      return 'sent';
    case 'delivered':
      return 'delivered';
    case 'failed':
    case 'undelivered':
      return 'failed';
    default:
      logger.warn({ twilioStatus }, `Unknown Twilio status: ${twilioStatus}`);
      return 'failed';
  }
}
