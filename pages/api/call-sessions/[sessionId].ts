import { NextApiRequest, NextApiResponse } from 'next';
import { callSessionsService } from '@/utils/firestore';

/**
 * Handler for GET /api/call-sessions/{sessionId}
 * Retrieves a call session by its sessionId
 * For internal UI use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Unauthorized - Missing token' });
    }

    // We're simply checking that a token is present - the actual validation would be
    // done by Firebase Auth on the client side before making this request
    const { sessionId } = req.query as { sessionId: string };

    // Fetch the call session data
    const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);
    if (!callSession) {
      return res.status(404).json({ message: `Call session with ID ${sessionId} not found` });
    }

    return res.status(200).json(callSession);
  } catch (error) {
    console.error('Error retrieving call session:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
