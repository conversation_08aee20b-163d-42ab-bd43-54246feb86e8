import { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, validate<PERSON><PERSON><PERSON><PERSON>, getProviderFromRequest } from '@/lib/external-api/v2';
import { appointmentAvailabilityQuerySchema } from '@/lib/external-api/v2/validators';
import { NextechAppointmentTypes } from '@/models/AppointmentTypes';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { CallType } from '@/models/CallTypes';
import { updateCallSessionType } from '@/lib/external-api/v2/utils/call-type-utils';

/**
 * @swagger
 * /api/external-api/v2/appointment-availability:
 *   get:
 *     summary: Get appointment availability
 *     description: Retrieve available slots for appointment booking based on criteria
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: appointmentTypeId
 *         required: false
 *         description: ID of the appointment type to check availability for
 *       - in: query
 *         name: practitionerId
 *         required: false
 *         description: Provider ID to check availability for
 *       - in: query
 *         name: locationId
 *         required: false
 *         description: Location ID to check availability for
 *       - in: query
 *         name: startDate
 *         required: false
 *         description: Start date for availability check (YYYY-MM-DD). Default - today
 *       - in: query
 *         name: endDate
 *         required: false
 *         description: End date for availability check (YYYY-MM-DD). Default - today
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of available slots
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       startDateTime:
 *                         type: string
 *                         format: date-time
 *                         description: Start time of the available slot in ISO format
 *                         example: "2025-04-10T07:15:00-04:00"
 *                       endDateTime:
 *                         type: string
 *                         format: date-time
 *                         description: End time of the available slot in ISO format
 *                         example: "2025-04-10T07:30:00-04:00"
 *                       practitionerId:
 *                         type: string
 *                         description: ID of the practitioner for this slot
 *                         example: "110"
 *                       practitionerName:
 *                         type: string
 *                         description: Name of the practitioner for this slot
 *                         example: "Meena George"
 *                       locationId:
 *                         type: string
 *                         description: ID of the location for this slot
 *                         example: "118"
 *                       locationName:
 *                         type: string
 *                         description: Name of the location for this slot
 *                         example: "URMA - Lombard"
 *                       appointmentTypeId:
 *                         type: string
 *                         description: ID of the appointment type for this slot
 *                         example: "18"
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalCount:
 *                       type: integer
 *                       description: Total number of items available
 *                       example: 25
 *                     limit:
 *                       type: integer
 *                       description: Number of items per page
 *                       example: 10
 *                     offset:
 *                       type: integer
 *                       description: Starting position for pagination
 *                       example: 0
 *                     hasMore:
 *                       type: boolean
 *                       description: Whether there are more items available
 *                       example: true
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/appointment-availability
 * Retrieves available appointment slots based on criteria
 */
async function getAppointmentAvailabilityHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    const defaultAppointmentTypeId = NextechAppointmentTypes.FOLLOW_UP;
    const defaultPractitionerId = '110'; // "Meena George"
    const defaultLocationId = URMA_LOMBARD_LOCATION_ID; // "URMA - Lombard"

    // Set default dates if not provided
    req.query.startDate = req.query.startDate || new Date().toISOString().split('T')[0];
    req.query.endDate = req.query.endDate || new Date().toISOString().split('T')[0];

    // Validate query parameters using Zod
    const validatedQuery = appointmentAvailabilityQuerySchema.parse(req.query);

    // Get available slots with the provided filters
    const availableSlots = await appointmentService.getAvailableSlots({
      appointmentTypeId: validatedQuery.appointmentTypeId || defaultAppointmentTypeId,
      startDate: validatedQuery.startDate,
      endDate: validatedQuery.endDate,
      practitionerId: validatedQuery.practitionerId || defaultPractitionerId,
      locationId: validatedQuery.locationId || defaultLocationId,
    });

    // Transform practitioner names to include "Doctor" prefix
    const transformedSlots = {
      ...availableSlots,
      items: availableSlots.items.map(slot => ({
        ...slot,
        practitionerName: `Doctor ${slot.practitionerName}`,
      })),
    };

    // Update call session with LOOKUP type if sessionId provided
    const { sessionId } = req.query;
    if (typeof sessionId === 'string' && sessionId.trim() !== '') {
      await updateCallSessionType(sessionId, CallType.LOOKUP, 'Appointment Availability Lookup');
    }

    // Return the transformed available slots
    return res.status(200).json(transformedSlots);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/appointment-availability
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // GET request for appointment availability
  if (req.method === 'GET') {
    await getAppointmentAvailabilityHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.status(405).json({ message: 'Method not allowed' });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
