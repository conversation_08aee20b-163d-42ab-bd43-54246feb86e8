import {
  addPaginationHeaders,
  BadRequestError,
  createApiHandler,
  getProviderFromRequest,
  NextApiRequestWithPagination,
  NotFoundError,
  Patient,
  requirePatientAccess,
  validate<PERSON>pi<PERSON><PERSON>,
  withPagination,
} from '@/lib/external-api/v2';
import { createPatientWithReference } from '@/lib/external-api/v2/utils/patient-utils';
import {
  createPatientSchema,
  getPatientQuerySchema,
  updatePatientSchema,
} from '@/lib/external-api/v2/validators';
import { NextApiResponse } from 'next';
import { CallType } from '@/models/CallTypes';
import {
  updateCallSessionType,
  updateCallSessionTypeForPatient,
} from '@/lib/external-api/v2/utils/call-type-utils';

/**
 * @swagger
 * /api/external-api/v2/patients:
 *   get:
 *     summary: Get patients
 *     description: Retrieve all patients or a specific patient by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: id
 *         required: false
 *         description: ID of the patient to retrieve
 *       - in: query
 *         name: fullName
 *         required: false
 *         description: Full name filter for patients
 *       - in: query
 *         name: dateOfBirth
 *         required: false
 *         description: Date of birth filter for patients (YYYY-MM-DD)
 *       - in: query
 *         name: email
 *         required: false
 *         description: Email filter for patients
 *       - in: query
 *         name: phoneNumber
 *         required: false
 *         description: Phone number filter for patients
 *       - in: query
 *         name: limit
 *         required: false
 *         description: "Maximum number of results to return (default: 10, max: 50)"
 *       - in: query
 *         name: offset
 *         required: false
 *         description: "Number of results to skip for pagination (default: 0)"
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Patient or list of patients
 *         headers:
 *           Link:
 *             description: Pagination links (first, prev, next, last)
 *             schema:
 *               type: string
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     dateOfBirth:
 *                       type: string
 *                       format: date
 *                     email:
 *                       type: string
 *                       format: email
 *                     phoneNumber:
 *                       type: string
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                           fullName:
 *                             type: string
 *                           dateOfBirth:
 *                             type: string
 *                             format: date
 *                           email:
 *                             type: string
 *                             format: email
 *                           phoneNumber:
 *                             type: string
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         totalCount:
 *                           type: number
 *                         limit:
 *                           type: number
 *                         offset:
 *                           type: number
 *                         hasMore:
 *                           type: boolean
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Patient not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *   post:
 *     summary: Create a new patient
 *     description: Create a new patient with the specified data
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *               - dateOfBirth
 *               - email
 *               - phoneNumber
 *               - address
 *             properties:
 *               firstName:
 *                 type: string
 *               lastName:
 *                 type: string
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: "Format: YYYY-MM-DD"
 *               gender:
 *                 type: string
 *                 enum: [male, female, other, unknown]
 *               email:
 *                 type: string
 *                 format: email
 *               phoneNumber:
 *                 type: string
 *               address:
 *                 type: object
 *                 required:
 *                   - line1
 *                   - city
 *                   - state
 *                   - postalCode
 *                 properties:
 *                   line1:
 *                     type: string
 *                   line2:
 *                     type: string
 *                   city:
 *                     type: string
 *                   state:
 *                     type: string
 *                   postalCode:
 *                     type: string
 *                   country:
 *                     type: string
 *                     default: "US"
 *               patientTypeId:
 *                 type: string
 *               referralSourceId:
 *                 type: string
 *               notes:
 *                 type: string
 *               insurances:
 *                 type: array
 *                 description: Insurance information to be created with the patient
 *                 items:
 *                   type: object
 *                   required:
 *                     - companyName
 *                     - memberId
 *                   properties:
 *                     companyName:
 *                       type: string
 *                       description: Name of the insurance company
 *                     memberId:
 *                       type: string
 *                       description: Member ID or policy number
 *                     groupNumber:
 *                       type: string
 *                       description: Group number for the insurance plan
 *                     planName:
 *                       type: string
 *                       description: Name of the insurance plan
 *                     isPrimary:
 *                       type: boolean
 *                       default: true
 *                       description: Whether this is the primary insurance
 *                     subscriberName:
 *                       type: string
 *                       description: Name of the subscriber
 *                     subscriberRelationship:
 *                       type: string
 *                       description: Relationship to the subscriber (self, spouse, child, etc.)
 *                     effectiveDate:
 *                       type: string
 *                       format: date
 *                       description: Start date of the insurance coverage
 *                     expirationDate:
 *                       type: string
 *                       format: date
 *                       description: End date of the insurance coverage
 *                     copay:
 *                       type: number
 *                       description: Copay amount
 *     responses:
 *       201:
 *         description: Patient created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *                 gender:
 *                   type: string
 *                 email:
 *                   type: string
 *                   format: email
 *                 phoneNumber:
 *                   type: string
 *                 address:
 *                   type: object
 *                   properties:
 *                     line1:
 *                       type: string
 *                     line2:
 *                       type: string
 *                     city:
 *                       type: string
 *                     state:
 *                       type: string
 *                     postalCode:
 *                       type: string
 *                     country:
 *                       type: string
 *                 providerInfo:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                     externalId:
 *                       type: string
 *                 insurances:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       companyName:
 *                         type: string
 *                       memberId:
 *                         type: string
 *                       groupNumber:
 *                         type: string
 *                       planName:
 *                         type: string
 *                       isPrimary:
 *                         type: boolean
 *                       subscriberName:
 *                         type: string
 *                       subscriberRelationship:
 *                         type: string
 *                       effectiveDate:
 *                         type: string
 *                       expirationDate:
 *                         type: string
 *                       copay:
 *                         type: number
 *                       providerInfo:
 *                         type: object
 *                         properties:
 *                           provider:
 *                             type: string
 *                           externalId:
 *                             type: string
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       409:
 *         description: Patient already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 details:
 *                   type: object
 *                   properties:
 *                     fields:
 *                       type: array
 *                       items:
 *                         type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *   put:
 *     summary: Update a patient
 *     description: Update specific fields of an existing patient
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: id
 *         required: true
 *         description: ID of the patient to update
 *         schema:
 *           type: string
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: Patient's first name
 *               lastName:
 *                 type: string
 *                 description: Patient's last name
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Patient's date of birth (YYYY-MM-DD)
 *               phoneNumber:
 *                 type: string
 *                 description: Patient's phone number
 *               notes:
 *                 type: string
 *                 description: Additional notes about the patient
 *     responses:
 *       200:
 *         description: Patient updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 firstName:
 *                   type: string
 *                 lastName:
 *                   type: string
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *                 phoneNumber:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 providerInfo:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                     externalId:
 *                       type: string
 *       400:
 *         description: Invalid request data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Patient not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
async function handler(req: NextApiRequestWithPagination, res: NextApiResponse) {
  try {
    // Check request method
    if (req.method === 'GET') {
      return await handleGetRequest(req, res);
    } else if (req.method === 'POST') {
      return await handlePostRequest(req, res);
    } else if (req.method === 'PUT') {
      return await handlePutRequest(req, res);
    } else {
      return res.status(405).json({ message: 'Method not allowed' });
    }
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Handle GET requests for patients
 */
async function handleGetRequest(req: NextApiRequestWithPagination, res: NextApiResponse) {
  // Update call session with LOOKUP type if sessionId provided
  const { sessionId } = req.query;
  if (typeof sessionId === 'string' && sessionId.trim() !== '') {
    await updateCallSessionType(sessionId, CallType.LOOKUP, 'Patient Lookup');
  } else if (typeof req.query.id === 'string' && req.query.id.trim() !== '') {
    await updateCallSessionTypeForPatient(req.query.id, CallType.LOOKUP, 'Patient Lookup by ID');
  }

  // Validate request parameters
  const validationResult = getPatientQuerySchema.safeParse(req.query);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid request parameters', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  // Get provider from request
  const provider = getProviderFromRequest(req);
  const patientService = provider.getPatientService();

  // Get a specific patient by ID
  if (req.query.id) {
    const patient = await patientService.getPatientById(req.query.id as string);
    if (!patient) {
      throw new NotFoundError('Patient not found');
    }

    // Sanitize sensitive information before returning
    // This is a simplified example - in a real implementation, you'd want to:
    // 1. Define what fields are considered PHI
    // 2. Implement proper data minimization based on the requester's permissions
    // 3. Apply redaction or transformation as needed
    const sanitizedPatient = sanitizePatientData(patient);

    return res.status(200).json(sanitizedPatient);
  }

  // Get all patients with optional filtering and pagination
  const paginatedPatients = await patientService.getPatients(req.query, req.pagination);

  // Sanitize sensitive information before returning
  const sanitizedPatients = paginatedPatients.items.map(sanitizePatientData);

  // Add pagination link headers to the response
  addPaginationHeaders(res, paginatedPatients.pagination.links);

  // Return paginated result
  return res.status(200).json({
    items: sanitizedPatients,
    pagination: paginatedPatients.pagination,
  });
}

/**
 * Handle POST requests for creating patients
 */
async function handlePostRequest(req: NextApiRequestWithPagination, res: NextApiResponse) {
  // Validate request body
  const validationResult = createPatientSchema.safeParse(req.body);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid patient data', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  // Get validated data
  const patientData = validationResult.data;

  // Get provider from request
  const provider = getProviderFromRequest(req);

  // Add patientId to insurance objects if present
  // We use a temporary ID that will be replaced by the real patient ID in createPatientWithReference
  if (patientData.insurances && patientData.insurances.length > 0) {
    patientData.insurances = patientData.insurances.map(insurance => ({
      ...insurance,
      patientId: 'TEMP_ID', // This will be replaced by the actual patient ID in the service
    }));
  }

  // Create patient using the shared utility function
  const patient = await createPatientWithReference(
    provider,
    patientData as Omit<Patient, 'id' | 'providerInfo'>,
  );

  // Update all call sessions that match the patient's phone number
  if (patientData.phoneNumber && patient.providerInfo.externalId) {
    try {
      // Import callSessionsService here to avoid circular dependencies
      const { callSessionsService } = await import('@/utils/firestore');

      // Find call sessions with matching phone number
      const callSessions = await callSessionsService.findCallSessionsByPhone(
        patientData.phoneNumber,
      );

      if (callSessions.length > 0) {
        console.log(
          `Found ${callSessions.length} call sessions with phone number ${patientData.phoneNumber}`,
        );

        // Update each call session with the patient ID
        for (const session of callSessions) {
          await callSessionsService.addOrUpdateCallSession(session.sessionId, {
            patientId: patient.providerInfo.externalId,
          });
          console.log(
            `Updated call session ${session.sessionId} with patient ID ${patient.providerInfo.externalId} (by phone match)`,
          );
        }
      }
    } catch (phoneSessionError) {
      console.error(
        `Failed to update call sessions by phone ${patientData.phoneNumber}:`,
        phoneSessionError,
      );
      // Continue even if session updates by phone fail
    }
  }

  // Sanitize and return created patient
  const sanitizedPatient = sanitizePatientData(patient);
  return res.status(201).json(sanitizedPatient);
}

/**
 * Handle PUT requests for updating patients
 */
async function handlePutRequest(req: NextApiRequestWithPagination, res: NextApiResponse) {
  // Get patient ID from URL
  const patientId = req.query.id as string;
  if (!patientId) {
    throw new BadRequestError('Patient ID is required');
  }

  // Validate request body
  const validationResult = updatePatientSchema.safeParse(req.body);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid patient data', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  // Get validated data
  const patientData = validationResult.data;

  // Get provider from request
  const provider = getProviderFromRequest(req);
  const patientService = provider.getPatientService();

  console.log(`Trying to update patient ${patientId} with data ${JSON.stringify(patientData)}`);
  const updatedPatient = await patientService.updatePatient(patientId, patientData);

  // Return updated patient
  return res.status(200).json(updatedPatient);
}

/**
 * Helper function to sanitize patient data before returning to the client
 * This helps with data minimization and PHI protection
 * @param patient The patient data to sanitize
 * @returns Sanitized patient data
 */
function sanitizePatientData(patient: Patient): Patient {
  // Clone the patient object to avoid modifying the original
  const sanitized = { ...patient };

  // Example of data minimization - in a real implementation, this would
  // be more sophisticated and based on the requester's permissions

  // Remove or redact sensitive fields if needed
  // In this example, we're keeping all fields but in a real implementation
  // you might redact or remove certain fields based on permissions

  return sanitized;
}

// Export with API key validation, patient access middleware, and pagination
export default createApiHandler(withPagination(handler), {
  middleware: [validateApiKey, requirePatientAccess],
});
