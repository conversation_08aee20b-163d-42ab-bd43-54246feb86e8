import { NextApiRequest, NextApiResponse } from 'next';
import { createApiHandler } from '../../../../lib/external-api/v2/utils/handler';

/**
 * Health check endpoint for the v2 API
 * This endpoint can be used by monitoring systems to check API health
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Basic health check response
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0',
    environment: process.env.NODE_ENV,
  });
}

// No authentication needed for health check endpoint
export default createApiHandler(handler);
