import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import logger from '@/lib/external-api/v2/utils/logger';
import {
  NOT_SHOW_REMINDER_SMS_DRU_RUN,
  NOT_SHOW_REMINDER_TIME_WINDOW_MINUTES,
  URMA_LOMBARD_LOCATION_ID,
} from '@/app-config';
import {
  type Appointment,
  type PaginatedResult,
  AppointmentStatus,
  createApiHand<PERSON>,
  ensureProvidersInitialized,
  getProviderFromRequest,
} from '@/lib/external-api/v2';
import { smsService } from '@/lib/services/sms-service';
import { locationsService } from '@/utils/firestore';
import { Scheduler } from '@/utils/scheduler';
import { AppLinkBuilder } from '@/utils/app-link.builder';

// Job's timeout can be up to 30 minutes according to the GCP documentation:
// https://cloud.google.com/config-connector/docs/reference/resource-docs/cloudscheduler/cloudschedulerjob#spec
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  logger.info(
    {
      context: `NotShowReminderJob.handler`,
    },
    'Starting not show reminder job',
  );
  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();
  const patientService = provider.getPatientService();
  const scheduler = Scheduler.getInstance();
  const linkBuilder = AppLinkBuilder.getInstance();

  const timeFrameWindow = NOT_SHOW_REMINDER_TIME_WINDOW_MINUTES;
  const locationId = URMA_LOMBARD_LOCATION_ID;
  const location = await locationsService.getLocationById(locationId);
  if (!location) {
    logger.error(
      {
        context: `NotShowReminderJob.handler`,
      },
      'Location not found',
    );
    return;
  }

  const now = dayjs.utc();
  const startDate = `${now.subtract(timeFrameWindow, 'minutes').format('YYYY-MM-DDTHH:mm:ss')}Z`;
  const endDate = `${now.format('YYYY-MM-DDTHH:mm:ss')}Z`;
  logger.info(
    {
      context: `NotShowReminderJob.handler`,
      provider: provider.name,
      startDate,
      endDate,
      timeFrameWindow,
      timeZone: location.timeZone,
      locationId,
    },
    'Not show reminder job parameters',
  );

  const scheduledCalls: Promise<unknown>[] = [];
  let totalSmsSent = 0;
  let paginationResult: PaginatedResult<Appointment> | null = null;
  do {
    paginationResult = await appointmentService.getAppointmentsByStatus(AppointmentStatus.NOSHOW, {
      startLastUpdated: startDate,
      endLastUpdated: endDate,
      locationId: locationId as string,
      pagination: {
        limit: 50,
        offset: totalSmsSent,
      },
    });

    const appointments = paginationResult.items || [];
    for (const appointment of appointments) {
      const appointmentId = appointment.providerInfo?.externalId;
      logger.info(
        {
          context: `NotShowReminderJob.handler`,
          appointmentId,
          provider: appointment.providerInfo?.provider,
          patientId: appointment.patientId,
        },
        'Start processing appointment',
      );
      const patient = await patientService.getPatientByIdWithoutInsurance(appointment.patientId);
      const phoneNumber = patient?.phoneNumber;
      if (!phoneNumber) {
        logger.error(
          {
            context: `NotShowReminderJob.handler`,
            appointmentId,
          },
          'Patient not found for appointment',
        );
        continue;
      }
      if (!smsService.isValidPhoneNumber(phoneNumber)) {
        logger.error(
          {
            context: `NotShowReminderJob.handler`,
            phoneNumber,
            appointmentId,
            patientId: appointment.patientId,
          },
          'Invalid phone number for appointment',
        );
        continue;
      }

      const patientName = patient?.firstName ?? '';
      const initialStartTime = appointment.startTime;
      // Remove the timezone from the start time 2025-05-20T14:30:00-04:00 => 2025-05-20T14:30:00
      const noTzTime = initialStartTime.replace(/-\d{2}:\d{2}$/, '');
      const appointmentTime = dayjs.tz(noTzTime, location?.timeZone).format('MM/DD/YYYY hh:mm a');

      logger.info(
        {
          context: `NotShowReminderJob.handler`,
          providerInfo: appointment.providerInfo,
          initialStartTime,
          noTzTime,
          appointmentTime,
        },
        'Preparing appointment data',
      );
      const smsSid = await smsService.sendNotShowReminder(
        phoneNumber,
        {
          patientName,
          dateTime: appointmentTime,
          location: appointment.locationName ?? '',
          locationPhone: location?.phone ?? '',
        },
        {
          dryRun: NOT_SHOW_REMINDER_SMS_DRU_RUN,
        },
      );
      // Schedule a call reminder 3 hours after the SMS was sent
      const callJobPromise = scheduler.createJob({
        name: `not-show-call-reminder-${appointmentId}`,
        executionTime: dayjs.utc().add(3, 'hours').toISOString(),
        httpTarget: {
          uri: linkBuilder.getNotShowCallReminderEndpoint(),
          jsonPayload: {
            externalAppointmentId: appointmentId,
            patientId: appointment.patientId,
            smsSid,
            phoneNumber,
          },
        },
      });
      scheduledCalls.push(callJobPromise);
    }

    totalSmsSent += paginationResult.items.length;
  } while (paginationResult?.pagination?.hasMore);
  if (scheduledCalls.length) {
    logger.info(
      {
        context: `NotShowReminderJob.handler`,
        scheduledCalls: scheduledCalls.length,
      },
      'Waiting for scheduled jobs to be created',
    );
    await Promise.all(scheduledCalls).catch(err => {
      logger.error(
        {
          context: `NotShowReminderJob.handler`,
          innerError: err,
        },
        'Error waiting for scheduled calls to complete',
      );
    });
  }

  logger.info(
    {
      context: `NotShowReminderJob.handler`,
      totalSmsSent,
    },
    'Not show reminder job completed',
  );

  res.status(200).json({
    message: 'OK',
    totalSmsSent,
  });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
