import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

import logger from '@/lib/external-api/v2/utils/logger';
import {
  createApiHandler,
  ensureProvidersInitialized,
  getProviderFromRequest,
} from '@/lib/external-api/v2';
import { callService } from '@/lib/services/call-service';
import { Scheduler } from '@/utils/scheduler';

const notShowCallReminderBodySchema = z.object({
  phoneNumber: z.string().nonempty('Phone number is required'),
  patientId: z.string().nonempty('Patient ID is required'),
  externalAppointmentId: z.string().nonempty('External appointment ID is required'),
  dryRun: z.boolean().optional(),
  redirectPhoneNumber: z.string().optional(),
});

const deleteJob = async (req: NextApiRequest) => {
  const scheduler = Scheduler.getInstance();
  const jobName = scheduler.getJobNameFromRequest(req);
  if (jobName) {
    await scheduler.deleteJob(jobName);
  } else {
    logger.warn(
      { context: `NotShowCallReminderJob.handler` },
      'Can not delete job, no job name found in request headers',
    );
  }
};

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  logger.info(
    {
      context: `NotShowCallReminderJob.handler`,
    },
    'Starting "Not show" call reminder job',
  );

  const validationResult = notShowCallReminderBodySchema.safeParse(req.body);
  if (!validationResult.success) {
    const errors = validationResult.error.flatten().fieldErrors;
    logger.error(
      {
        context: `NotShowCallReminderJob.handler`,
        errors,
      },
      'Invalid request body',
    );
    return res.status(400).json({ message: `Invalid request body`, errors });
  }

  const { phoneNumber, dryRun, redirectPhoneNumber, patientId, externalAppointmentId } =
    validationResult.data;

  try {
    const provider = getProviderFromRequest(req);
    const [patient, appointment] = await Promise.all([
      provider.getPatientService().getPatientByIdWithoutInsurance(patientId),
      provider.getAppointmentService().getAppointmentById(externalAppointmentId),
    ]);

    if (!appointment) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          externalAppointmentId,
        },
        'Appointment not found',
      );
      return res.status(400).json({ message: `Appointment not found` });
    }
    if (!patient) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          patientId,
        },
        'Patient not found',
      );
      return res.status(400).json({ message: `Patient not found` });
    }

    const location = await provider.getLocationService().getLocationById(appointment.locationId);
    if (!location) {
      logger.error(
        {
          context: `NotShowCallReminderJob.handler`,
          locationId: appointment!.locationId,
        },
        'Location not found',
      );
      return res.status(400).json({ message: `Location not found` });
    }

    // Remove the timezone from the start time 2025-05-20T14:30:00-04:00 => 2025-05-20T14:30:00
    const noTzTime = appointment.startTime.replace(/-\d{2}:\d{2}$/, '');
    const appointmentDay = dayjs(noTzTime).format('MMMM DD');
    const appointmentTime = dayjs(noTzTime).format('hh:mm a');
    const [insurance] = patient.insurances ?? [];

    const callSid = await callService
      .makeCall({
        event: 'no-show',
        toPhoneNumber: phoneNumber,
        dryRun,
        redirectPhoneNumber,
        callContext: {
          patient_id: patientId,
          patient_first_name: patient.firstName,
          patient_last_name: patient.lastName,
          patient_birthday: patient.dateOfBirth,
          patient_phone_number: phoneNumber,
          patient_address_line_1: patient.address?.line1,
          patient_address_city: patient.address?.city,
          patient_address_state: patient.address?.state,
          patient_address_postal_code: patient.address?.postalCode,
          patient_address_country: patient.address?.country,
          insurance_company_name: insurance?.companyName,
          insurance_member_id: insurance?.memberId,
          insurance_group_number: insurance?.groupNumber,
          no_show_location_id: appointment.locationId,
          no_show_location_name: location.name,
          no_show_appointment_id: externalAppointmentId,
          no_show_appointment_time: appointmentTime,
          no_show_appointment_day: appointmentDay,
          is_new_patient: 'false',
          is_no_show_event: 'true',
        },
      })
      .finally(() => deleteJob(req));

    logger.info(
      {
        context: `NotShowCallReminderJob.handler`,
        callSid,
      },
      '"Not show" call reminder job completed',
    );

    res.status(200).json({ message: 'OK', callSid });
  } catch (error) {
    logger.error(
      {
        context: `NotShowCallReminderJob.handler`,
        innerError: error,
      },
      'Error making a call',
    );
    return res.status(500).json({ message: 'Internal server error' });
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
