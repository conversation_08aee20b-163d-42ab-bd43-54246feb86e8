import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callService } from '@/lib/services/call-service';
import { Scheduler } from '@/utils/scheduler';
import { RepositoryManager } from '@/lib/repositories';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { URGENT_AFTER_HOURS_DOCTOR_CALL_DELAY_MINUTES } from '@/app-config';
import { User } from '@/models/auth';

const DEFAULT_LOCATION_TIMEZONE = 'America/Chicago';

const urgentAfterHoursDoctorCallReminderBodySchema = z.object({
  afterHoursCallId: z.string(),
  isPrimaryDoctor: z.boolean(),
  dryRun: z.boolean().optional(),
});

/**
 * Schedule a job to make an urgent after hours doctor call reminder
 * @param params.afterHoursCallId - The ID of the after hours call
 * @param params.isPrimaryDoctor - Whether the doctor is the primary doctor
 * @param params.executeInNextMinutes - The number of minutes to execute the job in (default: 30)
 * @returns The name of the scheduled job
 */
export const scheduleUrgentAfterHoursDoctorCallReminder = async ({
  afterHoursCallId,
  isPrimaryDoctor,
  executeInNextMinutes = URGENT_AFTER_HOURS_DOCTOR_CALL_DELAY_MINUTES,
}: {
  afterHoursCallId: string;
  isPrimaryDoctor: boolean;
  executeInNextMinutes?: number;
}): Promise<{ jobName: string }> => {
  const linkBuilder = AppLinkBuilder.getInstance();
  const scheduler = Scheduler.getInstance();
  const jobName = `urgent-after-hours-doctor-call-reminder-${afterHoursCallId}-${isPrimaryDoctor ? 'primary' : 'backup'}`;
  const executionTime = dayjs.utc().add(executeInNextMinutes, 'minutes').toISOString();
  logger.info(
    {
      context: `UrgentAfterHoursDoctorCallReminderJob.scheduleUrgentAfterHoursDoctorCallReminder`,
      jobName,
      executionTime,
      afterHoursCallId,
      isPrimaryDoctor,
    },
    'Scheduling urgent after hours doctor call reminder',
  );
  await scheduler.createJob({
    name: jobName,
    executionTime,
    httpTarget: {
      uri: linkBuilder.getUrgentAfterHoursDoctorCallReminderEndpoint(),
      jsonPayload: {
        afterHoursCallId,
        isPrimaryDoctor,
      },
    },
  });
  logger.info(
    {
      context: `UrgentAfterHoursDoctorCallReminderJob.scheduleUrgentAfterHoursDoctorCallReminder`,
      jobName,
      executionTime,
      afterHoursCallId,
      isPrimaryDoctor,
    },
    'Urgent after hours doctor call reminder scheduled',
  );

  return { jobName };
};

const deleteJob = async (req: NextApiRequest): Promise<void> => {
  const scheduler = Scheduler.getInstance();
  const jobName = scheduler.getJobNameFromRequest(req);
  if (jobName) {
    await scheduler.deleteJob(jobName);
  } else {
    logger.warn(
      { context: `UrgentAfterHoursDoctorCallReminderJob.handler` },
      'Can not delete job, no job name found in request headers',
    );
  }
};

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  logger.info(
    {
      context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
    },
    'Starting "Urgent after hours doctor" call reminder job',
  );

  try {
    const validationResult = urgentAfterHoursDoctorCallReminderBodySchema.safeParse(req.body);
    if (!validationResult.success) {
      const errors = validationResult.error.flatten().fieldErrors;
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          errors,
        },
        'Invalid request body',
      );
      return res.status(400).json({ message: `Invalid request body`, errors });
    }

    const { afterHoursCallId, isPrimaryDoctor } = validationResult.data;

    const repositoryManager = RepositoryManager.getInstance();
    await repositoryManager.initialize();
    const afterHoursCall = await repositoryManager.afterHoursCalls.findById(afterHoursCallId);
    if (!afterHoursCall) {
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
        },
        'After hours call not found',
      );
      return res.status(404).json({ message: 'After hours call not found' });
    }
    if (afterHoursCall.isReviewedByDoctor) {
      logger.info(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
          doctorId: afterHoursCall.doctorId,
        },
        'After hours call is already reviewed by doctor',
      );
      return res.status(200).json({ message: 'After hours call is already reviewed by doctor' });
    }
    if (!afterHoursCall.doctorId && !afterHoursCall.backupDoctorId) {
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
        },
        'Neither primary nor backup doctor ID found',
      );
      return res.status(400).json({ message: 'Neither primary nor backup doctor ID found' });
    }
    if (!afterHoursCall.patientPhoneNumber) {
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
        },
        'Patient phone number not found',
      );
      return res.status(400).json({ message: 'Patient phone number not found' });
    }

    let doctor: User | null = null;
    let doctorPhone: string | undefined = undefined;
    if (isPrimaryDoctor && afterHoursCall.doctorId) {
      doctor = await repositoryManager.users.findById(afterHoursCall.doctorId);
      doctorPhone = afterHoursCall.doctorPhone ?? doctor?.phone;
    } else if (!isPrimaryDoctor && afterHoursCall.backupDoctorId) {
      doctor = await repositoryManager.users.findById(afterHoursCall.backupDoctorId);
      doctorPhone = afterHoursCall.backupDoctorPhone ?? doctor?.phone;
    }
    if (!doctor) {
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
        },
        'Doctor not found',
      );
      return res.status(400).json({ message: 'Doctor not found' });
    }
    if (!doctorPhone) {
      logger.error(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
          doctorId: isPrimaryDoctor ? afterHoursCall.doctorId : afterHoursCall.backupDoctorId,
        },
        'Doctor phone number not found',
      );
      return res.status(400).json({ message: 'Doctor phone number not found' });
    }

    if (doctor.preferences?.isUrgentAfterHoursConsultationEnabled === false) {
      logger.info(
        {
          context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
          afterHoursCallId,
          doctorId: isPrimaryDoctor ? afterHoursCall.doctorId : afterHoursCall.backupDoctorId,
        },
        'Urgent after hours consultation is not enabled for this doctor',
      );
      return res
        .status(200)
        .json({ message: 'Doctor has disabled urgent after hours consultation' });
    }

    const callTimeFormatted = dayjs(afterHoursCall.createdAt)
      .tz(DEFAULT_LOCATION_TIMEZONE)
      .format('hh:mm A');

    const greetingMessage = `Hello Dr. ${doctor.name}, patient ${afterHoursCall.patientFullName} called at ${callTimeFormatted} with regards to ${afterHoursCall.callReason}. Please press 1 to be connected to the patient via auto transfer. Thank you.`;
    const callSid = await callService.createUrgencyDoctorCallReminder({
      doctorNumber: doctorPhone,
      patientNumber: afterHoursCall.patientPhoneNumber,
      greetingMessage,
    });
    logger.info(
      {
        context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
        callSid,
        afterHoursCallId,
      },
      'Call made successfully',
    );

    res.status(200).json({ message: 'OK', callSid });
  } catch (error) {
    logger.error(
      {
        context: `UrgentAfterHoursDoctorCallReminderJob.handler`,
        innerError: error,
      },
      'Error making a call',
    );
    return res.status(500).json({ message: 'Internal server error' });
  } finally {
    await deleteJob(req);
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
