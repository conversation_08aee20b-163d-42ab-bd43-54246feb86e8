import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
} from '../../../../../lib/external-api/v2';

/**
 * @swagger
 * /api/external-api/v2/appointment-purposes:
 *   get:
 *     summary: Get appointment purposes
 *     description: Retrieve all appointment purposes with optional filtering
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: name
 *         required: false
 *         description: Filter by name
 *       - in: query
 *         name: isActive
 *         required: false
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: List of appointment purposes
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: Unique identifier for the appointment purpose
 *                   name:
 *                     type: string
 *                     description: Name of the appointment purpose
 *                   description:
 *                     type: string
 *                     description: Description of the appointment purpose
 *                   isActive:
 *                     type: boolean
 *                     description: Whether the appointment purpose is active
 *                   providerInfo:
 *                     type: object
 *                     properties:
 *                       provider:
 *                         type: string
 *                       externalId:
 *                         type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/appointment-purposes
 * Retrieves all appointment purposes with optional filtering
 */
async function appointmentPurposesHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Get appointment purposes with the provided filters
    const appointmentPurposes = await appointmentService.getAppointmentPurposes(req.query);

    // Return the appointment purposes
    return res.status(200).json(appointmentPurposes);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

export default createApiHandler(appointmentPurposesHandler, {
  middleware: [validateApiKey],
});
