import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  requirePatientAccess,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  ConflictError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import {
  createPatientWithReference,
  findPatientByCriteria,
} from '@/lib/external-api/v2/utils/patient-utils';
import { storeAppointmentReference } from '@/lib/external-api/v2/utils/appointment-utils';
import { CallType } from '@/models/CallTypes';
import { callSessionsService } from '@/utils/firestore';
import { eventEmitter, EVENTS } from '@/lib/external-api/v2/utils/events';
import { patientFactory } from '@/lib/factories/patient-factory';
import { AppointmentStatus, Patient } from '@/lib/external-api/v2/models/types';
import { NextechAppointmentTypes } from '@/models/AppointmentTypes';
import logger from '@/lib/external-api/v2/utils/logger';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';

dayjs.extend(utc);
dayjs.extend(timezone);

export const bookingSchema = z.object({
  dryRun: z.boolean().optional().default(true),
  isAppointmentNotificationEnabled: z.boolean().optional().default(false),
  callId: z.string(),
  appointment: z.object({
    startTime: z.string().min(1, 'Start time is required'),
  }),
  patient: z.object({
    id: z.string().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    dateOfBirth: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, {
        message: 'Date of birth must be in YYYY-MM-DD format',
      })
      .optional(),
    phoneNumber: z.string().min(10, 'Phone number is required').optional(),
    address: z
      .object({
        line1: z.string().min(1, 'Address line 1 is required'),
        line2: z.string().optional(),
        city: z.string().min(1, 'City is required'),
        state: z.string().min(1, 'State is required'),
        postalCode: z.string().min(1, 'Zip code is required'),
        country: z.string().default('US'),
      })
      .optional(),
  }),
});

async function bookingHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // 1. Validate request body
    const validationResult = bookingSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const {
      dryRun,
      patient: patientData,
      appointment: appointmentData,
      callId,
    } = validationResult.data;

    // Validate appointment start time is not in the past
    const appointmentStartTime = new Date(appointmentData.startTime);
    const now = new Date();
    if (appointmentStartTime < now) {
      throw new BadRequestError('Appointment start time cannot be in the past', {
        code: 'INVALID_APPOINTMENT_TIME',
        message: 'The appointment start time must be in the future',
        suggestion: 'Please select a future date and time for the appointment',
      });
    }

    // 2. Get provider from request
    const provider = getProviderFromRequest(req);
    const patientService = provider.getPatientService();
    const appointmentService = provider.getAppointmentService();

    // 3. Check if patient exists (if ID is provided) or create a new one
    let patient;
    let patientCreated = false;

    // Variables to track session update status
    let sessionUpdateFailed = false;
    let sessionUpdateError = null;

    try {
      if (patientData.id) {
        // Get patient by ID
        patient = await patientService.getPatientById(patientData.id);
        if (!patient) {
          throw new NotFoundError(`Patient with ID ${patientData.id} not found`);
        }
      } else {
        // Try to find existing patient first
        const searchCriteria = {
          firstName: patientData.firstName,
          lastName: patientData.lastName,
          dateOfBirth: patientData.dateOfBirth,
          phoneNumber: patientData.phoneNumber,
        };

        patient = await findPatientByCriteria(provider, searchCriteria);

        if (!patient) {
          // Create new patient if not found
          const patientToCreate = {
            ...patientData,
            firstName: patientData.firstName || '',
            lastName: patientData.lastName || '',
            dateOfBirth: patientData.dateOfBirth || '',
            phoneNumber: patientData.phoneNumber || '',
            address: patientData.address || {
              line1: '',
              line2: '',
              city: '',
              state: '',
              postalCode: '',
              country: 'US',
            },
          };
          logger.info({ context: 'appointment-booking', patientToCreate }, `Creating new patient`);
          patient = await createPatientWithReference(provider, patientToCreate, true);
          logger.info(
            { context: 'appointment-booking', patientId: patient.providerInfo.externalId },
            `Created new patient`,
          );
          patientCreated = true;
        } else {
          logger.info(
            { context: 'appointment-booking' },
            `Found existing patient with ID ${patient.id}`,
          );
        }
      }

      // Cache the patient
      if (patient) {
        await cachePatient(patient, provider.name);
      }
    } catch (patientError) {
      // Enhance error with more details for the UI
      if (patientError instanceof ConflictError) {
        throw new ConflictError('Patient already exists', {
          code: 'PATIENT_ALREADY_EXISTS',
          message: 'A patient with this information already exists',
          suggestion: 'Try searching for the patient first or use a different email/phone',
          originalError: patientError,
        });
      } else if (patientError instanceof NotFoundError) {
        throw new NotFoundError(patientError.message, {
          code: 'PATIENT_NOT_FOUND',
          suggestion: 'Verify the patient ID or create a new patient',
        });
      } else {
        // For other errors, wrap with additional context
        throw new BadRequestError('Failed to process patient data', {
          code: 'PATIENT_PROCESSING_ERROR',
          originalError: patientError,
          suggestion: 'Check the patient information and try again',
        });
      }
    }

    // 4. Update the current call session with the patient ID if sessionId is provided
    if (callId && patient.providerInfo.externalId) {
      try {
        await callSessionsService.addOrUpdateCallSession(callId, {
          patientId: patient.providerInfo.externalId,
        });
        logger.info(
          { context: 'appointment-booking' },
          `Updated call session ${callId} with patient ID ${patient.providerInfo.externalId}`,
        );
      } catch (sessionError) {
        logger.error(
          { context: 'appointment-booking' },
          `Failed to update call session ${callId}:`,
          sessionError,
        );
        // Set flag for session update failure
        sessionUpdateFailed = true;
        sessionUpdateError = sessionError;
        // Continue even if session update fails
      }
    }

    // 5. Create appointment
    let appointment;
    try {
      const appointmentToCreate = {
        type: patientCreated
          ? NextechAppointmentTypes.NEW_PATIENT
          : NextechAppointmentTypes.COMPLETE_EXAM,
        patientId: patient.providerInfo.externalId,
        endTime: dayjs
          .utc(appointmentData.startTime)
          .add(15, 'minutes')
          .format('YYYY-MM-DDTHH:mm:ss'),
        ...appointmentData,
      };

      logger.info({ context: 'appointment-booking', appointmentToCreate }, `Creating appointment`);
      appointment = !dryRun
        ? await appointmentService.createAppointment(appointmentToCreate)
        : {
            id: uuidv4(),
            status: AppointmentStatus.BOOKED,
            providerId: patient.providerInfo.provider,
            providerInfo: patient.providerInfo,
            createdAt: dayjs.utc().toISOString(),
            updatedAt: dayjs.utc().toISOString(),
            locationId: URMA_LOMBARD_LOCATION_ID,
            clinicId: '12',
            ...appointmentToCreate,
          };

      logger.info(
        { context: 'appointment-booking', appointmentId: appointment.providerInfo.externalId },
        `Created appointment`,
      );

      // Store appointment reference in Firestore
      // Add patient ID to the appointment object for the reference
      const appointmentWithPatientId = {
        ...appointment,
        id: appointment.providerInfo.externalId,
        patientId: patient.providerInfo.externalId,
      };
      await storeAppointmentReference(provider, appointmentWithPatientId);

      // Update call session with appointmentId if sessionId exists in callData
      if (callId) {
        try {
          await callSessionsService.addOrUpdateCallSession(callId, {
            appointmentId: appointment.providerInfo.externalId,
            patientId: patient.providerInfo.externalId,
            callType: patientCreated
              ? CallType.NEW_PATIENT_NEW_APPOINTMENT
              : CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
            sendNewPatientForm: patientCreated,
          });
          logger.info(
            { context: 'appointment-booking' },
            `Updated call session ${callId} with appointment ID ${appointment.providerInfo.externalId}`,
          );

          // Emit the appointment:created event with the appointment as payload
          // This will trigger the email notifications
          eventEmitter.emit(EVENTS.APPOINTMENT.CREATED, {
            sessionId: callId,
            appointment,
          });
        } catch (sessionError) {
          logger.error(
            { context: 'appointment-booking' },
            `Failed to update call session ${callId}:`,
            sessionError,
          );
          // Set flag for session update failure
          sessionUpdateFailed = true;
          sessionUpdateError = sessionError;
          // Continue even if session update fails
        }
      }

      // 6. Send successful response
      // Note: Call creation has been moved to a separate endpoint

      // Check if there were any session update failures
      if (callId && sessionUpdateFailed) {
        // Return a partial success response with a warning
        res.status(200).json({
          success: true,
          warning:
            'Call session could not be updated, but patient and appointment were created successfully',
          warningCode: 'CALL_SESSION_UPDATE_FAILED',
          data: {
            patient,
            appointment,
            call: null, // Call creation has been moved to a separate endpoint
            sessionUpdateError,
          },
        });
      } else {
        // Return a standard success response
        res.status(200).json({
          success: true,
          message: 'Conversation finalized successfully',
          data: {
            patient,
            appointment,
            call: null, // Call creation has been moved to a separate endpoint
          },
        });
      }
    } catch (appointmentError) {
      // Handle appointment creation errors with detailed information
      // This is especially important for race conditions like double-booking
      const error = appointmentError as Error;
      if (error.message && error.message.includes('409')) {
        throw new ConflictError('Appointment slot is no longer available', {
          code: 'APPOINTMENT_SLOT_UNAVAILABLE',
          message: 'The requested appointment time is no longer available',
          suggestion: 'Please select a different time slot',
          patientId: patient.providerInfo.externalId, // Include the patient ID so the UI can use it for retrying
          patientCreated: patientCreated, // Let the UI know if we created a new patient
          originalError: appointmentError,
        });
      } else if (error.message && error.message.includes('400')) {
        throw new BadRequestError('Invalid appointment data', {
          code: 'INVALID_APPOINTMENT_DATA',
          message: 'The appointment data is invalid',
          suggestion: 'Check the appointment details and try again',
          patientId: patient.providerInfo.externalId,
          patientCreated: patientCreated,
          originalError: appointmentError,
        });
      } else {
        // For other errors, wrap with additional context
        throw new BadRequestError('Failed to create appointment', {
          code: 'APPOINTMENT_CREATION_ERROR',
          patientId: patient.providerInfo.externalId,
          patientCreated: patientCreated,
          originalError: appointmentError,
          suggestion: 'Verify the appointment details and try again',
        });
      }
    }
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/finalize-conversation
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method === 'POST') {
    await bookingHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

/**
 * Caches a patient in the local system
 * @param patient The patient data to cache
 * @param providerName The name of the provider
 * @returns Promise that resolves when caching is complete
 */
async function cachePatient(patient: Patient, providerName: string): Promise<void> {
  logger.info(
    { context: 'appointment-booking' },
    `Caching patient ${patient.id} for provider ${providerName}`,
  );
  const patientCoordinatorService = patientFactory.getPatientCoordinatorService();
  try {
    await patientCoordinatorService.storePatient({
      ...patient,
      providerInfo: {
        provider: providerName,
        externalId: patient.providerInfo.externalId,
      },
    });
  } catch (error) {
    logger.error({ context: 'appointment-booking' }, 'Error caching patient:', error);
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requirePatientAccess, ensureProvidersInitialized],
});
