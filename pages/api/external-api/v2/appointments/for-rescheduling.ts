import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { getAppointmentsForPatientQuerySchema } from '@/lib/external-api/v2/validators';
import {
  updateCallSessionType,
  updateCallSessionTypeForPatient,
} from '@/lib/external-api/v2/utils/call-type-utils';
import { CallType } from '@/models/CallTypes';

/**
 * @swagger
 * /api/external-api/v2/appointments/for-rescheduling:
 *   get:
 *     summary: Get appointments for a patient for rescheduling
 *     description: |
 *       Retrieves appointments for a specific patient with rescheduling logic.
 *       Returns all future non-cancelled appointments, or if none exist and isForRescheduling=true,
 *       returns the most recent no-show appointment from the last 10 days (if any).
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: patientId
 *         required: true
 *         description: ID of the patient
 *         schema:
 *           type: string
 *       - in: query
 *         name: isForRescheduling
 *         required: false
 *         description: Whether to include no-show appointments for rescheduling (true/false or 1/0)
 *         schema:
 *           type: boolean
 *           default: false
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *         schema:
 *           type: string
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Successful response with appointments
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Appointment'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalCount:
 *                       type: integer
 *                     hasMore:
 *                       type: boolean
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * Handler for GET /api/external-api/v2/appointments/for-rescheduling
 * Retrieves appointments for a specific patient with rescheduling logic
 */
async function getAppointmentsForReschedulingHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Update call session with LOOKUP type if sessionId provided
    const { sessionId } = req.query;
    if (typeof sessionId === 'string' && sessionId.trim() !== '') {
      await updateCallSessionType(
        sessionId,
        CallType.LOOKUP,
        'Appointments for Rescheduling Lookup',
      );
    }

    // Validate request parameters
    const validationResult = getAppointmentsForPatientQuerySchema.safeParse(req.query);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request parameters', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // If we did not have sessionId but have patientId, update sessions by patientId
    if (!sessionId && validationResult.data.patientId) {
      await updateCallSessionTypeForPatient(
        validationResult.data.patientId,
        CallType.LOOKUP,
        'Appointments for Rescheduling Lookup by Patient',
      );
    }

    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Extract validated parameters
    const { patientId, isForRescheduling } = validationResult.data;

    // Get appointments for the patient
    const appointments = await appointmentService.getAppointmentsForRescheduling(
      patientId,
      isForRescheduling,
    );

    // Transform practitioner names to include "Doctor" prefix
    const transformedAppointments = {
      ...appointments,
      items: appointments.items.map(appointment => ({
        ...appointment,
        practitionerName: appointment.practitionerName
          ? `Doctor ${appointment.practitionerName}`
          : undefined,
      })),
    };

    // Return the transformed appointments
    res.status(200).json(transformedAppointments);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/appointments/for-rescheduling
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // GET request for appointments
  if (req.method === 'GET') {
    await getAppointmentsForReschedulingHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['GET']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
