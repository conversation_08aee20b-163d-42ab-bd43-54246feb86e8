import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
} from '@/lib/external-api/v2';
import {
  updateAppointmentSchema,
  cancelAppointmentQuerySchema,
} from '@/lib/external-api/v2/validators';
import { callSessionsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';
import {
  updateCallSessionType,
  updateCallSessionTypeForAppointment,
} from '@/lib/external-api/v2/utils/call-type-utils';
import logger from '../../../../../lib/external-api/v2/utils/logger';

/**
 * @swagger
 * /api/external-api/v2/appointments/{id}:
 *   parameters:
 *     - in: path
 *       name: id
 *       required: true
 *       description: ID of the appointment
 *       schema:
 *         type: string
 *   patch:
 *     summary: Update appointment
 *     description: Update an existing appointment by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               patientId:
 *                 type: string
 *               providerId:
 *                 type: string
 *               locationId:
 *                 type: string
 *               clinicId:
 *                 type: string
 *               startTime:
 *                 type: string
 *                 format: date-time
 *                 description: ISO format date-time (YYYY-MM-DDTHH:MM:SS)
 *               endTime:
 *                 type: string
 *                 format: date-time
 *                 description: ISO format date-time (YYYY-MM-DDTHH:MM:SS)
 *               status:
 *                 type: string
 *                 enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *               type:
 *                 type: string
 *                 description: Appointment type
 *               reason:
 *                 type: string
 *                 description: Reason for the appointment
 *               notes:
 *                 type: string
 *                 description: Additional notes
 *               sessionId:
 *                 type: string
 *                 description: Current session ID for updating call session information
 *     responses:
 *       200:
 *         description: Updated appointment
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 patientId:
 *                   type: string
 *                 providerId:
 *                   type: string
 *                 locationId:
 *                   type: string
 *                 clinicId:
 *                   type: string
 *                 startTime:
 *                   type: string
 *                   format: date-time
 *                 endTime:
 *                   type: string
 *                   format: date-time
 *                 status:
 *                   type: string
 *                   enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *                 type:
 *                   type: string
 *                 reason:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                 providerInfo:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                     externalId:
 *                       type: string
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Appointment not found
 *       409:
 *         description: Conflict - appointment slot not available
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Cancel appointment
 *     description: Cancel an existing appointment by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: reason
 *         required: false
 *         description: Reason for cancellation
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *     responses:
 *       204:
 *         description: Appointment cancelled
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Appointment not found
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/appointments/[id]
 * Retrieves a specific appointment by ID
 */
async function getAppointmentByIdHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const appointmentId = req.query.id as string;
  if (!appointmentId || appointmentId.trim() === '') {
    throw new BadRequestError('Invalid or missing appointment ID');
  }
  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();
  const appointment = await appointmentService.getAppointmentById(appointmentId);
  if (!appointment) {
    throw new NotFoundError('Appointment not found');
  }

  // Update call session with LOOKUP type if sessionId is provided
  const { sessionId } = req.query;
  if (typeof sessionId === 'string' && sessionId.trim() !== '') {
    await updateCallSessionType(sessionId, CallType.LOOKUP, 'Appointment Lookup');
  }
  res.status(200).json(appointment);
}

/**
 * Handler for PATCH /api/external-api/v2/appointments/[id]
 * Updates an existing appointment
 */
async function updateAppointmentHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const appointmentId = req.query.id as string;
  if (!appointmentId || appointmentId.trim() === '') {
    throw new BadRequestError('Invalid or missing appointment ID');
  }

  const validationResult = updateAppointmentSchema.safeParse(req.body);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid request body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();

  const existingAppointment = await appointmentService.getAppointmentById(appointmentId);
  if (!existingAppointment) {
    throw new NotFoundError('Appointment not found');
  }

  const updatedAppointment = await appointmentService.updateAppointment(
    appointmentId,
    validationResult.data,
  );

  // Update call type for appointment updates/rescheduling
  try {
    // Get the current session ID from the request if available
    const currentSessionId = req.body.sessionId || req.query.sessionId;

    if (currentSessionId) {
      // Update the current session with RESCHEDULE type
      await updateCallSessionType(
        currentSessionId as string,
        CallType.RESCHEDULE,
        'Appointment Update',
      );

      logger.info(
        {
          sessionId: currentSessionId,
          appointmentId,
          callType: CallType.RESCHEDULE,
        },
        `Updated current call session ${currentSessionId} with RESCHEDULE type for appointment update`,
      );
    } else if (existingAppointment.patientId) {
      // No sessionId provided, try to find sessions by patient ID
      const snapshot = await callSessionsService.findCallSessionsByPhone('');
      for (const session of snapshot) {
        if (session.patientId === existingAppointment.patientId) {
          await callSessionsService.addOrUpdateCallSession(session.sessionId, {
            appointmentId: appointmentId,
          });
          logger.info(
            { sessionId: session.sessionId, appointmentId },
            `Updated call session ${session.sessionId} with appointment ID ${appointmentId}`,
          );
        }
      }
    }
  } catch (sessionError) {
    logger.error(
      { error: sessionError, appointmentId },
      `Failed to update call sessions for appointment ${appointmentId}`,
    );
    // Continue even if session update fails
  }

  res.status(200).json(updatedAppointment);
}

/**
 * Handler for DELETE /api/external-api/v2/appointments/[id]
 * Cancels an existing appointment
 */
async function cancelAppointmentHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const appointmentId = req.query.id as string;
  if (!appointmentId || appointmentId.trim() === '') {
    throw new BadRequestError('Invalid or missing appointment ID');
  }

  const validationResult = cancelAppointmentQuerySchema.safeParse(req.query);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid query parameters', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }
  const { reason } = validationResult.data;

  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();

  const existingAppointment = await appointmentService.getAppointmentById(appointmentId);
  if (!existingAppointment) {
    throw new NotFoundError('Appointment not found');
  }

  await appointmentService.cancelAppointment(appointmentId, reason);

  // Update call type for current session and any sessions associated with this appointment
  try {
    // Get the current session ID from the request if available (prioritize query params for DELETE)
    const currentSessionId = req.query.sessionId || req.body.sessionId;

    if (currentSessionId) {
      // Use the proper utility function to update call session type
      await updateCallSessionType(
        currentSessionId as string,
        CallType.CANCELLATION,
        'Appointment Cancellation',
      );

      // Also remove the appointment ID since it's cancelled
      await callSessionsService.addOrUpdateCallSession(currentSessionId as string, {
        appointmentId: undefined, // Remove the appointment ID since it's cancelled
      });

      logger.info(
        {
          sessionId: currentSessionId,
          appointmentId,
          callType: CallType.CANCELLATION,
        },
        `Updated current call session ${currentSessionId} with call type CANCELLATION and removed appointment ID`,
      );
    } else {
      // Fallback: find sessions by appointment ID and update them
      logger.info(
        { appointmentId },
        `No current session ID provided for cancelled appointment ${appointmentId}, searching by appointment ID`,
      );

      await updateCallSessionTypeForAppointment(
        appointmentId,
        CallType.CANCELLATION,
        'Appointment Cancellation (found by appointment ID)',
      );
    }
  } catch (sessionError) {
    logger.error(
      { error: sessionError, appointmentId },
      `Failed to update call session for cancelled appointment ${appointmentId}`,
    );
    // Continue even if session update fails
  }

  res.status(204).end();
}

// --- Main Handler for Routing ---

// Export this function so it can be imported directly in tests
export async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  if (req.method === 'GET') {
    await getAppointmentByIdHandler(req, res);
  } else if (req.method === 'PATCH') {
    await updateAppointmentHandler(req, res);
  } else if (req.method === 'DELETE') {
    await cancelAppointmentHandler(req, res);
  } else {
    res.setHeader('Allow', ['GET', 'PATCH', 'DELETE']);
    res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }
}

// Export the single main handler wrapped by createApiHandler with middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
