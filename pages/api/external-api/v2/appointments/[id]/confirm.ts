import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProvider<PERSON>romRequest,
  BadRequestError,
  NotFoundError,
  ensureProvidersInitialized,
  AppointmentStatus,
} from '@/lib/external-api/v2';
import { confirmAppointmentQuerySchema } from '@/lib/external-api/v2/validators';
import { CallType } from '@/models/CallTypes';
import { updateCallSessionType } from '@/lib/external-api/v2/utils/call-type-utils';
import logger from '../../../../../../lib/external-api/v2/utils/logger';

/**
 * Main handler for /api/external-api/v2/appointments/[id]/confirm
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    return;
  }

  await confirmAppointmentHandler(req, res);
}

/**
 * Handler for POST /api/external-api/v2/appointments/[id]/confirm
 * Confirms an existing appointment
 */
async function confirmAppointmentHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const appointmentId = req.query.id as string;
  if (!appointmentId || appointmentId.trim() === '') {
    throw new BadRequestError('Invalid or missing appointment ID');
  }

  const validationResult = confirmAppointmentQuerySchema.safeParse(req.query);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid query parameters', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const provider = getProviderFromRequest(req);
  const appointmentService = provider.getAppointmentService();

  const existingAppointment = await appointmentService.getAppointmentById(appointmentId);
  if (!existingAppointment) {
    throw new NotFoundError('Appointment not found');
  }

  await appointmentService.confirmAppointment(appointmentId);

  // Update call type for current session and any sessions associated with this appointment
  try {
    // Get the current session ID from the request if available (prioritize query params for DELETE)
    const currentSessionId = req.query.sessionId || req.body.sessionId;

    if (currentSessionId) {
      // Use the proper utility function to update call session type
      await updateCallSessionType(
        currentSessionId as string,
        CallType.CONFIRM_APPOINTMENT,
        'Appointment Confirmation',
      );

      logger.info(
        {
          sessionId: currentSessionId,
          appointmentId,
          callType: CallType.CONFIRM_APPOINTMENT,
        },
        `Updated current call session ${currentSessionId} with call type CONFIRM_APPOINTMENT`,
      );
    } else {
      logger.info(
        { appointmentId },
        `No current session ID provided for confirmed appointment ${appointmentId}`,
      );
    }
  } catch (sessionError) {
    logger.error(
      { error: sessionError, appointmentId },
      `Failed to update call session for confirmed appointment ${appointmentId}`,
    );
    // Continue even if session update fails
  }

  res.status(200).json({ ...existingAppointment, status: AppointmentStatus.BOOKED });
}

// Export the handler wrapped by createApiHandler with middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
