import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import {
  getAppointmentQuerySchema,
  createAppointmentSchema,
} from '@/lib/external-api/v2/validators';
import { storeAppointmentReference } from '@/lib/external-api/v2/utils/appointment-utils';
import {
  updateCallSessionTypeForPatient,
  updateCallSessionType,
} from '@/lib/external-api/v2/utils/call-type-utils';
import { CallType } from '@/models/CallTypes';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * @swagger
 * /api/external-api/v2/appointments:
 *   get:
 *     summary: Get appointments
 *     description: Retrieve all appointments or a specific appointment by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: id
 *         required: false
 *         description: ID of the appointment to retrieve
 *       - in: query
 *         name: patientId
 *         required: false
 *         description: Patient ID filter for appointments
 *       - in: query
 *         name: providerId
 *         required: false
 *         description: Provider ID filter for appointments
 *       - in: query
 *         name: locationId
 *         required: false
 *         description: Location ID filter for appointments
 *       - in: query
 *         name: clinicId
 *         required: false
 *         description: Clinic ID filter for appointments
 *       - in: query
 *         name: startDate
 *         required: false
 *         description: Start date filter (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         required: false
 *         description: End date filter (YYYY-MM-DD)
 *       - in: query
 *         name: status
 *         required: false
 *         description: Appointment status filter
 *         schema:
 *           type: string
 *           enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *       - in: query
 *         name: type
 *         required: false
 *         description: Appointment type filter
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Maximum number of results to return
 *       - in: query
 *         name: offset
 *         required: false
 *         description: Number of results to skip
 *       - in: query
 *         name: sessionId
 *         required: false
 *         description: Current session ID for updating call session information
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Appointment or list of appointments
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     patientId:
 *                       type: string
 *                     practitionerId:
 *                       type: string
 *                     locationId:
 *                       type: string
 *                     clinicId:
 *                       type: string
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                     status:
 *                       type: string
 *                       enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *                     type:
 *                       type: string
 *                     reason:
 *                       type: string
 *                     notes:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                     providerInfo:
 *                       type: object
 *                       properties:
 *                         provider:
 *                           type: string
 *                         externalId:
 *                           type: string
 *                 - type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       patientId:
 *                         type: string
 *                       practitionerId:
 *                         type: string
 *                       locationId:
 *                         type: string
 *                       clinicId:
 *                         type: string
 *                       startTime:
 *                         type: string
 *                         format: date-time
 *                       endTime:
 *                         type: string
 *                         format: date-time
 *                       status:
 *                         type: string
 *                         enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *                       type:
 *                         type: string
 *                       reason:
 *                         type: string
 *                       notes:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                       providerInfo:
 *                         type: object
 *                         properties:
 *                           provider:
 *                             type: string
 *                           externalId:
 *                             type: string
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Appointment not found
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create appointment
 *     description: Create a new appointment
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patientId
 *               - practitionerId
 *               - locationId
 *               - clinicId
 *               - startTime
 *               - endTime
 *             properties:
 *               patientId:
 *                 type: string
 *               practitionerId:
 *                 type: string
 *               locationId:
 *                 type: string
 *               clinicId:
 *                 type: string
 *               startTime:
 *                 type: string
 *                 format: date-time
 *                 description: ISO format date-time (YYYY-MM-DDTHH:MM:SS)
 *               endTime:
 *                 type: string
 *                 format: date-time
 *                 description: ISO format date-time (YYYY-MM-DDTHH:MM:SS)
 *               type:
 *                 type: string
 *                 description: Appointment type
 *               reason:
 *                 type: string
 *                 description: Reason for the appointment
 *               notes:
 *                 type: string
 *                 description: Additional notes
 *     responses:
 *       201:
 *         description: Created appointment
 *         headers:
 *           Location:
 *             schema:
 *               type: string
 *             description: URL of the created appointment
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 patientId:
 *                   type: string
 *                 practitionerId:
 *                   type: string
 *                 locationId:
 *                   type: string
 *                 clinicId:
 *                   type: string
 *                 startTime:
 *                   type: string
 *                   format: date-time
 *                 endTime:
 *                   type: string
 *                   format: date-time
 *                 status:
 *                   type: string
 *                   enum: [proposed, pending, booked, arrived, fulfilled, cancelled, noshow]
 *                 type:
 *                   type: string
 *                 reason:
 *                   type: string
 *                 notes:
 *                   type: string
 *                 createdAt:
 *                   type: string
 *                   format: date-time
 *                 updatedAt:
 *                   type: string
 *                   format: date-time
 *                 providerInfo:
 *                   type: object
 *                   properties:
 *                     provider:
 *                       type: string
 *                     externalId:
 *                       type: string
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       409:
 *         description: Conflict - appointment slot not available
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/appointments
 * Retrieves appointments with optional filtering
 */
async function getAppointmentsHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Enhanced logging for sessionId tracking
    const { sessionId } = req.query;
    const hasSessionId = typeof sessionId === 'string' && sessionId.trim() !== '';

    logger.info(
      {
        context: 'getAppointmentsHandler',
        hasSessionId,
        sessionId: hasSessionId ? sessionId : 'NOT_PROVIDED',
        patientId: req.query.patientId,
        queryParams: Object.keys(req.query),
        userAgent: req.headers['user-agent'],
        source: hasSessionId ? 'dialogflow-with-session' : 'dialogflow-missing-session-or-other',
      },
      `Appointment lookup request - SessionId ${hasSessionId ? 'PROVIDED' : 'MISSING'}`,
    );

    // If sessionId provided, mark call session as LOOKUP
    if (hasSessionId) {
      await updateCallSessionType(sessionId, CallType.LOOKUP, 'Appointment Search');
      logger.info(
        { sessionId, callType: CallType.LOOKUP },
        'Successfully updated call session with LOOKUP type',
      );
    } else if (typeof req.query.patientId === 'string' && req.query.patientId.trim() !== '') {
      // No sessionId, but we have patientId - update all sessions for this patient
      logger.warn(
        { patientId: req.query.patientId },
        'SessionId missing - using patientId fallback for call type update',
      );
      await updateCallSessionTypeForPatient(
        req.query.patientId,
        CallType.LOOKUP,
        'Appointment Search by Patient',
      );
    } else {
      logger.error(
        { queryParams: req.query },
        'CRITICAL: No sessionId or patientId provided - call type will remain OTHER',
      );
    }
    // Validate request parameters
    const validationResult = getAppointmentQuerySchema.safeParse(req.query);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request parameters', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Merge original query params with validated/transformed ones
    const queryParams = {
      ...req.query,
      ...validationResult.data,
    };

    const { id, startDate, endDate, locationId, clinicId, providerId, patientId } = queryParams;

    // // add first location identifier to the query params // TODO: remove this once we have more locations
    // queryParams.identifier = identifier ? Number(identifier) : 110;

    // Get a specific appointment by ID
    if (id) {
      const appointment = await appointmentService.getAppointmentById(id as string);
      if (!appointment) {
        throw new NotFoundError('Appointment not found');
      }
      res.status(200).json(appointment);
      return;
    }

    // Get appointments by date range and optional filters
    if (startDate && endDate) {
      let appointments;
      if (locationId) {
        appointments = await appointmentService.getAppointmentByDateRangeAndLocationId(
          startDate as string,
          endDate as string,
          locationId as string,
        );
      } else if (clinicId) {
        appointments = await appointmentService.getAppointmentByDateRangeAndClinicId(
          startDate as string,
          endDate as string,
          clinicId as string,
        );
      } else if (providerId) {
        appointments = await appointmentService.getAppointmentByDateRangeAndProviderId(
          startDate as string,
          endDate as string,
          providerId as string,
        );
      } else if (patientId) {
        appointments = await appointmentService.getAppointmentByDateRangeAndPatientId(
          startDate as string,
          endDate as string,
          patientId as string,
        );
      } else {
        appointments = await appointmentService.getAppointmentByDateRange(
          startDate as string,
          endDate as string,
        );
      }
      res.status(200).json(appointments);
      return;
    }

    // Get all appointments (with potential pagination from queryParams)
    const appointments = await appointmentService.getAppointments(queryParams);
    res.status(200).json(appointments);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Handler for POST /api/external-api/v2/appointments
 * Creates a new appointment
 */
async function createAppointmentHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request body
    const validationResult = createAppointmentSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Create appointment
    const createdAppointment = await appointmentService.createAppointment(req.body);

    // Store appointment reference in Firestore
    await storeAppointmentReference(provider, createdAppointment);

    // Update call type in call session based on appointment data
    try {
      // Check if this is a new patient appointment based on the request body
      // We'll use a flag in the request or check for specific appointment types
      let isNewPatient = false;

      // Option 1: Check if the appointment type indicates a new patient
      if (req.body.type) {
        // Check if the appointment type contains "new patient" in its name
        isNewPatient = req.body.type.toString().toLowerCase().includes('new patient');
      }

      // Option 2: Check if there are other appointments for this patient
      if (!isNewPatient) {
        const appointmentService = provider.getAppointmentService();
        const patientAppointmentsResult = await appointmentService.getAppointmentByPatientId(
          createdAppointment.patientId,
        );

        // If this is the only appointment (or first one), consider it a new patient
        // We subtract 1 because the list will include the appointment we just created
        isNewPatient = patientAppointmentsResult.items.length <= 1;
      }

      const callType = isNewPatient
        ? CallType.NEW_PATIENT_NEW_APPOINTMENT
        : CallType.NEW_APPOINTMENT_EXISTING_PATIENT;

      // Update call type for all sessions associated with this patient
      await updateCallSessionTypeForPatient(
        createdAppointment.patientId,
        callType,
        'Appointment Creation',
      );

      logger.info(
        {
          appointmentId: createdAppointment.id,
          patientId: createdAppointment.patientId,
          callType,
          isNewPatient,
        },
        `Updated call type to ${callType} for patient ${createdAppointment.patientId}`,
      );
    } catch (error) {
      // Log error but don't fail the request
      logger.error(
        { error, appointmentId: createdAppointment.id, patientId: createdAppointment.patientId },
        'Failed to update call type for appointment creation',
      );
    }

    // Set location header and return created appointment
    res.setHeader('Location', `/api/external-api/v2/appointments/${createdAppointment.id}`);
    res.status(201).json(createdAppointment);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/appointments
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // GET request for appointments
  if (req.method === 'GET') {
    await getAppointmentsHandler(req, res);
    return;
  }

  // POST request to create an appointment
  if (req.method === 'POST') {
    await createAppointmentHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.status(405).json({ message: 'Method not allowed' });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
