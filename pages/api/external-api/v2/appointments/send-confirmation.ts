import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  Appointment,
  parseISODateString,
} from '@/lib/external-api/v2';
import { z } from 'zod';
import { smsService } from '@/lib/services/sms-service';
import { callSessionsService } from '@/utils/firestore';
import logger from '../../../../../lib/external-api/v2/utils/logger';

/**
 * @swagger
 * /api/external-api/v2/appointments/send-confirmation:
 *   post:
 *     summary: Send SMS confirmation or new patient form link for an appointment
 *     description: Sends an SMS confirmation message to a patient about their appointment, or sends a new patient form link for new patients. The appointment details are retrieved from the database using the appointmentId.
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - appointmentId
 *               - phoneNumber
 *             properties:
 *               appointmentId:
 *                 type: string
 *                 description: ID of the appointment to send confirmation for
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number to send the SMS to
 *               customMessage:
 *                 type: string
 *                 description: Custom message to send instead of the default template (optional)
 *     responses:
 *       200:
 *         description: SMS confirmation sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Confirmation SMS sent successfully"
 *                 appointmentId:
 *                   type: string
 *                   example: "12345678-1234-1234-1234-123456789012"
 *                 messageSid:
 *                   type: string
 *                   description: Twilio message SID
 *                   example: "SM12345678901234567890123456789012"
 *       400:
 *         description: Bad request - invalid input or appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized - API key is missing or invalid
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

// Schema for validating the request body
const sendConfirmationSchema = z.object({
  appointmentId: z.string().nonempty('Appointment ID is required'),
  phoneNumber: z.string().nonempty('Phone number is required'),
  customMessage: z.string().optional(),
  // The following fields are no longer required as they're retrieved from the appointment
  // but kept for backward compatibility
  patientName: z.string().optional(),
  date: z.string().optional(),
  location: z.string().optional(),
  practitionerName: z.string().optional(),
  provider: z.string().optional(),
  isForCancellation: z.boolean().optional(),
});

/**
 * Handler for POST /api/external-api/v2/appointments/send-confirmation
 * Sends an SMS confirmation for an appointment
 */
async function sendConfirmationHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Validate request body
    const validationResult = sendConfirmationSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // Get provider from request to verify appointment exists
    const apiProvider = getProviderFromRequest(req);
    const appointmentService = apiProvider.getAppointmentService();

    let appointment: Appointment | null;
    const appointmentId = validationResult.data.appointmentId;

    // Verify that the appointment exists
    try {
      appointment = await appointmentService.getAppointmentById(appointmentId);
      if (!appointment) {
        throw new NotFoundError(`Appointment with ID ${appointmentId} not found`);
      }

      const { phoneNumber, customMessage, isForCancellation } = validationResult.data;
      const { patientName, locationName, practitionerName, startTime, locationId } = appointment;

      const locationService = apiProvider.getLocationService();
      const location = await locationService.getLocationById(locationId);

      // Parse date and time directly from the ISO string format without timezone conversion
      const { date, time } = parseISODateString(startTime);

      // Check if this is a new patient appointment by looking up the call session
      let isNewPatient = false;
      try {
        const callSessions =
          await callSessionsService.findCallSessionsByAppointmentId(appointmentId);
        if (callSessions.length > 0) {
          // Use the first call session found (there should typically be only one)
          const callSession = callSessions[0];
          isNewPatient = callSession.sendNewPatientForm || false;
        }
      } catch (error) {
        logger.warn({ error, appointmentId }, 'Failed to lookup call session for appointment');
      }

      logger.info(
        { appointmentId, phoneNumber, isNewPatient },
        `Sending ${isNewPatient ? 'new patient form' : 'confirmation'} SMS for appointment ${appointmentId}`,
      );

      // If a custom message is provided, send that instead of the default template
      let messageSid: string;
      if (customMessage) {
        messageSid = await smsService.sendSms(phoneNumber, customMessage);
      } else if (isNewPatient) {
        // Send new patient form link for new patients
        messageSid = await smsService.sendNewPatientFormLink(phoneNumber, patientName || 'Patient');
      } else {
        const appointmentDetails = {
          patientName: patientName || 'Patient',
          date,
          time,
          location: locationName || 'Location',
          provider: practitionerName ? `Doctor ${practitionerName}` : undefined,
          locationAddress: location?.address.line1,
          contactPhone: location?.phoneNumber,
        };

        // Use the appointment confirmation template
        if (isForCancellation === true) {
          messageSid = await smsService.sendAppointmentCancellationConfirmation(
            phoneNumber,
            appointmentDetails,
          );
        } else {
          messageSid = await smsService.sendAppointmentConfirmation(
            phoneNumber,
            appointmentDetails,
          );
        }
      }

      res.status(200).json({
        success: true,
        message: 'Confirmation SMS sent successfully',
        appointmentId,
        messageSid,
      });
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error({ error, appointmentId }, `Error verifying appointment ${appointmentId}`);

      throw new BadRequestError(
        `Error verifying appointment: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  } catch (error) {
    logger.error({ error }, 'Error sending confirmation SMS');
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/appointments/send-confirmation
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // POST request to send confirmation
  if (req.method === 'POST') {
    await sendConfirmationHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
