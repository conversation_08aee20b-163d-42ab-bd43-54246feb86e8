import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  BadRequestError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { LocationService } from '@/lib/services/locationService';
import admin from 'firebase-admin';

/**
 * @swagger
 * /api/external-api/v2/users/locations:
 *   get:
 *     summary: Get locations by practice
 *     description: Retrieve all locations for a specific practice
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: practiceId
 *         required: true
 *         description: ID of the practice to get locations for
 *         schema:
 *           type: string
 *       - in: query
 *         name: clinicId
 *         required: false
 *         description: ID of the clinic (optional - will be derived from practice if not provided)
 *         schema:
 *           type: number
 *       - in: query
 *         name: includeInactive
 *         required: false
 *         description: Whether to include inactive locations
 *         schema:
 *           type: boolean
 *           default: false
 *     responses:
 *       200:
 *         description: List of locations for the practice
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   clinicId:
 *                     type: number
 *                   practiceId:
 *                     type: string
 *                   address:
 *                     type: string
 *                   phone:
 *                     type: string
 *                   timeZone:
 *                     type: string
 *                   isActive:
 *                     type: boolean
 *                   practiceName:
 *                     type: string
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Get required parameters
    const practiceId = req.query.practiceId as string;
    const clinicIdParam = req.query.clinicId as string;
    const includeInactive = req.query.includeInactive === 'true';

    if (!practiceId) {
      throw new BadRequestError('practiceId query parameter is required');
    }

    let clinicId: number;

    // If clinicId is provided, use it. Otherwise, find it from the practice
    if (clinicIdParam) {
      clinicId = parseInt(clinicIdParam);
      if (isNaN(clinicId)) {
        throw new BadRequestError('clinicId must be a number if provided');
      }
    } else {
      // Find clinicId from the practice
      const db = admin.firestore();
      const practiceDoc = await db.collection('practices').doc(practiceId).get();

      if (!practiceDoc.exists) {
        throw new BadRequestError('Practice not found');
      }

      const practiceData = practiceDoc.data();
      clinicId = practiceData?.clinicId;

      if (!clinicId) {
        throw new BadRequestError('Could not determine clinic from practice');
      }
    }

    // Get locations by practice using LocationService
    const result = await LocationService.getLocationsByClinicId(clinicId, {
      practiceId,
      includeInactive,
    });

    // Return the locations
    return res.status(200).json(result.locations);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
