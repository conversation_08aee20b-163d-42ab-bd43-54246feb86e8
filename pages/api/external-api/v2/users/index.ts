import { NextApiRequest, NextApiResponse } from 'next';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  requireU<PERSON><PERSON><PERSON>ess,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
} from '../../../../../lib/external-api/v2';
import { getUserQuerySchema } from '../../../../../lib/external-api/v2/validators/user';

/**
 * @swagger
 * /api/external-api/v2/users:
 *   get:
 *     summary: Get users
 *     description: Retrieve all users or a specific user by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: id
 *         required: false
 *         description: ID of the user to retrieve
 *       - in: query
 *         name: fullName
 *         required: false
 *         description: Full name filter for users
 *       - in: query
 *         name: email
 *         required: false
 *         description: Email filter for users
 *       - in: query
 *         name: role
 *         required: false
 *         description: Role filter for users
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Maximum number of results to return
 *       - in: query
 *         name: offset
 *         required: false
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: User or list of users
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                       format: email
 *                     role:
 *                       type: string
 *                 - type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       fullName:
 *                         type: string
 *                       email:
 *                         type: string
 *                         format: email
 *                       role:
 *                         type: string
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       403:
 *         description: Forbidden
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Validate request parameters
    const validationResult = getUserQuerySchema.safeParse(req.query);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request parameters', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // Get provider from request
    const provider = getProviderFromRequest(req);
    const userService = provider.getUserService();

    // Get a specific user by ID
    if (req.query.id) {
      // ValidationResult.data contains the validated ID if present
      const user = await userService.getUserById(validationResult.data.id as string);
      if (!user) {
        throw new NotFoundError('User not found');
      }
      return res.status(200).json(user);
    }

    // Merge original query params with validated/transformed ones
    // This keeps params like clinicId while using parsed limit/offset
    const queryParams = {
      ...req.query,
      ...validationResult.data, // Validated/transformed values overwrite original strings
    };

    // Get all users with optional filtering and pagination
    const users = await userService.getUsers(queryParams); // Use merged params

    return res.status(200).json(users);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation and user access middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requireUserAccess],
});
