import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import { createApiHand<PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import { RepositoryManager } from '@/lib/repositories';
import { CallType } from '@/models/CallTypes';
import { LLMClient } from '@/utils/llm';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import logger from '@/lib/external-api/v2/utils/logger';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import { formatDialogflowDuration } from '@/utils/call-duration-utils';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { LocationService } from '@/lib/services/locationService';

const querySchema = z.object({
  createdAt: z
    .object({
      startDate: z.string().optional(),
      endDate: z.string().optional(),
    })
    .optional(),
  callTypes: z.array(z.nativeEnum(CallType)).optional(),
  callDirection: z.enum(['inbound', 'outbound', 'both']).optional(),
  limit: z.number().optional().default(5),
  offset: z.number().optional().default(0),
  delayBetweenLlmRequests: z.number().optional().default(250),
  dryRun: z.boolean().optional().default(true),
});

const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const { success, data, error } = querySchema.safeParse(req.body);
  if (!success) {
    res.status(400).json({ error: 'Invalid query parameters', details: error.issues });
    return;
  }
  const { createdAt, callTypes, callDirection, limit, offset, delayBetweenLlmRequests, dryRun } =
    data;

  const llmClient = LLMClient.getInstance();
  const repositoryManager = RepositoryManager.getInstance();
  await repositoryManager.initialize();

  const callResult = await repositoryManager.calls.fetchAndFilterCalls({
    limit,
    offset,
    callTypes,
    callDirection,
    excludeZeroDuration: false,
    excludeNoTranscription: true,
    createdAt: createdAt
      ? {
          startDate: createdAt.startDate,
          endDate: createdAt.endDate,
        }
      : undefined,
  });

  for (let i = 0; i < callResult.calls.length; i++) {
    const call = callResult.calls[i];
    logger.info(`Classifying call ${i + 1} of ${callResult.calls.length}`, { callId: call.id });

    // Fetch fresh conversation data from GCP to get duration and transcription
    let transcriptionData = {
      transcription: call.transcription || '',
      transcriptionWithAudio: call.transcriptionWithAudio || '',
      duration: call.duration || '0 min',
    };

    if (call.sessionId) {
      try {
        logger.info(
          {
            context: `CallClassificationTypeEndpoint`,
            callId: call.id,
            sessionId: call.sessionId,
          },
          `Fetching fresh conversation data from GCP for call ${call.id}`,
        );

        // Get GCP API credentials
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (projectId && agentId) {
          // Get access token for Dialogflow API
          const accessToken = await DialogflowAuthService.getAccessToken();
          if (accessToken) {
            // Fetch conversation data directly from Dialogflow API
            const conversation = await getDialogflowConversation({
              projectId,
              locationId,
              agentId,
              sessionId: call.sessionId,
              accessToken,
            });

            // Extract transcript from conversation
            const dialogflowTranscript = extractTranscriptFromConversation(conversation);

            // Extract duration directly from conversation object
            let dialogflowDuration = '0 min';
            if (conversation.duration) {
              dialogflowDuration = formatDialogflowDuration(conversation.duration);
              logger.info(
                {
                  context: `CallClassificationTypeEndpoint`,
                  callId: call.id,
                  duration: conversation.duration,
                  formatted: dialogflowDuration,
                },
                'Extracted duration from DialogflowConversation',
              );
            }

            // Get transcription with audio using GCP storage service (minimized for better performance)
            let transcriptionWithAudio = '';
            try {
              const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
              if (bucketName) {
                const storageService = getGcpStorageService();
                // Use minimized version to only get text and recordUrl, avoiding memory overflow
                const interactions = await storageService.getTranscriptWithAudioRecords(
                  bucketName,
                  call.sessionId,
                  undefined,
                  true, // minimized = true for better performance and memory usage
                );
                if (interactions && interactions.length > 0) {
                  transcriptionWithAudio = JSON.stringify(interactions);
                }
              }
            } catch (audioError) {
              logger.warn(
                {
                  context: `CallClassificationTypeEndpoint`,
                  callId: call.id,
                  sessionId: call.sessionId,
                  error: audioError instanceof Error ? audioError.message : String(audioError),
                },
                'Failed to get transcription with audio (continuing with text-only transcription)',
              );
            }

            // Update transcription data with fresh GCP data
            transcriptionData = {
              transcription: dialogflowTranscript,
              transcriptionWithAudio,
              duration: dialogflowDuration,
            };

            logger.info(
              {
                context: `CallClassificationTypeEndpoint`,
                callId: call.id,
                hasTranscription: !!transcriptionData.transcription,
                hasAudio: !!transcriptionData.transcriptionWithAudio,
                duration: dialogflowDuration,
              },
              'Successfully fetched fresh data from GCP DialogflowConversation',
            );
          }
        }
      } catch (error) {
        logger.warn(
          {
            context: `CallClassificationTypeEndpoint`,
            callId: call.id,
            sessionId: call.sessionId,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to fetch fresh conversation data, using existing data',
        );
      }
    } else {
      logger.info(
        {
          context: `CallClassificationTypeEndpoint`,
          callId: call.id,
        },
        'No sessionId available, cannot fetch GCP conversation data',
      );
    }

    // Prepare call object with fresh transcription data for LLM
    const callForClassification = {
      ...call,
      transcription: transcriptionData.transcription,
      transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
      duration: transcriptionData.duration,
    };

    // Check if call has AFTER_HOURS type that should be preserved or if it should be detected
    const preserveTypes: CallType[] = [];

    // Check existing AFTER_HOURS type
    if (
      callForClassification.callTypes &&
      callForClassification.callTypes.includes(CallType.AFTER_HOURS)
    ) {
      preserveTypes.push(CallType.AFTER_HOURS);
    }
    // Auto-detect AFTER_HOURS based on call time and office hours
    else if (callForClassification.date && callForClassification.locationId && callForClassification.clinicId) {
      try {
        logger.info(
          {
            callId: callForClassification.id,
            callDate: callForClassification.date,
            locationId: callForClassification.locationId,
            clinicId: callForClassification.clinicId
          },
          'Attempting after-hours detection for call (batch)',
        );

        const location = await LocationService.getLocationById(
          callForClassification.locationId.toString(),
          callForClassification.clinicId,
        );

        logger.info(
          {
            callId: callForClassification.id,
            locationId: location?.id,
            hasOfficeHours: !!location?.officeHours,
            hasTimeZone: !!location?.timeZone,
            timeZone: location?.timeZone,
            officeHours: location?.officeHours
          },
          'Location data retrieved for after-hours detection (batch)',
        );

        if (location?.officeHours && location?.timeZone) {
          const officeHoursStatus = OfficeHoursService.checkOfficeHours(
            location.officeHours,
            location.timeZone,
            new Date(callForClassification.date)
          );

          logger.info(
            {
              callId: callForClassification.id,
              callDate: callForClassification.date,
              isOpen: officeHoursStatus.isOpen,
              currentStatus: officeHoursStatus.currentStatus,
              timeZone: location.timeZone
            },
            'Office hours status check completed (batch)',
          );

          if (!officeHoursStatus.isOpen) {
            preserveTypes.push(CallType.AFTER_HOURS);
            logger.info(
              { callId: callForClassification.id, callDate: callForClassification.date, officeStatus: officeHoursStatus.currentStatus },
              'Auto-detected AFTER_HOURS call based on call time and office hours (batch)',
            );
          }
        } else {
          logger.warn(
            {
              callId: callForClassification.id,
              locationId: location?.id,
              hasOfficeHours: !!location?.officeHours,
              hasTimeZone: !!location?.timeZone
            },
            'Missing office hours or timezone configuration for after-hours detection (batch)',
          );
        }
      } catch (error) {
        logger.error(
          { callId: callForClassification.id, error: error instanceof Error ? error.message : String(error) },
          'Failed to check office hours for after-hours detection (batch)',
        );
      }
    } else {
      logger.warn(
        {
          callId: callForClassification.id,
          hasDate: !!callForClassification.date,
          hasLocationId: !!callForClassification.locationId,
          hasClinicId: !!callForClassification.clinicId
        },
        'Missing call date, location ID, or clinic ID for after-hours detection (batch)',
      );
    }

    // Use the new method that supports multiple types
    const classifiedTypes = await llmClient.classifyCallTypes(callForClassification, preserveTypes);

    if (classifiedTypes && classifiedTypes.length > 0) {
      // Determine primary type (first in array) and build callTypes array
      const primaryType = classifiedTypes[0];
      let updatedCallTypes = [primaryType];

      // Merge additional types if there are multiple
      if (classifiedTypes.length > 1) {
        for (let j = 1; j < classifiedTypes.length; j++) {
          updatedCallTypes = mergeCallTypes(updatedCallTypes, classifiedTypes[j]);
        }
      }

      if (dryRun) {
        logger.info(
          `[DRY RUN] Call ${call.id} classified with types: [${classifiedTypes.join(', ')}], primaryType: ${primaryType}, updatedCallTypes: [${updatedCallTypes.join(', ')}], duration: ${transcriptionData.duration}`,
        );
      } else {
        logger.info('Updating call', {
          callId: call.id,
          oldCallTypes: call.callTypes,
          oldType: call.type,
          oldDuration: call.duration,
          classifiedTypes: classifiedTypes,
          newCallTypes: updatedCallTypes,
          newType: primaryType,
          newDuration: transcriptionData.duration,
        });
        (call as unknown as Record<string, unknown>).oldCallTypes = call.callTypes;
        call.callTypes = updatedCallTypes;
        call.type = primaryType;
        call.transcription = transcriptionData.transcription;
        call.transcriptionWithAudio = transcriptionData.transcriptionWithAudio;
        call.duration = transcriptionData.duration;
        await repositoryManager.calls.update(call.id, call, {
          skipFirestore: true,
        });
      }
      await delay(delayBetweenLlmRequests);
    } else {
      logger.info(`Call ${call.id} not classified - no valid types returned`);
    }
  }

  res.status(200).json(callResult);
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
