import { NextApiRequest, NextApiResponse } from 'next';

import { createApiHandler, ensureProvidersInitialized } from '@/lib/external-api/v2';

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  res.status(200).json({ message: `OK` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
