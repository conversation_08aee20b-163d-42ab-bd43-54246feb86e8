import { z } from 'zod';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { NextApiRequest, NextApiResponse } from 'next';

import {
  BadRequestError,
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { callService as telephonyService } from '@/lib/services/call-service';
import logger from '@/lib/external-api/v2/utils/logger';
import { callService } from '@/lib/external-api/v2/services/call-service';
import { CallType } from '@/models/CallTypes';
import { patientFactory } from '@/lib/factories/patient-factory';

dayjs.extend(utc);
dayjs.extend(timezone);

const setUpInboundCallSchema = z.object({
  // Core call identifiers
  CallSid: z.string(),
  AccountSid: z.string().optional(),

  // Call direction and status
  Direction: z.enum(['inbound', 'outbound-api']).optional(),
  CallStatus: z.string().optional(),

  // Phone numbers and caller information
  From: z.string(),
  To: z.string().optional(),
  Caller: z.string().optional(),
  Called: z.string().optional(),

  // Geographic information - From/Caller location
  FromCountry: z.string().optional(),
  FromState: z.string().optional(),
  FromCity: z.string().optional(),
  FromZip: z.string().optional(),
  CallerCountry: z.string().optional(),
  CallerState: z.string().optional(),
  CallerCity: z.string().optional(),
  CallerZip: z.string().optional(),

  // Geographic information - To/Called location
  ToCountry: z.string().optional(),
  ToState: z.string().optional(),
  ToCity: z.string().optional(),
  ToZip: z.string().optional(),
  CalledCountry: z.string().optional(),
  CalledState: z.string().optional(),
  CalledCity: z.string().optional(),
  CalledZip: z.string().optional(),

  // Twilio API and security information
  ApiVersion: z.string().optional(),
  StirVerstat: z.string().optional(),
  CallToken: z.string().optional(),

  // Add-ons and extensions
  AddOns: z.string().optional(),
});

/**
 * Set up the webhook that is called by Twilio when an inbound call is received.
 * The webhook returns an XML response with TwiML instructions on how to connect the call to the virtual agent.
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Validate the Twilio webhook payload (optional - for logging/debugging purposes)
  const validationResult = setUpInboundCallSchema.safeParse(req.body);
  if (!validationResult.success) {
    // Log validation errors but don't fail the request since TwiML response is still needed
    logger.error('Twilio webhook validation failed:', validationResult.error.flatten().fieldErrors);
    throw new BadRequestError('Twilio webhook validation failed');
  }

  const { CallSid: callId, From: callerPhoneNumber } = validationResult.data;
  // Create empty call record - use "unknown" as patientId placeholder
  await callService.createEmptyCall(
    {
      callId,
      sessionId: callId,
      phoneNumber: callerPhoneNumber,
      reason: 'Inbound call',
      type: CallType.OTHER,
      hasVoiceMail: false,
      isOutboundCall: false,
    },
    'unknown',
  );

  const callerPhone = callerPhoneNumber.replace('+', '');
  const patientCoordinatorService = patientFactory.getPatientCoordinatorService();
  const [patient] = await patientCoordinatorService.getCompletePatientByPhoneNumber(callerPhone);

  const xml = telephonyService.createAdkInboundStreamRules({
    userId: callerPhone,
    callId,
    callContext: {
      patientPhoneNumber: callerPhone,
      isExistingPatient: patient ? `true` : `false`,
      patientId: patient?.id,
      patientBirthday: patient?.dateOfBirth
        ? dayjs.utc(patient.dateOfBirth).format('YYYY-MM-DD')
        : undefined,
      patientFirstName: patient?.firstName,
      patientLastName: patient?.lastName,
    },
  });

  res.setHeader('Content-Type', 'text/xml').status(200).send(xml);
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
