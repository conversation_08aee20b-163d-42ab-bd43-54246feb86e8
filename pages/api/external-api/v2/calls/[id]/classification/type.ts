import { NextApiRequest, NextApiResponse } from 'next';

import { create<PERSON>pi<PERSON><PERSON><PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import { LLMClient } from '@/utils/llm';
import { RepositoryManager } from '@/lib/repositories';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import { CallType } from '@/models/CallTypes';

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const { id: callId } = req.query;
  if (!callId) {
    return res.status(400).json({ message: 'Call ID is required' });
  }

  const llmClient = LLMClient.getInstance();
  const repositoryManager = RepositoryManager.getInstance();
  const call = await repositoryManager.calls.findById(callId as string);
  if (!call) {
    return res.status(404).json({ message: 'Call not found' });
  }

  // Check if call has AFTER_HOURS type that should be preserved or if it should be detected
  const preserveTypes: CallType[] = [];

  // Check existing AFTER_HOURS type
  if (call.callTypes && call.callTypes.includes(CallType.AFTER_HOURS)) {
    preserveTypes.push(CallType.AFTER_HOURS);
  }
  // Auto-detect AFTER_HOURS based on transcript content
  else if (call.transcription && call.transcription.includes('You have reached the on call Center for University Retina')) {
    preserveTypes.push(CallType.AFTER_HOURS);
    logger.info(
      { callId: call.id },
      'Auto-detected AFTER_HOURS call based on transcript content',
    );
  }

  // Use the new method that supports multiple types
  const classifiedTypes = await llmClient.classifyCallTypes(call, preserveTypes);

  if (!classifiedTypes || classifiedTypes.length === 0) {
    return res.status(200).json({
      callType: null,
      classifiedTypes: [],
      primaryType: null,
      finalCallTypes: [],
      message: 'No valid call types could be classified',
    });
  }

  // Determine primary type (first in array) and build callTypes array
  const primaryType = classifiedTypes[0];
  let finalCallTypes = [primaryType];

  // Merge additional types if there are multiple
  if (classifiedTypes.length > 1) {
    for (let i = 1; i < classifiedTypes.length; i++) {
      finalCallTypes = mergeCallTypes(finalCallTypes, classifiedTypes[i]);
    }
  }

  // Convert primary type to string for backward compatibility
  const primaryTypeString = Object.keys(CallType).find(
    key => CallType[key as keyof typeof CallType] === primaryType,
  );

  res.status(200).json({
    callType: primaryTypeString, // Legacy field for backward compatibility
    classifiedTypes: classifiedTypes, // All types identified by LLM
    primaryType: primaryType, // Primary type as number
    finalCallTypes: finalCallTypes, // Final merged call types array
    message: 'Call classified successfully',
  });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
