import { NextApiRequest, NextApiResponse } from 'next';

import { create<PERSON>pi<PERSON><PERSON><PERSON>, ensureProvidersInitialized } from '@/lib/external-api/v2';
import { LLMClient } from '@/utils/llm';
import { RepositoryManager } from '@/lib/repositories';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import { CallType } from '@/models/CallTypes';
import { OfficeHoursService } from '@/lib/services/office-hours';
import logger from '@/lib/external-api/v2/utils/logger';
import { LocationService } from '@/lib/services/locationService';

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const { id: callId } = req.query;
  if (!callId) {
    return res.status(400).json({ message: 'Call ID is required' });
  }

  const llmClient = LLMClient.getInstance();
  const repositoryManager = RepositoryManager.getInstance();
  const call = await repositoryManager.calls.findById(callId as string);
  if (!call) {
    return res.status(404).json({ message: 'Call not found' });
  }

  // Check if call has AFTER_HOURS type that should be preserved or if it should be detected
  const preserveTypes: CallType[] = [];

  // Check existing AFTER_HOURS type
  if (call.callTypes && call.callTypes.includes(CallType.AFTER_HOURS)) {
    preserveTypes.push(CallType.AFTER_HOURS);
  }
  // Auto-detect AFTER_HOURS based on call time and office hours
  else if (call.date && call.locationId && call.clinicId) {
    try {
      logger.info(
        {
          callId: call.id,
          callDate: call.date,
          locationId: call.locationId,
          clinicId: call.clinicId
        },
        'Attempting after-hours detection for call',
      );

      const location = await LocationService.getLocationById(
        call.locationId.toString(),
        call.clinicId,
      );

      logger.info(
        {
          callId: call.id,
          locationId: location?.id,
          hasOfficeHours: !!location?.officeHours,
          hasTimeZone: !!location?.timeZone,
          timeZone: location?.timeZone,
          officeHours: location?.officeHours
        },
        'Location data retrieved for after-hours detection',
      );

      if (location?.officeHours && location?.timeZone) {
        const officeHoursStatus = OfficeHoursService.checkOfficeHours(
          location.officeHours,
          location.timeZone,
          new Date(call.date)
        );

        logger.info(
          {
            callId: call.id,
            callDate: call.date,
            isOpen: officeHoursStatus.isOpen,
            currentStatus: officeHoursStatus.currentStatus,
            timeZone: location.timeZone
          },
          'Office hours status check completed',
        );

        if (!officeHoursStatus.isOpen) {
          preserveTypes.push(CallType.AFTER_HOURS);
          logger.info(
            { callId: call.id, callDate: call.date, officeStatus: officeHoursStatus.currentStatus },
            'Auto-detected AFTER_HOURS call based on call time and office hours',
          );
        }
      } else {
        logger.warn(
          {
            callId: call.id,
            locationId: location?.id,
            hasOfficeHours: !!location?.officeHours,
            hasTimeZone: !!location?.timeZone
          },
          'Missing office hours or timezone configuration for after-hours detection',
        );
      }
    } catch (error) {
      logger.error(
        { callId: call.id, error: error instanceof Error ? error.message : String(error) },
        'Failed to check office hours for after-hours detection',
      );
    }
  } else {
    logger.warn(
      {
        callId: call.id,
        hasDate: !!call.date,
        hasLocationId: !!call.locationId,
        hasClinicId: !!call.clinicId
      },
      'Missing call date, location ID, or clinic ID for after-hours detection',
    );
  }

  // Use the new method that supports multiple types
  const classifiedTypes = await llmClient.classifyCallTypes(call, preserveTypes);

  if (!classifiedTypes || classifiedTypes.length === 0) {
    return res.status(200).json({
      callType: null,
      classifiedTypes: [],
      primaryType: null,
      finalCallTypes: [],
      message: 'No valid call types could be classified',
    });
  }

  // Determine primary type (first in array) and build callTypes array
  const primaryType = classifiedTypes[0];
  let finalCallTypes = [primaryType];

  // Merge additional types if there are multiple
  if (classifiedTypes.length > 1) {
    for (let i = 1; i < classifiedTypes.length; i++) {
      finalCallTypes = mergeCallTypes(finalCallTypes, classifiedTypes[i]);
    }
  }

  // Convert primary type to string for backward compatibility
  const primaryTypeString = Object.keys(CallType).find(
    key => CallType[key as keyof typeof CallType] === primaryType,
  );

  res.status(200).json({
    callType: primaryTypeString, // Legacy field for backward compatibility
    classifiedTypes: classifiedTypes, // All types identified by LLM
    primaryType: primaryType, // Primary type as number
    finalCallTypes: finalCallTypes, // Final merged call types array
    message: 'Call classified successfully',
  });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
