import { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, validate<PERSON><PERSON><PERSON><PERSON>, BadRequestError } from '@/lib/external-api/v2';
import { z } from 'zod';
import { callSessionsService, callsService } from '@/utils/firestore';
import logger from '../../../../../lib/external-api/v2/utils/logger';
import { callService } from '@/lib/external-api/v2/services/call-service';
import { CallType } from '@/models/CallTypes';
import { patientFactory } from '@/lib/factories/patient-factory';
import { LocationService } from '@/lib/services/locationService';
import { URMA_CLINIC_ID, URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import { OfficeHoursService, OfficeHoursStatus } from '@/lib/services/office-hours';
import { scheduleCallClassificationUpdate } from '../jobs/call-classification-update';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

// Schema for validating the request body
const addOrUpdateCallSessionSchema = z.object({
  detectIntentResponseId: z.string().optional(),
  intentInfo: z
    .object({
      lastMatchedIntent: z.string().optional(),
      displayName: z.string().optional(),
      confidence: z.number().optional(),
    })
    .optional(),
  pageInfo: z
    .object({
      currentPage: z.string().optional(),
      displayName: z.string().optional(),
    })
    .optional(),
  sessionInfo: z.object({
    session: z.string().nonempty('Session is required'),
    parameters: z
      .object({
        caller_phone: z.string().optional(),
        isRedirected: z.boolean().optional(),
        'telephony-caller-id': z.string().optional(),
        patient_phone_number: z.string().optional(),
      })
      .optional(),
  }),
  fulfillmentInfo: z
    .object({
      tag: z.string().optional(),
    })
    .optional(),
  messages: z.array(z.any()).optional(),
  text: z.string().optional(),
  triggerEvent: z.string().optional(),
  languageCode: z.string().optional(),
  hasVoiceMail: z.boolean().optional(),
  callType: z.number().optional(),
  payload: z
    .object({
      telephony: z
        .object({
          caller_id: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
});

/**
 * Handler for POST /api/external-api/v2/calls/add-or-update-call-session
 * Adds or updates a call session
 * Also checks if there's a patient with the caller's phone number and returns it in the payload
 * Creates an initial empty call record to ensure calls are always saved
 */
async function addOrUpdateCallSessionHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Validate request body
    const validationResult = addOrUpdateCallSessionSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const { sessionInfo, payload, triggerEvent } = validationResult.data;
    let { callType, hasVoiceMail } = validationResult.data;
    const fullSessionPath = sessionInfo.session;
    // Sample session: projects/frontdesk-454309/locations/global/agents/0d35ec76-19cc-41c5-8ef3-693c9236c52d/sessions/3c5371dc-d5c0-4530-8363-d1ddf833c346
    // Extract just the sessionId part after "/sessions/"
    const sessionId = fullSessionPath.split('/sessions/')[1];
    const agentId = fullSessionPath.split('/agents/')[1].split('/')[0];

    // Prioritize telephony-caller-id, then payload.telephony.caller_id, then caller_phone
    const callerPhone =
      sessionInfo.parameters?.['telephony-caller-id'] ||
      payload?.telephony?.caller_id ||
      sessionInfo.parameters?.['patient_phone_number'] ||
      '';

    // We make an outbound call when Heather contacts a patient to reschedule a missed appointment.
    const isOutboundCall = triggerEvent?.toLowerCase() === 'no-show';
    let isRedirected = false;

    logger.info(
      { sessionId, callerPhone, hasVoiceMail, callType, agentId, triggerEvent },
      `Adding or updating call session ${sessionId}`,
    );

    // Prepare data object without undefined values and with proper defaults
    const sessionData: Record<string, unknown> = {};

    // Set default values or use provided values
    sessionData.hasVoiceMail = hasVoiceMail === true;
    sessionData.triggerEvent = triggerEvent;

    if (callType !== undefined) {
      sessionData.callType = callType;
    }
    if (agentId !== undefined) {
      sessionData.agentId = agentId;
    }

    let isFamilyNumber: boolean = false;
    let patient = null;
    let patientId: string | null = null;

    if (callerPhone) {
      sessionData.callerPhone = callerPhone;

      // Check if patient was redirected today in the latest call session with the same phone number
      try {
        // Find all recent call sessions with this phone number
        const callSessions = await callSessionsService.findCallSessionsByPhone(callerPhone);

        if (callSessions.length > 0) {
          logger.info(
            { count: callSessions.length, callerPhone },
            `Found ${callSessions.length} call sessions with phone number ${callerPhone}`,
          );

          const latestSession = callSessions[0]; // Already sorted by updatedAt desc

          // Check if the latest session was within the last 5 minutes
          const REDIRECT_TIME_GAP_MS = 5 * 60 * 1000;
          const now = Date.now();
          const sessionTime = new Date(latestSession.createdAt).getTime();
          const isWithinRedirectTimeGap = Math.abs(now - sessionTime) <= REDIRECT_TIME_GAP_MS;

          // Check if the patient was redirected in this session
          if (isWithinRedirectTimeGap && latestSession.isRedirected === true) {
            // If the patient was redirected in the last 5 minutes, we set the call type
            //  to voice mail to make sure call will have correct call type even if call was not ended as expected
            callType = CallType.VOICEMAIL;
            hasVoiceMail = true;
            isRedirected = true;
            logger.info(
              { sessionId: latestSession.sessionId, isRedirected: latestSession.isRedirected },
              `Patient was redirected today in session ${latestSession.sessionId}`,
            );
          }
        }
      } catch (phoneSessionError) {
        logger.error(
          { error: phoneSessionError, callerPhone },
          `Failed to check if patient was redirected today for phone ${callerPhone}`,
        );
        // Continue even if checking related sessions fails
      }

      // Check if patient exists in the database
      try {
        // Search for the patient by phone number
        const patientCoordinatorService = patientFactory.getPatientCoordinatorService();
        const patients =
          await patientCoordinatorService.getCompletePatientByPhoneNumber(callerPhone);

        if (patients.length === 1) {
          patient = patients[0];
          patientId = patients[0].providerInfo.externalId;
          // Add patientId to the call session data
          sessionData.patientId = patientId;
          logger.info({ patientId, callerPhone }, `Found patient for caller phone ${callerPhone}`);
        } else if (patients.length > 1) {
          isFamilyNumber = true;
          logger.info(
            {
              callerPhone,
              patients: patients.map(p => ({
                id: p.providerInfo.externalId,
                firstName: p.firstName,
                lastName: p.lastName,
                dateOfBirth: p.dateOfBirth,
                gender: p.gender,
              })),
            },
            `Found multiple patients for caller phone ${callerPhone}`,
          );
        } else {
          logger.info({ callerPhone }, `No patient found for caller phone ${callerPhone}`);
        }
      } catch (error) {
        logger.error({ error, callerPhone }, `Error searching for patient by phone ${callerPhone}`);
        // Continue processing even if patient search fails
      }
    }

    // Get location and office hours information
    let location = null;
    let officeHoursStatus: OfficeHoursStatus | null = null;

    try {
      // Get location by agent ID
      location = await AgentLocationMappingService.getLocationByAgentId(agentId);

      if (location?.officeHours && location?.timeZone) {
        // Check office hours status
        officeHoursStatus = OfficeHoursService.checkOfficeHours(
          location.officeHours,
          location.timeZone,
        );

        logger.info(
          {
            agentId,
            locationId: location.id,
            locationName: location.name,
            isOpen: officeHoursStatus.isOpen,
            currentStatus: officeHoursStatus.currentStatus,
          },
          'Office hours status determined for call session',
        );
      } else {
        logger.warn(
          {
            agentId,
            locationId: location?.id,
            hasOfficeHours: !!location?.officeHours,
            hasTimeZone: !!location?.timeZone,
          },
          'Location found but missing office hours or timezone configuration',
        );
      }
    } catch (locationError) {
      logger.error(
        {
          error: locationError,
          agentId,
        },
        'Error getting location or office hours for agent',
      );
      // Continue without office hours information
    }

    // Add or update the call session
    await callSessionsService.addOrUpdateCallSession(sessionId, sessionData);

    // Create an initial empty call record to ensure we always have a call in the database
    try {
      // Check if we already have a call for this session by directly querying with indexed filter
      const existingCalls = await callsService.getCallsBySessionId(sessionId);
      const existingCall = existingCalls.length > 0 ? existingCalls[0] : null;

      if (!existingCall) {
        // Create minimal call data
        const initialCallData = {
          sessionId,
          phoneNumber: callerPhone || 'Unknown',
          reason: isOutboundCall ? 'Outbound call' : 'Incoming call',
          notes: '',
          hasVoiceMail: hasVoiceMail || false,
          type: callType || CallType.OTHER,
          isOutboundCall,
          agentId,
        };

        // Create empty call record - use "unknown" as patientId placeholder
        const call = await callService.createEmptyCall(initialCallData, 'unknown');

        // Store reference to the call ID in the session data using the proper callId field
        await callSessionsService.addOrUpdateCallSession(sessionId, {
          callId: call.id,
        });

        logger.info(
          { callId: call.id, sessionId },
          `Created initial empty call record for session ${sessionId}`,
        );

        // Schedule call classification update job to run in 20 minutes (fire-and-forget)
        scheduleCallClassificationUpdate({
          callId: call.id,
          executeInNextMinutes: 20,
        })
          .then(({ jobName }) => {
            logger.info(
              { callId: call.id, sessionId, jobName },
              `Scheduled call classification update job for call ${call.id}`,
            );
          })
          .catch(schedulingError => {
            logger.error(
              {
                error: schedulingError,
                callId: call.id,
                sessionId,
                errorMessage:
                  schedulingError instanceof Error
                    ? schedulingError.message
                    : String(schedulingError),
              },
              `Failed to schedule call classification update job for call ${call.id}`,
            );
            // Continue even if job scheduling fails - this is not critical to the main flow
          });
      } else {
        logger.info(
          { callId: existingCall.id, sessionId },
          `Call record already exists for session ${sessionId}`,
        );

        // Update session with the call ID reference just in case it's missing
        await callSessionsService.addOrUpdateCallSession(sessionId, {
          callId: existingCall.id,
        });
      }
    } catch (callCreationError) {
      logger.error(
        { error: callCreationError, sessionId },
        `Failed to create initial call record for session ${sessionId}`,
      );
      // Continue even if call creation fails - this is a safety measure
    }

    // Format response as Dialogflow CX webhook response
    // See https://cloud.google.com/dialogflow/cx/docs/reference/rest/v3/WebhookResponse
    const webhookResponse = {
      sessionInfo: {
        parameters: {
          sessionId: sessionId,
          isRedirected: isRedirected,
          isAfterHours: await isAfterHours(),
          isFamilyNumber,
          patientId: patientId,
          patientDateOfBirth: patient?.dateOfBirth,
          patientFirstName: patient?.firstName,
          patientLastName: patient?.lastName,
          patientPhoneNumber: isFamilyNumber ? callerPhone : patient?.phoneNumber,
          patientEmail: patient?.email,
          patientGender: patient?.gender,
          patientAddress: patient?.address,
          locationId: location?.id,
          isOfficeHours: officeHoursStatus?.isOpen ?? false,
          officeStatus: officeHoursStatus?.currentStatus ?? 'unknown',
          nextOpenTime: officeHoursStatus?.nextOpenTime,
          todayHours: officeHoursStatus?.todayHours,
        },
      },
      payload: {
        sessionId: sessionId,
        patient: patient,
        location: location
          ? {
              id: location.id,
              name: location.name,
              timezone: location.timeZone,
              officeHours: location.officeHours,
            }
          : null,
        schedule: officeHoursStatus,
      },
    };

    // Return the response in the format expected by the test
    res.status(200).json(webhookResponse);
  } catch (error) {
    logger.error({ error }, 'Error adding or updating call session');
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/calls/add-or-update-call-session
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // POST request to add or update call session
  if (req.method === 'POST') {
    await addOrUpdateCallSessionHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});

/**
 * Determines if the current time is after hours based on the location's office hours
 * @returns true if currently after hours, false if during office hours
 */
async function isAfterHours(): Promise<boolean> {
  try {
    // Get current time in Chicago timezone
    const now = dayjs().tz('America/Chicago');
    const currentDayOfWeek = now.day(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
    const currentTime = now.format('HH:mm'); // Format as HH:mm for comparison

    // Convert day of week to match officeHours format (1 = Monday, 7 = Sunday)
    const officeHoursDayKey = currentDayOfWeek === 0 ? '7' : currentDayOfWeek.toString();

    logger.info(
      {
        currentTime,
        currentDayOfWeek,
        officeHoursDayKey,
        timezone: 'America/Chicago',
      },
      'Checking if current time is after hours',
    );

    // Fetch the location
    const location = await LocationService.getLocationById(
      URMA_LOMBARD_LOCATION_ID,
      URMA_CLINIC_ID,
    );

    if (!location) {
      logger.warn(
        { locationId: URMA_LOMBARD_LOCATION_ID, clinicId: URMA_CLINIC_ID },
        'Location not found, assuming after hours',
      );
      return true; // Assume after hours if location not found
    }

    if (!location?.officeHours) {
      logger.warn(
        { locationId: location?.id },
        'No office hours configured for location, assuming after hours',
      );
      return true; // Assume after hours if no office hours configured
    }

    const todayHours = location?.officeHours?.[officeHoursDayKey];

    if (!todayHours || !todayHours?.start || !todayHours?.end) {
      logger.info(
        {
          locationId: location?.id,
          dayOfWeek: officeHoursDayKey,
          officeHours: location?.officeHours,
        },
        'No office hours for today, assuming after hours',
      );
      return true; // No hours for today means closed
    }

    // Check if current time is between start and end times
    const isOfficeHours = currentTime >= todayHours!.start! && currentTime <= todayHours!.end!;

    logger.info(
      {
        currentTime,
        startTime: todayHours!.start,
        endTime: todayHours!.end,
        isOfficeHours: isOfficeHours,
        locationId: location!.id,
      },
      'Office hours check completed',
    );

    return !isOfficeHours; // Return true if after hours (not during hours)
  } catch (error) {
    logger.error({ error }, 'Error checking if current time is after hours, assuming after hours');
    return true; // Assume after hours on error
  }
}
