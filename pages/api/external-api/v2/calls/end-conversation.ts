import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  BadRequestError,
  NotFoundError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { z } from 'zod';
import { callSessionsService } from '@/utils/firestore';
import { callService } from '@/lib/external-api/v2/services/call-service';
import logger from '@/lib/external-api/v2/utils/logger';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import { providerRegistry } from '@/lib/external-api/v2/providers';
import { CallSession } from '@/models/CallSession';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { CallType } from '@/models/CallTypes';
import { callsService } from '@/utils/firestore';
import {
  calculateDurationFromTranscript,
  parseDurationToSeconds,
} from '@/utils/call-duration-utils';

// Extended type for CallSession that includes our custom fields
type CallSessionExtended = CallSession & {
  patientId?: string;
  appointmentId?: string;
};

// Schema for validating the request body
export const endConversationSchema = z.object({
  sessionInfo: z.object({
    session: z.string().nonempty('Session is required'),
    parameters: z
      .object({
        isRedirected: z.boolean().optional(),
        hasVoiceMail: z.boolean().optional(),
        isGeneralInfo: z.union([z.boolean(), z.string()]).optional(),
        isError: z.boolean().optional(),
        providerCallId: z.string().optional(),
        'telephony-caller-id': z.string().optional(),
      })
      .optional(),
  }),
  payload: z
    .object({
      telephony: z
        .object({
          caller_id: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
  messages: z.array(z.any()).optional(),
  text: z.string().optional(),
});

/**
 * Handler for POST /api/external-api/v2/calls/end-conversation
 * Updates an existing call record with complete information based on the session
 */
export async function endConversationHandler(data: z.infer<typeof endConversationSchema>) {
  logger.info(
    {
      context: `EndConversation.handler`,
      data,
    },
    'Starting end conversation handler',
  );

  try {
    const { sessionInfo, payload, text } = data;
    const fullSessionPath = sessionInfo.session;
    // Extract just the sessionId part after "/sessions/"
    const sessionId = fullSessionPath.split('/sessions/')[1];

    // Prioritize telephony-caller-id, then payload.telephony.caller_id, then caller_phone
    const callerPhone =
      sessionInfo.parameters?.['telephony-caller-id'] || payload?.telephony?.caller_id || '';
    const telephonyCallId = sessionInfo.parameters?.providerCallId;
    const isRedirected = sessionInfo.parameters?.isRedirected;
    const hasVoiceMail = sessionInfo.parameters?.hasVoiceMail;
    const isGeneralInfo = sessionInfo.parameters?.isGeneralInfo;
    const isError = sessionInfo.parameters?.isError;

    logger.info(
      { sessionId, callerPhone, isRedirected, hasVoiceMail, isGeneralInfo, isError },
      `Processing end of conversation for session ${sessionId}`,
    );

    // 1. Retrieve call session data
    const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);
    if (!callSession) {
      throw new NotFoundError(`Call session with ID ${sessionId} not found`);
    }
    await callSessionsService.addOrUpdateCallSession(sessionId, {
      status: 'completed',
      telephonyCallId,
    });

    // Update call session isRedirected value
    if (isRedirected) {
      await callSessionsService.addOrUpdateCallSession(sessionId, {
        isRedirected,
      });
    }
    logger.info(
      { sessionId, isRedirected },
      `Updated call session ${sessionId} with isRedirected: ${isRedirected}`,
    );

    // Cast to extended type that includes patientId and appointmentId
    const extendedSession = callSession as CallSessionExtended;

    // 2. Get patient information - both patientId and callerPhone can be empty
    let patient = null;
    let patientId = extendedSession.patientId || null;

    // Check for existing call ID stored in the session
    const existingCallId = extendedSession.callId || null;

    // Try to find patient by phone only if we have a phone number and no patientId
    if (!patientId && callerPhone && callerPhone.trim() !== '') {
      try {
        const provider = providerRegistry.getProvider();
        const patientService = provider.getPatientService();
        patient = await patientService.getPatientByPhone(callerPhone);

        if (patient) {
          patientId = patient.providerInfo.externalId;
          logger.info({ patientId, callerPhone }, `Found patient for caller phone ${callerPhone}`);

          // Update the current call session with the found patient ID
          try {
            await callSessionsService.addOrUpdateCallSession(sessionId, {
              patientId,
            });
            logger.info(
              { sessionId, patientId },
              `Updated call session ${sessionId} with patient ID ${patientId}`,
            );

            // Update all recent call sessions with the same phone number
            const callSessions = await callSessionsService.findCallSessionsByPhone(callerPhone);

            if (callSessions.length > 0) {
              logger.info(
                { count: callSessions.length, callerPhone },
                `Found ${callSessions.length} call sessions with phone number ${callerPhone}`,
              );

              // Update each call session with the patient ID
              const updatePromises = callSessions.map(async session => {
                // Skip the current session as it was already updated above
                if (session.sessionId === sessionId) {
                  return;
                }

                try {
                  await callSessionsService.addOrUpdateCallSession(session.sessionId, {
                    patientId: patientId as string,
                  });
                  logger.info(
                    { sessionId: session.sessionId, patientId },
                    `Updated call session ${session.sessionId} with patient ID ${patientId} (by phone match)`,
                  );
                } catch (err) {
                  logger.error(
                    { sessionId: session.sessionId, error: err },
                    `Failed to update call session ${session.sessionId} by phone match`,
                  );
                }
              });

              // Wait for all updates to complete but don't block the main flow if they fail
              Promise.all(updatePromises).catch(err => {
                logger.error({ error: err }, 'Error updating related call sessions');
              });
            }
          } catch (sessionUpdateError) {
            logger.error(
              { error: sessionUpdateError, sessionId, patientId },
              `Error updating call session with patient ID`,
            );
            // Continue even if session update fails
          }
        } else {
          logger.info({ callerPhone }, `No patient found for caller phone ${callerPhone}`);
        }
      } catch (error) {
        logger.error({ error, callerPhone }, `Error searching for patient by phone ${callerPhone}`);
        // Continue without patient info - it's acceptable to have an unknown caller
      }
    } else if (patientId) {
      logger.info({ patientId }, `Using patient ID from call session record`);
    } else {
      logger.info(`No patient information available for this call`);
    }

    // 3. Get appointment information if it exists in the call session
    let appointmentData:
      | {
          practitionerId?: string;
          clinicId?: string;
          locationId: string;
          reason?: string;
          patientId?: string;
          patientName?: string;
        }
      | undefined = undefined;

    if (extendedSession.appointmentId) {
      try {
        const provider = providerRegistry.getProvider();
        const appointmentService = provider.getAppointmentService();
        const appointment = await appointmentService.getAppointmentById(
          extendedSession.appointmentId,
        );

        if (appointment) {
          // Convert appointment data to the format expected by createCall
          appointmentData = {
            practitionerId: appointment.practitionerId,
            clinicId: appointment.clinicId?.toString(),
            locationId: appointment.locationId.toString(),
            reason: appointment.reason,
            patientId: appointment.patientId,
            patientName: appointment.patientName,
          };

          // If we don't have patient info from phone lookup but have it from appointment, use it
          if (!patient && appointment.patientId) {
            patientId = appointment.patientId;
            logger.info(
              { patientId: appointment.patientId, appointmentId: extendedSession.appointmentId },
              `Using patient ID from appointment since phone lookup didn't find patient`,
            );
          }

          logger.info(
            {
              appointmentId: extendedSession.appointmentId,
              patientId: appointment.patientId,
              patientName: appointment.patientName,
            },
            `Found appointment with patient information for call session`,
          );
        } else {
          logger.info(
            { appointmentId: extendedSession.appointmentId },
            `Appointment ID exists in session but appointment was not found`,
          );
        }
      } catch (error) {
        logger.error(
          { error, appointmentId: extendedSession.appointmentId },
          `Error retrieving appointment information`,
        );
        // Continue without appointment info - it's acceptable to have a call without an appointment
      }
    } else {
      logger.info(`No appointment was created during this call session`);
    }

    // 4. Process call record - find existing or create new
    let call = null;
    try {
      // Check if the call is redirected
      let voicemailUrl = null;

      if (hasVoiceMail) {
        // Retrieve voicemail audio file from GCP Storage
        try {
          const storageService = getGcpStorageService();

          // Get the bucket name from environment variables
          const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;

          if (!bucketName) {
            logger.warn(
              { sessionId },
              'GCP_AUDIO_BUCKET_NAME environment variable is not set. Cannot retrieve voicemail audio.',
            );
          } else {
            // Get the latest audio file for the session
            const audioFile = await storageService.getLatestAudioFileForSession(
              bucketName,
              sessionId,
            );

            if (audioFile) {
              voicemailUrl = audioFile.url;

              logger.info(
                { sessionId, fileName: audioFile.fileName, voicemailUrl },
                `Found voicemail audio file for redirected call`,
              );
            } else {
              logger.warn({ sessionId }, `No audio files found for redirected call session`);
            }
          }
        } catch (error) {
          logger.error(
            { error, sessionId },
            `Error retrieving voicemail audio file for redirected call`,
          );
          // Continue without voicemail URL - it's acceptable to have a call without a voicemail
        }
      }

      // Use fallback for phoneNumber if callerPhone is empty
      const effectivePhoneNumber = callerPhone || callSession.callerPhone || 'Unknown';

      // Fetch transcription data early so we can use it for name extraction if needed
      const transcriptionData = await callService.fetchTranscriptionData(sessionId);

      // Prepare client name from available sources, prioritizing patient lookup, then appointment data
      let clientName = 'Patient'; // Default fallback

      if (patient) {
        // Best case: we found the patient via phone lookup
        clientName = `${patient.firstName} ${patient.lastName}`.trim();
      } else if (appointmentData?.patientName) {
        // Good case: we have patient name from appointment
        clientName = appointmentData.patientName;
        logger.info(
          { clientName, appointmentId: extendedSession.appointmentId },
          `Using patient name from appointment data`,
        );
      }

      // Use the best available patientId: from patient lookup, appointment data, or unknown
      const finalPatientId = patientId || appointmentData?.patientId || 'unknown';

      // Prepare call data
      const callData = {
        sessionId,
        phoneNumber: effectivePhoneNumber,
        reason:
          appointmentData?.reason || callSession.triggerEvent === 'no-show'
            ? 'Outbound call'
            : 'Incoming call',
        notes: text || '',
        hasVoiceMail: hasVoiceMail || false,
        voicemailUrl: voicemailUrl || '',
        type: callSession.callType,
        callTypes: callSession.callTypes, // Include callTypes array from session
        // Include clientId, clientName, and userId for proper patient and practitioner tracking
        clientId: finalPatientId,
        clientName: clientName,
        userId: appointmentData?.practitionerId || 'system',
      };

      // Apply override logic only for specific conditions that should take precedence
      // Preserve existing meaningful call types (anything other than OTHER)

      // Check if this is an after-hours call based on transcript content
      const isAfterHoursCall = text && text.includes('You have reached the on call Center for University Retina');
      if (isAfterHoursCall && (!callData.callTypes || !callData.callTypes.includes(CallType.AFTER_HOURS))) {
        callData.callTypes = mergeCallTypes(callData.callTypes, CallType.AFTER_HOURS);
        logger.info(
          { sessionId, callTypes: callData.callTypes },
          'Detected after-hours call based on transcript content - added AFTER_HOURS type',
        );
      }

      // If the call has voicemail, override any existing type
      if (hasVoiceMail || callSession.hasVoiceMail) {
        if (isError) {
          callData.type = CallType.VOICEMAIL_SYSTEM_ERROR;
          callData.callTypes = mergeCallTypes(callData.callTypes, CallType.VOICEMAIL_SYSTEM_ERROR);
        } else {
          callData.type = CallType.VOICEMAIL;
          callData.callTypes = mergeCallTypes(callData.callTypes, CallType.VOICEMAIL);
        }
        logger.info(
          { sessionId, callType: callData.type, callTypes: callData.callTypes },
          `Overriding call type to ${callData.type} for session with voicemail`,
        );
      }
      // If the call is redirected, override any existing type
      else if (isRedirected || callSession.isRedirected) {
        callData.type = CallType.TRANSFER_TO_HUMAN;
        callData.callTypes = mergeCallTypes(callData.callTypes, CallType.TRANSFER_TO_HUMAN);
        logger.info(
          { sessionId, callType: CallType.TRANSFER_TO_HUMAN, callTypes: callData.callTypes },
          `Overriding call type to TRANSFER_TO_HUMAN for redirected session`,
        );
      }
      // Only set GENERAL_INFO if no meaningful type is already set
      else if (
        (isGeneralInfo === true || isGeneralInfo === 'true') &&
        (!callData.type || callData.type === CallType.OTHER)
      ) {
        callData.type = CallType.GENERAL_INFO;
        callData.callTypes = mergeCallTypes(callData.callTypes, CallType.GENERAL_INFO);
        logger.info(
          { sessionId, callType: CallType.GENERAL_INFO, callTypes: callData.callTypes },
          `Setting call type to GENERAL_INFO for session with general info request (no existing meaningful type)`,
        );
      }
      // Log if we're preserving an existing meaningful type
      else if (callData.type && callData.type !== CallType.OTHER) {
        logger.info(
          { sessionId, callType: callData.type, callTypes: callData.callTypes },
          `Preserving existing call type ${callData.type} for session`,
        );
      }

      // Calculate effective duration using improved logic
      let effectiveDuration = transcriptionData.duration;

      // If no duration from transcription service or duration is 0, calculate from transcript
      if (!effectiveDuration || parseDurationToSeconds(effectiveDuration) === 0) {
        const calculatedDurationSeconds = calculateDurationFromTranscript(
          transcriptionData.transcriptionWithAudio,
        );

        if (calculatedDurationSeconds > 0) {
          // Format the calculated duration consistently
          effectiveDuration =
            calculatedDurationSeconds < 60
              ? `${calculatedDurationSeconds} sec`
              : `${(calculatedDurationSeconds / 60).toFixed(1)} min`;

          logger.info(
            { sessionId, calculatedDurationSeconds, formattedDuration: effectiveDuration },
            `Calculated duration from transcript for session ${sessionId}`,
          );
        } else {
          // Use fallback only if transcript calculation also fails
          effectiveDuration = '0 min';
        }
      }

      // Add transcription data to callData
      let callDataWithTranscription = {
        ...callData,
        transcription: transcriptionData.transcription,
        transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
        duration: effectiveDuration,
        // Ensure clientId, clientName, and userId are properly set for updates
        clientId: finalPatientId,
        clientName: clientName,
        userId: appointmentData?.practitionerId || 'system',
      };

      // Check if we already have an existing call to update
      if (existingCallId) {
        // Get the existing call
        const existingCall = await callsService.getCallById(existingCallId);

        if (existingCall) {
          // If existingCall is an after-hours call, update the call data with the after-hours call data
          if (existingCall.type == CallType.AFTER_HOURS) {
            callDataWithTranscription = {
              ...callDataWithTranscription,
              clientId:
                existingCall.clientId && existingCall.clientId !== 'unknown'
                  ? existingCall.clientId
                  : finalPatientId,
              callTypes: [CallType.AFTER_HOURS],
              type: CallType.AFTER_HOURS,
              clientName: existingCall.clientName || 'Patient',
              reason: existingCall.reason || 'Inbound call',
            };
          }

          // Update the existing call with the new data
          await callsService.updateCall(existingCallId, callDataWithTranscription);
          call = { ...existingCall, ...callDataWithTranscription, id: existingCallId };

          logger.info(
            { callId: existingCallId, sessionId },
            `Successfully updated existing call record for session ${sessionId}`,
          );
        } else {
          logger.warn(
            { callId: existingCallId, sessionId },
            `Could not find existing call with ID ${existingCallId}, will create a new one`,
          );
          // Fall through to create a new call
        }
      }

      // If no existing call was found or updated, create a new one
      if (!call) {
        // Find if there's another call with this session ID using the efficient method
        const sessionCalls = await callsService.getCallsBySessionId(sessionId);
        const sessionCall = sessionCalls.length > 0 ? sessionCalls[0] : null;

        if (sessionCall) {
          // If sessionCall is an after-hours call, update the call data with the after-hours call data
          if (sessionCall.type == CallType.AFTER_HOURS) {
            callDataWithTranscription = {
              ...callDataWithTranscription,
              clientId:
                sessionCall.clientId && sessionCall.clientId !== 'unknown'
                  ? sessionCall.clientId
                  : finalPatientId,
              callTypes: [CallType.AFTER_HOURS],
              type: CallType.AFTER_HOURS,
              clientName: sessionCall.clientName || 'Patient',
              reason: sessionCall.reason || 'Inbound call',
            };
          }

          // Update the existing call with the new data
          await callsService.updateCall(sessionCall.id, callDataWithTranscription);
          call = { ...sessionCall, ...callDataWithTranscription };

          logger.info(
            { callId: sessionCall.id, sessionId },
            `Successfully updated existing call record found by session ID for session ${sessionId}`,
          );
        } else {
          // Create new call using the call service if no existing calls found
          // If no patientId is found, use "unknown" as a placeholder
          call = await callService.createCall(
            callDataWithTranscription,
            patientId || 'unknown',
            appointmentData?.practitionerId,
            appointmentData,
          );

          logger.info(
            { callId: call.id, sessionId },
            `Created new call record for session ${sessionId}`,
          );
        }
      }

      // update callSession with type and hasVoiceMail flag
      await callSessionsService.addOrUpdateCallSession(sessionId, {
        callType: callDataWithTranscription.type,
        hasVoiceMail: callDataWithTranscription.hasVoiceMail,
      });

      logger.info(
        { callId: call.id, sessionId },
        `Successfully processed call record for session ${sessionId}`,
      );
    } catch (error) {
      logger.error({ error, sessionId }, `Error processing call record for session ${sessionId}`);
      throw new Error(
        `Failed to process call record: ${error instanceof Error ? error.message : String(error)}`,
      );
    }

    // 5. Format response as Dialogflow CX webhook response
    const webhookResponse = {
      sessionInfo: {
        parameters: {
          sessionId,
          callId: call?.id,
          patientId,
        },
      },
      payload: {
        success: true,
        call,
      },
    };

    return webhookResponse;
  } catch (error) {
    logger.error({ error }, 'Error processing end conversation');
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/calls/end-conversation
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method === 'POST') {
    // Validate request body
    const validationResult = endConversationSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const webhookResponse = await endConversationHandler(validationResult.data);
    res.status(200).json(webhookResponse);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
