import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import {
  BadRequestError,
  createApi<PERSON><PERSON><PERSON>,
  ensureProvidersInitialized,
  NotFoundError,
  validate<PERSON><PERSON><PERSON><PERSON>,
} from '@/lib/external-api/v2';
import logger from '@/lib/external-api/v2/utils/logger';
import { callsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';

/**
 * @swagger
 * /api/external-api/v2/calls/mark-as-transferred-to-clinic:
 *   post:
 *     summary: Mark call as transferred to clinic
 *     description: Updates a call's status to indicate it has been transferred to a clinic location. This endpoint is used to track call flow and maintain accurate call status records.
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - callId
 *               - locationId
 *             properties:
 *               callId:
 *                 type: string
 *                 description: Unique identifier of the call to be marked as transferred
 *                 example: "call_123456789"
 *               locationId:
 *                 type: string
 *                 description: Unique identifier of the location where the call is being transferred
 *                 example: "loc_987654321"
 *     responses:
 *       200:
 *         description: Call successfully marked as transferred to clinic
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid body
 *                 errors:
 *                   type: object
 *                   description: Validation errors by field
 *                   example:
 *                     callId: ["Required"]
 *                     locationId: ["Required"]
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       403:
 *         description: Forbidden - Missing required access permissions
 *       404:
 *         description: Not found - Call with specified ID not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Call not found
 *       500:
 *         description: Internal server error
 */

/**
 * Request body validation schema for marking a call as transferred to clinic
 */
const markAsTransferredToClinicSchema = z.object({
  /** Unique identifier of the call to be marked as transferred */
  callId: z.string(),
  /** Unique identifier of the location where the call is being transferred */
  locationId: z.string(),
});

async function httpHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  const validationResult = markAsTransferredToClinicSchema.safeParse(req.body);
  if (!validationResult.success) {
    // Log validation errors but don't fail the request since TwiML response is still needed
    logger.warn(
      'Mark as transferred to clinic validation failed:',
      validationResult.error.flatten().fieldErrors,
    );
    throw new BadRequestError('Invalid body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const { callId, locationId } = validationResult.data;
  const call = await callsService.getCallById(callId);
  if (!call) {
    logger.error({ callId }, 'Call not found');
    throw new NotFoundError('Call not found');
  }

  await callsService.updateCall(call.id, {
    type: CallType.TRANSFER_TO_CLINIC,
    transferToLocationId: parseInt(locationId),
  });
  logger.info(
    { callId: call.id, transferToLocationId: locationId },
    'Call updated to transferred to clinic',
  );

  res.status(200).end();
}

// Export with API key validation middleware
export default createApiHandler(httpHandler, {
  middleware: [validateApiKey, ensureProvidersInitialized],
});
