import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  BadRequestError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { endConversationHandler, endConversationSchema } from './end-conversation';

async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Validate request body
  const validationResult = endConversationSchema.safeParse(req.body);
  if (!validationResult.success) {
    throw new BadRequestError('Invalid request body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const webhookResponse = await endConversationHandler(validationResult.data);
  res.status(200).json(webhookResponse);
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
