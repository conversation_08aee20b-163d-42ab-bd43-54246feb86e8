import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
} from '../../../../../lib/external-api/v2';
import { getLocationQuerySchema } from '../../../../../lib/external-api/v2/validators/location';

/**
 * @swagger
 * /api/external-api/v2/locations:
 *   get:
 *     summary: Get locations
 *     description: Retrieve all locations or a specific location by ID
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: id
 *         required: false
 *         description: ID of the location to retrieve
 *       - in: query
 *         name: name
 *         required: false
 *         description: Name filter for locations
 *       - in: query
 *         name: clinicId
 *         required: false
 *         description: Clinic ID filter for locations
 *       - in: query
 *         name: phoneNumber
 *         required: false
 *         description: Phone number filter for locations
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Maximum number of results to return
 *       - in: query
 *         name: offset
 *         required: false
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: Location or list of locations
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     clinicId:
 *                       type: string
 *                     address:
 *                       type: string
 *                     phoneNumber:
 *                       type: string
 *                 - type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       clinicId:
 *                         type: string
 *                       address:
 *                         type: string
 *                       phoneNumber:
 *                         type: string
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Location not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ message: 'Method not allowed' });
    }

    // Validate request parameters
    const validationResult = getLocationQuerySchema.safeParse(req.query);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request parameters', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    // Get provider from request
    const provider = getProviderFromRequest(req);
    const locationService = provider.getLocationService();

    // Get a specific location by ID
    if (req.query.id) {
      const location = await locationService.getLocationById(req.query.id as string);
      if (!location) {
        throw new NotFoundError('Location not found');
      }
      return res.status(200).json(location);
    }

    // Get all locations with optional filtering and pagination
    const locations = await locationService.getLocations(req.query);

    return res.status(200).json(locations);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
