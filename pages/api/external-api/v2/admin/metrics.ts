import { NextApiRequest, NextApiResponse } from 'next';
import { createApiHandler } from '../../../../../lib/external-api/v2/utils/handler';
import { getMetrics, logMetrics } from '../../../../../lib/external-api/v2/utils/monitoring';
import { adminMiddleware } from '../../../../../lib/external-api/v2/middleware/admin-middleware';

/**
 * Admin endpoint to retrieve API metrics
 * This endpoint requires authentication through the adminMiddleware
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Log metrics to help with debugging/monitoring
  logMetrics();

  // Return metrics data
  res.status(200).json({
    timestamp: new Date().toISOString(),
    metrics: getMetrics(),
  });
}

// Apply admin middleware to ensure only authorized users can access metrics
export default createApiHandler(handler, {
  middleware: [adminMiddleware],
});
