import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  providerRegistry,
} from '../../../../lib/external-api/v2';

/**
 * Main handler for the External API v2
 * Provides information about the API and available providers
 */
const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const providers = providerRegistry.getAvailableProviders();

  res.status(200).json({
    name: 'External API',
    version: 'v2',
    description: 'Provider-agnostic API for accessing clinic, patient, and appointment data',
    providers,
    endpoints: [
      { path: '/api/external-api/v2/clinics', methods: ['GET'] },
      { path: '/api/external-api/v2/locations', methods: ['GET'] },
      { path: '/api/external-api/v2/patients', methods: ['GET'] },
      { path: '/api/external-api/v2/users', methods: ['GET'] },
      {
        path: '/api/external-api/v2/appointments',
        methods: ['GET', 'POST', 'PATCH', 'DELETE'],
      },
      {
        path: '/api/external-api/v2/appointments/{id}/change',
        methods: ['POST'],
        description:
          'Change appointment data by creating a new appointment with updated fields and canceling the original',
      },
      {
        path: '/api/external-api/v2/appointments/{id}/confirm',
        methods: ['POST'],
        description: 'Confirm an existing appointment',
      },
      {
        path: '/api/external-api/v2/appointments/for-rescheduling',
        methods: ['GET'],
        description: 'Get appointments for a patient with rescheduling logic',
      },
      {
        path: '/api/external-api/v2/finalize-conversation',
        methods: ['POST'],
        description:
          'Finalize a conversation by creating a patient (if needed), creating an appointment, and saving call data',
      },
    ],
  });
};

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
