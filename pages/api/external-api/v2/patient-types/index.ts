import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
} from '../../../../../lib/external-api/v2';

/**
 * @swagger
 * /api/external-api/v2/patient-types:
 *   get:
 *     summary: Get patient types
 *     description: Retrieve all patient types with optional filtering
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: name
 *         required: false
 *         description: Filter by name
 *       - in: query
 *         name: isActive
 *         required: false
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: List of patient types
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: Unique identifier for the patient type
 *                   name:
 *                     type: string
 *                     description: Name of the patient type
 *                   description:
 *                     type: string
 *                     description: Description of the patient type
 *                   isActive:
 *                     type: boolean
 *                     description: Whether the patient type is active
 *                   providerInfo:
 *                     type: object
 *                     properties:
 *                       provider:
 *                         type: string
 *                       externalId:
 *                         type: string
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/patient-types
 * Retrieves all patient types with optional filtering
 */
async function patientTypesHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // Get provider from request
    const provider = getProviderFromRequest(req);
    const patientService = provider.getPatientService();

    // Get patient types with the provided filters
    const patientTypes = await patientService.getPatientTypes(req.query);

    // Return the patient types
    return res.status(200).json(patientTypes);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

export default createApiHandler(patientTypesHandler, {
  middleware: [validateApiKey],
});
