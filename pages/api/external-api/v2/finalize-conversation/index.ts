import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  requirePatientAccess,
  getProviderFromRequest,
  BadRequestError,
  NotFoundError,
  ConflictError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { finalizeConversationSchema } from '@/lib/external-api/v2/validators/finalize-conversation';
import {
  createPatientWithReference,
  findPatientByCriteria,
} from '@/lib/external-api/v2/utils/patient-utils';
import { storeAppointmentReference } from '@/lib/external-api/v2/utils/appointment-utils';
import { CallType } from '@/models/CallTypes';
import { callSessionsService } from '@/utils/firestore';
import { eventEmitter, EVENTS } from '@/lib/external-api/v2/utils/events';
import { patientFactory } from '@/lib/factories/patient-factory';
import { Patient } from '@/lib/external-api/v2/models/types';

/**
 * @swagger
 * /api/external-api/v2/finalize-conversation:
 *   post:
 *     summary: Finalize a conversation
 *     description: Finalizes a conversation by creating a patient (if needed), creating an appointment, and saving call data
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - patient
 *               - appointment
 *               - call
 *             properties:
 *               patient:
 *                 type: object
 *                 required:
 *                   - firstName
 *                   - lastName
 *                   - dateOfBirth
 *                   - email
 *                   - phoneNumber
 *                   - address
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: Optional - include if using existing patient
 *                   firstName:
 *                     type: string
 *                     description: Patient's first name
 *                   lastName:
 *                     type: string
 *                     description: Patient's last name
 *                   dateOfBirth:
 *                     type: string
 *                     format: date
 *                     description: Date of birth in YYYY-MM-DD format
 *                   gender:
 *                     type: string
 *                     description: Patient's gender
 *                   email:
 *                     type: string
 *                     format: email
 *                     description: Patient's email address
 *                   phoneNumber:
 *                     type: string
 *                     description: Patient's phone number
 *                   address:
 *                     type: object
 *                     required:
 *                       - line1
 *                       - city
 *                       - state
 *                       - postalCode
 *                     properties:
 *                       line1:
 *                         type: string
 *                         description: Address line 1
 *                       line2:
 *                         type: string
 *                         description: Address line 2 (optional)
 *                       city:
 *                         type: string
 *                         description: City
 *                       state:
 *                         type: string
 *                         description: State/province
 *                       postalCode:
 *                         type: string
 *                         description: Postal/ZIP code
 *                       country:
 *                         type: string
 *                         description: Country (defaults to US)
 *                   patientTypeId:
 *                     type: string
 *                     description: Patient type ID (optional)
 *                   referralSourceId:
 *                     type: string
 *                     description: Referral source ID (optional)
 *                   notes:
 *                     type: string
 *                     description: Additional notes about the patient (optional)
 *               appointment:
 *                 type: object
 *                 required:
 *                   - practitionerId
 *                   - locationId
 *                   - startTime
 *                   - endTime
 *                   - type
 *                 properties:
 *                   practitionerId:
 *                     type: string
 *                     description: ID of the practitioner for the appointment
 *                   locationId:
 *                     type: string
 *                     description: ID of the location for the appointment
 *                   clinicId:
 *                     type: string
 *                     description: ID of the clinic (optional)
 *                   startTime:
 *                     type: string
 *                     format: date-time
 *                     description: Start time in ISO format (YYYY-MM-DDTHH:MM:SS)
 *                   endTime:
 *                     type: string
 *                     format: date-time
 *                     description: End time in ISO format (YYYY-MM-DDTHH:MM:SS)
 *                   type:
 *                     type: string
 *                     description: Appointment type ID
 *                   reason:
 *                     type: string
 *                     description: Reason for the appointment (optional)
 *                   notes:
 *                     type: string
 *                     description: Additional notes about the appointment (optional)
 *               call:
 *                 type: object
 *                 properties:
 *                   sessionId:
 *                     type: string
 *                     description: Unique session ID for the call
 *                   practitionerId:
 *                     type: string
 *                     description: ID of the practitioner who handled the call (optional)
 *                   clinicId:
 *                     type: number
 *                     description: ID of the clinic (optional)
 *                   locationId:
 *                     type: number
 *                     description: ID of the location (optional)
 *                   date:
 *                     type: string
 *                     format: date-time
 *                     description: Date and time of the call in ISO format (optional)
 *                   reason:
 *                     type: string
 *                     description: Reason for the call (optional)
 *                   recordingUrl:
 *                     type: string
 *                     description: URL to the call recording (optional)
 *                   notes:
 *                     type: string
 *                     description: Additional notes about the call (optional)
 *                   summary:
 *                     type: string
 *                     description: Summary of the call (optional)
 *                   transcription:
 *                     type: string
 *                     description: Transcription of the call (optional)
 *                   priorityScore:
 *                     type: number
 *                     description: Priority score for the call (optional)
 *                   urgent:
 *                     type: boolean
 *                     description: Whether the call is urgent (optional)
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Tags associated with the call (optional)
 *     responses:
 *       201:
 *         description: Conversation finalized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Conversation finalized successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient:
 *                       type: object
 *                       description: Created or found patient data
 *                     appointment:
 *                       type: object
 *                       description: Created appointment data
 *                     call:
 *                       type: object
 *                       description: Created call data
 *       207:
 *         description: Partial success - patient and appointment created but call data could not be saved
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 warning:
 *                   type: string
 *                   example: Call data could not be saved, but patient and appointment were created successfully
 *                 warningCode:
 *                   type: string
 *                   example: CALL_CREATION_FAILED
 *                 data:
 *                   type: object
 *                   properties:
 *                     patient:
 *                       type: object
 *                       description: Created or found patient data
 *                     appointment:
 *                       type: object
 *                       description: Created appointment data
 *                     call:
 *                       type: null
 *                       description: Call data (null in this case)
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid request body
 *                 errors:
 *                   type: object
 *                   description: Validation errors by field
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       403:
 *         description: Forbidden - Missing required access permissions
 *       404:
 *         description: Not found - Patient not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Patient with ID 123 not found
 *                 code:
 *                   type: string
 *                   example: PATIENT_NOT_FOUND
 *       409:
 *         description: Conflict - Patient already exists or appointment slot unavailable
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Appointment slot is no longer available
 *                 code:
 *                   type: string
 *                   example: APPOINTMENT_SLOT_UNAVAILABLE
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for POST /api/external-api/v2/finalize-conversation
 * Finalizes a conversation by creating a patient (if needed) and creating an appointment.
 * Note: Call creation has been moved to a separate endpoint.
 *
 * @returns A response with status 200 and the following structure:
 * {
 *   success: true,
 *   message: "Conversation finalized successfully",
 *   data: {
 *     patient: PatientData,
 *     appointment: AppointmentData,
 *     call: null // Call creation has been moved to a separate endpoint
 *   }
 * }
 */
async function finalizeConversationHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // 1. Validate request body
    const validationResult = finalizeConversationSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const {
      patient: patientData,
      appointment: appointmentData,
      call: callData,
    } = validationResult.data;

    // Validate appointment start time is not in the past
    const appointmentStartTime = new Date(appointmentData.startTime);
    const now = new Date();
    if (appointmentStartTime < now) {
      throw new BadRequestError('Appointment start time cannot be in the past', {
        code: 'INVALID_APPOINTMENT_TIME',
        message: 'The appointment start time must be in the future',
        suggestion: 'Please select a future date and time for the appointment',
      });
    }

    // 2. Get provider from request
    const provider = getProviderFromRequest(req);
    const patientService = provider.getPatientService();
    const appointmentService = provider.getAppointmentService();

    // 3. Check if patient exists (if ID is provided) or create a new one
    let patient;
    let patientCreated = false;

    // Variables to track session update status
    let sessionUpdateFailed = false;
    let sessionUpdateError = null;

    try {
      if (patientData.id) {
        // Get patient by ID
        patient = await patientService.getPatientById(patientData.id);
        if (!patient) {
          throw new NotFoundError(`Patient with ID ${patientData.id} not found`);
        }
      } else {
        // Try to find existing patient first
        const searchCriteria = {
          firstName: patientData.firstName,
          lastName: patientData.lastName,
          dateOfBirth: patientData.dateOfBirth,
          phoneNumber: patientData.phoneNumber,
        };

        patient = await findPatientByCriteria(provider, searchCriteria);

        if (!patient) {
          // Create new patient if not found
          patient = await createPatientWithReference(provider, patientData, true);
          patientCreated = true;
        } else {
          console.log(`Found existing patient with ID ${patient.id}`);
        }
      }

      // Cache the patient
      if (patient) {
        await cachePatient(patient, provider.name);
      }
    } catch (patientError) {
      // Enhance error with more details for the UI
      if (patientError instanceof ConflictError) {
        throw new ConflictError('Patient already exists', {
          code: 'PATIENT_ALREADY_EXISTS',
          message: 'A patient with this information already exists',
          suggestion: 'Try searching for the patient first or use a different email/phone',
          originalError: patientError,
        });
      } else if (patientError instanceof NotFoundError) {
        throw new NotFoundError(patientError.message, {
          code: 'PATIENT_NOT_FOUND',
          suggestion: 'Verify the patient ID or create a new patient',
        });
      } else {
        // For other errors, wrap with additional context
        throw new BadRequestError('Failed to process patient data', {
          code: 'PATIENT_PROCESSING_ERROR',
          originalError: patientError,
          suggestion: 'Check the patient information and try again',
        });
      }
    }

    // 4. Update the current call session with the patient ID if sessionId is provided
    if (callData.sessionId && patient.providerInfo.externalId) {
      try {
        await callSessionsService.addOrUpdateCallSession(callData.sessionId, {
          patientId: patient.providerInfo.externalId,
        });
        console.log(
          `Updated call session ${callData.sessionId} with patient ID ${patient.providerInfo.externalId}`,
        );
      } catch (sessionError) {
        console.error(`Failed to update call session ${callData.sessionId}:`, sessionError);
        // Set flag for session update failure
        sessionUpdateFailed = true;
        sessionUpdateError = sessionError;
        // Continue even if session update fails
      }
    }

    // 5. Create appointment
    let appointment;
    try {
      const appointmentToCreate = {
        ...appointmentData,
        patientId: patient.providerInfo.externalId,
      };

      appointment = await appointmentService.createAppointment(appointmentToCreate);

      // Store appointment reference in Firestore
      // Add patient ID to the appointment object for the reference
      const appointmentWithPatientId = {
        ...appointment,
        patientId: patient.providerInfo.externalId,
      };
      await storeAppointmentReference(provider, appointmentWithPatientId);

      // Update call session with appointmentId if sessionId exists in callData
      if (callData.sessionId) {
        try {
          await callSessionsService.addOrUpdateCallSession(callData.sessionId, {
            appointmentId: appointment.id,
            patientId: patient.providerInfo.externalId,
            callType: patientCreated
              ? CallType.NEW_PATIENT_NEW_APPOINTMENT
              : CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
            sendNewPatientForm: patientCreated,
          });
          console.log(
            `Updated call session ${callData.sessionId} with appointment ID ${appointment.id}`,
          );

          // Emit the appointment:created event with the appointment as payload
          // This will trigger the email notifications
          eventEmitter.emit(EVENTS.APPOINTMENT.CREATED, {
            sessionId: callData.sessionId,
            appointment,
          });
        } catch (sessionError) {
          console.error(`Failed to update call session ${callData.sessionId}:`, sessionError);
          // Set flag for session update failure
          sessionUpdateFailed = true;
          sessionUpdateError = sessionError;
          // Continue even if session update fails
        }
      }

      // 6. Send successful response
      // Note: Call creation has been moved to a separate endpoint

      // Check if there were any session update failures
      if (callData.sessionId && sessionUpdateFailed) {
        // Return a partial success response with a warning
        res.status(200).json({
          success: true,
          warning:
            'Call session could not be updated, but patient and appointment were created successfully',
          warningCode: 'CALL_SESSION_UPDATE_FAILED',
          data: {
            patient,
            appointment,
            call: null, // Call creation has been moved to a separate endpoint
            sessionUpdateError,
          },
        });
      } else {
        // Return a standard success response
        res.status(200).json({
          success: true,
          message: 'Conversation finalized successfully',
          data: {
            patient,
            appointment,
            call: null, // Call creation has been moved to a separate endpoint
          },
        });
      }
    } catch (appointmentError) {
      // Handle appointment creation errors with detailed information
      // This is especially important for race conditions like double-booking
      const error = appointmentError as Error;
      if (error.message && error.message.includes('409')) {
        throw new ConflictError('Appointment slot is no longer available', {
          code: 'APPOINTMENT_SLOT_UNAVAILABLE',
          message: 'The requested appointment time is no longer available',
          suggestion: 'Please select a different time slot',
          patientId: patient.providerInfo.externalId, // Include the patient ID so the UI can use it for retrying
          patientCreated: patientCreated, // Let the UI know if we created a new patient
          originalError: appointmentError,
        });
      } else if (error.message && error.message.includes('400')) {
        throw new BadRequestError('Invalid appointment data', {
          code: 'INVALID_APPOINTMENT_DATA',
          message: 'The appointment data is invalid',
          suggestion: 'Check the appointment details and try again',
          patientId: patient.providerInfo.externalId,
          patientCreated: patientCreated,
          originalError: appointmentError,
        });
      } else {
        // For other errors, wrap with additional context
        throw new BadRequestError('Failed to create appointment', {
          code: 'APPOINTMENT_CREATION_ERROR',
          patientId: patient.providerInfo.externalId,
          patientCreated: patientCreated,
          originalError: appointmentError,
          suggestion: 'Verify the appointment details and try again',
        });
      }
    }
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/finalize-conversation
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method === 'POST') {
    await finalizeConversationHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

/**
 * Caches a patient in the local system
 * @param patient The patient data to cache
 * @param providerName The name of the provider
 * @returns Promise that resolves when caching is complete
 */
async function cachePatient(patient: Patient, providerName: string): Promise<void> {
  console.log(`Caching patient ${patient.id} for provider ${providerName}`);
  const patientCoordinatorService = patientFactory.getPatientCoordinatorService();
  try {
    await patientCoordinatorService.storePatient({
      ...patient,
      providerInfo: {
        provider: providerName,
        externalId: patient.providerInfo.externalId,
      },
    });
  } catch (error) {
    console.error('Error caching patient:', error);
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requirePatientAccess, ensureProvidersInitialized],
});
