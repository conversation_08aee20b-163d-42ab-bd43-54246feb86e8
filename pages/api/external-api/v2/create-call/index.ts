import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  require<PERSON><PERSON><PERSON><PERSON>ccess,
  BadRequestError,
  getProviderFromRequest,
  NotFoundError,
  ensureProvidersInitialized,
} from '@/lib/external-api/v2';
import { callService } from '@/lib/external-api/v2/services/call-service';
import { callSessionsService } from '@/utils/firestore';
import { z } from 'zod';
import { IProvider } from '@/lib/external-api/v2/providers/types';

/**
 * @swagger
 * /api/external-api/v2/create-call:
 *   post:
 *     summary: Create a call record
 *     description: Creates a call record based on session ID or phone number
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Session ID for the call (either this or phoneNumber is required)
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number for the call (either this or sessionId is required)
 *               conversationId:
 *                 type: string
 *                 description: Dialogflow conversation ID (optional)
 *               practitionerId:
 *                 type: string
 *                 description: ID of the practitioner who handled the call (optional)
 *               clinicId:
 *                 type: number
 *                 description: ID of the clinic (optional)
 *               locationId:
 *                 type: number
 *                 description: ID of the location (optional)
 *               date:
 *                 type: string
 *                 format: date-time
 *                 description: Date and time of the call in ISO format (optional)
 *               reason:
 *                 type: string
 *                 description: Reason for the call (optional)
 *               recordingUrl:
 *                 type: string
 *                 description: URL to the call recording (optional)
 *               notes:
 *                 type: string
 *                 description: Additional notes about the call (optional)
 *               summary:
 *                 type: string
 *                 description: Summary of the call (optional)
 *               transcription:
 *                 type: string
 *                 description: Transcription of the call (optional)
 *               priorityScore:
 *                 type: number
 *                 description: Priority score for the call (optional)
 *               urgent:
 *                 type: boolean
 *                 description: Whether the call is urgent (optional)
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Tags associated with the call (optional)
 *     responses:
 *       201:
 *         description: Call created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Call created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     call:
 *                       type: object
 *                       description: Created call data
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: Invalid request body
 *                 errors:
 *                   type: object
 *                   description: Validation errors by field
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       403:
 *         description: Forbidden - Missing required access permissions
 *       404:
 *         description: Not found - Patient not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: No patient found for the provided session ID or phone number
 *       500:
 *         description: Internal server error
 */

// Schema for request validation
const createCallSchema = z
  .object({
    sessionId: z.string().optional(),
    phoneNumber: z.string().optional(),
    patientId: z.string().optional(),
    conversationId: z.string().optional(),
    practitionerId: z.string().optional(),
    clinicId: z.number().optional(),
    locationId: z.number().optional(),
    date: z.string().optional(),
    reason: z.string().optional(),
    recordingUrl: z.string().optional(),
    notes: z.string().optional(),
    summary: z.string().optional(),
    transcription: z.string().optional(),
    priorityScore: z.number().optional(),
    urgent: z.boolean().optional(),
    tags: z.array(z.string()).optional(),
    duration: z.string().optional(),
  })
  .refine(data => data.sessionId || data.phoneNumber, {
    message: 'Either sessionId or phoneNumber must be provided',
    path: ['sessionId', 'phoneNumber'],
  });

/**
 * Extended CallSession type to include optional patientId
 */
type CallSessionWithPatient = {
  id: string;
  sessionId: string;
  hasVoiceMail?: boolean;
  callType?: number;
  callerPhone?: string;
  createdAt: Date;
  updatedAt: Date;
  patientId?: string;
};

/**
 * Find patient ID and phone number from session or phone number
 * @param sessionId Session ID
 * @param phoneNumber Phone number
 * @param provider Provider instance
 * @returns Object containing patientId and phoneNumber
 * @throws NotFoundError if patient not found
 */
async function findPatientIdFromSessionOrPhone(
  sessionId?: string,
  phoneNumber?: string,
  provider?: IProvider,
): Promise<{ patientId: string; phoneNumber?: string }> {
  // 1. Check if we have a sessionId, and try to find a call session
  if (sessionId) {
    const callSession = await callSessionsService.getCallSessionBySessionId(sessionId);

    // If the call session has a patientId in the custom data, return it
    // Note: patientId is stored as a custom field in some implementations
    if (callSession) {
      const sessionWithPatient = callSession as unknown as CallSessionWithPatient;
      if (sessionWithPatient.patientId) {
        return {
          patientId: sessionWithPatient.patientId,
          phoneNumber: callSession.callerPhone,
        };
      }
    }

    // If call session has a phone number but no patientId, use the phone number to find the patient
    if (callSession && callSession.callerPhone && provider) {
      const patientService = provider.getPatientService();
      const patient = await patientService.getPatientByPhone(callSession.callerPhone);

      if (patient) {
        return {
          patientId: patient.providerInfo.externalId,
          phoneNumber: callSession.callerPhone,
        };
      }
    }
  }

  // 2. Try to find patient by phone number directly
  if (phoneNumber && provider) {
    const patientService = provider.getPatientService();
    const patient = await patientService.getPatientByPhone(phoneNumber);

    if (patient) {
      return {
        patientId: patient.providerInfo.externalId,
        phoneNumber,
      };
    }
  }

  // If we got here, we couldn't find a patient
  throw new NotFoundError('No patient found for the provided session ID or phone number');
}

/**
 * Handler for POST /api/external-api/v2/create-call
 * Creates a call record based on session ID or phone number
 */
async function createCallHandler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  try {
    // 1. Validate request body
    const validationResult = createCallSchema.safeParse(req.body);
    if (!validationResult.success) {
      throw new BadRequestError('Invalid request body', {
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const callData = validationResult.data;
    let patientId = callData.patientId;
    let phoneNumber = callData.phoneNumber;

    // 2. If patientId is not provided, try to find it from sessionId or phoneNumber
    if (!patientId) {
      const provider = getProviderFromRequest(req);
      const patientData = await findPatientIdFromSessionOrPhone(
        callData.sessionId,
        callData.phoneNumber,
        provider,
      );
      patientId = patientData.patientId;

      // Use phone number from lookup if not provided in request
      if (!phoneNumber && !callData.phoneNumber && patientData.phoneNumber) {
        phoneNumber = patientData.phoneNumber;
        callData.phoneNumber = phoneNumber;
      }
    }

    // 3. Create call using the call service
    const call = await callService.createCall(callData, patientId);

    // 4. Return response with created call
    res.status(201).json({
      success: true,
      message: 'Call created successfully',
      data: {
        call,
      },
    });
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/create-call
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // Only allow POST method
  if (req.method === 'POST') {
    await createCallHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.setHeader('Allow', ['POST']);
  res.status(405).json({ message: `Method ${req.method} Not Allowed` });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requirePatientAccess, ensureProvidersInitialized],
});
