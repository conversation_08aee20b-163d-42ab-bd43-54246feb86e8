import { NextApiRequest, NextApiResponse } from 'next';
import {
  create<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validate<PERSON><PERSON><PERSON><PERSON>,
  getProviderFromRequest,
  getCachedAppointmentTypes,
  NotFoundError,
} from '../../../../../lib/external-api/v2';
import { appointmentTypesQuerySchema } from '../../../../../lib/external-api/v2/validators/appointment';

/**
 * @swagger
 * /api/external-api/v2/appointment-types:
 *   get:
 *     summary: Get appointment types
 *     description: Retrieve all appointment types or filter by criteria
 *     tags: [External API v2]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: header
 *         name: x-provider
 *         required: false
 *         description: Provider to use (defaults to configured default provider)
 *       - in: query
 *         name: id
 *         required: false
 *         description: ID of the appointment type to retrieve
 *       - in: query
 *         name: providerId
 *         required: false
 *         description: Provider ID filter for appointment types
 *       - in: query
 *         name: locationId
 *         required: false
 *         description: Location ID filter for appointment types
 *       - in: query
 *         name: clinicId
 *         required: false
 *         description: Clinic ID filter for appointment types
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Maximum number of results to return
 *       - in: query
 *         name: offset
 *         required: false
 *         description: Number of results to skip
 *     responses:
 *       200:
 *         description: Appointment type or list of appointment types
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                     name:
 *                       type: string
 *                     description:
 *                       type: string
 *                     duration:
 *                       type: number
 *                       description: Duration in minutes
 *                     color:
 *                       type: string
 *                     isActive:
 *                       type: boolean
 *                     providerInfo:
 *                       type: object
 *                       properties:
 *                         provider:
 *                           type: string
 *                         externalId:
 *                           type: string
 *                 - type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       duration:
 *                         type: number
 *                         description: Duration in minutes
 *                       color:
 *                         type: string
 *                       isActive:
 *                         type: boolean
 *                       providerInfo:
 *                         type: object
 *                         properties:
 *                           provider:
 *                             type: string
 *                           externalId:
 *                             type: string
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Appointment type not found
 *       500:
 *         description: Internal server error
 */

/**
 * Handler for GET /api/external-api/v2/appointment-types
 * Retrieves all appointment types or filters by criteria
 */
async function getAppointmentTypesHandler(
  req: NextApiRequest,
  res: NextApiResponse,
): Promise<void> {
  try {
    // Get provider from request
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Validate query parameters using Zod
    const validatedQuery = appointmentTypesQuerySchema.parse(req.query);

    // Check if we're looking for a specific appointment type by ID
    if (validatedQuery.id) {
      const appointmentType = await appointmentService.getAppointmentTypeById(validatedQuery.id);

      if (!appointmentType) {
        throw new NotFoundError(`Appointment type with ID ${validatedQuery.id} not found`);
      }

      return res.status(200).json(appointmentType);
    }

    // Get appointment types with caching and filtering
    const appointmentTypes = await getCachedAppointmentTypes(appointmentService, validatedQuery);

    // Add cache-related headers
    res.setHeader('Cache-Control', 'public, max-age=3600');

    return res.status(200).json(appointmentTypes);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

/**
 * Main handler for /api/external-api/v2/appointment-types
 * Routes to specific functions based on HTTP method
 */
async function handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
  // GET request for appointment types
  if (req.method === 'GET') {
    await getAppointmentTypesHandler(req, res);
    return;
  }

  // Handle unsupported methods
  res.status(405).json({ message: 'Method not allowed' });
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
