# External API v2

## Overview

External API v2 is a provider-agnostic API for accessing clinic, patient, and appointment data. It's designed to support multiple providers, starting with Nextech Practice+ API, and can be extended to support additional providers in the future.

## Architecture

The API is built around a provider adapter pattern, which allows for different provider implementations to be plugged in while maintaining a consistent interface for clients.

## Appointment Management

### Updating Appointments

The Nextech API has a limitation where only the appointment status can be updated via the PATCH endpoint. To work around this limitation, we provide two methods for updating appointments:

1. **Status Updates**: Use the standard PATCH endpoint at `/api/external-api/v2/appointments/{id}` to update only the appointment status.

2. **Full Updates**: Use the special endpoint at `/api/external-api/v2/appointments/{id}/change` to update any appointment field. This endpoint works by:
   - Creating a new appointment with the updated fields
   - Canceling the original appointment
   - Returning the newly created appointment

This approach allows for updating any appointment field while working within the constraints of the Nextech API.

### Key Components

- **Provider Interfaces**: Common interfaces that all providers must implement
- **Provider Registry**: A central registry that manages available provider implementations
- **Provider Factory**: Creates provider instances based on configuration
- **Models**: Common data models that provide a consistent interface regardless of provider
- **Middleware**: Cross-cutting concerns like authentication, validation, and error handling

### Project Structure

```
pages/api/external-api/v2/
├── index.ts                 # Main API entry point
├── providers/               # Provider implementations
│   ├── index.ts             # Provider registry and factory exports
│   ├── registry.ts          # Provider registry implementation
│   ├── factory.ts           # Provider factory implementation
│   ├── types.ts             # Common provider interfaces
│   └── nextech/             # Nextech provider implementation
│       └── index.ts         # Nextech provider class
├── models/                  # Common data models
│   ├── types.ts             # Common type definitions
│   └── dto.ts               # Data transfer objects
├── middleware/              # Middleware functions
│   ├── index.ts             # Middleware exports
│   └── auth.ts              # Authentication middleware
├── utils/                   # Utility functions
│   ├── index.ts             # Utility exports
│   ├── errors.ts            # Error handling utilities
│   └── handler.ts           # API handler utilities
└── controllers/             # API route controllers
```

## Providers

### Available Providers

- **Nextech**: Implementation for the Nextech Practice+ API

### Adding a New Provider

To add a new provider:

1. Create a new directory under `providers/` for your provider (e.g., `providers/newprovider/`)
2. Implement the provider interfaces defined in `providers/types.ts`
3. Register the provider with the registry in your application startup code:

```typescript
import { providerRegistry } from './providers';
import { NewProvider } from './providers/newprovider';

// Create provider instance
const newProvider = new NewProvider({
  name: 'newprovider',
  // Provider-specific configuration
});

// Register with the registry
providerRegistry.registerProvider(newProvider);
```

## Authentication

The API uses two levels of authentication:

1. **API Key Authentication**: Client applications must provide a valid API key in the `x-api-key` header
2. **Provider Authentication**: Each provider implementation handles its own authentication with the external API (e.g., OAuth for Nextech)

## Error Handling

Errors are standardized across all providers. The API returns consistent error responses with the following format:

```json
{
  "status": 400,
  "code": "BAD_REQUEST",
  "message": "Invalid input",
  "details": {
    // Optional additional details about the error
  }
}
```

## Usage

### Selecting a Provider

Clients can select a specific provider by including the `x-provider` header in their request. If not specified, the default provider (Nextech) will be used.

```
GET /api/external-api/v2/clinics
x-api-key: your-api-key
x-provider: nextech
```

### API Endpoints

- `GET /api/external-api/v2/clinics` - Get all clinics
- `GET /api/external-api/v2/locations` - Get all locations
- `GET /api/external-api/v2/patients` - Get all patients
- `GET /api/external-api/v2/users` - Get all users/practitioners
- `GET /api/external-api/v2/appointments` - Get all appointments
- `POST /api/external-api/v2/appointments` - Create a new appointment
- `PATCH /api/external-api/v2/appointments/:id` - Update an appointment
- `DELETE /api/external-api/v2/appointments/:id` - Cancel an appointment
- `GET /api/external-api/v2/appointments/for-rescheduling` - Get appointments for a patient with rescheduling logic
- `POST /api/external-api/v2/appointments/send-confirmation` - Send SMS confirmation for an appointment
- `GET /api/external-api/v2/appointment-types` - Get all appointment types
- `GET /api/external-api/v2/appointment-availability` - Check appointment slot availability
- `POST /api/external-api/v2/finalize-conversation` - Finalize a conversation by creating a patient (if needed), creating an appointment, and saving call data

## Conversation Finalization

The `finalizeConversation` endpoint provides a way to complete a conversation workflow by:

1. Creating a new patient if they don't exist in the system (or using an existing patient if ID is provided)
2. Creating an appointment for the patient
3. Saving call data to the Firestore database

This endpoint is designed to be called at the end of a conversation with a patient, after all necessary information has been collected.

### Request Format

```json
{
  "patient": {
    "id": "string", // Optional - include if using existing patient
    "firstName": "John",
    "lastName": "Doe",
    "dateOfBirth": "1980-01-15",
    "gender": "male",
    "email": "<EMAIL>",
    "phoneNumber": "**********",
    "address": {
      "line1": "123 Main St",
      "line2": "Apt 4B",
      "city": "Chicago",
      "state": "IL",
      "postalCode": "60601",
      "country": "US"
    },
    "referralSourceId": "1",
    "notes": "New patient referral"
  },
  "appointment": {
    "practitionerId": "123",
    "locationId": "456",
    "startTime": "2023-12-15T14:30:00Z",
    "endTime": "2023-12-15T15:00:00Z",
    "type": "NEW_PATIENT",
    "reason": "Initial consultation",
    "notes": "Patient has questions about treatment options"
  },
  "call": {
    "sessionId": "call-session-123",
    "userId": "staff_789", // Optional
    "clinicId": 1, // Optional
    "date": "2023-12-01T10:15:00Z", // Optional
    "reason": "Appointment scheduling", // Optional
    "recordingUrl": "https://example.com/recordings/call123.mp3", // Optional
    "summary": "Patient called to schedule initial consultation", // Optional
    "transcription": "Detailed call transcription...", // Optional
    "priorityScore": 3, // Optional
    "urgent": false, // Optional
    "tags": ["new-patient", "consultation"] // Optional
  }
}
```

### Response Format

#### Success Response

```json
{
  "success": true,
  "message": "Conversation finalized successfully",
  "data": {
    "patient": {
      "id": "patient_123",
      "firstName": "TEST_John",
      "lastName": "TEST_Doe",
      "dateOfBirth": "1980-01-15",
      "email": "<EMAIL>",
      "phoneNumber": "**********",
      "gender": "male",
      "address": {
        "line1": "123 Main St",
        "line2": "Apt 4B",
        "city": "Chicago",
        "state": "IL",
        "postalCode": "60601",
        "country": "US"
      }
    },
    "appointment": {
      "id": "appointment_456",
      "patientId": "patient_123",
      "practitionerId": "123",
      "locationId": "456",
      "startTime": "2023-12-15T14:30:00Z",
      "endTime": "2023-12-15T15:00:00Z",
      "type": "NEW_PATIENT",
      "status": "booked"
    },
    "call": {
      "id": "call_789",
      "clientId": "patient_123",
      "userId": "staff_789",
      "clinicId": 1,
      "locationId": 1,
      "date": "2023-12-01T10:15:00Z",
      "reason": "Appointment scheduling",
      "recordingUrl": "https://example.com/recordings/call123.mp3",
      "sessionId": "call-session-123"
    }
  }
}
```

#### Partial Success Response (Call Creation Failed)

```json
{
  "success": true,
  "warning": "Call data could not be saved, but patient and appointment were created successfully",
  "warningCode": "CALL_CREATION_FAILED",
  "data": {
    "patient": {
      /* patient data */
    },
    "appointment": {
      /* appointment data */
    },
    "call": null
  }
}
```

### Error Handling

The endpoint provides detailed error responses to help the UI handle failures and retry with updated data when appropriate.

#### Appointment Slot Unavailable (Race Condition)

```json
{
  "status": 409,
  "code": "CONFLICT",
  "message": "Appointment slot is no longer available",
  "details": {
    "code": "APPOINTMENT_SLOT_UNAVAILABLE",
    "message": "The requested appointment time is no longer available",
    "suggestion": "Please select a different time slot",
    "patientId": "patient_123",
    "patientCreated": true
  }
}
```

#### Invalid Appointment Data

```json
{
  "status": 400,
  "code": "BAD_REQUEST",
  "message": "Invalid appointment data",
  "details": {
    "code": "INVALID_APPOINTMENT_DATA",
    "message": "The appointment data is invalid",
    "suggestion": "Check the appointment details and try again",
    "patientId": "patient_123",
    "patientCreated": false
  }
}
```

#### Patient Already Exists

```json
{
  "status": 409,
  "code": "CONFLICT",
  "message": "Patient already exists",
  "details": {
    "code": "PATIENT_ALREADY_EXISTS",
    "message": "A patient with this information already exists",
    "suggestion": "Try searching for the patient first or use a different email/phone"
  }
}
```
