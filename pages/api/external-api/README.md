# External API Documentation

This directory contains endpoints that are designed for external consumption by third-party services and integrations.

## API Structure

All endpoints use the following version-based structure:

- **Version**: v1 (Legacy), v2 (Current)
- **Base URL**:
  - V1: `/api/external-api/v1/...`
  - V2: `/api/external-api/v2/...`

## Authentication

All external API endpoints are protected with API key authentication:

```
x-api-key: YOUR_API_KEY
```

API keys should be requested from the system administrator and stored securely.

## V2 API Features

### Provider Selection

V2 API supports multiple providers. Clients can select a specific provider by including the `x-provider` header:

```
x-provider: nextech
```

If not specified, the default provider (Nextech) will be used.

### Standardized Pagination

All V2 list endpoints support standardized pagination with the following parameters:

- **limit**: Number of items per page (default: 10, max: 50)
- **offset**: Starting position for results (default: 0)

Example request:

```
GET /api/external-api/v2/patients?limit=20&offset=40
```

Responses include pagination metadata and navigation links:

```json
{
  "items": [...],
  "pagination": {
    "totalCount": 100,
    "limit": 20,
    "offset": 40,
    "hasMore": true,
    "links": {
      "first": "/api/external-api/v2/patients?limit=20&offset=0",
      "prev": "/api/external-api/v2/patients?limit=20&offset=20",
      "next": "/api/external-api/v2/patients?limit=20&offset=60",
      "last": "/api/external-api/v2/patients?limit=20&offset=80"
    }
  }
}
```

## Available Endpoints

### V1 API (Legacy)

See the v1 folder for specific endpoints. Major functionality includes:

- Appointment booking
- Appointment cancellation
- Calendar management
- User specialties
- Clinic information

### V2 API (Current)

See the v2 folder for specific endpoints. Major functionality includes:

- Patients management
- Appointments management (creation, updates, cancellation)
- Users and practitioners information
- Locations and clinics data
- Appointment types and availability

#### Special Endpoints

- `/api/external-api/v2/appointments/{id}/change`: Change appointment data by creating a new appointment with updated fields and canceling the original. This is a workaround for the Nextech API limitation where you can only update the status of an appointment.

All list endpoints in V2 support pagination and filtering.

## OpenAPI Documentation

### Comprehensive API Documentation

- Complete API reference: [openapi-external-api.yaml](./v1/openapi-external-api.yaml) - Contains documentation for all external API endpoints
- V2 API reference: [openapi-external-api-v2.yaml](./v2/openapi-external-api-v2.yaml) - Contains documentation for all V2 endpoints

### Functional Area Documentation

We also provide documentation organized by functional area:

- Appointment Management: [openapi-manage-appointment.yaml](./v1/openapi-manage-appointment.yaml) - Contains all appointment and calendar-related endpoints
- General Information: [openapi-general-info.yaml](./v1/openapi-general-info.yaml) - Contains clinic and user specialties endpoints

### Individual Endpoint Documentation

Detailed OpenAPI/Swagger documentation is also available for specific endpoint groups:

- Booking appointments: [openapi-book-appointment.yaml](./v1/openapi-book-appointment.yaml)
- Cancelling appointments: [openapi-cancel-appointment.yaml](./v1/openapi-cancel-appointment.yaml)

These files can be imported into Swagger UI, Postman, or other API tools for interactive documentation.

## Security Considerations

- External API endpoints only support API key authentication
- Rate limiting is applied to prevent abuse
  - V2 API complies with Nextech's rate limit of 20 requests per second per endpoint
- All requests are logged for audit purposes

## Getting Started

1. Request an API key from the system administrator
2. Include the API key in all requests as a header: `x-api-key: YOUR_API_KEY`
3. Refer to the Swagger documentation at `/api/swagger` for detailed API specs

## Example Usage

```javascript
// Example V1 API call
const fetchDataV1 = async () => {
  const response = await fetch('https://your-domain.com/api/external-api/v1/clinics', {
    method: 'GET',
    headers: {
      'x-api-key': 'YOUR_API_KEY',
    },
  });

  const data = await response.json();
  console.log(data);
};

// Example V2 API call with pagination
const fetchDataV2 = async (limit = 10, offset = 0) => {
  const response = await fetch(
    `https://your-domain.com/api/external-api/v2/patients?limit=${limit}&offset=${offset}`,
    {
      method: 'GET',
      headers: {
        'x-api-key': 'YOUR_API_KEY',
      },
    },
  );

  const data = await response.json();
  console.log(data);

  // Check if there are more pages
  if (data.pagination.hasMore) {
    console.log(`Next page available at: ${data.pagination.links.next}`);
  }
};
```

## Version Notes

The current recommended API is v2. V1 is maintained for backward compatibility.
