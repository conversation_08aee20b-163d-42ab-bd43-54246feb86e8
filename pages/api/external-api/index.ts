import { NextApiRequest, NextApiResponse } from 'next';

/**
 * @swagger
 * tags:
 *   name: External API
 *   description: External API endpoints - v1 and v2
 */

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Redirect all requests to v1 endpoints
  const targetPath = `/api/external-api/v1${req.url?.replace('/api/external-api', '') || ''}`;

  if (req.method === 'GET' && req.url === '/api/external-api') {
    return res.status(200).json({
      message: 'Front Desk Portal External API',
      documentation: '/api/swagger',
      versions: {
        v1: {
          baseEndpoint: '/api/external-api/v1',
          availableEndpoints: [
            '/api/external-api/v1/user-specialties',
            '/api/external-api/v1/appointments/active',
            '/api/external-api/v1/appointments/send-confirmation',
            '/api/external-api/v1/calendar/next-available',
            '/api/external-api/v1/calendar/add-appointment',
            '/api/external-api/v1/calendar/cancel-appointment',
            '/api/external-api/v1/clinics/by-phone',
            '/api/external-api/v1/calls/add-or-update-call-session',
          ],
        },
        v2: {
          baseEndpoint: '/api/external-api/v2',
          availableEndpoints: [
            '/api/external-api/v2/clinics',
            '/api/external-api/v2/locations',
            '/api/external-api/v2/appointments',
            '/api/external-api/v2/appointments/for-rescheduling',
            '/api/external-api/v2/appointments/send-confirmation',
            '/api/external-api/v2/calls/add-or-update-call-session',
          ],
        },
      },
    });
  }

  // For all other requests, redirect to the corresponding v1 endpoint
  return res.redirect(308, targetPath);
}
