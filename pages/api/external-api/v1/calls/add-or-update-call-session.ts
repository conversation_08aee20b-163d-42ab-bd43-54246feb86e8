import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import { callSessionsService } from '@/utils/firestore';

/**
 * @swagger
 * /api/external-api/v1/calls/add-or-update-call-session:
 *   post:
 *     summary: Add or update a call session
 *     description: Creates a new call session or updates an existing one based on the sessionId
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: Unique identifier for the call session
 *               hasVoiceMail:
 *                 type: boolean
 *                 description: Indicates if the call has a voicemail
 *               callType:
 *                 type: number
 *                 description: Enum value representing the type of call
 *     responses:
 *       200:
 *         description: Call session added or updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Call session added or updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "12345678-1234-1234-1234-123456789012"
 *                     sessionId:
 *                       type: string
 *                       example: "call-session-123"
 *                     hasVoiceMail:
 *                       type: boolean
 *                       example: true
 *                     callType:
 *                       type: number
 *                       example: 1
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T10:30:00Z"
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2023-06-15T10:35:00Z"
 *       400:
 *         description: Bad request - Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "sessionId is required"
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Unauthorized - Invalid or missing API key"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 error:
 *                   type: string
 *                   example: "Error adding or updating call session"
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method for this endpoint
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  try {
    // Extract and validate request body
    const { sessionId, hasVoiceMail, callType } = req.body;

    // Validate required fields
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        message: 'sessionId is required',
      });
    }

    // Validate data types
    if (hasVoiceMail !== undefined && typeof hasVoiceMail !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'hasVoiceMail must be a boolean',
      });
    }

    if (callType !== undefined && typeof callType !== 'number') {
      return res.status(400).json({
        success: false,
        message: 'callType must be a number',
      });
    }

    // Add or update the call session
    const callSession = await callSessionsService.addOrUpdateCallSession(sessionId, {
      hasVoiceMail,
      callType,
    });

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Call session added or updated successfully',
      data: callSession,
    });
  } catch (error) {
    console.error('Error adding or updating call session:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
