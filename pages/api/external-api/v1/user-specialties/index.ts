import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import admin from '@/utils/firebase-admin';

/**
 * @swagger
 * /api/external-api/v1/user-specialties:
 *   get:
 *     summary: Get all user specialties in a clinic
 *     description: Retrieves a list of user specialties for a specific clinic
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: clinicId
 *         schema:
 *           type: number
 *         required: true
 *         description: The ID of the clinic to get specialties from
 *     responses:
 *       200:
 *         description: A list of user specialties
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 specialties:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       name:
 *                         type: string
 *                       specialty:
 *                         type: string
 *       400:
 *         description: Bad request - Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check if clinicId is provided
    const clinicId = req.query.clinicId ? Number(req.query.clinicId) : undefined;

    if (!clinicId) {
      return res.status(400).json({ message: 'clinicId is required' });
    }

    // Get Firestore instance
    const db = admin.firestore();
    const staffCollection = db.collection('users');

    // Query staff collection for users in the given clinic
    const snapshot = await staffCollection.where('clinicId', '==', clinicId).get();

    // Extract specialties from each user
    const specialties = snapshot.docs
      .map(doc => {
        const data = doc.data();
        return {
          userId: doc.id,
          name: data.name || '',
          specialty: data.specialty || '',
        };
      })
      .filter(user => user.specialty); // Only return users with specialties

    return res.status(200).json({ specialties });
  } catch (error) {
    console.error('Error in user specialties API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
