import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import { appointmentsService } from '@/utils/firestore';
import { smsService } from '@/lib/services/sms-service';

/**
 * @swagger
 * /api/external-api/v1/appointments/send-confirmation:
 *   post:
 *     summary: Send SMS confirmation for an appointment
 *     description: Sends an SMS confirmation message to a patient about their appointment
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - appointmentId
 *               - phoneNumber
 *               - patientName
 *               - date
 *               - time
 *               - location
 *             properties:
 *               appointmentId:
 *                 type: string
 *                 description: ID of the appointment to confirm
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number to send the SMS to
 *               patientName:
 *                 type: string
 *                 description: Name of the patient
 *               date:
 *                 type: string
 *                 description: Date of the appointment
 *               time:
 *                 type: string
 *                 description: Time of the appointment
 *               location:
 *                 type: string
 *                 description: Location of the appointment
 *               provider:
 *                 type: string
 *                 description: Name of the provider (optional)
 *               customMessage:
 *                 type: string
 *                 description: Custom message to send instead of the default template (optional)
 *     responses:
 *       200:
 *         description: SMS confirmation sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Confirmation SMS sent successfully"
 *                 appointmentId:
 *                   type: string
 *                   example: "12345678-1234-1234-1234-123456789012"
 *                 messageSid:
 *                   type: string
 *                   example: "SM12345678901234567890123456789012"
 *       400:
 *         description: Bad request - Missing required parameters or invalid appointment
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "appointmentId is required"
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Unauthorized - Invalid or missing API key"
 *       404:
 *         description: Appointment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Appointment not found"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 error:
 *                   type: string
 *                   example: "Failed to send SMS"
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method for this endpoint
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract and validate request body
    const {
      appointmentId,
      phoneNumber,
      patientName,
      date,
      time,
      location,
      provider,
      customMessage,
    } = req.body;

    // Validate required fields
    if (!appointmentId) {
      return res.status(400).json({ message: 'appointmentId is required' });
    }
    if (!phoneNumber) {
      return res.status(400).json({ message: 'phoneNumber is required' });
    }
    if (!patientName) {
      return res.status(400).json({ message: 'patientName is required' });
    }
    if (!date) {
      return res.status(400).json({ message: 'date is required' });
    }
    if (!time) {
      return res.status(400).json({ message: 'time is required' });
    }
    if (!location) {
      return res.status(400).json({ message: 'location is required' });
    }

    // Verify that the appointment exists
    const appointment = await appointmentsService.getAppointmentById(appointmentId);
    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Send the SMS
    let messageSid: string;
    if (customMessage) {
      // Send custom message
      messageSid = await smsService.sendSms(phoneNumber, customMessage);
    } else {
      // Send default appointment confirmation template
      messageSid = await smsService.sendAppointmentConfirmation(phoneNumber, {
        patientName,
        date,
        time,
        location,
        provider,
      });
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Confirmation SMS sent successfully',
      appointmentId,
      messageSid,
    });
  } catch (error) {
    console.error('Error sending SMS confirmation:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
