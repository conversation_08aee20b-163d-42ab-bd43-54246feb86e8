import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import { appointmentsService, userService } from '@/utils/firestore';

/**
 * @swagger
 * /api/external-api/v1/appointments/active:
 *   post:
 *     summary: Get active appointments for a client
 *     description: Retrieves the latest 5 active appointments for a client by name and date of birth, including the associated user data
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the client
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Client's date of birth in ISO format (optional, for more precise matching)
 *     responses:
 *       200:
 *         description: Active appointments for the client with associated user data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 appointments:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       clientId:
 *                         type: string
 *                       slotId:
 *                         type: string
 *                       callId:
 *                         type: string
 *                       date:
 *                         type: string
 *                       time:
 *                         type: string
 *                       status:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *                       user:
 *                         type: object
 *                         nullable: true
 *                         properties:
 *                           id:
 *                             type: string
 *                           email:
 *                             type: string
 *                           name:
 *                             type: string
 *                           role:
 *                             type: string
 *                           clinicId:
 *                             type: number
 *                             nullable: true
 *                           specialty:
 *                             type: string
 *                             nullable: true
 *                           canTakeAppointments:
 *                             type: boolean
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                           updatedAt:
 *                             type: string
 *                             format: date-time
 *       400:
 *         description: Bad request - Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method for this endpoint
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract and validate request body
    const { fullName, dateOfBirth } = req.body;

    if (!fullName) {
      return res.status(400).json({ message: 'fullName is required' });
    }

    // Get active appointments for the client
    const appointments = await appointmentsService.getActiveAppointmentsByClient(
      fullName,
      dateOfBirth,
    );

    // Format the appointments and get user data
    const appointmentsWithUsers = await Promise.all(
      appointments.map(async appointment => ({
        ...appointment,
        createdAt: appointment.createdAt.toISOString(),
        updatedAt: appointment.updatedAt.toISOString(),
        user: await userService.getUserById(appointment.userId),
      })),
    );

    return res.status(200).json({ appointments: appointmentsWithUsers });
  } catch (error) {
    console.error('Error in get active appointments API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
