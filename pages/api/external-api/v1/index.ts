import { NextApiRequest, NextApiResponse } from 'next';

/**
 * @swagger
 * tags:
 *   name: External API v1
 *   description: Version 1 of endpoints available for external service integration
 */

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  res.status(200).json({
    message: 'Front Desk Portal External API - v1',
    documentation: '/api/swagger',
    version: '1.0.0',
    availableEndpoints: ['/api/external-api/v1/clinics/by-phone'],
  });
}
