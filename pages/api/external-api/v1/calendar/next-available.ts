import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import { calendarSlotsService } from '@/utils/firestore';
import { CalendarSlot } from '@/models/CalendarSlot';

/**
 * @swagger
 * /api/external-api/v1/calendar/next-available:
 *   get:
 *     summary: Get the next available time slot
 *     description: Retrieves the next available time slot for a user, optionally starting from a specific date or after a specific slot
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the user to find available slots for
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Optional date to start looking for available slots (format YYYY-MM-DD)
 *       - in: query
 *         name: slotId
 *         schema:
 *           type: string
 *         description: Optional slot ID to find the next available slot after
 *     responses:
 *       200:
 *         description: Next available time slot
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 slot:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     date:
 *                       type: string
 *                     time:
 *                       type: string
 *                     slotId:
 *                       type: string
 *       400:
 *         description: Bad request - Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: No available slots found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract and validate query parameters
    const { userId, date, slotId } = req.query;

    if (!userId || typeof userId !== 'string') {
      return res.status(400).json({ message: 'userId is required' });
    }

    // If date is provided, validate it
    let startDate: string;
    if (date && typeof date === 'string') {
      // Validate date format (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        return res.status(400).json({ message: 'Invalid date format. Use YYYY-MM-DD' });
      }
      startDate = date;
    } else {
      // Use today's date if not provided
      const today = new Date();
      startDate = today.toISOString().split('T')[0];
    }

    // Get all calendar slots for this user from the start date
    const slots = await getAllFutureSlots(userId, startDate);

    if (slots.length === 0) {
      return res.status(404).json({ message: 'No available slots found for this user' });
    }

    // Find the next available slot
    const nextSlot = findNextAvailableSlot(slots, slotId as string | undefined);

    if (!nextSlot) {
      return res.status(404).json({ message: 'No available slots found' });
    }

    return res.status(200).json({ slot: nextSlot });
  } catch (error) {
    console.error('Error in next available slot API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Helper function to get all future slots for a user
async function getAllFutureSlots(userId: string, startDate: string): Promise<CalendarSlot[]> {
  // We'll query for all slots for this user from the start date
  // Note: In a production system with many slots, we might want to limit this or use pagination
  const allSlots = await calendarSlotsService.getSlotsByUserId(userId);

  // Filter slots to only include those on or after the start date
  return allSlots.filter(slot => slot.date >= startDate);
}

// Find the next available slot
function findNextAvailableSlot(
  slots: CalendarSlot[],
  afterSlotId?: string,
): { userId: string; date: string; time: string; slotId: string } | null {
  // Sort slots by date (and implicitly by time within each date)
  const sortedSlots = [...slots].sort((a, b) => a.date.localeCompare(b.date));

  let foundTargetSlot = afterSlotId ? false : true;

  // Iterate through all slots and their time slots
  for (const calendarSlot of sortedSlots) {
    // Sort time slots by time
    const sortedTimeSlots = [...calendarSlot.timeSlots].sort((a, b) =>
      a.time.localeCompare(b.time),
    );

    for (const timeSlot of sortedTimeSlots) {
      // If we're looking for a slot after a specific one, skip until we find it
      if (afterSlotId && !foundTargetSlot) {
        if (timeSlot.id === afterSlotId) {
          foundTargetSlot = true;
        }
        continue;
      }

      // If this slot is available, return it
      if (timeSlot.isAvailable) {
        return {
          userId: calendarSlot.userId,
          date: calendarSlot.date,
          time: timeSlot.time,
          slotId: timeSlot.id,
        };
      }
    }
  }

  // No available slot found
  return null;
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
