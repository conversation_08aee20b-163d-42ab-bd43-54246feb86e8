import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import {
  appointmentsService,
  calendarSlotsService,
  clientsService,
  callsService,
} from '@/utils/firestore';
import { v4 as uuidv4 } from 'uuid';
import { Client } from '@/models/Client';
import { Call } from '@/models/Call';

interface AddAppointmentRequest {
  userId: string;
  slotId: string;
  client: Client;
  call: Call;
}

/**
 * @swagger
 * /api/external-api/v1/calendar/add-appointment:
 *   post:
 *     summary: Add a new appointment
 *     description: Creates a new appointment and marks the selected time slot as unavailable
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - slotId
 *               - client
 *               - call
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user (provider) for the appointment
 *               slotId:
 *                 type: string
 *                 description: ID of the time slot to book
 *               client:
 *                 type: object
 *                 description: Client information
 *                 required:
 *                   - fullName
 *                   - birthday
 *                   - phoneNumber
 *                   - insuranceCompany
 *                   - insuranceGroupNumber
 *                   - subscriberName
 *                   - clinicId
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: UUID v4 format (optional for new clients)
 *                   fullName:
 *                     type: string
 *                     description: Full name of the client
 *                   birthday:
 *                     type: string
 *                     format: date
 *                     description: Date of birth of the client (YYYY-MM-DD)
 *                   phoneNumber:
 *                     type: string
 *                     description: Contact phone number
 *                   email:
 *                     type: string
 *                     format: email
 *                     description: Email address
 *                   medicalHistory:
 *                     type: string
 *                     description: Medical history notes
 *                   recentNotes:
 *                     type: string
 *                     description: Recent notes about the client
 *                   insuranceCompany:
 *                     type: string
 *                     description: Name of insurance company
 *                   insuranceGroupNumber:
 *                     type: string
 *                     description: Insurance group number
 *                   subscriberName:
 *                     type: string
 *                     description: Name of the insurance subscriber
 *                   listOfCalls:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Array of Call UUIDs
 *                   clinicId:
 *                     type: number
 *                     description: For clinic-specific scoping
 *               call:
 *                 type: object
 *                 description: Call information
 *                 required:
 *                   - userId
 *                   - clinicId
 *                   - date
 *                   - summary
 *                   - transcription
 *                   - recordingUrl
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: UUID v4 format (optional for new calls)
 *                   clientId:
 *                     type: string
 *                     description: UUID v4 of the client (will be set automatically)
 *                   userId:
 *                     type: string
 *                     description: ID of staff member who handled the call
 *                   clinicId:
 *                     type: number
 *                     description: ID of the clinic this call belongs to
 *                   date:
 *                     type: string
 *                     format: date-time
 *                     description: Date and time of the call
 *                   reason:
 *                     type: string
 *                     description: Reason for the call
 *                   summary:
 *                     type: string
 *                     description: Summary of the call
 *                   transcription:
 *                     type: string
 *                     description: Full transcription of the call
 *                   recordingUrl:
 *                     type: string
 *                     description: URL to the call recording
 *                   notes:
 *                     type: string
 *                     description: Additional notes about the call
 *                   priorityScore:
 *                     type: number
 *                     description: Priority score (higher means higher priority)
 *                   urgent:
 *                     type: boolean
 *                     description: Indicates if the call is urgent
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of tags associated with the call
 *     responses:
 *       201:
 *         description: Appointment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 appointmentId:
 *                   type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - Missing required parameters or slot not available
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method for this endpoint
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract and validate request body
    const { userId, slotId, client, call }: AddAppointmentRequest = req.body;

    // Basic validation
    if (!userId) {
      return res.status(400).json({ message: 'userId is required' });
    }
    if (!slotId) {
      return res.status(400).json({ message: 'slotId is required' });
    }
    if (!client) {
      return res.status(400).json({ message: 'client information is required' });
    }
    if (!call) {
      return res.status(400).json({ message: 'call information is required' });
    }

    // Find which calendar slot contains the requested slot ID
    const userSlots = await calendarSlotsService.getSlotsByUserId(userId);

    // Find the specific calendar slot and time slot
    let calendarSlotId: string | null = null;
    let calendarDate: string | null = null;
    let timeSlotDetails: { time: string; isAvailable: boolean } | null = null;

    for (const calendarSlot of userSlots) {
      const timeSlot = calendarSlot.timeSlots.find(ts => ts.id === slotId);
      if (timeSlot) {
        calendarSlotId = calendarSlot.id;
        calendarDate = calendarSlot.date;
        timeSlotDetails = {
          time: timeSlot.time,
          isAvailable: timeSlot.isAvailable,
        };
        break;
      }
    }

    // Check if the slot was found
    if (!calendarSlotId || !calendarDate || !timeSlotDetails) {
      return res.status(400).json({ message: 'Slot not found' });
    }

    // Check if the slot is available
    if (!timeSlotDetails.isAvailable) {
      return res.status(400).json({ message: 'Slot is not available' });
    }

    // Search for existing client by name and birthday
    const existingClient = await clientsService.findClientByNameAndBirthday(
      client.fullName,
      client.birthday,
    );

    let clientId: string;
    // Process client data - Create if doesn't exist, or update if it does
    if (existingClient) {
      // Use the existing client's ID
      clientId = existingClient.id;
      // Update the existing client with any new information
      await clientsService.updateClient(clientId, client);
    } else {
      // No existing client found, create a new one
      const newClient = await clientsService.createClient(client);
      clientId = newClient.id;
    }

    // Ensure client has the correct ID
    client.id = clientId;

    // Create call record
    const callId = call.id || uuidv4();
    call.id = callId;
    call.clientId = clientId;
    await callsService.createCall(call);

    // Create appointment
    const appointment = await appointmentsService.createAppointment({
      userId,
      clientId,
      clientName: client.fullName,
      slotId,
      callId,
      date: calendarDate,
      time: timeSlotDetails.time,
      status: 'active',
    });

    // Mark the slot as unavailable
    await calendarSlotsService.updateSlotAvailability(
      userId,
      calendarDate,
      slotId,
      false, // Mark as unavailable
    );

    return res.status(201).json({
      appointmentId: appointment.id,
      message: 'Appointment created successfully',
    });
  } catch (error) {
    console.error('Error in add appointment API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
