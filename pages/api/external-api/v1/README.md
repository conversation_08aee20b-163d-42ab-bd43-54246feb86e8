# External API v1 Documentation

This is version 1 of the External API, providing stable endpoints for third-party integrations.

## Available Endpoints

### User Specialties

- `GET /api/external-api/v1/user-specialties` - Get all user specialties in a specific clinic

### Appointments

- `POST /api/external-api/v1/appointments/active` - Get active appointments for a client by name and date of birth

### Calendar

- `GET /api/external-api/v1/calendar/next-available` - Get the next available time slot for a user
- `POST /api/external-api/v1/calendar/add-appointment` - Add a new appointment
- `POST /api/external-api/v1/calendar/cancel-appointment` - Cancel an existing appointment

### Clinics

- `GET /api/external-api/v1/clinics/by-phone` - Get a clinic by its phone number

## Authentication

All endpoints require API key authentication via the `x-api-key` header:

```
x-api-key: YOUR_API_KEY
```

## Data Schemas

### Calendar Slot Object

```typescript
{
  id: string; // UUID v4 format
  userId: string; // ID of the user this slot belongs to
  date: string; // Date in ISO format, e.g. "2025-01-30"
  timeSlots: {
    // Array of time slots
    id: string; // UUID v4 for the time slot
    time: string; // Time in 24-hour format, e.g. "09:00"
    isAvailable: boolean; // Whether the slot is available
  }
  [];
}
```

### Appointment Object

```typescript
{
  id: string; // UUID v4 format
  userId: string; // ID of the user (provider)
  clientId: string; // UUID v4 of the client
  slotId: string; // ID of the time slot
  callId: string; // ID of the related call
  date: string; // Date in ISO format
  time: string; // Time in 24-hour format
  status: string; // "active", "completed", or "cancelled"
  createdAt: string; // ISO date-time when created
  updatedAt: string; // ISO date-time when last updated
}
```

## Request Examples

### Get Active Appointments

```bash
curl -X POST https://your-domain.com/api/external-api/v1/appointments/active \
  -H "Content-Type: application/json" \
  -H "x-api-key: YOUR_API_KEY" \
  -d '{
    "fullName": "Jane Doe",
    "dateOfBirth": "1980-01-15"
  }'
```

### Get Next Available Slot

```bash
curl -X GET "https://your-domain.com/api/external-api/v1/calendar/next-available?userId=staff_123&date=2025-01-30" \
  -H "x-api-key: YOUR_API_KEY"
```

### Add Appointment

```bash
curl -X POST https://your-domain.com/api/external-api/v1/calendar/add-appointment \
  -H "Content-Type: application/json" \
  -H "x-api-key: YOUR_API_KEY" \
  -d '{
    "userId": "staff_123",
    "slotId": "slot_456",
    "client": {
      "id": "12345678-1234-1234-1234-123456789012",
      "fullName": "Jane Doe",
      "birthday": "1980-01-15",
      "phoneNumber": "+15551234567",
      "insuranceCompany": "Health Insurance Inc",
      "insuranceGroupNumber": "HI12345",
      "subscriberName": "Jane Doe",
      "clinicId": 42
    },
    "call": {
      "userId": "staff_123",
      "clinicId": 42,
      "date": "2023-04-15T14:30:00Z",
      "summary": "Scheduling an appointment",
      "transcription": "Transcription text goes here...",
      "recordingUrl": "https://storage.example.com/recordings/call123.mp3"
    }
  }'
```

## Changelog

- **v1.0.0** (2023-04-15): Initial stable release
- **v1.1.0** (2025-01-30): Added calendar and appointments endpoints
