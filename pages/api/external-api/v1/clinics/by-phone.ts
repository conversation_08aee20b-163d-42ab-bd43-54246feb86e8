import { NextApiRequest, NextApiResponse } from 'next';
import { withExternal<PERSON>piAuth } from '@/utils/middleware/externalApiAuth';
import admin from '@/utils/firebase-admin';
import { Clinic } from '@/models/auth';

/**
 * @swagger
 * /api/external-api/v1/clinics/by-phone:
 *   get:
 *     summary: Get a clinic by phone number
 *     description: Retrieves clinic information by its phone number
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: phone
 *         schema:
 *           type: string
 *         required: true
 *         description: The phone number of the clinic to find
 *     responses:
 *       200:
 *         description: Clinic information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 clinic:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: number
 *                     name:
 *                       type: string
 *                     logoUrl:
 *                       type: string
 *                     address:
 *                       type: string
 *                     phone:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                     updatedAt:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Bad request - Missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Clinic not found with the provided phone number
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Check if phone number is provided
    const phone = req.query.phone;

    if (!phone || typeof phone !== 'string') {
      return res.status(400).json({ message: 'Valid phone number is required' });
    }

    // Get Firestore instance
    const db = admin.firestore();
    const clinicsCollection = db.collection('clinics');

    // Query clinics collection for the clinic with the given phone number
    const snapshot = await clinicsCollection.where('phone', '==', phone).limit(1).get();

    if (snapshot.empty) {
      return res.status(404).json({ message: 'No clinic found with the provided phone number' });
    }

    // Get the first matching clinic
    const doc = snapshot.docs[0];
    const data = doc.data();

    // Format dates for response
    const clinic: Clinic = {
      id: parseInt(doc.id, 10),
      name: data.name,
      logoUrl: data.logoUrl || undefined,
      address: data.address || undefined,
      phone: data.phone || undefined,
      createdAt: data.createdAt.toDate(),
      updatedAt: data.updatedAt.toDate(),
    };

    // Return the clinic with formatted dates for JSON
    return res.status(200).json({
      clinic: {
        ...clinic,
        createdAt: clinic.createdAt.toISOString(),
        updatedAt: clinic.updatedAt.toISOString(),
      },
    });
  } catch (error) {
    console.error('Error in get clinic by phone API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
