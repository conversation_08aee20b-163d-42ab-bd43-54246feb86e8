import { NextApiRequest, NextApiResponse } from 'next';
import { withExternalApiAuth } from '@/utils/middleware/externalApiAuth';
import { locationsService } from '@/utils/firestore';

/**
 * @swagger
 * /api/external-api/v1/clinics/locations:
 *   get:
 *     summary: Get all locations for a clinic
 *     description: Retrieves all physical locations associated with a specific clinic
 *     tags: [External API v1]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - name: clinicId
 *         in: query
 *         description: The ID of the clinic to get locations for
 *         required: true
 *         schema:
 *           type: number
 *     responses:
 *       200:
 *         description: List of clinic locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 locations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: UUID v4 format
 *                       clinicId:
 *                         type: number
 *                         description: References the parent Clinic.id
 *                       name:
 *                         type: string
 *                         description: Name of the location
 *                       address:
 *                         type: string
 *                         description: Physical address of the location
 *                       phone:
 *                         type: string
 *                         description: Contact phone number for this location
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       updatedAt:
 *                         type: string
 *                         format: date-time
 *       400:
 *         description: Bad request - Missing required parameters
 *       401:
 *         description: Unauthorized - Invalid or missing API key
 *       404:
 *         description: No locations found for the specified clinic
 *       500:
 *         description: Server error
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method for this endpoint
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract and validate request parameters
    const clinicId = req.query.clinicId;

    if (!clinicId) {
      return res.status(400).json({ message: 'clinicId is required' });
    }

    // Convert clinicId to number (it comes as string from query params)
    const clinicIdNumber = parseInt(clinicId as string, 10);
    if (isNaN(clinicIdNumber)) {
      return res.status(400).json({ message: 'clinicId must be a valid number' });
    }

    // Get locations for the clinic
    const locations = await locationsService.getLocationsByClinicId(clinicIdNumber);

    // If no locations found, return 404
    if (!locations || locations.length === 0) {
      return res.status(404).json({
        message: 'No locations found for the specified clinic',
      });
    }

    // Return the locations
    return res.status(200).json({ locations });
  } catch (error) {
    console.error('Error in get clinic locations API:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}

// Wrap the handler with the API key authentication middleware
export default withExternalApiAuth(handler);
