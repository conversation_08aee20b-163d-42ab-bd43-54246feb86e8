import { NextApiRequest, NextApiResponse } from 'next';
import { callsService } from '@/utils/firestore';
import { verifyAuthAndGetUserWithLocationContext } from '@/utils/firebase-admin';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

/**
 * Debug endpoint to diagnose ordering/pagination issues with calls
 * GET /api/debug/calls-diagnosis
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    console.log('🔍 Starting ordering/pagination calls diagnosis...');

    // Get user's location context
    const userContext = await verifyAuthAndGetUserWithLocationContext(req);

    if (!userContext) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { user, currentLocation } = userContext;

    console.log('🔍 User context:', {
      userId: user.id,
      currentLocationId: user.currentLocationId,
      currentLocationName: currentLocation?.name,
      clinicId: user.clinicId,
    });

    // 1. Get most recent calls with no filters to see what's truly newest
    console.log('🔍 Getting most recent calls (no filters)...');
    const recentCallsNoFilters = await callsService.paginateCalls({
      limit: 10,
    });

    // 2. Get most recent calls with location filter
    console.log('🔍 Getting most recent calls (with location filter)...');
    const recentCallsWithLocation = await callsService.paginateCalls({
      limit: 10,
      locationId: currentLocation?.id,
    });

    // 3. Get calls from the specific date range the user mentioned
    const startDate = dayjs.utc('2025-05-30T17:56').toDate();
    const endDate = dayjs.utc('2025-06-02T17:56').toDate();

    console.log('🔍 Getting calls in user-specified range with location filter...');
    const rangeCallsWithLocation = await callsService.paginateCalls({
      limit: 10,
      startDate,
      endDate,
      locationId: currentLocation?.id,
    });

    // 4. Get calls in the same range WITHOUT location filter to compare
    console.log('🔍 Getting calls in user-specified range WITHOUT location filter...');
    const rangeCallsNoLocation = await callsService.paginateCalls({
      limit: 10,
      startDate,
      endDate,
    });

    // 5. Check calls from June 2nd specifically (the date user says has newer calls)
    const june2Start = dayjs.utc('2025-06-02T00:00').toDate();
    const june2End = dayjs.utc('2025-06-02T23:59').toDate();

    console.log('🔍 Getting calls specifically from June 2nd...');
    const june2Calls = await callsService.paginateCalls({
      limit: 10,
      startDate: june2Start,
      endDate: june2End,
      locationId: currentLocation?.id,
    });

    // 6. Same for June 2nd without location filter
    console.log('🔍 Getting calls from June 2nd WITHOUT location filter...');
    const june2CallsNoLocation = await callsService.paginateCalls({
      limit: 10,
      startDate: june2Start,
      endDate: june2End,
    });

    return res.status(200).json({
      success: true,
      userContext: {
        userId: user.id,
        currentLocationId: user.currentLocationId,
        currentLocationName: currentLocation?.name,
        clinicId: user.clinicId,
      },

      // Most recent calls comparison
      mostRecentCalls: {
        withoutFilters: {
          total: recentCallsNoFilters.calls.length,
          newestCallDate: recentCallsNoFilters.calls[0]?.date.toISOString(),
          calls: recentCallsNoFilters.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
        withLocationFilter: {
          total: recentCallsWithLocation.calls.length,
          newestCallDate: recentCallsWithLocation.calls[0]?.date.toISOString(),
          calls: recentCallsWithLocation.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
      },

      // Date range calls comparison
      dateRangeCalls: {
        withLocationFilter: {
          total: rangeCallsWithLocation.calls.length,
          calls: rangeCallsWithLocation.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
        withoutLocationFilter: {
          total: rangeCallsNoLocation.calls.length,
          calls: rangeCallsNoLocation.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
      },

      // June 2nd specific calls
      june2Calls: {
        withLocationFilter: {
          total: june2Calls.calls.length,
          calls: june2Calls.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
        withoutLocationFilter: {
          total: june2CallsNoLocation.calls.length,
          calls: june2CallsNoLocation.calls.map(call => ({
            id: call.id,
            date: call.date.toISOString(),
            localTime: new Date(call.date).toLocaleString('en-US', {
              timeZone: 'America/New_York',
            }),
            locationId: call.locationId,
            clinicId: call.clinicId,
            phoneNumber: call.phoneNumber,
          })),
        },
      },

      testParameters: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        june2Start: june2Start.toISOString(),
        june2End: june2End.toISOString(),
      },
    });
  } catch (error) {
    console.error('❌ Diagnosis failed:', error);
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
  }
}
