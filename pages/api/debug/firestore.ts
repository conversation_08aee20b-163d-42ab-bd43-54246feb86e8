import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { checkFirestoreCollections } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const collections = await checkFirestoreCollections();
    return res.status(200).json({
      success: true,
      ...collections,
      message: 'Firestore collections checked successfully',
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Error checking Firestore collections',
    });
  }
}
