import type { NextApiRequest, NextApiResponse } from 'next';
import { callsService } from '@/utils/firestore';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * Debug endpoint to diagnose pagination issues
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const {
      clinicId,
      locationId,
      targetDate = '2025-06-02',
      startDate,
      endDate,
      limit = '10',
      type = 'date', // 'basic' or 'date'
      callDirection,
      officeHoursOnly,
    } = req.query;

    if (type === 'basic') {
      // Run basic diagnosis
      const diagnosis = await callsService.diagnoseFetchIssues();
      return res.status(200).json(diagnosis);
    } else {
      // Run date-specific diagnosis
      const params = {
        clinicId: clinicId ? parseInt(clinicId as string) : undefined,
        locationId: locationId as string,
        targetDate: targetDate as string,
        limit: parseInt(limit as string),
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        callDirection: (callDirection as 'inbound' | 'outbound' | 'both') || 'both',
        officeHoursOnly: officeHoursOnly === 'true',
      };

      logger.info({ context: 'pagination-diagnosis', params }, 'Running date pagination diagnosis');

      const diagnosis = await callsService.diagnoseDatePaginationIssue(params);
      return res.status(200).json(diagnosis);
    }
  } catch (error) {
    logger.error(
      {
        context: 'pagination-diagnosis',
        error: error instanceof Error ? error.message : String(error),
      },
      'Diagnosis failed',
    );

    return res.status(500).json({
      error: 'Failed to run pagination diagnosis',
      details: error instanceof Error ? error.message : String(error),
    });
  }
}
