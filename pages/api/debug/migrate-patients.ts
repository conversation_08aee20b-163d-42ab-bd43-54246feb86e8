import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { migratePatientToClientCollection } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const result = await migratePatientToClientCollection();
    return res.status(200).json({
      success: true,
      ...result,
      message: `Successfully migrated ${result.migrated} patients to clients collection`,
    });
  } catch (error) {
    console.error('Error in migration endpoint:', error);
    return res.status(500).json({
      success: false,
      message: 'Error migrating patients to clients',
    });
  }
}
