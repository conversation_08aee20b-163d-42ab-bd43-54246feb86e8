import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { mailService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify the user is authenticated
    const email = req.body.email;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Generate a verification link for the given email
    // Extract the origin from request headers, or fall back to localhost for development
    // We use req.headers.origin instead of environment variables because:
    // 1. It automatically adapts to different environments (dev, staging, prod)
    // 2. It works correctly even when the app is accessed from different domains
    // 3. It doesn't require configuration of environment variables
    const origin = req.headers.origin || 'http://localhost:3000';
    const continueUrl = `${origin}/login`;

    const actionCodeSettings = {
      url: continueUrl,
      handleCodeInApp: false,
    };

    const user = await admin
      .auth()
      .getUserByEmail(email)
      .catch(err => {
        if (err.errorInfo.code === 'auth/user-not-found') {
          return null;
        }
        throw err;
      });

    if (!user) {
      return res.status(400).json({ message: 'Email not found' });
    }

    const link = await admin.auth().generatePasswordResetLink(email, actionCodeSettings);

    await mailService.sendPasswordResetEmail(email, link);

    // In a production app, you would send this email using your email service
    // For now, we'll just return it in the response
    return res.status(200).json({
      success: true,
      message: 'Password reset email sent',
    });
  } catch (error) {
    console.error('Error sending verification email:', error);
    return res.status(500).json({ message: 'Failed to send verification email' });
  }
}
