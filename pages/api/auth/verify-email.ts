import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { mailService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify the user is authenticated
    const user = await verifyAuthAndGetUser(req);

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the Firebase user
    const firebaseUser = await admin.auth().getUser(user.id);

    // Generate a verification link for the current email
    // Extract the origin from request headers, or fall back to localhost for development
    // We use req.headers.origin instead of environment variables because:
    // 1. It automatically adapts to different environments (dev, staging, prod)
    // 2. It works correctly even when the app is accessed from different domains
    // 3. It doesn't require configuration of environment variables
    const origin = req.headers.origin || 'http://localhost:3000';
    const continueUrl = `${origin}/dashboard/profile`;

    const actionCodeSettings = {
      url: continueUrl,
      handleCodeInApp: false,
    };

    const link = await admin
      .auth()
      .generateEmailVerificationLink(firebaseUser.email || '', actionCodeSettings);

    if (firebaseUser.email) {
      await mailService.sendVerificationEmail(firebaseUser.email, link);
    }

    // In a production app, you would send this email using your email service
    // For now, we'll just return it in the response
    return res.status(200).json({
      success: true,
      message: 'Verification email sent',
      verificationLink: link,
    });
  } catch (error) {
    console.error('Error sending verification email:', error);
    return res.status(500).json({ message: 'Failed to send verification email' });
  }
}
