import { NextApiRequest, NextApiResponse } from 'next';
import { otpService } from '@/utils/firestore';

/**
 * This endpoint is used to check if the 2FA verification code is valid by email and code.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Extract the email and code from the request body
    const { email, code } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }
    if (!code) {
      return res.status(400).json({ message: 'Code is required' });
    }

    const isValid = await otpService.verifyOtp(email, code);
    if (!isValid) {
      return res.status(400).json({ message: 'Invalid code' });
    }

    // Set the 2FA completed flag
    await otpService.set2FACompleted(email);

    return res.status(200).json({
      success: true,
      message: '2FA verification completed',
    });
  } catch (error) {
    console.error('Error verifying 2FA:', error);
    return res.status(500).json({ message: 'Failed to verify 2FA' });
  }
}
