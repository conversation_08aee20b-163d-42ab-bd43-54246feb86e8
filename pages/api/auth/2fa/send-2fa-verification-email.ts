import { NextApiRequest, NextApiResponse } from 'next';
import { generateRandomCode } from '@/utils/common';
import { otpService, mailService } from '@/utils/firestore';
import { TWO_FA_CODE_LENGTH, TWO_FA_CODE_TTL_IN_SECONDS } from '@/app-config';

/**
 * This endpoint is used to send a 2FA verification email to the user.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { email } = req.body;
    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    const canSendOtp = await otpService.canSendOtp(email);
    if (!canSendOtp) {
      return res.status(429).json({ message: 'Rate limit exceeded' });
    }

    const codeTtlInMinutes = Math.floor(TWO_FA_CODE_TTL_IN_SECONDS / 60);
    const code = generateRandomCode(TWO_FA_CODE_LENGTH);
    await otpService.saveOtp(email, code);
    await mailService.send2FAVerificationEmail(email, code, codeTtlInMinutes);

    return res.status(200).json({
      success: true,
      message: '2FA verification email sent',
    });
  } catch (error) {
    console.error('Error sending 2FA verification email:', error);
    return res.status(500).json({ message: 'Failed to send 2FA verification email' });
  }
}
