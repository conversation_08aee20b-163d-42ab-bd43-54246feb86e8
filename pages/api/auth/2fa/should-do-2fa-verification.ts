import { NextApiRequest, NextApiResponse } from 'next';
import { otpService } from '@/utils/firestore';
import { TWO_FA_ENABLED } from '@/app-config';

/**
 * This endpoint is used to check if the user should do 2FA verification.
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    if (!TWO_FA_ENABLED) {
      return res.status(200).json({
        isEligibleToLogin: true,
        message: 'Two factor authentication is disabled',
      });
    }

    const { email } = req.body;
    if (!email) {
      return res.status(400).json({
        isEligibleToLogin: false,
        message: 'Email is required',
      });
    }

    const shouldDo2FA = await otpService.shouldDo2FA(email);
    if (shouldDo2FA) {
      return res.status(200).json({
        isEligibleToLogin: false,
        message: 'The user should go through 2FA verification flow',
      });
    }

    return res.status(200).json({
      isEligibleToLogin: true,
      message: 'The user is eligible to login',
    });
  } catch (error) {
    console.error('Error checking if user should do 2FA verification:', error);
    return res.status(500).json({ message: 'Failed to check if user should do 2FA verification' });
  }
}
