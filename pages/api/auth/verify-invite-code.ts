import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        valid: false,
        message: 'Invite code is required',
      });
    }

    // Query for the code
    const codeSnapshot = await admin
      .firestore()
      .collection('staffInviteCodes')
      .where('code', '==', code.toUpperCase().trim())
      .where('used', '==', false)
      .get();

    if (codeSnapshot.empty) {
      return res.status(200).json({
        valid: false,
        message: 'Invalid invite code. Please check and try again.',
      });
    }

    const codeDoc = codeSnapshot.docs[0];
    const codeData = codeDoc.data();

    // Check if code is expired
    const now = new Date();
    const expiresAt = codeData.expiresAt.toDate();

    if (now > expiresAt) {
      return res.status(200).json({
        valid: false,
        message: 'This invite code has expired. Please request a new one.',
      });
    }

    return res.status(200).json({
      valid: true,
      clinicId: codeData.clinicId,
    });
  } catch (error) {
    console.error('Error verifying staff invite code:', error);
    return res.status(500).json({
      valid: false,
      message: 'An error occurred while verifying the invite code.',
    });
  }
}
