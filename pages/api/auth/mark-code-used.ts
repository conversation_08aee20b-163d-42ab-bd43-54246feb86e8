import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'Invite code is required',
      });
    }

    // Query for the code
    const codeSnapshot = await admin
      .firestore()
      .collection('staffInviteCodes')
      .where('code', '==', code.toUpperCase().trim())
      .where('used', '==', false)
      .get();

    if (codeSnapshot.empty) {
      return res.status(200).json({
        success: false,
        message: 'Invalid invite code or already used.',
      });
    }

    const codeDoc = codeSnapshot.docs[0];

    // Update the code as used
    await admin.firestore().collection('staffInviteCodes').doc(codeDoc.id).update({
      used: true,
      usedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return res.status(200).json({
      success: true,
      message: 'Invite code marked as used successfully.',
    });
  } catch (error) {
    console.error('Error marking invite code as used:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while marking the invite code as used.',
    });
  }
}
