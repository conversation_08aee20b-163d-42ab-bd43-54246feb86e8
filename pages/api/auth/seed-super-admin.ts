import { NextApiRequest, NextApiResponse } from 'next';
import { UserRole } from '@/models/auth';
import { getAuth, UserRecord } from 'firebase-admin/auth';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getFirestore, FieldValue } from 'firebase-admin/firestore';

// Initialize Firebase Admin SDK (server-side only)
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    }),
  });
}

// Get admin Firestore instance
const adminDb = getFirestore();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check for admin API key
  const apiKey = req.headers.authorization?.replace('Bearer ', '');

  if (!apiKey || apiKey !== process.env.SUPER_ADMIN_SEED_API_KEY) {
    return res.status(401).json({ success: false, message: 'Unauthorized' });
  }

  try {
    const { email, password, name } = req.body;

    // Validate required fields
    if (!email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: 'Email, password, and name are required',
      });
    }

    // Check if the super-admin already exists
    const auth = getAuth();
    let superAdminUser: UserRecord;

    try {
      superAdminUser = await auth.getUserByEmail(email);

      // User already exists, check if it's already a super-admin
      const userDoc = await adminDb.collection('users').doc(superAdminUser.uid).get();

      if (userDoc.exists && userDoc.data()?.role === UserRole.SUPER_ADMIN) {
        return res.status(400).json({
          success: false,
          message: 'Super-admin already exists',
        });
      }
    } catch (error: unknown) {
      console.log('User does not exist, create a new one', error);
      // User doesn't exist, create a new one
      superAdminUser = await auth.createUser({
        email,
        password,
        displayName: name,
      });
    }

    // Set custom claims
    await auth.setCustomUserClaims(superAdminUser.uid, {
      role: UserRole.SUPER_ADMIN,
      clinicId: null,
    });

    // Create or update user document in Firestore
    await adminDb.collection('users').doc(superAdminUser.uid).set({
      email,
      name,
      role: UserRole.SUPER_ADMIN,
      clinicId: null,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
    });

    return res.status(200).json({
      success: true,
      message: 'Super-admin created successfully',
      userId: superAdminUser.uid,
    });
  } catch (error: unknown) {
    console.error('Error creating super-admin:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error creating super-admin',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
