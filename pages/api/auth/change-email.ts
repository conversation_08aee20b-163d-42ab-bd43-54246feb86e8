import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify the user is authenticated
    const user = await verifyAuthAndGetUser(req);

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { newEmail } = req.body;

    if (!newEmail) {
      return res.status(400).json({ message: 'New email is required' });
    }

    // Get the Firebase user
    const firebaseUser = await admin.auth().getUser(user.id);

    // Check if the current email is verified
    if (!firebaseUser.emailVerified) {
      // Send verification email for current email
      // Extract the origin from request headers, or fall back to localhost for development
      // We use req.headers.origin instead of environment variables because:
      // 1. It automatically adapts to different environments (dev, staging, prod)
      // 2. It works correctly even when the app is accessed from different domains
      // 3. It doesn't require configuration of environment variables
      const origin = req.headers.origin || 'http://localhost:3000';
      const continueUrlForCurrent = `${origin}/dashboard/profile`;

      const currentEmailLink = await admin
        .auth()
        .generateEmailVerificationLink(firebaseUser.email || '', {
          url: continueUrlForCurrent,
          handleCodeInApp: false,
        });

      // In a production app, you would send this email using your email service
      // For now, we'll just return it in the response
      return res.status(400).json({
        message: 'Your current email must be verified first',
        verificationLink: currentEmailLink,
      });
    }

    // Generate a link to verify the new email
    // Extract the origin from request headers, or fall back to localhost for development
    // We use req.headers.origin instead of environment variables because:
    // 1. It automatically adapts to different environments (dev, staging, prod)
    // 2. It works correctly even when the app is accessed from different domains
    // 3. It doesn't require configuration of environment variables
    const origin = req.headers.origin;
    const continueUrl = `${origin}/dashboard/profile`;

    const actionCodeSettings = {
      url: continueUrl,
      handleCodeInApp: false,
    };

    // Use the Firebase Admin SDK to generate a verification link
    const link = await admin
      .auth()
      .generateVerifyAndChangeEmailLink(firebaseUser.email || '', newEmail, actionCodeSettings);

    // Update Firestore with pending email
    await admin.firestore().collection('users').doc(user.id).update({
      pendingEmail: newEmail,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // In a production app, you would send this email using your email service
    // For now, we'll just return it in the response
    return res.status(200).json({
      success: true,
      message: 'Verification email sent to new address',
      verificationLink: link,
    });
  } catch (error) {
    console.error('Error changing email:', error);

    // Provide more detailed error messages
    if (error instanceof Error) {
      const errorMessage = error.message || '';

      if (errorMessage.includes('auth/email-already-in-use')) {
        return res.status(400).json({ message: 'This email is already in use by another account' });
      } else if (errorMessage.includes('auth/invalid-email')) {
        return res.status(400).json({ message: 'The email address is not valid' });
      } else if (
        errorMessage.includes('permission-denied') ||
        errorMessage.includes('insufficient permissions')
      ) {
        return res.status(403).json({
          message: 'Missing or insufficient permissions. Please contact support.',
          details: errorMessage,
        });
      }
    }

    return res.status(500).json({
      message: 'Failed to change email',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
