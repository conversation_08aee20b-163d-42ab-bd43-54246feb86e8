import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify the user is authenticated and is an admin
    const user = await verifyAuthAndGetUser(req);

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Only allow super admins or clinic admins to use this endpoint
    if (user.role !== UserRole.SUPER_ADMIN && user.role !== UserRole.CLINIC_ADMIN) {
      return res.status(403).json({ message: 'Forbidden: Only admins can perform this action' });
    }

    const { userId, newEmail } = req.body;

    if (!userId || !newEmail) {
      return res.status(400).json({ message: 'User ID and new email are required' });
    }

    // Update the email in Firebase Auth
    await admin.auth().updateUser(userId, {
      email: newEmail,
      emailVerified: true, // Mark as verified automatically
    });

    // Update the email in Firestore
    await admin.firestore().collection('users').doc(userId).update({
      email: newEmail,
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    return res.status(200).json({
      success: true,
      message: 'Email updated successfully',
    });
  } catch (error) {
    console.error('Error updating email:', error);
    return res.status(500).json({
      message: 'Failed to update email',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
