import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { uid, role, clinicId } = req.body;

    // Validate required fields
    if (!uid || !role) {
      return res.status(400).json({
        success: false,
        message: 'User ID and role are required',
      });
    }

    // Validate role is a valid UserRole
    if (!Object.values(UserRole).includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role',
      });
    }

    // Set custom claims
    await admin.auth().setCustomUserClaims(uid, {
      role,
      clinicId: clinicId || null,
    });

    return res.status(200).json({
      success: true,
      message: 'Custom claims set successfully',
    });
  } catch (error: unknown) {
    console.error('Error setting custom claims:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error setting custom claims',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
