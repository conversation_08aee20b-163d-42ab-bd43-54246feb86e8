import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { email, password, name, role, clinicId, specialty, locationId } = req.body;

    // Validate required fields
    if (!email || !password || !name || !role) {
      return res.status(400).json({
        success: false,
        message: 'Email, password, name, and role are required',
      });
    }

    // Validate role is a valid UserRole
    if (!Object.values(UserRole).includes(role)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid role',
      });
    }

    try {
      // Create the user in Firebase Auth
      const userRecord = await admin.auth().createUser({
        email,
        password,
        displayName: name,
        emailVerified: false,
      });

      // Set custom claims for role-based access control
      await admin.auth().setCustomUserClaims(userRecord.uid, {
        role,
        clinicId: clinicId || null,
      });

      // Create user document in Firestore
      const now = admin.firestore.FieldValue.serverTimestamp();
      const userData = {
        email,
        name,
        role,
        locationId: locationId || URMA_LOMBARD_LOCATION_ID, // TODO: Remove this default value
        clinicId: clinicId || null,
        specialty: specialty || null,
        canTakeAppointments: false, // Default value, can be updated later
        preferences: {
          isAppointmentNotificationsEnabled: true,
          isIncomingCallNotificationsEnabled: true,
          isVoiceMailNotificationsEnabled: true,
          isDailyMonitoringNotificationsEnabled: true,
          isHourlyMonitoringNotificationsEnabled: true,
        },
        createdAt: now,
        updatedAt: now,
      };

      await admin.firestore().collection('users').doc(userRecord.uid).set(userData);

      return res.status(201).json({
        success: true,
        message: 'User created successfully',
        user: {
          id: userRecord.uid,
          email,
          name,
          role,
          clinicId: clinicId || null,
          specialty: specialty || null,
        },
      });
    } catch (authError: unknown) {
      // Handle specific Firebase Auth errors
      const firebaseError = authError as { code?: string };
      if (firebaseError.code === 'auth/email-already-exists') {
        return res.status(400).json({
          success: false,
          message: 'Email already in use. Please use a different email or login.',
        });
      } else if (firebaseError.code === 'auth/invalid-email') {
        return res.status(400).json({
          success: false,
          message: 'Invalid email format',
        });
      } else if (firebaseError.code === 'auth/weak-password') {
        return res.status(400).json({
          success: false,
          message: 'Password is too weak. Please use a stronger password.',
        });
      }

      throw authError;
    }
  } catch (error: unknown) {
    console.error('Error creating user:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error creating user',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
