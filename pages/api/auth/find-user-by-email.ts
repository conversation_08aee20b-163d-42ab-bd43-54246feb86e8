import { NextApiRequest, NextApiResponse } from 'next';
import admin from '@/utils/firebase-admin';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify the user is authenticated and is an admin
    const user = await verifyAuthAndGetUser(req);

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Only allow super admins or clinic admins to use this endpoint
    if (user.role !== UserRole.SUPER_ADMIN && user.role !== UserRole.CLINIC_ADMIN) {
      return res.status(403).json({ message: 'Forbidden: Only admins can perform this action' });
    }

    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    try {
      // Try to find the user by email
      const userRecord = await admin.auth().getUserByEmail(email);

      // Get additional user data from Firestore
      const userDoc = await admin.firestore().collection('users').doc(userRecord.uid).get();
      const userData = userDoc.exists ? userDoc.data() : null;

      return res.status(200).json({
        success: true,
        user: {
          uid: userRecord.uid,
          email: userRecord.email,
          emailVerified: userRecord.emailVerified,
          displayName: userRecord.displayName,
          ...userData,
        },
      });
    } catch (error) {
      // If user not found, return a 404
      if (error instanceof Error && error.message.includes('no user record')) {
        return res.status(404).json({ message: 'User not found' });
      }
      throw error;
    }
  } catch (error) {
    console.error('Error finding user:', error);
    return res.status(500).json({
      message: 'Failed to find user',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
