import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';
import admin from 'firebase-admin';

interface FirestoreUser {
  id: string;
  name?: string;
  email?: string;
  role: string | UserRole;
  clinicId: number | null;
  createdAt: Date | admin.firestore.Timestamp;
  updatedAt: Date | admin.firestore.Timestamp;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = (await verifyAuthAndGetUser(req)) as FirestoreUser;
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Normalize roles for comparison (handle potential case sensitivity issues)
  const userRole = typeof user.role === 'string' ? user.role.toUpperCase() : user.role;

  // Compare with enum values and string literals
  const isAdmin = userRole === UserRole.CLINIC_ADMIN || userRole === UserRole.SUPER_ADMIN;
  const isSuperAdmin = userRole === UserRole.SUPER_ADMIN;

  // Only clinic admin or super admin can access staff list
  if (!isAdmin) {
    return res.status(403).json({
      message: 'Forbidden. Only admins can access staff list',
      debug: {
        userRole: user.role,
        allowedRoles: [UserRole.CLINIC_ADMIN, UserRole.SUPER_ADMIN],
      },
    });
  }

  try {
    // Get user collection reference
    const usersRef = admin.firestore().collection('users');
    let query: FirebaseFirestore.Query = usersRef;

    // If not super admin, filter by user's clinic ID
    if (!isSuperAdmin && user.clinicId !== null) {
      query = usersRef.where('clinicId', '==', user.clinicId);
    }

    const snapshot = await query.get();

    const clinicStaff = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        name: data.name || 'User',
        email: data.email || '',
        role: data.role,
        status: 'active',
        clinicId: data.clinicId,
      };
    });

    return res.status(200).json({
      success: true,
      staff: clinicStaff,
    });
  } catch (error) {
    console.error('Error fetching Firestore users:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching staff data',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
