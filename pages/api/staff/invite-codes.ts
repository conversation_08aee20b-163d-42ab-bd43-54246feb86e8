import { NextApiRequest, NextApiResponse } from 'next';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { UserRole } from '@/models/auth';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import admin from 'firebase-admin';

// Initialize Firebase Admin SDK (server-side only)
if (!getApps().length) {
  try {
    initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized for invite-codes endpoint');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
  }
}

// Get admin Firestore instance
const db = getFirestore();

// Interface for user data
interface UserData {
  id: string;
  role: UserRole;
  clinicId?: number | null;
  [key: string]: string | number | boolean | null | undefined;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('Handling invite-codes request');

  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Try to authenticate
    let userData: UserData | null = null;

    // 1. First try cookie auth
    const cookieUser = await verifyAuthAndGetUser(req);
    if (cookieUser) {
      console.log('User authenticated via cookie:', cookieUser.id);
      userData = cookieUser as unknown as UserData;
    } else {
      // 2. Try bearer token auth
      const authHeader = req.headers.authorization || '';
      console.log('Auth header:', authHeader ? `${authHeader.substring(0, 10)}...` : 'none');

      if (authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);

        try {
          const decodedToken = await admin.auth().verifyIdToken(token);
          console.log('Token verified for uid:', decodedToken.uid);

          // Get user data from Firestore
          const userSnapshot = await db.collection('users').doc(decodedToken.uid).get();

          if (userSnapshot.exists) {
            const data = userSnapshot.data();
            userData = {
              id: userSnapshot.id,
              ...data,
              role: data?.role || UserRole.STAFF,
            };
            console.log('User data retrieved:', userData.id, userData.role);
          }
        } catch (error) {
          console.error('Error verifying token:', error);
        }
      }
    }

    if (!userData) {
      console.log('Authentication failed');
      return res.status(401).json({
        success: false,
        message: 'Unauthorized',
      });
    }

    // Check role
    if (userData.role !== UserRole.CLINIC_ADMIN && userData.role !== UserRole.SUPER_ADMIN) {
      console.log('User role not allowed:', userData.role);
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only admins can view invite codes',
      });
    }

    // Get clinic ID
    const clinicIdParam = req.query.clinicId ? Number(req.query.clinicId) : null;
    const clinicId = clinicIdParam || userData.clinicId || null;

    if (!clinicId) {
      console.log('No clinic ID found');
      return res.status(400).json({
        success: false,
        message: 'Bad request: Missing clinic ID',
      });
    }

    // Role-based access control for clinic admins
    if (userData.role === UserRole.CLINIC_ADMIN && userData.clinicId !== clinicId) {
      console.log('Clinic admin trying to access other clinic:', userData.clinicId, 'vs', clinicId);
      return res.status(403).json({
        success: false,
        message: 'Forbidden: You can only view invite codes for your own clinic',
      });
    }

    console.log('Querying invite codes for clinic:', clinicId);

    // Query for invite codes
    const codesQuery = db.collection('staffInviteCodes').where('clinicId', '==', clinicId);

    // For debugging, let's simplify and get all codes for the clinic
    const snapshot = await codesQuery.get();

    console.log('Found codes:', snapshot.size);

    if (snapshot.empty) {
      return res.status(200).json({
        success: true,
        inviteCodes: [],
      });
    }

    // Format the results
    const inviteCodes = snapshot.docs.map(doc => {
      const data = doc.data();
      const result = {
        id: doc.id,
        code: data.code,
        clinicId: data.clinicId,
        used: data.used || false,
        expiresAt: data.expiresAt
          ? data.expiresAt.toDate().toISOString()
          : new Date().toISOString(),
        createdAt: data.createdAt
          ? data.createdAt.toDate().toISOString()
          : new Date().toISOString(),
        usedAt: data.usedAt ? data.usedAt.toDate().toISOString() : null,
      };
      return result;
    });

    return res.status(200).json({
      success: true,
      inviteCodes,
    });
  } catch (error: unknown) {
    console.error('Error fetching invite codes:', error);

    return res.status(500).json({
      success: false,
      message: 'Server error fetching invite codes',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
