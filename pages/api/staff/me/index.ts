import { NextApiRequest, NextApiResponse } from 'next';
import {
  verifyAuthAndGetUser,
  verifyFirebaseIdToken,
  getUserFromFirestore,
} from '@/utils/firebase-admin';
import admin from 'firebase-admin';
import { UserRole } from '@/models/auth';

interface FirestoreUser {
  id: string;
  name?: string;
  email?: string;
  role: string | UserRole;
  clinicId: number | null;
  createdAt: Date | admin.firestore.Timestamp;
  updatedAt: Date | admin.firestore.Timestamp;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Support both token in header and standard Firebase auth
  let user: FirestoreUser | null = null;
  const authHeader = req.headers.authorization || '';

  if (authHeader.startsWith('Bearer ')) {
    // Extract token
    const idToken = authHeader.split('Bearer ')[1];

    // Verify token
    try {
      const decodedToken = await verifyFirebaseIdToken(idToken);
      if (decodedToken) {
        user = (await getUserFromFirestore(decodedToken.uid)) as FirestoreUser;
      }
    } catch (error: unknown) {
      console.error('Error verifying token:', error);

      // Check for token expiration error
      if (error instanceof Error && error.message === 'id-token-expired') {
        return res.status(401).json({
          message: 'id-token-expired',
          error:
            'Firebase ID token has expired. Get a fresh ID token from your client app and try again.',
        });
      }

      return res.status(401).json({ message: 'Invalid token' });
    }
  } else {
    // Try normal session-based auth
    user = (await verifyAuthAndGetUser(req)) as FirestoreUser;
  }

  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Only return basic information
  return res.status(200).json({
    id: user.id,
    name: user.name || 'User',
    email: user.email || '',
    role: user.role,
    clinicId: user.clinicId || null,
  });
}
