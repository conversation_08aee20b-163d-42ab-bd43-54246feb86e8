import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import { decodeTokenPayloadFromRequest } from '@/utils/firebase-admin';
import { userService } from '@/utils/firestore';

const settingsSchema = z.object({
  isAppointmentNotificationsEnabled: z.boolean(),
  isIncomingCallNotificationsEnabled: z.boolean(),
  isVoiceMailNotificationsEnabled: z.boolean(),
  isDailyMonitoringNotificationsEnabled: z.boolean(),
  isHourlyMonitoringNotificationsEnabled: z.boolean(),
});

async function getSettings(req: NextApiRequest, res: NextApiResponse) {
  const tokenPayload = await decodeTokenPayloadFromRequest(req);
  const userId = tokenPayload?.uid as string;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const user = await userService.getUserById(userId);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  return res.status(200).json(user.preferences || {});
}

async function saveSettings(req: NextApiRequest, res: NextApiResponse) {
  const tokenPayload = await decodeTokenPayloadFromRequest(req);
  const userId = tokenPayload?.uid as string;
  if (!userId) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const validationResult = settingsSchema.safeParse(req.body);
  if (!validationResult.success) {
    return res.status(400).json({
      message: 'Invalid request body',
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const user = await userService.getUserById(userId);
  if (!user) {
    return res.status(404).json({ message: 'User not found' });
  }

  const preferences = validationResult.data;
  await userService.updateUser(userId, {
    preferences: {
      ...(user.preferences || {}),
      ...preferences,
    },
  });

  return res.status(200).json({ message: 'Settings saved' });
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  switch (req.method) {
    case 'GET':
      return getSettings(req, res);
    case 'PUT':
      return saveSettings(req, res);
    default:
      return res.status(405).json({ message: 'Method not allowed' });
  }
}
