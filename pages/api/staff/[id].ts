import { NextApiRequest, NextApiResponse } from 'next';
import userData from '../../../data/users.json';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserRole } from '@/models/auth';

interface UserData {
  id: string;
  name: string;
  role: string;
  email: string;
  [key: string]: string | number | boolean | null | undefined;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Special case for /api/staff/me endpoint
  if (req.query.id === 'me') {
    // Get the user who is logged in
    const userInfo = (userData as UserData[]).find(u => u.id === user.id);

    if (!userInfo) {
      return res.status(404).json({ message: 'User not found' });
    }

    return res.status(200).json({
      id: userInfo.id,
      name: userInfo.name,
      email: userInfo.email,
      role: userInfo.role,
      clinicId: userInfo.clinicId || null,
    });
  }

  // Regular case for specific user ID
  const userInfo = (userData as UserData[]).find(u => u.id === req.query.id);

  if (!userInfo) {
    return res.status(404).json({ message: 'User not found' });
  }

  // Check if requesting user has permission to view this user
  if (
    user.role !== UserRole.SUPER_ADMIN &&
    user.role !== UserRole.CLINIC_ADMIN &&
    user.id !== userInfo.id
  ) {
    return res.status(403).json({ message: 'Forbidden' });
  }

  return res.status(200).json({
    id: userInfo.id,
    name: userInfo.name,
    email: userInfo.email,
    role: userInfo.role,
    clinicId: userInfo.clinicId || null,
  });
}
