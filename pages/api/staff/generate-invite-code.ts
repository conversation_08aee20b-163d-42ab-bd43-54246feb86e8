import { NextApiRequest, NextApiResponse } from 'next';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { UserRole } from '@/models/auth';
import {
  verifyAuthAndGetUser,
  verifyFirebaseIdToken,
  getUserFromFirestore,
} from '@/utils/firebase-admin';
import { generateRandomCode } from '@/utils/common';

// Initialize Firebase Admin SDK (server-side only)
if (!getApps().length) {
  try {
    initializeApp({
      credential: cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized for generate-invite-code endpoint');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
  }
}

// Get admin Firestore instance
const db = getFirestore();

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('Handling generate-invite-code request');

  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Support both token in header and standard Firebase auth
  let user;
  const authHeader = req.headers.authorization || '';

  if (authHeader.startsWith('Bearer ')) {
    // Extract token
    const idToken = authHeader.split('Bearer ')[1];
    console.log('Got Bearer token, verifying...');

    // Verify token
    try {
      const decodedToken = await verifyFirebaseIdToken(idToken);
      if (decodedToken) {
        console.log('Token verified for:', decodedToken.uid);
        user = await getUserFromFirestore(decodedToken.uid);
      }
    } catch (error) {
      console.error('Error verifying token:', error);
      return res.status(401).json({
        success: false,
        message: 'Invalid token',
      });
    }
  } else {
    // Try normal session-based auth
    user = await verifyAuthAndGetUser(req);
  }

  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized',
    });
  }

  // Check if user has the correct role to generate codes
  if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: Only clinic admins can generate invite codes',
    });
  }

  const clinicId = req.body.clinicId || user.clinicId;

  if (!clinicId) {
    return res.status(400).json({
      success: false,
      message: 'Bad request: Missing clinic ID',
    });
  }

  // Only allow clinic admins to generate codes for their own clinic
  if (user.role === UserRole.CLINIC_ADMIN && user.clinicId !== Number(clinicId)) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: You can only generate invite codes for your own clinic',
    });
  }

  try {
    const code = generateRandomCode();

    // Set expiration date (24 hours from now)
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);

    // Create code document
    const codeRef = db.collection('staffInviteCodes').doc();
    await codeRef.set({
      code,
      clinicId: Number(clinicId),
      used: false,
      expiresAt,
      createdBy: user.id,
      createdAt: new Date(),
    });

    console.log('Successfully generated code:', code, 'for clinic:', clinicId);

    return res.status(200).json({
      success: true,
      message: 'Invite code generated successfully',
      code,
      expiresAt,
      id: codeRef.id,
    });
  } catch (error: unknown) {
    console.error('Error generating invite code:', error);

    return res.status(500).json({
      success: false,
      message: 'Server error generating invite code',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
