import { NextApiRequest, NextApiResponse } from 'next';

interface ApiResponse {
  message?: string;
  error?: string;
  data?: unknown;
}

/**
 * @swagger
 * /api/monitoring:
 *   get:
 *     summary: Get monitoring API endpoints information
 *     description: Returns documentation and available endpoints for the monitoring API
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: API endpoints information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Monitoring API endpoints"
 *                 data:
 *                   type: object
 *                   properties:
 *                     endpoints:
 *                       type: array
 *                       description: List of available monitoring endpoints
 *                       items:
 *                         type: object
 *                         properties:
 *                           path:
 *                             type: string
 *                             description: API endpoint path
 *                             example: "/api/monitoring/hourly-stats"
 *                           method:
 *                             type: string
 *                             description: HTTP method
 *                             example: "POST"
 *                           description:
 *                             type: string
 *                             description: Endpoint description
 *                             example: "Get hourly call statistics"
 *                           body:
 *                             type: object
 *                             description: Request body parameters
 *                             properties:
 *                               date:
 *                                 type: string
 *                                 description: Optional date parameter
 *                                 example: "string (ISO date) - optional (defaults to current date/time)"
 *                               clinicId:
 *                                 type: string
 *                                 description: Optional clinic ID parameter
 *                                 example: "number - optional (defaults to URMA_CLINIC_ID)"
 *                               locationId:
 *                                 type: string
 *                                 description: Optional location ID parameter
 *                                 example: "string - optional (defaults to URMA_LOMBARD_LOCATION_ID)"
 *       405:
 *         description: Method not allowed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Method not allowed"
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  if (req.method === 'GET') {
    return res.status(200).json({
      message: 'Monitoring API endpoints',
      data: {
        endpoints: [
          {
            path: '/api/monitoring/hourly-stats',
            method: 'POST',
            description: 'Get hourly call statistics',
            body: {
              date: 'string (ISO date) - optional (defaults to current date/time)',
              clinicId: 'number - optional (defaults to URMA_CLINIC_ID)',
              locationId: 'string - optional (defaults to URMA_LOMBARD_LOCATION_ID)',
            },
          },
          {
            path: '/api/monitoring/daily-stats',
            method: 'POST',
            description: 'Get daily call statistics',
            body: {
              date: 'string (ISO date) - optional (defaults to current date/time)',
              clinicId: 'number - optional (defaults to URMA_CLINIC_ID)',
              locationId: 'string - optional (defaults to URMA_LOMBARD_LOCATION_ID)',
            },
          },
        ],
      },
    });
  }

  return res.status(405).json({ error: 'Method not allowed' });
}
