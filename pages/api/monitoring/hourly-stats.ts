import { NextApiRequest, NextApiResponse } from 'next';
import { CallsMonitoring } from '@/lib/services/calls-monitoring';
import { URMA_CLINIC_ID, URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { AppLinkBuilder } from '@/utils/app-link.builder';
import { mailService, userService } from '@/utils/firestore';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import logger from '@/lib/external-api/v2/utils/logger';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

interface ApiResponse {
  message?: string;
  error?: string;
  data?: unknown;
}

interface HourlyStatsRequest {
  date?: string; // ISO date string (optional, defaults to current date/time)
  clinicId?: number;
  locationId?: string;
}

/**
 * @swagger
 * /api/monitoring/hourly-stats:
 *   post:
 *     summary: Get hourly call statistics
 *     description: Retrieves call statistics for a specific hour, including total calls, call summaries, and potential issues
 *     tags: [Monitoring]
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: []
 *             properties:
 *               date:
 *                 type: string
 *                 format: date-time
 *                 description: ISO date string for the hour to analyze (optional, defaults to current date/time)
 *                 example: "2024-01-15T10:00:00.000Z"
 *               clinicId:
 *                 type: number
 *                 description: Clinic ID (optional, defaults to URMA_CLINIC_ID)
 *                 example: 12
 *               locationId:
 *                 type: string
 *                 description: Location ID (optional, defaults to URMA_LOMBARD_LOCATION_ID)
 *                 example: "118"
 *     responses:
 *       200:
 *         description: Hourly call statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCalls:
 *                       type: number
 *                       description: Total number of calls in the hour
 *                       example: 15
 *                     calls:
 *                       type: array
 *                       description: Array of call details with summaries and issue flags
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: Call ID
 *                           type:
 *                             type: string
 *                             description: Call type
 *                           duration:
 *                             type: string
 *                             description: Call duration
 *                           summary:
 *                             type: string
 *                             description: AI-generated call summary
 *                           isPotentialIssue:
 *                             type: boolean
 *                             description: Whether the call represents a potential issue
 *       400:
 *         description: Bad request - missing or invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Missing required field: date"
 *       405:
 *         description: Method not allowed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Method not allowed"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Failed to fetch hourly stats"
 *                 message:
 *                   type: string
 *                   description: Detailed error message
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { date, clinicId, locationId } = req.body as HourlyStatsRequest;

    // Use current date/time if not provided
    const dateToUse = date || new Date().toISOString();

    const parsedDate = new Date(dateToUse);
    if (isNaN(parsedDate.getTime())) {
      return res.status(400).json({ error: 'Invalid date format' });
    }

    // Use default values if not provided
    const finalClinicId = clinicId ?? URMA_CLINIC_ID;
    const finalLocationId = locationId ?? URMA_LOMBARD_LOCATION_ID;

    const monitoringService = CallsMonitoring.getInstance(finalClinicId, finalLocationId);
    const stats = await monitoringService.getHourlyStats(parsedDate);

    // Get the staff member ids
    const staffMemberIds = await userService.getNotifiableStaffIdsForHourlyMonitoring({
      locationId: finalLocationId,
      clinicId: finalClinicId,
    });
    if (!staffMemberIds.length) {
      logger.info(
        {
          context: 'Email Notification Service',
        },
        'No staff members found for hourly monitoring notification',
      );
      return res.status(200).json({ data: stats });
    }

    const appLinkBuilder = AppLinkBuilder.getInstance();
    const dashboardLink = appLinkBuilder.getDashboardLink();

    // Send the email to the staff members
    await mailService.sendHourlyMonitoringEmail(staffMemberIds, {
      ctaLink: dashboardLink,
      reportDate: dayjs(parsedDate).utc().format('MMM D, YYYY'),
      reportTime: `${dayjs(parsedDate)
        .subtract(1, 'hour')
        .startOf('hour')
        .utc()
        .format('hh:mm A')} - ${dayjs(parsedDate)
        .subtract(1, 'hour')
        .endOf('hour')
        .utc()
        .format('hh:mm A UTC')}`,
      totalCalls: stats.totalCalls,
      potentialIssuesCalls: stats.calls
        .filter(call => call.isPotentialIssue)
        .map(call => ({
          ...call,
          summary: call.summary,
          callDetailsLink: appLinkBuilder.getCallDetailsLink(call.id),
        })),
      nonIssuesCalls: stats.calls
        .filter(call => !call.isPotentialIssue)
        .map(call => ({
          ...call,
          summary: call.summary,
          callDetailsLink: appLinkBuilder.getCallDetailsLink(call.id),
        })),
    });

    return res.status(200).json({ data: stats });
  } catch (error) {
    console.error('Error fetching hourly stats:', error);
    return res.status(500).json({
      error: 'Failed to fetch hourly stats',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
