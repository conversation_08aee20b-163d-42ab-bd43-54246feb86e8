import type { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getRepositories } from '@/lib/repositories';
import { DashboardMetricsService, TimeRange } from '@/lib/services/dashboard-metrics-service';
import { UserLocationService } from '@/lib/services/userLocationService';

const querySchema = z.object({
  range: z.enum(['today', 'yesterday', 'week', 'month', 'custom']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  excludeZeroDuration: z
    .string()
    .optional()
    .transform(val => val === 'true'),
  excludeDisconnected: z
    .string()
    .optional()
    .transform(val => val === 'true'),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', 'GET');
    return res.status(405).json({ message: 'Method not allowed' });
  }

  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    const repoManager = getRepositories();
    await repoManager.initialize();

    const parse = querySchema.safeParse(req.query);
    if (!parse.success) {
      return res
        .status(400)
        .json({ message: 'Invalid query parameters', errors: parse.error.format() });
    }

    const range = (parse.data.range ?? 'today') as TimeRange;
    const { startDate, endDate, excludeZeroDuration, excludeDisconnected } = parse.data;

    // Get user's current location timezone
    let timezone = 'America/Chicago'; // Default fallback
    try {
      const locationContext = await UserLocationService.getUserLocationContext(user.id!);
      if (locationContext.currentLocation?.timeZone) {
        timezone = locationContext.currentLocation.timeZone;
        console.log(
          `📍 Using location timezone: ${timezone} (Location: ${locationContext.currentLocation.name})`,
        );
      } else {
        console.log(`📍 No current location found, using default timezone: ${timezone}`);
      }
    } catch (error) {
      console.warn(`📍 Failed to get user location, using default timezone: ${timezone}`, error);
    }

    const service = new DashboardMetricsService(repoManager.calls);

    const filterOptions = {
      excludeZeroDuration: excludeZeroDuration || false,
      excludeDisconnected: excludeDisconnected || false,
    };

    let summary;
    if (range === 'custom' && startDate && endDate) {
      // Validate custom date range
      const start = new Date(startDate);
      const end = new Date(endDate);

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return res.status(400).json({ message: 'Invalid date format' });
      }

      if (start > end) {
        return res.status(400).json({ message: 'Start date must be before end date' });
      }

      summary = await service.getSummaryForDateRange(start, end, filterOptions, timezone);
    } else {
      summary = await service.getSummary(range, filterOptions, timezone);
    }
    return res.status(200).json(summary);
  } catch (error) {
    console.error('Dashboard summary handler error', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
