import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallNotificationService } from '@/lib/services/on-call-notification';
import { OnCallNotification } from '@/models/OnCallNotification';
import logger from '@/lib/external-api/v2/utils/logger';

interface ApiResponse {
  notifications?: OnCallNotification[];
  stats?: {
    totalSent: number;
    delivered: number;
    failed: number;
    pending: number;
    retrying: number;
    deliveryRate: number;
  };
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/on-call-notifications:
 *   get:
 *     summary: Get notification history
 *     description: Retrieve on-call doctor notification history with optional filtering
 *     tags: [On-Call Notifications]
 *     parameters:
 *       - name: doctorId
 *         in: query
 *         description: Filter by doctor ID
 *         schema:
 *           type: string
 *       - name: clinicId
 *         in: query
 *         description: Filter by clinic ID
 *         schema:
 *           type: number
 *       - name: startDate
 *         in: query
 *         description: Start date filter (ISO format)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: endDate
 *         in: query
 *         description: End date filter (ISO format)
 *         schema:
 *           type: string
 *           format: date-time
 *       - name: limit
 *         in: query
 *         description: Maximum number of results (default 50)
 *         schema:
 *           type: number
 *           minimum: 1
 *           maximum: 100
 *       - name: stats
 *         in: query
 *         description: Include statistics summary
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: Notification history retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 notifications:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OnCallNotification'
 *                 stats:
 *                   type: object
 *                   properties:
 *                     totalSent:
 *                       type: number
 *                     delivered:
 *                       type: number
 *                     failed:
 *                       type: number
 *                     pending:
 *                       type: number
 *                     retrying:
 *                       type: number
 *                     deliveryRate:
 *                       type: number
 *       400:
 *         description: Invalid query parameters
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    const { doctorId, clinicId, startDate, endDate, limit = '50', stats } = req.query;

    // Validate parameters
    const limitNum = parseInt(limit as string, 10);
    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({ error: 'Invalid limit parameter (1-100)' });
    }

    const clinicIdNum = clinicId ? parseInt(clinicId as string, 10) : undefined;
    if (clinicId && isNaN(clinicIdNum!)) {
      return res.status(400).json({ error: 'Invalid clinicId parameter' });
    }

    // Parse dates
    let startDateObj: Date | undefined;
    let endDateObj: Date | undefined;

    if (startDate) {
      startDateObj = new Date(startDate as string);
      if (isNaN(startDateObj.getTime())) {
        return res.status(400).json({ error: 'Invalid startDate format' });
      }
    }

    if (endDate) {
      endDateObj = new Date(endDate as string);
      if (isNaN(endDateObj.getTime())) {
        return res.status(400).json({ error: 'Invalid endDate format' });
      }
    }

    const response: ApiResponse = {};

    // Get notification history
    if (doctorId) {
      response.notifications = await OnCallNotificationService.getNotificationHistory(
        doctorId as string,
        limitNum,
      );
    } else {
      // If no doctorId specified, we can't retrieve notifications
      // In a full implementation, you might add a method to get all notifications for a clinic
      response.notifications = [];
    }

    // Get statistics if requested
    if (stats === 'true' && clinicIdNum) {
      response.stats = await OnCallNotificationService.getNotificationStats(
        clinicIdNum,
        startDateObj,
        endDateObj,
      );
    }

    logger.info(
      {
        doctorId,
        clinicId: clinicIdNum,
        startDate: startDateObj,
        endDate: endDateObj,
        limit: limitNum,
        resultCount: response.notifications?.length || 0,
      },
      'Retrieved notification history',
    );

    res.status(200).json(response);
  } catch (error) {
    logger.error({ error, query: req.query }, 'Error retrieving notification history');
    res.status(500).json({ error: 'Internal server error' });
  }
}
