import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallNotificationService } from '@/lib/services/on-call-notification';
import logger from '@/lib/external-api/v2/utils/logger';

interface ApiResponse {
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/on-call-notifications/{id}/retry:
 *   post:
 *     summary: Retry failed notification
 *     description: Retry sending a failed on-call doctor notification
 *     tags: [On-Call Notifications]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Notification ID to retry
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Notification retry initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Notification retry initiated successfully"
 *       400:
 *         description: Invalid notification ID or retry not allowed
 *       404:
 *         description: Notification not found
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  try {
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid notification ID' });
    }

    logger.info({ notificationId: id }, `Attempting to retry notification ${id}`);

    // Retry the notification
    await OnCallNotificationService.retryNotification(id);

    logger.info({ notificationId: id }, `Successfully initiated retry for notification ${id}`);

    res.status(200).json({ message: 'Notification retry initiated successfully' });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error(
      { error, notificationId: req.query.id },
      `Error retrying notification ${req.query.id}: ${errorMessage}`,
    );

    // Handle specific error types
    if (errorMessage.includes('not found')) {
      return res.status(404).json({ error: 'Notification not found' });
    }

    if (errorMessage.includes('Maximum retry attempts')) {
      return res.status(400).json({ error: errorMessage });
    }

    res.status(500).json({ error: 'Internal server error' });
  }
}
