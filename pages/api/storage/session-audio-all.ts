import { NextApiRequest, NextApiResponse } from 'next';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

/**
 * @swagger
 * /api/storage/session-audio-all:
 *   get:
 *     summary: Get all audio files for a session
 *     description: Retrieves all audio recordings associated with a given session ID and returns an array of objects with text and record URL
 *     tags: [Storage]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the session to retrieve audio for
 *       - in: query
 *         name: bucket
 *         schema:
 *           type: string
 *         required: false
 *         description: The name of the GCP Storage bucket to search in (defaults to GCP_AUDIO_BUCKET_NAME environment variable)
 *       - in: query
 *         name: minTimestamp
 *         schema:
 *           type: integer
 *           format: int64
 *         required: false
 *         description: Minimum timestamp (in milliseconds) to filter files - only returns files created after this timestamp
 *     responses:
 *       200:
 *         description: Successfully retrieved the audio files
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   text:
 *                     type: string
 *                     description: The text of the interaction
 *                     example: "Hello, how can I help you today?"
 *                   recordUrl:
 *                     type: string
 *                     description: The signed URL to access the audio file
 *                     example: "https://storage.googleapis.com/bucket-name/audio/session123_1609545600000.mp3?X-Goog-Algorithm=..."
 *       400:
 *         description: Bad request - missing required parameters
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       404:
 *         description: No audio files found for the session
 *       405:
 *         description: Method not allowed - only GET is supported
 *       500:
 *         description: Internal server error
 */

/**
 * API endpoint for retrieving all audio files for a specific session ID
 *
 * This endpoint retrieves all audio recordings associated with a given session ID.
 * It returns an array of objects with text and record URL for each interaction.
 *
 * @route GET /api/storage/session-audio-all
 *
 * @param {string} sessionId - Required. The unique identifier of the session to retrieve audio for
 * @param {string} [bucket] - Optional. The name of the GCP Storage bucket to search in.
 *                           If not provided, uses the GCP_AUDIO_BUCKET_NAME environment variable.
 * @param {number} [minTimestamp] - Optional. Minimum timestamp (in milliseconds) to filter files.
 *                                 Only returns files created after this timestamp.
 *
 * @returns {Array} 200 - Success response with array of interaction objects
 * @returns {string} 200[].text - The text of the interaction
 * @returns {string} 200[].recordUrl - The signed URL to access the audio file
 *
 * @returns {Object} 400 - Error response if required parameters are missing
 * @returns {Object} 401 - Error response if user is not authenticated
 * @returns {Object} 404 - Error response if no audio files are found for the session
 * @returns {Object} 405 - Error response if method is not GET
 * @returns {Object} 500 - Error response if server error occurs
 *
 * @example
 * // Get all audio files for a session
 * GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA
 *
 * @example
 * // Get all audio files for a session after a specific timestamp
 * GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA&minTimestamp=1744269450000
 *
 * @example
 * // Get all audio files for a session from a specific bucket
 * GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA&bucket=custom-audio-bucket
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the session ID from the query parameters
    const sessionId = req.query.sessionId as string;
    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    // Get the bucket name from the query parameters or environment variables
    const bucketName = (req.query.bucket as string) || process.env.GCP_AUDIO_BUCKET_NAME;
    if (!bucketName) {
      return res.status(400).json({
        error:
          'Bucket name is required. Provide it as a query parameter or set GCP_AUDIO_BUCKET_NAME environment variable.',
      });
    }

    // Get the minimum timestamp from the query parameters
    const minTimestamp = req.query.minTimestamp
      ? parseInt(req.query.minTimestamp as string, 10)
      : undefined;

    // Get the GCP Storage service
    const storageService = getGcpStorageService();

    // Get transcript with audio records using the service method
    const interactions = await storageService.getTranscriptWithAudioRecords(
      bucketName,
      sessionId,
      minTimestamp,
    );

    if (!interactions || interactions.length === 0) {
      return res.status(404).json({
        error: `No audio files found for session ${sessionId}${minTimestamp ? ` after timestamp ${minTimestamp}` : ''}`,
      });
    }

    // Return the interactions
    return res.status(200).json(interactions);
  } catch (error) {
    console.error('Error in session-audio-all API:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
