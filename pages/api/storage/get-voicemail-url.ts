import { NextApiRequest, NextApiResponse } from 'next';
// Auth import removed as it's not being used
import { getGcpStorageService } from '@/utils/gcp-storage';

/**
 * @swagger
 * /api/storage/get-voicemail-url:
 *   get:
 *     summary: Get a fresh signed URL for a voicemail file
 *     description: Converts a voicemail reference (gcp://bucket/path) to a signed URL for direct access
 *     tags: [Storage]
 *     parameters:
 *       - in: query
 *         name: voicemailUrl
 *         schema:
 *           type: string
 *         required: true
 *         description: The voicemail URL in the format gcp://bucket-name/file-path
 *       - in: query
 *         name: expiresInMinutes
 *         schema:
 *           type: number
 *         required: false
 *         description: Expiration time for the signed URL in minutes (default is 15)
 *     responses:
 *       200:
 *         description: Successfully generated signed URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The signed URL for direct access to the voicemail file
 *                 expiresAt:
 *                   type: number
 *                   description: Timestamp when the URL will expire (in milliseconds)
 *       400:
 *         description: Bad request - missing or invalid voicemail URL
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       500:
 *         description: Internal server error
 */

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Note: Authentication is temporarily disabled for demo purposes
    // In production, you should uncomment the following lines:
    // const user = await verifyAuthAndGetUser(req);
    // if (!user) {
    //   return res.status(401).json({ error: 'Unauthorized' });
    // }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the voicemail URL from query parameters
    const voicemailUrl = req.query.voicemailUrl as string;
    if (!voicemailUrl) {
      return res.status(400).json({ error: 'Voicemail URL is required' });
    }

    // Parse the voicemail URL (format: gcp://bucket-name/file-path)
    const match = voicemailUrl.match(/^gcp:\/\/([^\/]+)\/(.+)$/);
    if (!match) {
      return res.status(400).json({
        error: 'Invalid voicemail URL format',
        message: 'URL must be in the format gcp://bucket-name/file-path',
      });
    }

    const [, bucketName, filePath] = match;

    // Get expiration time from query parameters (default to 15 minutes)
    const expiresInMinutes = parseInt((req.query.expiresInMinutes as string) || '15', 10);
    const expiresInMs = expiresInMinutes * 60 * 1000;
    const expiresAt = Date.now() + expiresInMs;

    // Get the GCP Storage service
    const storageService = getGcpStorageService();

    // Generate a signed URL for the file
    const signedUrl = await storageService.getSignedUrl(bucketName, filePath, {
      expires: expiresAt,
    });

    // Return the signed URL and expiration timestamp
    return res.status(200).json({
      url: signedUrl,
      expiresAt: expiresAt,
    });
  } catch (error) {
    console.error('Error generating signed URL for voicemail:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
