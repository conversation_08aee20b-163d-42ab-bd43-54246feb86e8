import { NextApiRequest, NextApiResponse } from 'next';
import axios, { AxiosError } from 'axios';
import { PassThrough, Readable } from 'stream';
import ffmpeg, { FfmpegCommand } from 'fluent-ffmpeg';
import ffmpegPath from '@ffmpeg-installer/ffmpeg';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { getGcpStorageService } from '@/utils/gcp-storage';
// We're using specific stream types to ensure compatibility between
// different stream implementations

// Configure ffmpeg to use the installed binary
ffmpeg.setFfmpegPath(ffmpegPath.path);

// Define supported output formats
const SUPPORTED_FORMATS = ['mp3', 'wav'] as const;
type OutputFormat = (typeof SUPPORTED_FORMATS)[number];

// Define storage reference format
const STORAGE_REF_REGEX = /^gcp:\/\/([^\/]+)\/(.+)$/;

// Define logger for consistent logging
const logger = {
  info: (message: string, data?: unknown) => {
    console.log(`[AudioConverter] ${message}`, data || '');
  },
  error: (message: string, error?: unknown) => {
    console.error(`[AudioConverter] ${message}`, error || '');
    if (error instanceof Error) {
      console.error(`[AudioConverter] Stack: ${error.stack}`);
    }
  },
};

/**
 * Helper function to resolve a storage reference to a signed URL
 * @param storageRef Storage reference in the format gcp://bucket-name/file-path
 * @returns Promise with the signed URL
 */
async function resolveStorageReference(storageRef: string): Promise<string> {
  const match = storageRef.match(STORAGE_REF_REGEX);
  if (!match) {
    throw new Error(
      'Invalid storage reference format. URL must be in the format gcp://bucket-name/file-path',
    );
  }

  const [, bucketName, filePath] = match;
  logger.info(`Resolving storage reference: bucket=${bucketName}, path=${filePath}`);

  // Get the GCP Storage service
  const storageService = getGcpStorageService();

  // Generate a signed URL for the file
  const signedUrl = await storageService.getSignedUrl(
    bucketName,
    filePath,
    { expires: Date.now() + 15 * 60 * 1000 }, // 15 minutes
  );

  logger.info(`Generated signed URL for storage reference`);
  return signedUrl;
}

/**
 * Helper function to extract audio parameters from filename
 * @param filePath File path or URL
 * @returns Audio parameters extracted from filename
 */
function extractAudioParams(filePath: string): {
  sampleRate: number;
  channels: number;
  format: string;
} {
  // Look for pattern like "audio-encoding-linear-16_48000_1_timestamp"
  const match = filePath.match(/audio-encoding-linear-(\d+)_(\d+)_(\d+)_/);

  if (match) {
    const [, bitDepth, sampleRate, channels] = match;

    logger.info(
      `Extracted audio parameters from filename: bitDepth=${bitDepth}, sampleRate=${sampleRate}, channels=${channels}`,
    );

    return {
      sampleRate: parseInt(sampleRate, 10),
      channels: parseInt(channels, 10),
      format: 's16le', // linear-16 means signed 16-bit little-endian
    };
  }

  logger.info('No audio parameters found in filename, using fallback values');

  // Fallback to default values
  return {
    sampleRate: 8000,
    channels: 1,
    format: 's16le',
  };
}

/**
 * Helper function to fetch audio data from a URL
 * @param url URL to fetch audio from
 * @returns Promise with the response stream
 */
async function fetchAudioFromUrl(url: string): Promise<NodeJS.ReadableStream> {
  logger.info(`Fetching audio file from URL: ${url.substring(0, 100)}...`);

  const response = await axios({
    method: 'GET',
    url: url,
    responseType: 'stream',
    timeout: 60000, // 60 seconds timeout
    maxContentLength: 50 * 1024 * 1024, // 50MB max file size
    validateStatus: status => status >= 200 && status < 300, // Only accept 2xx status codes
    headers: {
      'User-Agent': 'StaffPortal/1.0 AudioConverter',
    },
  });

  // Check if the response is valid
  if (!response.data) {
    throw new Error('Response data is empty');
  }

  logger.info(`Successfully fetched audio file. Status: ${response.status}`);
  return response.data;
}

/**
 * Helper function to create an FFmpeg command for audio conversion
 * @param inputStream Input audio stream
 * @param outputFormat Output format (mp3 or wav)
 * @param audioParams Audio parameters extracted from filename
 * @returns FFmpeg command
 */
function createFfmpegCommand(
  inputStream: NodeJS.ReadableStream | Readable,
  outputFormat: OutputFormat,
  audioParams: { sampleRate: number; channels: number; format: string },
): FfmpegCommand {
  // Ensure inputStream is a Readable stream that fluent-ffmpeg can work with
  // Using unknown instead of any to satisfy ESLint rules
  const readableStream =
    inputStream instanceof Readable
      ? inputStream
      : Readable.from(inputStream as unknown as Iterable<unknown>);

  const inputOptions = [
    `-f ${audioParams.format}`,
    `-ar ${audioParams.sampleRate}`,
    `-ac ${audioParams.channels}`,
  ];

  logger.info(`Creating FFmpeg command with options: ${inputOptions.join(' ')}`);

  return (
    ffmpeg()
      .input(readableStream)
      // Use extracted audio parameters
      .inputOptions(inputOptions)
      .audioCodec(outputFormat === 'mp3' ? 'libmp3lame' : 'pcm_s16le')
      .audioBitrate('128k')
      .format(outputFormat)
      .on('start', commandLine => {
        logger.info(`FFmpeg conversion started with command: ${commandLine}`);
      })
      .on('progress', progress => {
        logger.info(`FFmpeg conversion progress: ${JSON.stringify(progress)}`);
      })
      .on('stderr', stderrLine => {
        logger.info(`FFmpeg stderr: ${stderrLine}`);
      })
      .on('end', () => {
        logger.info('FFmpeg conversion completed successfully');
      })
  );
}

/**
 * Helper function to create a fallback FFmpeg command with auto-detection
 * @param inputStream Input audio stream
 * @param outputFormat Output format (mp3 or wav)
 * @returns FFmpeg command
 */
function createFallbackFfmpegCommand(
  inputStream: NodeJS.ReadableStream | Readable,
  outputFormat: OutputFormat,
): FfmpegCommand {
  logger.info(
    `Creating fallback FFmpeg command for ${outputFormat} conversion with auto-detection`,
  );

  // Ensure inputStream is a Readable stream that fluent-ffmpeg can work with
  const readableStream =
    inputStream instanceof Readable
      ? inputStream
      : Readable.from(inputStream as unknown as Iterable<unknown>);

  return (
    ffmpeg()
      .input(readableStream)
      // Let FFmpeg auto-detect everything
      .audioCodec(outputFormat === 'mp3' ? 'libmp3lame' : 'pcm_s16le')
      .audioBitrate('128k')
      .format(outputFormat)
      .on('start', commandLine => {
        logger.info(`Fallback FFmpeg conversion started with command: ${commandLine}`);
      })
      .on('progress', progress => {
        logger.info(`Fallback FFmpeg conversion progress: ${JSON.stringify(progress)}`);
      })
      .on('end', () => {
        logger.info('Fallback FFmpeg conversion completed successfully');
      })
      .on('stderr', stderrLine => {
        logger.info(`Fallback FFmpeg stderr: ${stderrLine}`);
      })
  );
}

/**
 * Helper function to send an error response
 * @param res Response object
 * @param status HTTP status code
 * @param error Error message
 * @param details Additional error details
 */
function sendErrorResponse(
  res: NextApiResponse,
  status: number,
  error: string,
  details?: Record<string, unknown>,
): void {
  if (!res.headersSent) {
    res.status(status).json({
      error,
      ...details,
    });
  }
}

/**
 * @swagger
 * /api/storage/convert-audio:
 *   get:
 *     summary: Convert audio file to a web-compatible format
 *     description: Takes a signed URL for an audio file and converts it to a web-compatible format
 *     tags: [Storage]
 *     parameters:
 *       - in: query
 *         name: url
 *         schema:
 *           type: string
 *         required: true
 *         description: The signed URL of the audio file to convert or a storage reference (gcp://bucket-name/file-path)
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [mp3, wav]
 *         required: false
 *         description: The output format (defaults to mp3)
 *     responses:
 *       200:
 *         description: Successfully converted audio file
 *         content:
 *           audio/mpeg:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Bad request - missing required parameters
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       500:
 *         description: Internal server error
 */

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Authenticate the request
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return sendErrorResponse(res, 401, 'Unauthorized');
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return sendErrorResponse(res, 405, 'Method not allowed');
    }

    // Get the URL from query parameters
    const url = req.query.url as string;
    if (!url) {
      return sendErrorResponse(res, 400, 'URL is required');
    }

    // Get the output format (default to mp3)
    const formatParam = ((req.query.format as string) || 'mp3').toLowerCase();
    if (!SUPPORTED_FORMATS.includes(formatParam as OutputFormat)) {
      return sendErrorResponse(res, 400, 'Invalid format', {
        message: `Supported formats: ${SUPPORTED_FORMATS.join(', ')}`,
      });
    }
    const format = formatParam as OutputFormat;

    try {
      // Resolve the URL if it's a storage reference
      let fileUrl = url;
      if (url.startsWith('gcp://')) {
        try {
          fileUrl = await resolveStorageReference(url);
        } catch (error) {
          logger.error('Error resolving storage reference', error);
          return sendErrorResponse(res, 400, 'Invalid storage reference', {
            message: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }

      // Fetch the audio file
      let audioStream: NodeJS.ReadableStream;
      try {
        audioStream = await fetchAudioFromUrl(fileUrl);
      } catch (error) {
        logger.error('Error fetching audio file', error);

        // Handle Axios errors
        if (axios.isAxiosError(error)) {
          const axiosError = error as AxiosError;
          const statusCode = axiosError.response?.status || 500;
          const errorMessage = axiosError.response
            ? `Server responded with status ${statusCode}`
            : 'No response received from server - possible timeout or network issue';

          return sendErrorResponse(res, statusCode, 'Failed to fetch audio file', {
            message: errorMessage,
            url: fileUrl.substring(0, 100) + '...', // Include part of the URL for debugging
            originalUrl: url.startsWith('gcp://') ? url : undefined, // Include the original storage reference if applicable
          });
        }

        // Handle other errors
        return sendErrorResponse(res, 500, 'Failed to fetch audio file', {
          message: error instanceof Error ? error.message : 'Unknown error',
          url: fileUrl.substring(0, 100) + '...',
        });
      }

      // Set response headers
      const contentType = format === 'mp3' ? 'audio/mpeg' : 'audio/wav';
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="converted.${format}"`);

      // Extract audio parameters from the URL or file path
      const audioParams = extractAudioParams(url);

      // Create a pass-through stream for ffmpeg
      const passThrough = new PassThrough();

      // Create the FFmpeg command
      const ffmpegCommand = createFfmpegCommand(audioStream, format, audioParams);

      // Add error handler with multiple format fallbacks
      ffmpegCommand.on('error', err => {
        logger.error('Error during FFmpeg conversion', err);

        // Try different format combinations
        const formatAttempts = [
          { format: 'mulaw', sampleRate: 8000, channels: 1 },
          { format: 'alaw', sampleRate: 8000, channels: 1 },
          { format: 'u8', sampleRate: 8000, channels: 1 },
          { format: 's16be', sampleRate: 8000, channels: 1 },
          { format: 's16le', sampleRate: 16000, channels: 1 },
          { format: 's16le', sampleRate: 44100, channels: 1 },
        ];

        let attemptIndex = 0;

        const tryNextFormat = () => {
          if (attemptIndex >= formatAttempts.length) {
            // All formats failed, try auto-detection as final fallback
            logger.info('All format attempts failed, trying auto-detection fallback');
            const fallbackCommand = createFallbackFfmpegCommand(audioStream, format);

            fallbackCommand.on('error', fallbackErr => {
              logger.error('All conversion attempts failed', fallbackErr);
              if (!res.headersSent) {
                sendErrorResponse(res, 500, 'Error converting audio', {
                  message: fallbackErr.message,
                  details: fallbackErr.stack,
                  originalError: err.message,
                });
              }
            });

            fallbackCommand.pipe(passThrough, { end: true });
            return;
          }

          const attempt = formatAttempts[attemptIndex];
          logger.info(
            `Trying format attempt ${attemptIndex + 1}/${formatAttempts.length}: ${attempt.format} ${attempt.sampleRate}Hz ${attempt.channels}ch`,
          );

          const readableStream =
            audioStream instanceof Readable
              ? audioStream
              : Readable.from(audioStream as unknown as Iterable<unknown>);

          const formatCommand = ffmpeg()
            .input(readableStream)
            .inputOptions([
              `-f ${attempt.format}`,
              `-ar ${attempt.sampleRate}`,
              `-ac ${attempt.channels}`,
            ])
            .audioCodec(format === 'mp3' ? 'libmp3lame' : 'pcm_s16le')
            .audioBitrate('128k')
            .format(format)
            .on('start', commandLine => {
              logger.info(`Format attempt ${attemptIndex + 1} started: ${commandLine}`);
            })
            .on('error', formatErr => {
              logger.error(`Format attempt ${attemptIndex + 1} failed:`, formatErr);
              attemptIndex++;
              tryNextFormat();
            })
            .on('end', () => {
              logger.info(`Format attempt ${attemptIndex + 1} succeeded`);
            });

          formatCommand.pipe(passThrough, { end: true });
        };

        tryNextFormat();
      });

      // Pipe the output to the pass-through stream
      ffmpegCommand.pipe(passThrough, { end: true });

      // Pipe the converted audio to the response
      passThrough.pipe(res);
    } catch (processingError) {
      logger.error('Error processing audio', processingError);
      return sendErrorResponse(res, 500, 'Error processing audio', {
        message: processingError instanceof Error ? processingError.message : 'Unknown error',
      });
    }
  } catch (error) {
    logger.error('Unhandled error in convert-audio API', error);
    return sendErrorResponse(res, 500, 'Internal server error', {
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
