import { NextApiRequest, NextApiResponse } from 'next';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

/**
 * @swagger
 * /api/storage/audio-files:
 *   get:
 *     summary: Access audio files from GCP Storage
 *     description: Supports listing files and getting signed URLs for direct access to audio files
 *     tags: [Storage]
 *     parameters:
 *       - in: query
 *         name: bucket
 *         schema:
 *           type: string
 *         required: false
 *         description: The name of the GCP Storage bucket to access (defaults to GCP_AUDIO_BUCKET_NAME environment variable)
 *       - in: query
 *         name: fileName
 *         schema:
 *           type: string
 *         required: false
 *         description: If provided, returns a signed URL for this specific file. If not provided, lists files in the bucket.
 *       - in: query
 *         name: prefix
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter files by prefix (folder path)
 *       - in: query
 *         name: audioOnly
 *         schema:
 *           type: boolean
 *         required: false
 *         description: If true, only returns audio files (.mp3, .wav, .ogg, .m4a)
 *       - in: query
 *         name: expiresInMinutes
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 60
 *         required: false
 *         description: Expiration time in minutes for signed URLs (default is 15 minutes)
 *     responses:
 *       200:
 *         description: Successfully retrieved file list or signed URL
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     files:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: List of file names in the bucket
 *                       example: ["audio/file1.mp3", "audio/file2.wav"]
 *                 - type: object
 *                   properties:
 *                     url:
 *                       type: string
 *                       description: Signed URL for accessing the requested file
 *                       example: "https://storage.googleapis.com/bucket-name/audio/file1.mp3?X-Goog-Algorithm=..."
 *       400:
 *         description: Bad request - missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       404:
 *         description: Not found - requested file not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */

/**
 * API endpoint for accessing audio files from GCP Storage
 * Supports listing files and getting signed URLs for direct access
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify authentication
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get the GCP Storage service
    const storageService = getGcpStorageService();

    // Get the bucket name from environment variables or request
    const bucketName = (req.query.bucket as string) || process.env.GCP_AUDIO_BUCKET_NAME;

    if (!bucketName) {
      return res.status(400).json({
        error:
          'Bucket name is required. Provide it as a query parameter or set GCP_AUDIO_BUCKET_NAME environment variable.',
      });
    }

    // Handle different HTTP methods
    switch (req.method) {
      case 'GET':
        // If a specific file is requested, return a signed URL
        if (req.query.fileName) {
          const fileName = req.query.fileName as string;

          // Check if the file exists
          const exists = await storageService.fileExists(bucketName, fileName);
          if (!exists) {
            return res
              .status(404)
              .json({ error: `File ${fileName} not found in bucket ${bucketName}` });
          }

          // Generate a signed URL with expiration (default 15 minutes)
          const expiresInMinutes = req.query.expiresInMinutes
            ? parseInt(req.query.expiresInMinutes as string)
            : 15;

          const url = await storageService.getSignedUrl(bucketName, fileName, {
            expires: Date.now() + expiresInMinutes * 60 * 1000,
          });

          return res.status(200).json({ url });
        }
        // Otherwise, list files in the bucket
        else {
          // Get optional prefix filter from query params
          const prefix = req.query.prefix as string;

          // List files in the bucket
          const files = await storageService.listFiles(bucketName, prefix ? { prefix } : {});

          // Filter for audio files if requested
          const audioOnly = req.query.audioOnly === 'true';
          const filteredFiles = audioOnly
            ? files.filter(
                file =>
                  file.endsWith('.mp3') ||
                  file.endsWith('.wav') ||
                  file.endsWith('.ogg') ||
                  file.endsWith('.m4a'),
              )
            : files;

          return res.status(200).json({ files: filteredFiles });
        }

      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Error in audio-files API:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
