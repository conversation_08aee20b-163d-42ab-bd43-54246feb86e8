import { NextApiRequest, NextApiResponse } from 'next';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

/**
 * @swagger
 * /api/storage/session-audio:
 *   get:
 *     summary: Get the latest audio file for a session
 *     description: Retrieves the most recent audio recording associated with a given session ID and returns a signed URL for direct access
 *     tags: [Storage]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the session to retrieve audio for
 *       - in: query
 *         name: bucket
 *         schema:
 *           type: string
 *         required: false
 *         description: The name of the GCP Storage bucket to search in (defaults to GCP_AUDIO_BUCKET_NAME environment variable)
 *       - in: query
 *         name: minTimestamp
 *         schema:
 *           type: integer
 *           format: int64
 *         required: false
 *         description: Minimum timestamp (in milliseconds) to filter files - only returns files created after this timestamp
 *     responses:
 *       200:
 *         description: Successfully retrieved the audio file
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 fileName:
 *                   type: string
 *                   description: The name of the audio file
 *                   example: "audio/session123_1609545600000.mp3"
 *                 url:
 *                   type: string
 *                   description: A signed URL to access the audio file
 *                   example: "https://storage.googleapis.com/bucket-name/audio/session123_1609545600000.mp3?X-Goog-Algorithm=..."
 *       400:
 *         description: Bad request - missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       404:
 *         description: Not found - no audio file found for the session
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       405:
 *         description: Method not allowed - only GET requests are supported
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 message:
 *                   type: string
 */

/**
 * API endpoint for retrieving the latest audio file for a specific session ID
 *
 * This endpoint retrieves the most recent audio recording associated with a given session ID.
 * It returns a signed URL that can be used to access the audio file directly.
 *
 * @route GET /api/storage/session-audio
 *
 * @param {string} sessionId - Required. The unique identifier of the session to retrieve audio for
 * @param {string} [bucket] - Optional. The name of the GCP Storage bucket to search in.
 *                           If not provided, uses the GCP_AUDIO_BUCKET_NAME environment variable.
 * @param {number} [minTimestamp] - Optional. Minimum timestamp (in milliseconds) to filter files.
 *                                 Only returns files created after this timestamp.
 *
 * @returns {Object} 200 - Success response with file information
 * @returns {string} 200.fileName - The name of the audio file
 * @returns {string} 200.url - A signed URL to access the audio file
 *
 * @returns {Object} 400 - Error response if required parameters are missing
 * @returns {Object} 401 - Error response if user is not authenticated
 * @returns {Object} 404 - Error response if no audio file is found for the session
 * @returns {Object} 405 - Error response if method is not GET
 * @returns {Object} 500 - Error response if server error occurs
 *
 * @example
 * // Get the latest audio file for a session
 * GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA
 *
 * @example
 * // Get the latest audio file for a session after a specific timestamp
 * GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA&minTimestamp=1744269450000
 *
 * @example
 * // Get the latest audio file for a session from a specific bucket
 * GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA&bucket=custom-audio-bucket
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow GET requests
    if (req.method !== 'GET') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the session ID from query parameters
    const sessionId = req.query.sessionId as string;
    if (!sessionId) {
      return res.status(400).json({ error: 'Session ID is required' });
    }

    // Get optional minimum timestamp
    const minTimestamp = req.query.minTimestamp
      ? parseInt(req.query.minTimestamp as string)
      : undefined;

    // Get the GCP Storage service
    const storageService = getGcpStorageService();

    // Get the bucket name from environment variables or request
    const bucketName = (req.query.bucket as string) || process.env.GCP_AUDIO_BUCKET_NAME;

    if (!bucketName) {
      return res.status(400).json({
        error:
          'Bucket name is required. Provide it as a query parameter or set GCP_AUDIO_BUCKET_NAME environment variable.',
      });
    }

    // Get the latest audio file for the session
    const audioFile = await storageService.getLatestAudioFileForSession(
      bucketName,
      sessionId,
      minTimestamp,
    );

    if (!audioFile) {
      return res.status(404).json({
        error: `No audio files found for session ${sessionId}${minTimestamp ? ` after timestamp ${minTimestamp}` : ''}`,
      });
    }

    // Return the file information
    return res.status(200).json({
      fileName: audioFile.fileName,
      url: audioFile.url,
    });
  } catch (error) {
    console.error('Error in session-audio API:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
