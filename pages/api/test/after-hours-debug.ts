import { NextApiRequest, NextApiResponse } from 'next';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { LocationService } from '@/lib/services/locationService';
import logger from '@/lib/external-api/v2/utils/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Test data from your call
    const testCallData = {
      id: "hbuPwKd9zDJTZmedP6tU",
      date: "2025-08-01T00:00:34.000Z",
      locationId: "118",
      clinicId: 12
    };

    console.log('=== AFTER HOURS DEBUG TEST ===');
    console.log('Test call data:', testCallData);

    // Step 1: Try to get location
    console.log('\n1. Attempting to get location...');
    const location = await LocationService.getLocationById(
      testCallData.locationId.toString(),
      testCallData.clinicId,
    );

    console.log('Location result:', {
      found: !!location,
      id: location?.id,
      name: location?.name,
      hasOfficeHours: !!location?.officeHours,
      hasTimeZone: !!location?.timeZone,
      timeZone: location?.timeZone,
      officeHours: location?.officeHours
    });

    if (!location) {
      return res.status(200).json({
        error: 'Location not found',
        locationId: testCallData.locationId,
        clinicId: testCallData.clinicId
      });
    }

    if (!location.officeHours || !location.timeZone) {
      return res.status(200).json({
        error: 'Missing office hours or timezone',
        location: {
          id: location.id,
          hasOfficeHours: !!location.officeHours,
          hasTimeZone: !!location.timeZone,
          timeZone: location.timeZone
        }
      });
    }

    // Step 2: Check office hours
    console.log('\n2. Checking office hours...');
    const callDate = new Date(testCallData.date);
    console.log('Call date:', callDate.toISOString());
    console.log('Call date local:', callDate.toString());

    const officeHoursStatus = OfficeHoursService.checkOfficeHours(
      location.officeHours,
      location.timeZone,
      callDate
    );

    console.log('Office hours status:', {
      isOpen: officeHoursStatus.isOpen,
      currentStatus: officeHoursStatus.currentStatus,
      timeZone: location.timeZone
    });

    // Step 3: Determine if after hours
    const isAfterHours = !officeHoursStatus.isOpen;
    console.log('\n3. After hours determination:', {
      isAfterHours,
      shouldAddAfterHoursType: isAfterHours
    });

    return res.status(200).json({
      success: true,
      testCallData,
      location: {
        id: location.id,
        name: location.name,
        timeZone: location.timeZone,
        hasOfficeHours: !!location.officeHours,
        officeHours: location.officeHours
      },
      officeHoursCheck: {
        callDate: callDate.toISOString(),
        isOpen: officeHoursStatus.isOpen,
        currentStatus: officeHoursStatus.currentStatus,
        isAfterHours
      },
      conclusion: {
        shouldAddAfterHoursType: isAfterHours,
        reason: isAfterHours ? 'Office is closed at call time' : 'Office is open at call time'
      }
    });

  } catch (error) {
    console.error('Debug test error:', error);
    return res.status(500).json({
      error: 'Debug test failed',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}
