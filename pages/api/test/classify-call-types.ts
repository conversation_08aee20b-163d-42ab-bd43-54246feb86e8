import { NextApiRequest, NextApiResponse } from 'next';
import { callsService } from '@/utils/firestore';
import { LLMClient } from '@/utils/llm/llm.client';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import { formatDialogflowDuration } from '@/utils/call-duration-utils';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { CallType } from '@/models/CallTypes';

/**
 * @swagger
 * /api/test/classify-call-types:
 *   post:
 *     summary: Classify call types using LLM
 *     description: Takes a call ID and updates its type and duration based on GCP conversation data using LLM classification
 *     tags: [Test, Calls, LLM]
 *     parameters:
 *       - in: query
 *         name: callId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the call to classify
 *     responses:
 *       200:
 *         description: Successfully classified and updated call types
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Call types classified and updated successfully"
 *                 callId:
 *                   type: string
 *                   example: "abc123"
 *                 classification:
 *                   type: object
 *                   properties:
 *                     classifiedTypes:
 *                       type: array
 *                       items:
 *                         type: integer
 *                       example: [4, 9]
 *                     primaryType:
 *                       type: integer
 *                       example: 4
 *                     finalCallTypes:
 *                       type: array
 *                       items:
 *                         type: integer
 *                       example: [4, 9]
 *                 changes:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: object
 *                       properties:
 *                         old:
 *                           type: integer
 *                         new:
 *                           type: integer
 *                     callTypes:
 *                       type: object
 *                       properties:
 *                         old:
 *                           type: array
 *                           items:
 *                             type: integer
 *                         new:
 *                           type: array
 *                           items:
 *                             type: integer
 *                     duration:
 *                       type: object
 *                       properties:
 *                         old:
 *                           type: string
 *                         new:
 *                           type: string
 *       400:
 *         description: Bad request - missing call ID
 *       401:
 *         description: Unauthorized - user is not authenticated
 *       404:
 *         description: Call not found
 *       405:
 *         description: Method not allowed - only POST is supported
 *       500:
 *         description: Internal server error
 */

/**
 * API endpoint for classifying call types using LLM
 *
 * This endpoint takes a call ID, fetches fresh transcription data from GCP,
 * uses LLM to classify the call types, and updates the call record with
 * the new classification and duration.
 *
 * @route POST /api/test/classify-call-types
 *
 *
 * @returns {Object} 200 - Success response with classification results
 * @returns {boolean} 200.success - Whether the operation was successful
 * @returns {string} 200.message - Success message
 * @returns {string} 200.callId - The call ID that was processed
 * @returns {Object} 200.classification - Classification results
 * @returns {Array<number>} 200.classification.classifiedTypes - All types identified by LLM
 * @returns {number} 200.classification.primaryType - Primary call type
 * @returns {Array<number>} 200.classification.finalCallTypes - Final merged call types
 * @returns {Object} 200.changes - Changes made to the call record
 *
 * @example
 * // Classify a call
 * POST /api/test/classify-call-types?callId=abc123
 * @param req
 * @param res
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    // Get the call ID from query parameters
    const callId = req.query.callId as string;
    if (!callId) {
      return res.status(400).json({ error: 'Call ID is required' });
    }

    console.log(`Starting call type classification test for call ID: ${callId}`);

    // 1. Get the call from the database
    const call = await callsService.getCallById(callId);
    if (!call) {
      return res.status(404).json({ error: `Call with ID ${callId} not found` });
    }

    console.log(
      `Found call record - Type: ${call.type}, CallTypes: [${call.callTypes?.join(', ') || 'none'}], SessionId: ${call.sessionId}`,
    );

    // 2. Fetch fresh conversation data directly from GCP to get duration and transcription
    let transcriptionData = {
      transcription: call.transcription || '',
      transcriptionWithAudio: call.transcriptionWithAudio || '',
      duration: '0 min', // Will be updated with GCP conversation duration if available
    };

    if (call.sessionId) {
      try {
        console.log(`Fetching fresh conversation data from GCP for session: ${call.sessionId}`);

        // Get GCP API credentials
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (!projectId || !agentId) {
          throw new Error('Missing GCP configuration (GCP_PROJECT_ID or GCP_AGENT_ID)');
        }

        // Get access token for Dialogflow API
        const accessToken = await DialogflowAuthService.getAccessToken();
        if (!accessToken) {
          throw new Error('Failed to obtain access token for Dialogflow API');
        }

        // Fetch conversation data directly from Dialogflow API
        const conversation = await getDialogflowConversation({
          projectId,
          locationId,
          agentId,
          sessionId: call.sessionId,
          accessToken,
        });

        // Extract transcript from conversation
        const dialogflowTranscript = extractTranscriptFromConversation(conversation);

        // Extract duration directly from conversation object
        let dialogflowDuration = '0 min';
        if (conversation.duration) {
          dialogflowDuration = formatDialogflowDuration(conversation.duration);
          console.log(
            `Extracted duration from DialogflowConversation: ${conversation.duration} → ${dialogflowDuration}`,
          );
        }

        // Get transcription with audio using GCP storage service
        let transcriptionWithAudio = '';
        try {
          const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
          if (bucketName) {
            const storageService = getGcpStorageService();
            const interactions = await storageService.getTranscriptWithAudioRecords(
              bucketName,
              call.sessionId,
              undefined,
            );
            if (interactions && interactions.length > 0) {
              transcriptionWithAudio = JSON.stringify(interactions);
            }
          }
        } catch (audioError) {
          console.warn(
            `Failed to get transcription with audio for session ${call.sessionId}:`,
            audioError instanceof Error ? audioError.message : String(audioError),
          );
        }

        // Update transcription data with fresh GCP data
        transcriptionData = {
          transcription: dialogflowTranscript,
          transcriptionWithAudio,
          duration: dialogflowDuration, // Duration directly from DialogflowConversation
        };

        console.log(`Fetched fresh data from GCP DialogflowConversation:`);
        console.log(`  - HasTranscription: ${!!transcriptionData.transcription}`);
        console.log(`  - HasAudio: ${!!transcriptionData.transcriptionWithAudio}`);
        console.log(
          `  - Duration from conversation: ${dialogflowDuration} (was: ${call.duration})`,
        );
      } catch (error) {
        console.warn(
          `Failed to fetch fresh conversation data for session ${call.sessionId}, using existing data:`,
          error instanceof Error ? error.message : String(error),
        );
      }
    } else {
      console.log(
        `No sessionId available, cannot fetch GCP conversation duration. Using fallback duration: 0 min`,
      );
    }

    // 3. Prepare call object with fresh transcription data for LLM
    const callForClassification = {
      ...call,
      transcription: transcriptionData.transcription,
      transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
      duration: transcriptionData.duration,
    };

    // 4. Use LLM to classify call types
    const llmClient = LLMClient.getInstance();

    // Check if call has AFTER_HOURS type that should be preserved
    const preserveTypes: CallType[] = [];
    if (
      callForClassification.callTypes &&
      callForClassification.callTypes.includes(CallType.AFTER_HOURS)
    ) {
      preserveTypes.push(CallType.AFTER_HOURS);
    }

    const classifiedTypes = await llmClient.classifyCallTypes(callForClassification, preserveTypes);

    if (!classifiedTypes || classifiedTypes.length === 0) {
      console.warn(`LLM classification failed or returned no types for call ${callId}`);
      return res.status(500).json({
        error: 'Failed to classify call types',
        message: 'LLM classification returned no valid types',
      });
    }

    console.log(
      `LLM classified call ${callId} with ${classifiedTypes.length} types: [${classifiedTypes.join(', ')}]`,
    );

    // 5. Determine primary type (first in array) and build callTypes array
    const primaryType = classifiedTypes[0];
    let updatedCallTypes = [primaryType];

    // Merge additional types if there are multiple
    if (classifiedTypes.length > 1) {
      for (let i = 1; i < classifiedTypes.length; i++) {
        updatedCallTypes = mergeCallTypes(updatedCallTypes, classifiedTypes[i]);
      }
    }

    // 6. Update the call in the database
    const updateData = {
      type: primaryType,
      callTypes: updatedCallTypes,
      transcription: transcriptionData.transcription,
      transcriptionWithAudio: transcriptionData.transcriptionWithAudio,
      duration: transcriptionData.duration,
    };

    await callsService.updateCall(callId, updateData);

    console.log(`Successfully updated call ${callId}:`);
    console.log('updateData: ', updateData);
    console.log(`  Type: ${call.type} → ${primaryType}`);
    console.log(
      `  CallTypes: [${call.callTypes?.join(', ') || 'none'}] → [${updatedCallTypes.join(', ')}]`,
    );
    console.log(`  Duration: ${call.duration} → ${transcriptionData.duration}`);

    // 7. Get the updated call for response
    const updatedCall = await callsService.getCallById(callId);

    return res.status(200).json({
      success: true,
      message: 'Call types classified and updated successfully',
      callId,
      classification: {
        classifiedTypes,
        primaryType,
        finalCallTypes: updatedCallTypes,
      },
      changes: {
        type: {
          old: call.type,
          new: primaryType,
        },
        callTypes: {
          old: call.callTypes,
          new: updatedCallTypes,
        },
        duration: {
          old: call.duration,
          new: transcriptionData.duration,
        },
      },
      updatedCall,
    });
  } catch (error) {
    console.error('Error in classify-call-types API:', error);

    return res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
