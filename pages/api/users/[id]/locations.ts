import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserLocationService } from '@/lib/services/userLocationService';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';

/**
 * @swagger
 * /api/users/{id}/locations:
 *   get:
 *     summary: Get all locations assigned to a specific user
 *     tags: [User Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     responses:
 *       200:
 *         description: User's assigned locations and practices
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 locations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                 practices:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Practice'
 *                 currentLocationId:
 *                   type: string
 *                   description: Currently selected location ID
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Assign user to additional locations
 *     tags: [User Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - locationIds
 *             properties:
 *               locationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of location IDs to assign to user
 *               setAsCurrent:
 *                 type: boolean
 *                 default: false
 *                 description: Set the first location as current location
 *     responses:
 *       200:
 *         description: User assigned to locations successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Remove user from specific locations
 *     tags: [User Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - locationIds
 *             properties:
 *               locationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of location IDs to remove from user
 *     responses:
 *       200:
 *         description: User removed from locations successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 user:
 *                   $ref: '#/components/schemas/User'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */

interface AssignUserToLocationsRequest {
  locationIds: string[];
  setAsCurrent?: boolean;
}

interface RemoveUserFromLocationsRequest {
  locationIds: string[];
}

interface UserLocationsResponse {
  user: User;
  locations: Location[];
  practices: Practice[];
  currentLocationId?: string;
}

interface UserLocationAssignmentResponse {
  success: boolean;
  user: User;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract user ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid user ID',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetUserLocations(req, res, user as User, id);
    } else if (req.method === 'POST') {
      return await handleAssignUserToLocations(req, res, user as User, id);
    } else if (req.method === 'DELETE') {
      return await handleRemoveUserFromLocations(req, res, user as User, id);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in user locations API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve all locations assigned to a user
 */
async function handleGetUserLocations(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  userId: string,
): Promise<void> {
  try {
    // Validate permissions (users can view their own locations, admins can view any)
    if (userId !== requestingUser.id) {
      if (
        requestingUser.role !== UserRole.CLINIC_ADMIN &&
        requestingUser.role !== UserRole.SUPER_ADMIN
      ) {
        return res.status(403).json({
          success: false,
          message: 'Forbidden: You can only view your own location assignments',
        });
      }
    }

    const result = await UserLocationService.getUserLocations(userId, requestingUser.clinicId!);

    const response: UserLocationsResponse = {
      user: result.user,
      locations: result.locations,
      practices: result.practices,
      currentLocationId: result.currentLocationId,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting user locations:', error);

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('does not belong')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve user locations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle POST request to assign user to additional locations
 */
async function handleAssignUserToLocations(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  userId: string,
): Promise<void> {
  try {
    // Validate user permissions (only admins can assign locations)
    if (
      requestingUser.role !== UserRole.CLINIC_ADMIN &&
      requestingUser.role !== UserRole.SUPER_ADMIN
    ) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can assign user locations',
      });
    }

    // Validate request body
    const { locationIds, setAsCurrent }: AssignUserToLocationsRequest = req.body;

    if (!locationIds || !Array.isArray(locationIds) || locationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'locationIds array is required and must not be empty',
      });
    }

    // Validate all location IDs are strings
    if (!locationIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        message: 'All location IDs must be non-empty strings',
      });
    }

    if (setAsCurrent !== undefined && typeof setAsCurrent !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'setAsCurrent must be a boolean',
      });
    }

    // Assign user to locations
    const updatedUser = await UserLocationService.assignUserToLocations(
      userId,
      locationIds,
      requestingUser,
      { setAsCurrent: setAsCurrent || false },
    );

    const response: UserLocationAssignmentResponse = {
      success: true,
      user: updatedUser,
      message: `User assigned to ${locationIds.length} location(s) successfully`,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error assigning user to locations:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('outside your clinic') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to assign user to locations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle DELETE request to remove user from specific locations
 */
async function handleRemoveUserFromLocations(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  userId: string,
): Promise<void> {
  try {
    // Validate user permissions (only admins can remove location assignments)
    if (
      requestingUser.role !== UserRole.CLINIC_ADMIN &&
      requestingUser.role !== UserRole.SUPER_ADMIN
    ) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can remove user location assignments',
      });
    }

    // Validate request body
    const { locationIds }: RemoveUserFromLocationsRequest = req.body;

    if (!locationIds || !Array.isArray(locationIds) || locationIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'locationIds array is required and must not be empty',
      });
    }

    // Validate all location IDs are strings
    if (!locationIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        message: 'All location IDs must be non-empty strings',
      });
    }

    // Remove user from locations
    const updatedUser = await UserLocationService.removeUserFromLocations(
      userId,
      locationIds,
      requestingUser,
    );

    const response: UserLocationAssignmentResponse = {
      success: true,
      user: updatedUser,
      message: `User removed from ${locationIds.length} location(s) successfully`,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error removing user from locations:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('outside your clinic') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to remove user from locations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
