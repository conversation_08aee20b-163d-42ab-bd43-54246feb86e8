import { NextApiRequest, NextApiResponse } from 'next';
import logger from '@/lib/external-api/v2/utils/logger';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { locationsService } from '@/utils/firestore';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import { compactFormatLocationHours } from '@/lib/external-api/v2/utils/location-utils';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify Firebase authentication
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const { locationId = URMA_LOMBARD_LOCATION_ID } = req.query;
    const location = await locationsService.getLocationById(locationId as string);
    if (!location) {
      logger.info({ context: 'locations.details', locationId }, 'Location not found');
      return res.status(404).json({ message: 'Location not found' });
    }

    return res.status(200).json({
      ...location,
      compactOfficeHours: compactFormatLocationHours(location.officeHours).join('\n'),
    });
  } catch (err) {
    logger.error({ context: 'locations.details', err }, 'Error getting location details');
    return res.status(500).json({ message: 'Internal server error' });
  }
}
