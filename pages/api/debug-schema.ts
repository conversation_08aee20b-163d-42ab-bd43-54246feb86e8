import { NextApiRequest, NextApiResponse } from 'next';
import { getSwaggerSpec, OpenAPISpec } from '@/utils/swagger';

/**
 * Debug endpoint for the Swagger schema
 * This endpoint returns a formatted JSON representation of the Swagger schema
 * with additional debugging information
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get the Swagger spec
    const spec: OpenAPISpec = getSwaggerSpec();

    // Add debugging information
    const debugInfo = {
      spec,
      meta: {
        pathsCount: Object.keys(spec.paths || {}).length,
        tagsCount: (spec.tags || []).length,
        schemasCount: Object.keys(spec.components?.schemas || {}).length,
        hasSecuritySchemes:
          !!spec.components?.securitySchemes &&
          Object.keys(spec.components.securitySchemes).length > 0,
      },
    };

    // Set appropriate headers for JSON response
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'no-store');

    // Return the debug info
    res.status(200).json(debugInfo);
  } catch (error) {
    console.error('Error generating debug schema:', error);

    // Return error information
    res.status(500).json({
      error: 'Failed to generate debug schema',
      message: error instanceof Error ? error.message : String(error),
      stack:
        process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined,
    });
  }
}
