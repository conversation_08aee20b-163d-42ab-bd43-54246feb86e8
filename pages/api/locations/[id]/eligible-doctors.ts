import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallScheduleService } from '@/lib/services/on-call-schedule';
import { LocationsRepository } from '@/lib/repositories/locations-repository';
import { UsersRepository } from '@/lib/repositories/users-repository';
import { ensureDbInitialized } from '@/lib/middleware/db-init';
import { EligibleDoctor } from '@/models/EligibleDoctor';

interface ApiResponse {
  doctors?: EligibleDoctor[];
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/locations/{id}/eligible-doctors:
 *   get:
 *     summary: Get doctors eligible for on-call duty
 *     tags: [On-Call Schedules, Locations]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Location ID
 *         schema:
 *           type: string
 *       - name: date
 *         in: query
 *         description: Check availability for specific date (YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: startTime
 *         in: query
 *         description: Check conflicts for specific start time (HH:MM)
 *         schema:
 *           type: string
 *       - name: endTime
 *         in: query
 *         description: Check conflicts for specific end time (HH:MM)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of eligible doctors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 doctors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       email:
 *                         type: string
 *                       phone:
 *                         type: string
 *                       specialty:
 *                         type: string
 *                       canTakeAppointments:
 *                         type: boolean
 *                       currentSchedules:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             date:
 *                               type: string
 *                             startTime:
 *                               type: string
 *                             endTime:
 *                               type: string
 *                       hasConflicts:
 *                         type: boolean
 *       400:
 *         description: Bad request
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  try {
    if (req.method !== 'GET') {
      res.setHeader('Allow', ['GET']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }

    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Location ID is required' });
    }

    await handleGet(req, res, id);
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Internal server error',
    });
  }
}

async function handleGet(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
  locationId: string,
): Promise<void> {
  try {
    // Initialize database connection
    await ensureDbInitialized();

    const { date, startTime, endTime } = req.query;

    // Get location to validate access using repository
    const locationsRepository = new LocationsRepository();
    const location = await locationsRepository.findById(locationId);

    if (!location) {
      return res.status(404).json({ error: 'Location not found' });
    }

    // Validate date format if provided
    if (date && typeof date === 'string' && !/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      return res.status(400).json({ error: 'Invalid date format. Use YYYY-MM-DD' });
    }

    // Validate time format if provided
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (startTime && typeof startTime === 'string' && !timeRegex.test(startTime)) {
      return res.status(400).json({ error: 'Invalid startTime format. Use HH:MM' });
    }
    if (endTime && typeof endTime === 'string' && !timeRegex.test(endTime)) {
      return res.status(400).json({ error: 'Invalid endTime format. Use HH:MM' });
    }

    // Get all users who can take appointments, have specialty, and have access to this location
    const usersRepository = new UsersRepository();
    const allUsers = await usersRepository.findCanTakeAppointments(
      location.clinicId,
      locationId,
      500,
    );

    // Filter users who have specialty and access to this location
    const eligibleUsers = allUsers.filter(user => {
      // Must have a specialty
      if (!user.specialty || user.specialty.trim() === '') {
        return false;
      }

      // Must have access to this location
      return user.locationIds && user.locationIds.includes(locationId);
    });

    if (eligibleUsers.length === 0) {
      return res.status(200).json({
        doctors: [],
        message: 'No eligible doctors with specialties found for this location',
      });
    }

    const eligibleDoctors: EligibleDoctor[] = [];

    for (const user of eligibleUsers) {
      // Get current schedules for this doctor
      let currentSchedules: { date: string; startTime: string; endTime: string }[] = [];
      let hasConflicts = false;

      try {
        // Get doctor's existing schedules (next 30 days)
        const startDate = new Date();
        const endDate = new Date();
        endDate.setDate(endDate.getDate() + 30);

        const doctorSchedules = await OnCallScheduleService.getSchedulesByDoctor(
          user.id,
          startDate,
          endDate,
          location.clinicId || 0,
        );

        currentSchedules = doctorSchedules.map(schedule => ({
          date: schedule.date,
          startTime: schedule.startTime,
          endTime: schedule.endTime,
        }));

        // Check for conflicts if specific date/time provided
        if (
          date &&
          startTime &&
          endTime &&
          typeof date === 'string' &&
          typeof startTime === 'string' &&
          typeof endTime === 'string'
        ) {
          const conflicts = await OnCallScheduleService.checkScheduleConflicts(
            locationId,
            date,
            startTime,
            endTime,
          );

          // Check if any conflicts involve this doctor
          hasConflicts = conflicts.some(
            conflict => conflict.conflictingSchedule.doctorId === user.id,
          );
        }
      } catch (error) {
        console.warn(`Error checking schedules for doctor ${user.id}:`, error);
        // Continue with doctor but mark as having potential conflicts
        hasConflicts = true;
      }

      const doctor: EligibleDoctor = {
        id: user.id,
        name: user.name || 'Unknown Doctor',
        email: user.email || '',
        phone: user.phone,
        specialty: user.specialty!, // We already filtered for non-empty specialties
        canTakeAppointments: user.canTakeAppointments,
        currentSchedules,
        hasConflicts,
      };

      eligibleDoctors.push(doctor);
    }

    // Sort doctors by name
    eligibleDoctors.sort((a, b) => a.name.localeCompare(b.name));

    res.status(200).json({
      doctors: eligibleDoctors,
      message: `Found ${eligibleDoctors.length} eligible doctors for this location`,
    });
  } catch (error) {
    console.error('Error fetching eligible doctors:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch eligible doctors',
    });
  }
}
