import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { LocationService } from '@/lib/services/locationService';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { User } from '@/models/auth';
import { Location } from '@/models/Location';

/**
 * @swagger
 * /api/locations/{id}/office-hours/next-open:
 *   get:
 *     summary: Get next open time information
 *     tags: [Office Hours]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *       - name: fromTime
 *         in: query
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Optional timestamp to calculate from (defaults to now)
 *     responses:
 *       200:
 *         description: Next open time information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 nextBusinessDay:
 *                   type: object
 *                   properties:
 *                     date:
 *                       type: string
 *                       format: date
 *                       description: Date of next business day
 *                     hours:
 *                       type: object
 *                       properties:
 *                         start:
 *                           type: string
 *                           description: Opening time (HH:MM)
 *                         end:
 *                           type: string
 *                           description: Closing time (HH:MM)
 *                 nextOpenDateTime:
 *                   type: string
 *                   format: date-time
 *                   description: Next opening date and time in location timezone
 *                 locationTimezone:
 *                   type: string
 *                   description: Location timezone
 *                 timezoneLabel:
 *                   type: string
 *                   description: Human-readable timezone label
 *                 daysUntilOpen:
 *                   type: number
 *                   description: Number of days until next open
 *                 formattedNextOpen:
 *                   type: string
 *                   description: Human-readable next open time
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */

interface NextOpenResponse {
  nextBusinessDay: { date: string; hours: { start: string; end: string } } | null;
  nextOpenDateTime?: string;
  locationTimezone: string;
  timezoneLabel: string;
  daysUntilOpen?: number;
  formattedNextOpen?: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      message: `Method ${req.method} not allowed`,
    });
  }

  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract location ID from query
  const { id, fromTime } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid location ID',
    });
  }

  try {
    return await handleGetNextOpenTime(req, res, user, id, fromTime as string | undefined);
  } catch (error) {
    console.error('Error in next open time API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve next open time information
 */
async function handleGetNextOpenTime(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
  fromTime?: string,
): Promise<void> {
  try {
    // Get the location
    const result = await LocationService.getLocationsByClinicId(user.clinicId!);
    const location = result.locations.find((loc: Location) => loc.id === locationId);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found',
      });
    }

    // Default values if not set
    const officeHours = location.officeHours || {};
    const locationTimezone = location.timeZone || 'America/Chicago'; // Default timezone

    // Parse fromTime if provided
    let checkTime: Date | undefined;
    if (fromTime) {
      checkTime = new Date(fromTime);
      if (isNaN(checkTime.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid fromTime format',
        });
      }
    }

    // Get next business day
    const nextBusinessDay = OfficeHoursService.getNextBusinessDay(
      officeHours,
      locationTimezone,
      checkTime,
    );

    // Build response
    const response: NextOpenResponse = {
      nextBusinessDay,
      locationTimezone,
      timezoneLabel: getTimezoneLabel(locationTimezone),
    };

    if (nextBusinessDay) {
      // Calculate next open date time
      const nextOpenDate = new Date(`${nextBusinessDay.date}T${nextBusinessDay.hours.start}:00`);
      response.nextOpenDateTime = nextOpenDate.toISOString();

      // Calculate days until open
      const now = checkTime || new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const businessDay = new Date(nextBusinessDay.date);
      response.daysUntilOpen = Math.ceil(
        (businessDay.getTime() - today.getTime()) / (1000 * 60 * 60 * 24),
      );

      // Format human-readable next open time
      response.formattedNextOpen = formatNextOpenTime(nextBusinessDay);
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting next open time:', error);

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to get next open time',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Get human-readable timezone label
 */
function getTimezoneLabel(timezone: string): string {
  try {
    const now = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      timeZoneName: 'shortOffset',
    });
    const parts = formatter.formatToParts(now);
    const offset = parts.find(part => part.type === 'timeZoneName')?.value || '';
    return `${timezone} (${offset})`;
  } catch {
    return timezone;
  }
}

/**
 * Format next open time for human readability
 */
function formatNextOpenTime(nextBusinessDay: {
  date: string;
  hours: { start: string; end: string };
}): string {
  try {
    const date = new Date(nextBusinessDay.date);
    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
    const dateString = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });

    // Format time in 12-hour format
    const [hours, minutes] = nextBusinessDay.hours.start.split(':').map(Number);
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
    const timeString = `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;

    return `${dayName}, ${dateString} at ${timeString}`;
  } catch {
    return `${nextBusinessDay.date} at ${nextBusinessDay.hours.start}`;
  }
}
