import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { LocationService } from '@/lib/services/locationService';
import { OfficeHoursService, OfficeHoursStatus } from '@/lib/services/office-hours';
import { User } from '@/models/auth';
import { Location } from '@/models/Location';

/**
 * @swagger
 * /api/locations/{id}/office-hours/status:
 *   get:
 *     summary: Get current office hours status
 *     tags: [Office Hours]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *       - name: timestamp
 *         in: query
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Optional timestamp to check status at specific time (defaults to now)
 *     responses:
 *       200:
 *         description: Current office hours status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/OfficeHoursStatus'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({
      success: false,
      message: `Method ${req.method} not allowed`,
    });
  }

  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract location ID from query
  const { id, timestamp } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid location ID',
    });
  }

  try {
    return await handleGetOfficeHoursStatus(req, res, user, id, timestamp as string | undefined);
  } catch (error) {
    console.error('Error in office hours status API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve office hours status
 */
async function handleGetOfficeHoursStatus(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
  timestamp?: string,
): Promise<void> {
  try {
    // Get the location
    const result = await LocationService.getLocationsByClinicId(user.clinicId!);
    const location = result.locations.find((loc: Location) => loc.id === locationId);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found',
      });
    }

    // Default values if not set
    const officeHours = location.officeHours || {};
    const locationTimezone = location.timeZone || 'America/Chicago'; // Default timezone

    // Parse timestamp if provided
    let checkTime: Date | undefined;
    if (timestamp) {
      checkTime = new Date(timestamp);
      if (isNaN(checkTime.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid timestamp format',
        });
      }
    }

    // Get current status
    const status: OfficeHoursStatus = OfficeHoursService.checkOfficeHours(
      officeHours,
      locationTimezone,
      checkTime,
    );

    res.status(200).json(status);
  } catch (error) {
    console.error('Error getting office hours status:', error);

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to get office hours status',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
