import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { User, UserRole } from '@/models/auth';
import { OnCallSchedule } from '@/models/OnCallSchedule';
import type { LocationsRepository, OnCallSchedulesRepository } from '@/lib/repositories';
import { getRepositories } from '@/lib/repositories';

interface OnCallScheduleRequest {
  doctorId: string;
  doctorName: string;
  doctorPhone?: string;
  startDate: string; // ISO string
  endDate: string; // ISO string
  notes?: string;
  isPrimary?: boolean;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id: locationId } = req.query;

  if (!locationId || typeof locationId !== 'string') {
    return res.status(400).json({ error: 'Location ID is required' });
  }

  // Verify authentication
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    // Ensure database connections are ready (runs only once per server lifecycle)
    const repoManager = getRepositories();
    await repoManager.initialize();

    const locationsRepo = repoManager.locations;
    const schedulesRepo = repoManager.onCallSchedules;

    switch (req.method) {
      case 'GET':
        return await handleGet(req, res, locationId, user, locationsRepo, schedulesRepo);
      case 'POST':
        return await handlePost(req, res, locationId, user, locationsRepo, schedulesRepo);
      case 'DELETE':
        return await handleDelete(req, res, locationId, user, locationsRepo, schedulesRepo);
      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('On-call schedules API error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleGet(
  req: NextApiRequest,
  res: NextApiResponse,
  locationId: string,
  user: User,
  locationsRepo: LocationsRepository,
  schedulesRepo: OnCallSchedulesRepository,
) {
  try {
    // Fetch location to verify existence and clinic
    const location = await locationsRepo.findById(locationId);

    if (!location) {
      return res.status(404).json({ error: 'Location not found' });
    }

    // Permission check
    if (user.clinicId !== location.clinicId) {
      return res.status(403).json({ error: 'Access denied to this location' });
    }

    // Fetch schedules from MySQL (active only)
    const scheduleEntities = await schedulesRepo.findActiveByLocation(
      locationId,
      location.clinicId,
    );

    const schedules = scheduleEntities.map(s => ({
      id: s.id,
      doctorId: s.doctorId,
      doctorName: s.doctorName,
      locationId: s.locationId,
      startDate: new Date(`${s.date}T${s.startTime}`),
      endDate: new Date(`${s.date}T${s.endTime}`),
      isActive: s.isActive,
      contactInfo: {
        phone: s.doctorPhone,
        email: '', // Email not stored
      },
      notes: s.notes,
      isPrimary: s.isPrimary,
    }));

    return res.status(200).json({ schedules });
  } catch (error) {
    console.error('Error fetching on-call schedules:', error);
    return res.status(500).json({ error: 'Failed to fetch schedules' });
  }
}

async function handlePost(
  req: NextApiRequest,
  res: NextApiResponse,
  locationId: string,
  user: User,
  locationsRepo: LocationsRepository,
  schedulesRepo: OnCallSchedulesRepository,
) {
  try {
    const scheduleData: OnCallScheduleRequest = req.body;

    // Validate required fields
    if (
      !scheduleData.doctorId ||
      !scheduleData.doctorName ||
      !scheduleData.startDate ||
      !scheduleData.endDate
    ) {
      return res.status(400).json({
        error: 'Missing required fields: doctorId, doctorName, startDate, endDate',
      });
    }

    const location = await locationsRepo.findById(locationId);

    if (!location) {
      return res.status(404).json({ error: 'Location not found' });
    }

    if (user.clinicId !== location.clinicId) {
      return res.status(403).json({ error: 'Access denied to this location' });
    }

    if (user.role !== UserRole.CLINIC_ADMIN) {
      return res.status(403).json({ error: 'Admin privileges required to create schedules' });
    }

    // Parse and validate dates
    const startDate = new Date(scheduleData.startDate);
    const endDate = new Date(scheduleData.endDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json({ error: 'Invalid startDate or endDate format' });
    }

    if (endDate < startDate) {
      return res.status(400).json({ error: 'endDate must be after startDate' });
    }

    // Helper to generate list of dates between start and end (inclusive)
    const dates: Date[] = [];
    const cursor = new Date(startDate);
    while (cursor <= endDate) {
      dates.push(new Date(cursor));
      cursor.setDate(cursor.getDate() + 1);
    }

    // Check for conflicts before creating anything
    for (const dateObj of dates) {
      const dateStr = dateObj.toISOString().split('T')[0];

      const existing = await schedulesRepo.findMany({
        where: {
          doctorId: scheduleData.doctorId,
          locationId,
          date: dateStr,
          isActive: true,
        },
        limit: 1,
      });

      if (existing.items.length > 0) {
        return res.status(409).json({
          error: `Doctor already has an active on-call schedule for ${dateStr}`,
        });
      }
    }

    // Create per-day schedules
    const createdSchedules: OnCallSchedule[] = [];

    for (const dateObj of dates) {
      const isFirstDay = dateObj.toDateString() === startDate.toDateString();
      const isLastDay = dateObj.toDateString() === endDate.toDateString();

      const dateStr = dateObj.toISOString().split('T')[0];

      const perDaySchedule: Omit<OnCallSchedule, 'id'> = {
        doctorId: scheduleData.doctorId,
        doctorName: scheduleData.doctorName,
        doctorPhone: scheduleData.doctorPhone ?? '',
        locationId,
        clinicId: location.clinicId,
        date: dateStr,
        startTime: isFirstDay ? startDate.toTimeString().slice(0, 5) : '00:00',
        endTime: isLastDay ? endDate.toTimeString().slice(0, 5) : '23:59',
        isActive: true,
        timezone: location.timeZone || 'America/Chicago',
        notes: scheduleData.notes ?? '',
        isPrimary: scheduleData.isPrimary ?? true,
        createdBy: user.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const created = await schedulesRepo.create(perDaySchedule);
      createdSchedules.push(created);
    }

    const summarySchedule = {
      id: createdSchedules[0]?.id || '',
      doctorId: scheduleData.doctorId,
      doctorName: scheduleData.doctorName,
      locationId,
      startDate,
      endDate,
      isActive: true,
      contactInfo: {
        phone: scheduleData.doctorPhone ?? '',
        email: '',
      },
      notes: scheduleData.notes ?? '',
      isPrimary: scheduleData.isPrimary ?? true,
    };

    return res.status(201).json({
      message: 'On-call schedules created successfully',
      schedule: summarySchedule,
      schedules: createdSchedules.map(s => ({
        id: s.id,
        doctorId: s.doctorId,
        doctorName: s.doctorName,
        locationId: s.locationId,
        startDate: new Date(`${s.date}T${s.startTime}`),
        endDate: new Date(`${s.date}T${s.endTime}`),
        isActive: s.isActive,
        contactInfo: {
          phone: s.doctorPhone,
          email: '',
        },
        notes: s.notes,
        isPrimary: s.isPrimary,
      })),
    });
  } catch (error) {
    console.error('Error creating on-call schedule:', error);
    return res.status(500).json({ error: 'Failed to create schedule' });
  }
}

async function handleDelete(
  req: NextApiRequest,
  res: NextApiResponse,
  locationId: string,
  user: User,
  locationsRepo: LocationsRepository,
  schedulesRepo: OnCallSchedulesRepository,
) {
  try {
    const { scheduleId } = req.query;

    if (!scheduleId || typeof scheduleId !== 'string') {
      return res.status(400).json({ error: 'Schedule ID is required as query parameter' });
    }

    // Verify location exists and user has access
    const location = await locationsRepo.findById(locationId);
    if (!location) {
      return res.status(404).json({ error: 'Location not found' });
    }

    if (user.clinicId !== location.clinicId) {
      return res.status(403).json({ error: 'Access denied to this location' });
    }

    if (user.role !== UserRole.CLINIC_ADMIN) {
      return res.status(403).json({ error: 'Admin privileges required to remove schedules' });
    }

    // Get the schedule to verify it exists and belongs to this location
    const schedule = await schedulesRepo.findById(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    if (schedule.locationId !== locationId) {
      return res.status(400).json({ error: 'Schedule does not belong to this location' });
    }

    // Soft delete by deactivating the schedule (save to both databases)
    await schedulesRepo.update(scheduleId, {
      isActive: false,
      updatedAt: new Date(),
    });

    console.log('Schedule deactivated successfully:', {
      scheduleId,
      locationId,
      doctorName: schedule.doctorName,
      deactivatedBy: user.id,
    });

    return res.status(200).json({
      message: 'Schedule removed successfully',
      scheduleId,
    });
  } catch (error) {
    console.error('Error removing on-call schedule:', error);
    return res.status(500).json({ error: 'Failed to remove schedule' });
  }
}
