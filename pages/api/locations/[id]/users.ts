import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserLocationService } from '@/lib/services/userLocationService';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';

/**
 * @swagger
 * /api/locations/{id}/users:
 *   get:
 *     summary: Get all users assigned to a specific location
 *     tags: [Location Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Maximum number of users to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of users to skip
 *     responses:
 *       200:
 *         description: Users assigned to the location
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 location:
 *                   $ref: '#/components/schemas/Location'
 *                 users:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 total:
 *                   type: integer
 *                   description: Total number of users assigned to location
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Assign additional users to a location
 *     tags: [Location Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of user IDs to assign to location
 *     responses:
 *       200:
 *         description: Users assigned to location successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 successful:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       user:
 *                         $ref: '#/components/schemas/User'
 *                 failed:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       error:
 *                         type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Remove users from a location
 *     tags: [Location Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userIds
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of user IDs to remove from location
 *     responses:
 *       200:
 *         description: Users removed from location successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 successful:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       user:
 *                         $ref: '#/components/schemas/User'
 *                 failed:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       error:
 *                         type: string
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */

interface AssignUsersToLocationRequest {
  userIds: string[];
}

interface RemoveUsersFromLocationRequest {
  userIds: string[];
}

interface LocationUsersResponse {
  location: Location;
  users: User[];
  total: number;
}

interface LocationUserAssignmentResponse {
  success: boolean;
  successful: Array<{ userId: string; user: User }>;
  failed: Array<{ userId: string; error: string }>;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract location ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid location ID',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetLocationUsers(req, res, user as User, id);
    } else if (req.method === 'POST') {
      return await handleAssignUsersToLocation(req, res, user as User, id);
    } else if (req.method === 'DELETE') {
      return await handleRemoveUsersFromLocation(req, res, user as User, id);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in location users API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve all users assigned to a location
 */
async function handleGetLocationUsers(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  locationId: string,
): Promise<void> {
  try {
    // Parse query parameters
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = Math.max(parseInt(req.query.offset as string) || 0, 0);

    const result = await UserLocationService.getLocationUsers(
      locationId,
      requestingUser.clinicId!,
      { limit, offset },
    );

    const response: LocationUsersResponse = {
      location: result.location,
      users: result.users,
      total: result.total,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting location users:', error);

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('does not belong')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve location users',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle POST request to assign users to a location
 */
async function handleAssignUsersToLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  locationId: string,
): Promise<void> {
  try {
    // Validate user permissions (only admins can assign users to locations)
    if (
      requestingUser.role !== UserRole.CLINIC_ADMIN &&
      requestingUser.role !== UserRole.SUPER_ADMIN
    ) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can assign users to locations',
      });
    }

    // Validate request body
    const { userIds }: AssignUsersToLocationRequest = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'userIds array is required and must not be empty',
      });
    }

    // Validate all user IDs are strings
    if (!userIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        message: 'All user IDs must be non-empty strings',
      });
    }

    // Create assignments array for bulk operation
    const assignments = userIds.map(userId => ({
      userId,
      locationIds: [locationId],
    }));

    // Assign users to location using bulk operation
    const result = await UserLocationService.bulkAssignUsersToLocations(
      assignments,
      requestingUser,
    );

    const response: LocationUserAssignmentResponse = {
      success: true,
      successful: result.successful,
      failed: result.failed,
      message: `${result.successful.length} user(s) assigned to location successfully. ${result.failed.length} failed.`,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error assigning users to location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('outside your clinic') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to assign users to location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle DELETE request to remove users from a location
 */
async function handleRemoveUsersFromLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
  locationId: string,
): Promise<void> {
  try {
    // Validate user permissions (only admins can remove users from locations)
    if (
      requestingUser.role !== UserRole.CLINIC_ADMIN &&
      requestingUser.role !== UserRole.SUPER_ADMIN
    ) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can remove users from locations',
      });
    }

    // Validate request body
    const { userIds }: RemoveUsersFromLocationRequest = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'userIds array is required and must not be empty',
      });
    }

    // Validate all user IDs are strings
    if (!userIds.every(id => typeof id === 'string' && id.trim().length > 0)) {
      return res.status(400).json({
        success: false,
        message: 'All user IDs must be non-empty strings',
      });
    }

    const successful: Array<{ userId: string; user: User }> = [];
    const failed: Array<{ userId: string; error: string }> = [];

    // Remove each user from the location
    for (const userId of userIds) {
      try {
        const updatedUser = await UserLocationService.removeUserFromLocations(
          userId,
          [locationId],
          requestingUser,
        );
        successful.push({ userId, user: updatedUser });
      } catch (error) {
        failed.push({
          userId,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    const response: LocationUserAssignmentResponse = {
      success: true,
      successful,
      failed,
      message: `${successful.length} user(s) removed from location successfully. ${failed.length} failed.`,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error removing users from location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('outside your clinic') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to remove users from location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
