import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { LocationService } from '@/lib/services/locationService';
import {
  OfficeHoursService,
  OfficeHoursConfig,
  OfficeHoursStatus,
} from '@/lib/services/office-hours';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';

/**
 * @swagger
 * /api/locations/{id}/office-hours:
 *   get:
 *     summary: Get location office hours
 *     tags: [Office Hours]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     responses:
 *       200:
 *         description: Office hours information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 officeHours:
 *                   type: object
 *                   description: Raw office hours configuration
 *                 locationTimezone:
 *                   type: string
 *                   description: Location timezone
 *                 timezoneLabel:
 *                   type: string
 *                   description: Human-readable timezone label
 *                 currentStatus:
 *                   $ref: '#/components/schemas/OfficeHoursStatus'
 *                 displayHours:
 *                   type: object
 *                   description: Formatted office hours for display
 *                 note:
 *                   type: string
 *                   description: Usage note about timezone handling
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update location office hours
 *     tags: [Office Hours]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               officeHours:
 *                 type: object
 *                 description: Office hours configuration
 *                 example:
 *                   "1": { "start": "09:00", "end": "17:00" }
 *                   "2": { "start": "09:00", "end": "17:00" }
 *                   "6": null
 *                   "7": null
 *               timezone:
 *                 type: string
 *                 description: Location timezone (IANA timezone identifier)
 *                 example: "America/Chicago"
 *     responses:
 *       200:
 *         description: Office hours updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 location:
 *                   $ref: '#/components/schemas/Location'
 *                 message:
 *                   type: string
 *       400:
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 */

interface UpdateOfficeHoursRequest {
  officeHours: OfficeHoursConfig;
  timezone?: string;
}

interface OfficeHoursResponse {
  officeHours: OfficeHoursConfig;
  locationTimezone: string;
  timezoneLabel: string;
  currentStatus: OfficeHoursStatus;
  displayHours: Record<string, string>;
  note: string;
}

interface OfficeHoursUpdateResponse {
  success: boolean;
  location: Location;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract location ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid location ID',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetOfficeHours(req, res, user, id);
    } else if (req.method === 'PUT') {
      return await handleUpdateOfficeHours(req, res, user, id);
    } else {
      res.setHeader('Allow', ['GET', 'PUT']);
      return res.status(405).json({
        success: false,
        message: `Method ${req.method} not allowed`,
      });
    }
  } catch (error) {
    console.error('Error in office hours API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve office hours
 */
async function handleGetOfficeHours(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
): Promise<void> {
  try {
    // Get the location
    const result = await LocationService.getLocationsByClinicId(user.clinicId!);
    const location = result.locations.find((loc: Location) => loc.id === locationId);

    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found',
      });
    }

    // Default values if not set
    const officeHours = location.officeHours || {};
    const locationTimezone = location.timeZone || 'America/Chicago'; // Default timezone

    // Get current status
    const currentStatus = OfficeHoursService.checkOfficeHours(officeHours, locationTimezone);

    // Get UI-formatted data
    const uiData = OfficeHoursService.getOfficeHoursForUI(officeHours, locationTimezone);

    const response: OfficeHoursResponse = {
      officeHours: uiData.raw,
      locationTimezone: uiData.locationTimezone,
      timezoneLabel: uiData.timezoneLabel,
      currentStatus,
      displayHours: uiData.formatted,
      note: "Times shown in location timezone. UI should convert to user's local timezone for display.",
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting office hours:', error);

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to get office hours',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle PUT request to update office hours
 */
async function handleUpdateOfficeHours(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
): Promise<void> {
  try {
    // Validate permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Insufficient permissions to update office hours',
      });
    }

    // Validate request body
    const { officeHours, timezone }: UpdateOfficeHoursRequest = req.body;

    if (!officeHours) {
      return res.status(400).json({
        success: false,
        message: 'officeHours is required',
      });
    }

    // Validate office hours format
    const validation = OfficeHoursService.validateOfficeHoursWithErrors(officeHours);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid office hours configuration',
        errors: validation.errors,
      });
    }

    // Validate timezone if provided
    if (timezone) {
      if (typeof timezone !== 'string' || timezone.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'timezone must be a non-empty string if provided',
        });
      }

      if (!OfficeHoursService.validateTimezone(timezone.trim())) {
        return res.status(400).json({
          success: false,
          message:
            'Invalid timezone format. Use IANA timezone identifiers (e.g., "America/Chicago")',
        });
      }
    }

    // Prepare update data
    const updateData: {
      officeHours: OfficeHoursConfig;
      timeZone?: string;
    } = { officeHours };

    if (timezone) {
      updateData.timeZone = timezone.trim();
    }

    // Update the location
    const updatedLocation = await LocationService.updateLocation(
      locationId,
      updateData,
      user.clinicId!,
      user,
    );

    const response: OfficeHoursUpdateResponse = {
      success: true,
      location: updatedLocation,
      message: 'Office hours updated successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error updating office hours:', error);

    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('permissions') ||
        error.message.includes('Forbidden') ||
        error.message.includes('Insufficient')
      ) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('required') ||
        error.message.includes('Invalid') ||
        error.message.includes('validation')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update office hours',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
