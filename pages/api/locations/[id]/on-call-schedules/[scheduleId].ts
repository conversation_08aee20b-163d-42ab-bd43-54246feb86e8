import type { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { User, UserRole } from '@/models/auth';
import type { LocationsRepository, OnCallSchedulesRepository } from '@/lib/repositories';
import { getRepositories } from '@/lib/repositories';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id: locationId, scheduleId } = req.query;

  if (!locationId || typeof locationId !== 'string') {
    return res.status(400).json({ error: 'Location ID is required' });
  }

  if (!scheduleId || typeof scheduleId !== 'string') {
    return res.status(400).json({ error: 'Schedule ID is required' });
  }

  // Verify authentication
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  try {
    // Ensure database connections are ready
    const repoManager = getRepositories();
    await repoManager.initialize();

    const locationsRepo = repoManager.locations;
    const schedulesRepo = repoManager.onCallSchedules;

    switch (req.method) {
      case 'DELETE':
        return await handleDelete(
          req,
          res,
          locationId,
          scheduleId,
          user,
          locationsRepo,
          schedulesRepo,
        );
      default:
        res.setHeader('Allow', ['DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error) {
    console.error('On-call schedule operation error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

async function handleDelete(
  req: NextApiRequest,
  res: NextApiResponse,
  locationId: string,
  scheduleId: string,
  user: User,
  locationsRepo: LocationsRepository,
  schedulesRepo: OnCallSchedulesRepository,
) {
  try {
    // Verify location exists and user has access
    const location = await locationsRepo.findById(locationId);
    if (!location) {
      return res.status(404).json({ error: 'Location not found' });
    }

    if (user.clinicId !== location.clinicId) {
      return res.status(403).json({ error: 'Access denied to this location' });
    }

    if (user.role !== UserRole.CLINIC_ADMIN) {
      return res.status(403).json({ error: 'Admin privileges required to remove schedules' });
    }

    // Get the schedule to verify it exists and belongs to this location
    const schedule = await schedulesRepo.findById(scheduleId);
    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    if (schedule.locationId !== locationId) {
      return res.status(400).json({ error: 'Schedule does not belong to this location' });
    }

    // Soft delete by deactivating the schedule (save to both databases)
    await schedulesRepo.update(scheduleId, {
      isActive: false,
      updatedAt: new Date(),
    });

    console.log('Schedule deactivated successfully:', {
      scheduleId,
      locationId,
      doctorName: schedule.doctorName,
      deactivatedBy: user.id,
    });

    return res.status(200).json({
      message: 'Schedule removed successfully',
      scheduleId,
    });
  } catch (error) {
    console.error('Error removing on-call schedule:', error);
    return res.status(500).json({ error: 'Failed to remove schedule' });
  }
}
