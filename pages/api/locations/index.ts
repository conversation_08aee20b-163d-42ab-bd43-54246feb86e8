import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { LocationService } from '@/lib/services/locationService';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';

/**
 * @swagger
 * /api/locations:
 *   get:
 *     summary: Get all locations for the user's clinic
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: practiceId
 *         schema:
 *           type: string
 *         description: Filter by practice ID
 *       - in: query
 *         name: includeInactive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include inactive locations
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Maximum number of locations to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of locations to skip
 *     responses:
 *       200:
 *         description: List of locations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 locations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                 total:
 *                   type: integer
 *                   description: Total number of locations
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create a new location
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - address
 *               - practiceId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the location
 *               address:
 *                 type: string
 *                 description: Physical address of the location
 *               phone:
 *                 type: string
 *                 description: Contact phone for this location
 *               officeHours:
 *                 type: object
 *                 description: Office hours for the location
 *               practiceId:
 *                 type: string
 *                 description: ID of the practice this location belongs to
 *     responses:
 *       201:
 *         description: Location created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 location:
 *                   $ref: '#/components/schemas/Location'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */

interface CreateLocationRequest {
  name: string;
  address: string;
  phone?: string;
  officeHours?: Record<string, { start: string; end: string } | null>;
  practiceId: string;
}

interface LocationsResponse {
  locations: Location[];
  total: number;
}

interface CreateLocationResponse {
  success: boolean;
  location: Location;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetLocations(req, res, user as User);
    } else if (req.method === 'POST') {
      return await handleCreateLocation(req, res, user as User);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in locations API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve all locations for the clinic
 */
async function handleGetLocations(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    // Parse query parameters
    const practiceId = req.query.practiceId as string | undefined;
    const includeInactive = req.query.includeInactive === 'true';
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = Math.max(parseInt(req.query.offset as string) || 0, 0);

    console.log('API handleGetLocations called for user:', {
      userId: user.id,
      clinicId: user.clinicId,
      role: user.role,
      queryParams: { practiceId, includeInactive, limit, offset },
    });

    // Get locations for the clinic
    const result = await LocationService.getLocationsByClinicId(user.clinicId!, {
      practiceId,
      includeInactive,
      limit,
      offset,
    });

    console.log('LocationService returned:', {
      locationsCount: result.locations.length,
      total: result.total,
    });

    const response: LocationsResponse = {
      locations: result.locations,
      total: result.total,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting locations:', error);

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve locations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle POST request to create a new location
 */
async function handleCreateLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    // Validate permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Insufficient permissions to create locations',
      });
    }

    // Validate request body
    const { name, address, phone, officeHours, practiceId }: CreateLocationRequest = req.body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'name is required and must be a non-empty string',
      });
    }

    if (!address || typeof address !== 'string' || address.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'address is required and must be a non-empty string',
      });
    }

    if (!practiceId || typeof practiceId !== 'string' || practiceId.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'practiceId is required and must be a non-empty string',
      });
    }

    if (phone !== undefined && typeof phone !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'phone must be a string if provided',
      });
    }

    // Create the location
    const newLocation = await LocationService.createLocation(
      {
        name: name.trim(),
        address: address.trim(),
        phone: phone?.trim(),
        officeHours,
        practiceId: practiceId.trim(),
      },
      user.clinicId!,
      user,
    );

    const response: CreateLocationResponse = {
      success: true,
      location: newLocation,
      message: 'Location created successfully',
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error creating location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('permissions') ||
        error.message.includes('Forbidden') ||
        error.message.includes('Insufficient')
      ) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('required') ||
        error.message.includes('Invalid') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
