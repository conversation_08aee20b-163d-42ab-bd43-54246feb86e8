import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { LocationService } from '@/lib/services/locationService';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';

/**
 * @swagger
 * /api/locations/{id}:
 *   get:
 *     summary: Get a specific location by ID
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *       - in: query
 *         name: includePractice
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include practice information
 *     responses:
 *       200:
 *         description: Location details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 location:
 *                   $ref: '#/components/schemas/Location'
 *                 practice:
 *                   $ref: '#/components/schemas/Practice'
 *                   description: Practice information (if includePractice=true)
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update a location
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the location
 *               address:
 *                 type: string
 *                 description: Physical address of the location
 *               phone:
 *                 type: string
 *                 description: Contact phone for this location
 *               officeHours:
 *                 type: object
 *                 description: Office hours for the location
 *               isActive:
 *                 type: boolean
 *                 description: Whether the location is active
 *               transferToPracticeId:
 *                 type: string
 *                 description: Transfer location to a different practice
 *     responses:
 *       200:
 *         description: Location updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 location:
 *                   $ref: '#/components/schemas/Location'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete a location (soft delete)
 *     tags: [Locations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Location ID
 *     responses:
 *       200:
 *         description: Location deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Location not found
 *       409:
 *         description: Conflict - location has assigned users
 *       500:
 *         description: Internal server error
 */

interface UpdateLocationRequest {
  name?: string;
  address?: string;
  phone?: string;
  officeHours?: Record<string, { start: string; end: string } | null>;
  timeZone?: string;
  isActive?: boolean;
  transferToPracticeId?: string;
}

interface LocationDetailResponse {
  location: Location;
  practice?: Practice;
}

interface LocationUpdateResponse {
  success: boolean;
  location: Location;
  message: string;
}

interface LocationDeleteResponse {
  success: boolean;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract location ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid location ID',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetLocation(req, res, user as User, id);
    } else if (req.method === 'PUT') {
      return await handleUpdateLocation(req, res, user as User, id);
    } else if (req.method === 'DELETE') {
      return await handleDeleteLocation(req, res, user as User, id);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in location detail API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve a specific location
 */
async function handleGetLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
): Promise<void> {
  try {
    const includePractice = req.query.includePractice === 'true';

    // Get the location
    const location = await LocationService.getLocationById(locationId, user.clinicId!);
    if (!location) {
      return res.status(404).json({
        success: false,
        message: 'Location not found',
      });
    }

    const response: LocationDetailResponse = {
      location,
    };

    // Include practice information if requested
    if (includePractice) {
      const practice = await LocationService.getLocationPractice(locationId, user.clinicId!);
      if (practice) {
        response.practice = practice;
      }
    }

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting location:', error);

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('does not belong')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle PUT request to update a location
 */
async function handleUpdateLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
): Promise<void> {
  try {
    // Validate permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Insufficient permissions to update locations',
      });
    }

    // Validate request body
    const {
      name,
      address,
      phone,
      officeHours,
      timeZone,
      isActive,
      transferToPracticeId,
    }: UpdateLocationRequest = req.body;

    // Validate string fields if provided
    if (name !== undefined && (typeof name !== 'string' || name.trim().length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'name must be a non-empty string if provided',
      });
    }

    if (address !== undefined && (typeof address !== 'string' || address.trim().length === 0)) {
      return res.status(400).json({
        success: false,
        message: 'address must be a non-empty string if provided',
      });
    }

    if (phone !== undefined && typeof phone !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'phone must be a string if provided',
      });
    }

    if (isActive !== undefined && typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean if provided',
      });
    }

    if (
      transferToPracticeId !== undefined &&
      (typeof transferToPracticeId !== 'string' || transferToPracticeId.trim().length === 0)
    ) {
      return res.status(400).json({
        success: false,
        message: 'transferToPracticeId must be a non-empty string if provided',
      });
    }

    // Validate office hours if provided
    if (officeHours !== undefined) {
      const validation = OfficeHoursService.validateOfficeHoursWithErrors(officeHours);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          message: 'Invalid office hours configuration',
          errors: validation.errors,
        });
      }
    }

    // Validate timezone if provided
    if (timeZone !== undefined) {
      if (typeof timeZone !== 'string' || timeZone.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'timeZone must be a non-empty string if provided',
        });
      }

      if (!OfficeHoursService.validateTimezone(timeZone.trim())) {
        return res.status(400).json({
          success: false,
          message:
            'Invalid timezone format. Use IANA timezone identifiers (e.g., "America/Chicago")',
        });
      }
    }

    let updatedLocation: Location;

    // Handle practice transfer if requested
    if (transferToPracticeId) {
      updatedLocation = await LocationService.transferLocationToPractice(
        locationId,
        transferToPracticeId.trim(),
        user.clinicId!,
        user,
      );
    } else {
      // Regular update
      const updateData: {
        name?: string;
        address?: string;
        phone?: string;
        officeHours?: Record<string, { start: string; end: string } | null>;
        timeZone?: string;
        isActive?: boolean;
      } = {};

      if (name !== undefined) updateData.name = name.trim();
      if (address !== undefined) updateData.address = address.trim();
      if (phone !== undefined) updateData.phone = phone.trim();
      if (officeHours !== undefined) updateData.officeHours = officeHours;
      if (timeZone !== undefined) updateData.timeZone = timeZone.trim();
      if (isActive !== undefined) updateData.isActive = isActive;

      updatedLocation = await LocationService.updateLocation(
        locationId,
        updateData,
        user.clinicId!,
        user,
      );
    }

    const response: LocationUpdateResponse = {
      success: true,
      location: updatedLocation,
      message: transferToPracticeId
        ? 'Location transferred to new practice successfully'
        : 'Location updated successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error updating location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('permissions') ||
        error.message.includes('Forbidden') ||
        error.message.includes('Insufficient')
      ) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('required') ||
        error.message.includes('Invalid') ||
        error.message.includes('does not belong') ||
        error.message.includes('cannot be empty')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle DELETE request to delete a location
 */
async function handleDeleteLocation(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  locationId: string,
): Promise<void> {
  try {
    // Validate permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Insufficient permissions to delete locations',
      });
    }

    // Delete the location
    await LocationService.deleteLocation(locationId, user.clinicId!, user);

    const response: LocationDeleteResponse = {
      success: true,
      message: 'Location deleted successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error deleting location:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('permissions') ||
        error.message.includes('Forbidden') ||
        error.message.includes('Insufficient')
      ) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('Cannot delete location with assigned users')) {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to delete location',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
