import { NextApiRequest, NextApiResponse } from 'next';
import { getSwaggerSpec, OpenAPISpec } from '@/utils/swagger';

/**
 * Generates and serves the Swagger JSON schema
 * This endpoint can be consumed by other API tools
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const spec: OpenAPISpec = getSwaggerSpec();

    // Ensure components section exists
    if (!spec.components) {
      spec.components = {};
    }

    // Ensure all required component sections exist
    const requiredSections = ['schemas', 'responses', 'parameters', 'securitySchemes'];
    requiredSections.forEach(section => {
      if (!spec.components![section]) {
        spec.components![section] = {};
      }
    });

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Cache-Control', 'public, max-age=60'); // Cache for 1 minute

    // Return the spec
    res.status(200).json(spec);
  } catch (error) {
    console.error('Error generating Swagger spec:', error);

    // Return a minimal valid OpenAPI spec on error
    res.status(200).json({
      openapi: '3.0.0',
      info: {
        title: 'Front Desk Portal API Documentation',
        version: '1.0.0',
        description: 'An error occurred while generating the full API documentation.',
      },
      paths: {},
      components: {
        schemas: {},
        responses: {},
        parameters: {},
        securitySchemes: {},
      },
    });
  }
}
