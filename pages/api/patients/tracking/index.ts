import { NextApiRequest, NextApiResponse } from 'next';
import { patientFactory } from '@/lib/factories/patient-factory';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { z } from 'zod';

// No schema for creating patients - patients are created through the external API

// Schema for getting a patient by provider ID
const getPatientSchema = z.object({
  provider: z.string().min(1, 'Provider is required'),
  providerId: z.string().min(1, 'Provider ID is required'),
});

/**
 * API handler for patient tracking
 * POST: Create a patient in the external provider and store a reference
 * GET: Get a patient by provider and provider ID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Verify authentication
    const user = await verifyAuthAndGetUser(req);
    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Get the patient coordinator service
    const patientCoordinatorService = patientFactory.getPatientCoordinatorService();

    // POST method is not allowed - patients are created through the external API
    if (req.method === 'POST') {
      return res
        .status(405)
        .json({ message: 'Method not allowed. Use the external API to create patients.' });
    }

    // Handle GET request to get a patient by provider ID
    if (req.method === 'GET') {
      // Validate query parameters
      const validationResult = getPatientSchema.safeParse(req.query);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid query parameters',
          errors: validationResult.error.flatten().fieldErrors,
        });
      }

      // Get validated data
      const { provider, providerId } = validationResult.data;

      // Get patient
      const patientReference = await patientCoordinatorService.getPatientByProviderId(
        provider,
        providerId,
      );

      // Return patient reference
      return res.status(200).json(patientReference);
    }

    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling patient tracking request:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({ message: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ message: error.message });
      }
    }

    // Handle generic errors
    return res.status(500).json({
      message: 'An error occurred while processing your request',
      error: (error as Error).message,
    });
  }
}
