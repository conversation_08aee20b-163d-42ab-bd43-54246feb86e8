import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import logger from '@/lib/external-api/v2/utils/logger';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import {
  AppointmentStatus,
  createApiHandler,
  getProviderFromRequest,
  IProvider,
  Patient,
  validateApiKey,
} from '@/lib/external-api/v2';
import { PatientCoordinatorService } from '@/lib/services/patient-coordinator-service';

const cachePatientsSchema = z.object({
  locationId: z.string().optional().default(URMA_LOMBARD_LOCATION_ID),
  period: z
    .object({
      type: z.enum(['day', 'week', 'month']),
      count: z.number(),
    })
    .optional()
    .default({ type: 'day', count: 1 }),
  appointmentStatuses: z
    .array(z.nativeEnum(AppointmentStatus))
    .optional()
    .default([
      AppointmentStatus.FULFILLED,
      AppointmentStatus.CANCELLED,
      AppointmentStatus.NOSHOW,
      AppointmentStatus.PENDING,
    ]),
});

/**
 * Helper function to add delay between API calls to respect rate limits
 * @param ms Milliseconds to delay
 */
const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Rate-limited version of Promise.all that processes items in batches
 * @param items Array of items to process
 * @param processor Function to process each item
 * @param batchSize Number of items to process in parallel
 * @param delayMs Delay between batches in milliseconds
 */
async function processWithRateLimit<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  batchSize: number = 10,
  delayMs: number = 100,
): Promise<R[]> {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);

    // Add delay between batches (except for the last batch)
    if (i + batchSize < items.length) {
      await delay(delayMs);
    }
  }

  return results;
}

const getPatients = async (
  appointmentStatuses: AppointmentStatus[],
  period: { type: 'day' | 'week' | 'month'; count: number },
  locationId: string,
  provider: IProvider,
  appointmentOffset = 0,
  globalPatientIds?: Set<string>,
): Promise<(Patient | null)[]> => {
  logger.info(
    {
      context: 'patients.cache',
      locationId,
      appointmentOffset,
      appointmentStatuses,
      period,
    },
    'Getting patients',
  );

  const appointmentService = provider.getAppointmentService();
  const patientService = provider.getPatientService();

  const now = dayjs.utc();
  const startDate = now.subtract(period.count, period.type).toISOString();
  const endDate = now.toISOString();

  const appointments = await appointmentService.getAppointmentsByStatuses({
    statuses: appointmentStatuses,
    locationId,
    startLastUpdated: startDate,
    endLastUpdated: endDate,
    pagination: {
      limit: 50,
      offset: appointmentOffset,
    },
  });

  const patientIds = globalPatientIds ?? new Set<string>();
  const patientIdsToFetch: string[] = [];
  for (const appointment of appointments.items) {
    if (!patientIds.has(appointment.patientId)) {
      patientIds.add(appointment.patientId);
      patientIdsToFetch.push(appointment.patientId);
    }
  }

  // Process patient fetching with rate limiting (10 patients at a time, 100ms delay)
  const patients = await processWithRateLimit(
    patientIdsToFetch,
    async id => {
      const patient = await patientService.getPatientByIdWithoutInsurance(id);
      // Add small delay between individual patient requests
      await delay(150);
      return patient;
    },
    10, // batch size
    300, // delay between batches
  );

  const finalPatients = [...patients].filter(Boolean);

  if (appointments.pagination.hasMore) {
    // Add delay before making the next recursive call
    await delay(250);
    const nextAppointmentOffset = appointments.pagination.offset + appointments.items.length;
    const nextPatients = await getPatients(
      appointmentStatuses,
      period,
      locationId,
      provider,
      nextAppointmentOffset,
      patientIds,
    );
    finalPatients.push(...nextPatients);
  }

  // Order patients by last name
  // return finalPatients.sort((a, b) => a.lastName.localeCompare(b.lastName));
  return finalPatients;
};

/**
 * API handler for caching patients
 * POST: Cache patients for a specific location and time period
 */
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Handle POST request to cache patients by location and time period
    if (req.method === 'POST') {
      // Validate request body
      const validationResult = cachePatientsSchema.safeParse(req.body);
      if (!validationResult.success) {
        return res.status(400).json({
          message: 'Invalid request body',
          errors: validationResult.error.flatten().fieldErrors,
        });
      }

      // Get validated data
      const { locationId, appointmentStatuses, period } = validationResult.data;
      const provider = getProviderFromRequest(req);

      // Initialize the patient coordinator service
      const patientCoordinatorService = new PatientCoordinatorService();

      // Get all patients using the getPatients method
      const providerPatients = (
        await getPatients(appointmentStatuses, period, locationId, provider)
      ).filter(p => p?.providerInfo?.externalId) as Patient[];

      // Exclude existing patients from the list
      const patientsToInsert =
        await patientCoordinatorService.excludeExistingPatients(providerPatients);

      logger.info(
        {
          context: 'patients.cache',
          locationId,
          period,
          totalPatientsFound: providerPatients.length,
          uniquePatients: patientsToInsert.length,
        },
        'Retrieved patients for caching',
      );

      // Cache each patient using the PatientCoordinatorService
      const cacheResults = await Promise.allSettled(
        patientsToInsert.map(async patient => {
          if (!patient) {
            return { status: 'skipped', reason: 'null patient' };
          }

          try {
            const cachedPatient = await patientCoordinatorService.storePatient(patient);
            return { status: 'cached', patientId: cachedPatient.id };
          } catch (error) {
            logger.error(
              {
                context: 'patients.cache',
                patientId: patient.id,
                error: error instanceof Error ? error.message : String(error),
              },
              'Failed to cache patient',
            );
            return { status: 'failed', patientId: patient.id, error: String(error) };
          }
        }),
      );

      // Process results
      const successful = cacheResults.filter(
        result => result.status === 'fulfilled' && result.value.status === 'cached',
      ).length;
      const failed = cacheResults.filter(
        result => result.status === 'fulfilled' && result.value.status === 'failed',
      ).length;
      const skipped = cacheResults.filter(
        result => result.status === 'fulfilled' && result.value.status === 'skipped',
      ).length;

      logger.info(
        {
          context: 'patients.cache',
          locationId,
          period,
          totalPatients: patientsToInsert.length,
          successful,
          failed,
          skipped,
        },
        'Patient caching completed',
      );

      return res.status(200).json({
        message: 'Patient caching completed',
        summary: {
          totalPatientsFound: patientsToInsert.length,
          successfullyCached: successful,
          failedToCache: failed,
          skipped: skipped,
        },
        filters: {
          locationId,
          provider: provider?.name || 'nextech',
        },
      });
    }

    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    console.error('Error handling patients cache request:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({ message: error.message });
      }
      if (error.message.includes('Provider')) {
        return res.status(400).json({ message: error.message });
      }
    }

    // Handle generic errors
    return res.status(500).json({
      message: 'An error occurred while processing your request',
      error: (error as Error).message,
    });
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
