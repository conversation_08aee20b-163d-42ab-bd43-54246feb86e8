import { NextApiRequest, NextApiResponse } from 'next';

import logger from '@/lib/external-api/v2/utils/logger';
import { LLMClient } from '@/utils/llm';
import { callsService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow PUT requests
  if (req.method !== 'PUT') {
    logger.info({ method: req.method }, 'Method not allowed');
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { callId } = req.body;
    if (!callId) {
      return res.status(400).json({ message: 'Call ID is required' });
    }

    logger.info({ callId }, 'Summarizing voicemail');

    // Get the call with merged detail data from MySQL
    const call = await callsService.getCallWithDetail(callId);
    if (!call) {
      logger.error({ callId }, 'Call not found');
      return res.status(404).json({ message: 'Call not found' });
    }

    const voicemailTranscription = call.transcription;
    if (!voicemailTranscription) {
      logger.error({ callId }, 'Voicemail transcription is not available');
      return res.status(400).json({ message: 'Voicemail transcription is not available' });
    }

    // Generate the voicemail summary
    const ai = LLMClient.getInstance();
    const voicemailSummary = await ai.summarizeVoiceMail(voicemailTranscription);

    // Update the call with the new voicemail summary (this will update the MySQL calls table)
    await callsService.updateCall(callId, {
      voicemailSummary,
    });

    logger.info({ callId, summaryLength: voicemailSummary?.length }, 'Voicemail summary generated');

    return res.status(200).json({ voicemailSummary });
  } catch (error) {
    logger.error(error, 'Error handling call records request');
    return res.status(500).json({ message: 'Internal server error' });
  }
}
