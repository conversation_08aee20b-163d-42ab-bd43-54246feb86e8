import { NextApiRequest, NextApiResponse } from 'next';
import { afterHoursCallsLogService } from '@/utils/firestore';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * Handler for GET /api/calls/after-hours/call-logs/[callId]
 * Retrieves after-hours call logs by call_id with user names for UI display
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    const { callId } = req.query;

    if (!callId || typeof callId !== 'string') {
      return res.status(400).json({ message: 'Call ID is required' });
    }

    logger.info(
      { context: 'after-hours-logs-api', callId },
      'Getting after-hours call logs by call ID',
    );

    const logs = await afterHoursCallsLogService.getAfterHoursCallLogsByCallIdWithUserNames(callId);

    // Format the data for UI display
    const formattedLogs = logs
      .map(log => {
        const date = new Date(log.createdAt);
        const formattedDate = date.toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        });
        const formattedTime = date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        });

        if (log.viewedBy && log.viewedByUserName) {
          return {
            type: 'viewed',
            message: `${log.viewedByUserName} Viewed at ${formattedTime} on ${formattedDate}`,
            timestamp: log.createdAt,
            userName: log.viewedByUserName,
          };
        }

        if (log.contactedBy && log.contactedByUserName) {
          return {
            type: 'contacted',
            message: `${log.contactedByUserName} Contacted at ${formattedTime} on ${formattedDate}`,
            timestamp: log.createdAt,
            userName: log.contactedByUserName,
          };
        }

        return null;
      })
      .filter(Boolean);

    return res.status(200).json({
      success: true,
      data: formattedLogs,
    });
  } catch (error) {
    logger.error(
      {
        context: 'after-hours-logs-api',
        callId: req.query.callId,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error getting after-hours call logs',
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
