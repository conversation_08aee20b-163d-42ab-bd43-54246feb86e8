import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import logger from '@/lib/external-api/v2/utils/logger';
import { afterHoursCallsLogService, afterHoursCallsService, callsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';

// Validation schema for after-hours call contact request
const contactAfterHoursCallSchema = z.object({
  callId: z.string().nonempty('Call ID is required'),
  doctorId: z.string().optional(), // Optional since we use authenticated user's ID
});

/**
 * Handler for POST /api/calls/after-hours/contact
 * Creates a record in after_hours_calls_log with contacted_by field
 * For internal use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    // Validate request body using Zod schema
    const validationResult = contactAfterHoursCallSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const { callId, doctorId } = validationResult.data;

    logger.info(
      { context: 'after-hours-contact-api', callId, doctorId },
      'Creating after-hours call contact log',
    );

    // Find the after-hours call by callId
    let afterHoursCall = await afterHoursCallsService.getAfterHoursCallByCallId(callId);

    // If no after-hours call record exists, check if this is an after-hours call and create one
    if (!afterHoursCall) {
      logger.info(
        { context: 'after-hours-contact-api', callId },
        'No after-hours call record found, checking if call is after-hours type',
      );

      // Get the regular call to check if it's an after-hours call
      const regularCall = await callsService.getCallById(callId);
      if (!regularCall) {
        return res.status(404).json({
          success: false,
          message: `Call with ID ${callId} not found`,
        });
      }

      // Check if the call is after-hours type
      const isAfterHoursType = Array.isArray(regularCall.type)
        ? regularCall.type.includes(CallType.AFTER_HOURS)
        : regularCall.type === CallType.AFTER_HOURS;

      if (!isAfterHoursType) {
        return res.status(400).json({
          success: false,
          message: `Call with ID ${callId} is not an after-hours call`,
        });
      }

      // Create after-hours call record for this call
      logger.info(
        { context: 'after-hours-contact-api', callId },
        'Creating after-hours call record for existing after-hours call',
      );

      afterHoursCall = await afterHoursCallsService.createAfterHoursCall({
        callId: callId,
        patientPhoneNumber: regularCall.phoneNumber || '',
        patientFullName: regularCall.clientName || 'Unknown Patient',
        patientBirthday: new Date(), // Default to current date since not available from regular call
        callReason: regularCall.reason || 'After-hours call',
        sessionId: regularCall.sessionId || '',
        createdAt: regularCall.date || new Date(),
        updatedAt: new Date(),
      });

      logger.info(
        { context: 'after-hours-contact-api', callId, afterHoursCallId: afterHoursCall.id },
        'Created after-hours call record',
      );
    }

    // Create the after-hours call log record with both contactedBy and contactedByInfo
    const createdLog = await afterHoursCallsLogService.createAfterHoursCallLog({
      afterHoursCallId: afterHoursCall.id,
      contactedBy: user.id, // Set the user ID
      contactedByInfo: `${user.name || user.email} (${user.id})`, // Store additional contact info
    });

    // Store the contact information separately for the response
    const contactInfo = {
      userId: user.id,
      userName: user.name || user.email,
      contactedAt: new Date(),
    };

    logger.info(
      { context: 'after-hours-contact-api', callId, contactInfo },
      'Successfully created contact log',
    );

    logger.info(
      {
        context: 'after-hours-contact-api',
        callId,
        doctorId,
        logId: createdLog.id,
        afterHoursCallId: afterHoursCall.id,
      },
      'Successfully created after-hours call contact log',
    );

    return res.status(201).json({
      success: true,
      message: 'Contact log created successfully',
      data: {
        logId: createdLog.id,
        afterHoursCallId: afterHoursCall.id,
        contactedBy: doctorId,
        createdAt: createdLog.createdAt,
      },
    });
  } catch (error) {
    logger.error(
      {
        context: 'after-hours-contact-api',
        callId: req.body?.callId,
        doctorId: req.body?.doctorId,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error creating after-hours call contact log',
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
