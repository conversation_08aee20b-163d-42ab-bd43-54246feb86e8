import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import logger from '@/lib/external-api/v2/utils/logger';
import { afterHoursCallsLogService, afterHoursCallsService } from '@/utils/firestore';

// Validation schema for after-hours call contact request
const contactAfterHoursCallSchema = z.object({
  callId: z.string().nonempty('Call ID is required'),
  doctorId: z.string().nonempty('Doctor ID is required'),
});

/**
 * Handler for POST /api/calls/after-hours/contact
 * Creates a record in after_hours_calls_log with contacted_by field
 * For internal use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    // Validate request body using Zod schema
    const validationResult = contactAfterHoursCallSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const { callId, doctorId } = validationResult.data;

    logger.info(
      { context: 'after-hours-contact-api', callId, doctorId },
      'Creating after-hours call contact log',
    );

    // Find the after-hours call by callId
    const afterHoursCall = await afterHoursCallsService.getAfterHoursCallByCallId(callId);
    if (!afterHoursCall) {
      return res.status(404).json({
        success: false,
        message: `After-hours call with call ID ${callId} not found`,
      });
    }

    // Create the after-hours call log record with contacted_by field
    const createdLog = await afterHoursCallsLogService.createAfterHoursCallLog({
      afterHoursCallId: afterHoursCall.id,
      contactedBy: doctorId,
    });

    logger.info(
      {
        context: 'after-hours-contact-api',
        callId,
        doctorId,
        logId: createdLog.id,
        afterHoursCallId: afterHoursCall.id,
      },
      'Successfully created after-hours call contact log',
    );

    return res.status(201).json({
      success: true,
      message: 'Contact log created successfully',
      data: {
        logId: createdLog.id,
        afterHoursCallId: afterHoursCall.id,
        contactedBy: doctorId,
        createdAt: createdLog.createdAt,
      },
    });
  } catch (error) {
    logger.error(
      {
        context: 'after-hours-contact-api',
        callId: req.body?.callId,
        doctorId: req.body?.doctorId,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error creating after-hours call contact log',
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
