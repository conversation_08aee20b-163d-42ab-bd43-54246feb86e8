import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import { afterHoursCallsService, callsService } from '@/utils/firestore';
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import logger from '@/lib/external-api/v2/utils/logger';
import { OnCallScheduleService } from '@/lib/services/on-call-schedule';
import { CallType } from '@/models/CallTypes';
import { OnCallNotificationService } from '@/lib/services/on-call-notification';
import { AfterHoursCall } from '@/models/AfterHoursCall';
import { OnCallSchedule } from '@/models/OnCallSchedule';
import { Call } from '@/models/Call';
import { Location } from '@/models/Location';
import { scheduleUrgentAfterHoursDoctorCallReminder } from '../../external-api/v2/jobs/urgent-after-hours-doctor-call-reminder';
import { getProviderFromRequest } from '@/lib/external-api/v2';

// Validation schema for after-hours call registration
const registerAfterHoursCallSchema = z.object({
  patientId: z.string().optional(),
  patientBirthday: z.string().nonempty('Patient birthday is required'),
  patientFullName: z.string().nonempty('Patient full name is required'),
  patientPhoneNumber: z.string().nonempty('Patient phone number is required'),
  callReason: z.string().nonempty('Call reason is required'),
  sessionId: z.string().nonempty('Session ID is required'),
});

/**
 * Gets the current on-call doctor for a given location
 * @param locationId - The location ID to find the on-call doctor for
 * @param sessionId - The session ID for logging purposes
 * @param isPrimary - 'true' to get the primary on-call doctor, 'false' to get the backup on-call doctor
 * @returns The on-call doctor if found, null otherwise
 */
async function getOnCallDoctor(locationId: string, sessionId: string, isPrimary: boolean) {
  try {
    // Get current on-call doctor
    const onCallDoctor = await OnCallScheduleService.getCurrentOnCallDoctor(
      locationId,
      undefined,
      isPrimary,
    );

    if (onCallDoctor) {
      logger.info({ onCallDoctor }, 'On-call doctor found');
    } else {
      logger.info({ sessionId }, 'No on-call doctor found');
    }

    return onCallDoctor;
  } catch (notificationError) {
    logger.error(
      {
        error: notificationError,
        sessionId,
        locationId,
      },
      'Error handling on-call notification',
    );
    return null;
  }
}

async function notifyOnCallDoctorForAfterHoursCall(
  afterHoursCall: AfterHoursCall,
  onCallDoctor: OnCallSchedule,
  call: Call,
  location: Location,
): Promise<string> {
  try {
    const notificationResult = await OnCallNotificationService.notifyOnCallDoctorForAfterHoursCall(
      {
        schedule: onCallDoctor,
        callInfo: {
          sessionId: call.sessionId || '',
          callerPhone: call.phoneNumber,
          callType: CallType.AFTER_HOURS,
          locationName: location.name,
        },
      },
      afterHoursCall,
    );
    logger.info(
      {
        sessionId: call.sessionId,
        doctorId: onCallDoctor.doctorId,
        notificationResult,
      },
      'On-call doctor notification sent',
    );

    return notificationResult;
  } catch (error) {
    logger.error(error, 'Error notifying on-call doctor for after-hours call');
    return 'error';
  }
}

async function notifyPatientForAfterHoursCall(
  afterHoursCall: AfterHoursCall,
  onCallDoctor: OnCallSchedule,
  call: Call,
  location: Location,
): Promise<string> {
  try {
    const notificationResult = await OnCallNotificationService.notifyPatientForAfterHoursCall(
      {
        schedule: onCallDoctor,
        callInfo: {
          sessionId: call.sessionId || '',
          callerPhone: call.phoneNumber,
          callType: CallType.AFTER_HOURS,
          locationName: location.name,
        },
      },
      afterHoursCall,
    );
    logger.info(
      {
        sessionId: call.sessionId,
        patientPhoneNumber: afterHoursCall.patientPhoneNumber,
        notificationResult,
      },
      'After-hours call notification for patient sent',
    );

    return notificationResult;
  } catch (error) {
    logger.error(error, 'Error notifying patient for after-hours call');
    return 'error';
  }
}

/**
 * Handler for POST /api/calls/after-hours/register-call
 * Registers a new after-hours call record
 * For internal use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    // Validate request body using Zod schema
    const validationResult = registerAfterHoursCallSchema.safeParse(req.body);

    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const {
      patientId,
      patientBirthday,
      patientFullName,
      patientPhoneNumber,
      callReason,
      sessionId,
    } = validationResult.data;

    const calls = await callsService.getCallsBySessionId(sessionId);
    const call = calls.length > 0 ? calls[0] : null;

    if (!call) {
      return res.status(500).json({ message: `Call not found for session ID ${sessionId}` });
    }

    const agentId = call.agentId || process.env.GCP_AGENT_ID;
    if (!agentId) {
      return res.status(500).json({ message: 'Agent ID not found' });
    }

    const location = await AgentLocationMappingService.getLocationByAgentId(agentId);
    if (!location) {
      return res.status(500).json({ message: `Location not found for agent ID ${agentId}` });
    }

    // Get primary on-call doctor
    let primaryDoctorId = '';
    const primaryOnCallDoctor = await getOnCallDoctor(location.id, sessionId, true);
    if (primaryOnCallDoctor) {
      primaryDoctorId = primaryOnCallDoctor.doctorId;
    }

    // Get backup on-call doctor
    let backupDoctorId = '';
    const backupOnCallDoctor = await getOnCallDoctor(location.id, sessionId, false);
    if (backupOnCallDoctor) {
      backupDoctorId = backupOnCallDoctor.doctorId;
    }

    // Parse and validate patient birthday
    let parsedBirthday: Date;
    try {
      parsedBirthday = new Date(patientBirthday);
      if (isNaN(parsedBirthday.getTime())) {
        return res.status(400).json({ message: 'Invalid patient birthday format' });
      }
    } catch {
      return res.status(400).json({ message: 'Invalid patient birthday format' });
    }

    // Prepare the after-hours call data
    const afterHoursCallData = {
      patientBirthday: parsedBirthday,
      patientFullName: patientFullName.trim(),
      patientPhoneNumber: patientPhoneNumber.trim(),
      callReason: callReason.trim().substring(0, 255),
      callId: call.id,
      sessionId: sessionId,
      doctorId: primaryDoctorId?.trim() || undefined,
      doctorPhone: primaryOnCallDoctor?.doctorPhone?.trim() || undefined,
      backupDoctorId: backupDoctorId?.trim() || undefined,
      backupDoctorPhone: backupOnCallDoctor?.doctorPhone?.trim() || undefined,
      isReviewedByDoctor: false,
    };

    // Create the after-hours call record
    const createdAfterHoursCall =
      await afterHoursCallsService.createAfterHoursCall(afterHoursCallData);

    await callsService.updateCall(call.id, {
      clientId: patientId || 'unknown',
      callTypes: [CallType.AFTER_HOURS],
      type: CallType.AFTER_HOURS,
      clientName: patientFullName,
      reason: callReason,
    });

    if (patientId) {
      const provider = getProviderFromRequest(req);
      const appointmentService = provider.getAppointmentService();
      const lastFulfilledAppointment =
        await appointmentService.getLastFulfilledAppointmentByPatientId(patientId);

      if (lastFulfilledAppointment) {
        await callsService.updateCall(call.id, {
          lastAppointmentId: lastFulfilledAppointment.providerInfo?.externalId,
          lastAppointmentDate: lastFulfilledAppointment.startTime
            ? new Date(lastFulfilledAppointment.startTime)
            : undefined,
          lastAppointmentPractitionerId: lastFulfilledAppointment.practitionerId,
          lastAppointmentPractitionerName: lastFulfilledAppointment.practitionerName,
        });
      }
    }

    if (primaryOnCallDoctor) {
      const doctorNotificationResult = await notifyOnCallDoctorForAfterHoursCall(
        createdAfterHoursCall,
        primaryOnCallDoctor,
        call,
        location,
      );
      logger.info(
        {
          sessionId,
          doctorId: primaryOnCallDoctor.doctorId,
          notificationResult: doctorNotificationResult,
        },
        'Primary on-call doctor notification sent',
      );
      await scheduleUrgentAfterHoursDoctorCallReminder({
        afterHoursCallId: createdAfterHoursCall.id,
        isPrimaryDoctor: true,
      });
    } else {
      logger.info(
        { sessionId },
        'No primary on-call doctor found for after-hours call notification',
      );
    }

    if (backupOnCallDoctor) {
      const doctorNotificationResult = await notifyOnCallDoctorForAfterHoursCall(
        createdAfterHoursCall,
        backupOnCallDoctor,
        call,
        location,
      );
      logger.info(
        {
          sessionId,
          doctorId: backupOnCallDoctor.doctorId,
          notificationResult: doctorNotificationResult,
        },
        'Backup on-call doctor notification sent',
      );
      await scheduleUrgentAfterHoursDoctorCallReminder({
        afterHoursCallId: createdAfterHoursCall.id,
        isPrimaryDoctor: false,
      });
    } else {
      logger.info(
        { sessionId },
        'No backup on-call doctor found for after-hours call notification',
      );
    }

    const onCallDoctor = primaryOnCallDoctor || backupOnCallDoctor;
    if (onCallDoctor) {
      const patientNotificationResult = await notifyPatientForAfterHoursCall(
        createdAfterHoursCall,
        onCallDoctor,
        call,
        location,
      );
      logger.info(
        {
          sessionId,
          patientPhoneNumber: createdAfterHoursCall.patientPhoneNumber,
          notificationResult: patientNotificationResult,
        },
        'After-hours call notification for patient sent',
      );
    }

    return res.status(201).json({
      success: true,
      afterHoursCall: createdAfterHoursCall,
    });
  } catch (error) {
    console.error('Error registering after-hours call:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
