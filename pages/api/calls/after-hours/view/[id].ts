import { NextApiRequest, NextApiResponse } from 'next';
import { z } from 'zod';
import logger from '@/lib/external-api/v2/utils/logger';
import { afterHoursCallsLogService, afterHoursCallsService } from '@/utils/firestore';
import { AppLinkBuilder } from '@/utils/app-link.builder';

// Validation schema for after-hours call view request
const viewAfterHoursCallSchema = z.object({
  id: z.string().nonempty('After-hours call ID is required'),
  doctorId: z.string().nonempty('Doctor ID is required'),
});

/**
 * Handler for POST /api/calls/after-hours/view/[id]
 * Requires query parameter: d (ID of the doctor who is viewing the call)
 * Retrieves an after-hours call record by ID
 * For internal use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Get after-hours call ID from the URL parameter and doctor ID from query param 'd'
    const { id, d: doctorId } = req.query;

    const validationResult = viewAfterHoursCallSchema.safeParse({ id, doctorId });
    if (!validationResult.success) {
      return res.status(400).json({
        message: 'Validation failed',
        errors: validationResult.error.flatten().fieldErrors,
      });
    }

    const afterHoursCallId = validationResult.data.id;
    const viewedBy = validationResult.data.doctorId;

    const afterHoursCall = await afterHoursCallsService.getAfterHoursCallById(afterHoursCallId);
    if (!afterHoursCall) {
      return res
        .status(404)
        .json({ message: `After-hours call with ID ${afterHoursCallId} not found` });
    }
    await afterHoursCallsService.updateAfterHoursCall(afterHoursCall.id, {
      isReviewedByDoctor: true,
    });

    await afterHoursCallsLogService.createAfterHoursCallLog({
      afterHoursCallId,
      viewedBy,
    });

    const appLinkBuilder = AppLinkBuilder.getInstance();
    const callDetailsLink = appLinkBuilder.getCallDetailsLink(afterHoursCall.callId);

    // Redirect to the call details page
    return res.redirect(302, callDetailsLink);
  } catch (error) {
    logger.error(
      {
        error: error instanceof Error ? error.message : String(error),
        callId: req.query.id,
      },
      'Error viewing after-hours call',
    );
    return res.status(500).json({ message: 'Internal server error' });
  }
}
