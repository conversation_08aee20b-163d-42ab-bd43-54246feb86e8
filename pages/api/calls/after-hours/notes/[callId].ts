import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { afterHoursCallsService, callsService } from '@/utils/firestore';
import { providerRegistry } from '@/lib/external-api/v2';
import { CallType } from '@/models/CallTypes';
import logger from '@/lib/external-api/v2/utils/logger';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { callId } = req.query;
  const { notes } = req.body;

  // Ensure callId is a string
  if (!callId || Array.isArray(callId)) {
    return res.status(400).json({ message: 'Invalid call ID' });
  }

  // Only handle POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  if (!notes || typeof notes !== 'string' || notes.trim().length === 0) {
    return res.status(400).json({ message: 'Notes are required' });
  }

  try {
    // Get after-hours call by call ID
    let afterHoursCall = await afterHoursCallsService.getAfterHoursCallByCallId(callId);

    // If no after-hours call record exists, check if this is an after-hours call and create one
    if (!afterHoursCall) {
      logger.info(
        { context: 'after-hours-notes-api', callId },
        'No after-hours call record found, checking if call is after-hours type',
      );

      // Get the regular call to check if it's an after-hours call
      const regularCall = await callsService.getCallById(callId);
      if (!regularCall) {
        return res.status(404).json({ message: 'Call not found' });
      }

      // Check if the call is after-hours type
      const isAfterHoursType = Array.isArray(regularCall.type)
        ? regularCall.type.includes(CallType.AFTER_HOURS)
        : regularCall.type === CallType.AFTER_HOURS;

      if (!isAfterHoursType) {
        return res.status(400).json({ message: 'Call is not an after-hours call' });
      }

      // Create after-hours call record for this call
      logger.info(
        { context: 'after-hours-notes-api', callId },
        'Creating after-hours call record for existing after-hours call',
      );

      afterHoursCall = await afterHoursCallsService.createAfterHoursCall({
        callId: callId,
        patientPhoneNumber: regularCall.phoneNumber || '',
        patientFullName: regularCall.clientName || 'Unknown Patient',
        patientBirthday: new Date(), // Default to current date since not available from regular call
        callReason: regularCall.reason || 'After-hours call',
        sessionId: regularCall.sessionId || '',
        createdAt: regularCall.date || new Date(),
        updatedAt: new Date(),
      });

      logger.info(
        { context: 'after-hours-notes-api', callId, afterHoursCallId: afterHoursCall.id },
        'Created after-hours call record',
      );
    }

    // Format the notes for Practice+ with template
    const currentDate = new Date().toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });

    const formattedNotes = `On-Call Physician: ${user.name || user.email}
Date of On-Call Physician Notes: ${currentDate}
Notes: ${notes.trim()}`;

    // Save notes to Practice+ (Nextech)
    let savedToPractice = false;
    let practiceErrorMessage = '';

    try {
      // Get patient information first to save notes to their record
      const provider = providerRegistry.getProvider();
      const patientService = provider.getPatientService();

      logger.info(
        {
          context: 'after-hours-notes-api',
          callId,
          patientPhone: afterHoursCall.patientPhoneNumber,
        },
        'Attempting to find patient in Practice+ by phone number',
      );

      // Try to find patient by phone number from the after-hours call
      let patient = null;
      if (afterHoursCall.patientPhoneNumber) {
        patient = await patientService.getPatientByPhone(afterHoursCall.patientPhoneNumber);

        logger.info(
          {
            context: 'after-hours-notes-api',
            callId,
            patientPhone: afterHoursCall.patientPhoneNumber,
            patientFound: !!patient,
            patientId: patient?.id,
          },
          'Patient lookup result',
        );
      }

      if (patient) {
        logger.info(
          {
            context: 'after-hours-notes-api',
            callId,
            patientId: patient.id,
            existingNotesLength: patient.notes?.length || 0,
            newNotesLength: formattedNotes.length,
          },
          'Updating patient notes in Practice+',
        );

        // Update patient notes in Practice+
        // For after-hours notes, we replace any existing after-hours notes instead of appending
        let updatedNotes = formattedNotes;

        if (patient.notes) {
          // Remove any existing after-hours notes (look for our template pattern)
          const afterHoursPattern =
            /On-Call Physician: [\s\S]*?\nDate of On-Call Physician Notes: [\s\S]*?\nNotes: [\s\S]*?(?=\n\nOn-Call Physician: |$)/g;
          const cleanedNotes = patient.notes.replace(afterHoursPattern, '').trim();

          // If there are other notes, keep them and add our new notes
          if (cleanedNotes) {
            updatedNotes = `${cleanedNotes}\n\n${formattedNotes}`;
          }
        }

        const updatedPatient = {
          ...patient,
          notes: updatedNotes,
        };

        await patientService.updatePatient(patient.id, updatedPatient);
        savedToPractice = true;

        logger.info(
          {
            context: 'after-hours-notes-api',
            callId,
            patientId: patient.id,
            userId: user.id,
          },
          'Successfully saved notes to Practice+',
        );
      } else {
        practiceErrorMessage = `Patient not found in Practice+ by phone: ${afterHoursCall.patientPhoneNumber}`;
        logger.warn(
          {
            context: 'after-hours-notes-api',
            callId,
            patientPhone: afterHoursCall.patientPhoneNumber,
            userId: user.id,
          },
          practiceErrorMessage,
        );
      }
    } catch (practiceError) {
      practiceErrorMessage =
        practiceError instanceof Error ? practiceError.message : String(practiceError);
      logger.error(
        {
          context: 'after-hours-notes-api',
          callId,
          userId: user.id,
          error: practiceErrorMessage,
        },
        'Error saving notes to Practice+, continuing with local save',
      );
    }

    // Save notes to our system by updating the after-hours call record
    try {
      await afterHoursCallsService.updateAfterHoursCall(afterHoursCall.id, {
        notes: formattedNotes,
        updatedAt: new Date(),
      });

      logger.info(
        {
          context: 'after-hours-notes-api',
          callId,
          userId: user.id,
          notesLength: notes.length,
        },
        'Notes saved to after-hours call record',
      );
    } catch (dbError) {
      logger.error(
        {
          context: 'after-hours-notes-api',
          callId,
          userId: user.id,
          error: dbError instanceof Error ? dbError.message : String(dbError),
        },
        'Error saving notes to database',
      );
      // Don't throw here, we still want to return success if Practice+ save worked
    }

    return res.status(200).json({
      success: true,
      message: savedToPractice
        ? 'Notes saved successfully'
        : 'Notes saved to our system only - Practice+ save failed',
      data: {
        notes: formattedNotes,
        savedToPractice,
        savedToSystem: true,
        practiceError: practiceErrorMessage || null,
      },
    });
  } catch (error) {
    logger.error(
      {
        context: 'after-hours-notes-api',
        callId,
        userId: user.id,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error saving after-hours call notes',
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
