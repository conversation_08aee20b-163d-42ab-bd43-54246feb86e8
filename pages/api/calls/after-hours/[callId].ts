import { NextApiRequest, NextApiResponse } from 'next';
import { afterHoursCallsService } from '@/utils/firestore';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * Handler for GET /api/calls/after-hours/[callId]
 * Retrieves after-hours call details by call ID
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    const { callId } = req.query;

    if (!callId || typeof callId !== 'string') {
      return res.status(400).json({ message: 'Call ID is required' });
    }

    logger.info({ context: 'after-hours-get-api', callId }, 'Getting after-hours call by call ID');

    const afterHoursCall = await afterHoursCallsService.getAfterHoursCallByCallId(callId);

    return res.status(200).json({
      success: true,
      data: afterHoursCall,
    });
  } catch (error) {
    logger.error(
      {
        context: 'after-hours-get-api',
        callId: req.query.callId,
        error: error instanceof Error ? error.message : String(error),
      },
      'Error getting after-hours call',
    );

    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
