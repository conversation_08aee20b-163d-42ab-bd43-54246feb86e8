import { NextApiRequest, NextApiResponse } from 'next';
import { callsService, callSessionsService } from '@/utils/firestore';
import { CallType } from '@/models/CallTypes';
import { getDialogflowConversation, extractTranscriptFromConversation } from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { formatDialogflowDuration, getEffectiveCallDuration } from '@/utils/call-duration-utils';

/**
 * Handler for POST /api/calls/update-call
 * Updates an existing call record with data from UI
 * For internal use with user authentication
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ message: `Method ${req.method} Not Allowed` });
  }

  try {
    // Check authentication
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Unauthorized - Missing token' });
    }

    // Validate request body
    const {
      callId,
      sessionId,
      phoneNumber,
      reason,
      notes,
      hasVoiceMail,
      voicemailUrl,
      type,
      patientId,
      summary,
      transcription,
      isRedirected,
    } = req.body;

    if (!callId) {
      return res.status(400).json({ message: 'Call ID is required' });
    }

    // Check if the call exists
    const existingCall = await callsService.getCallById(callId);
    if (!existingCall) {
      return res.status(404).json({ message: `Call with ID ${callId} not found` });
    }

    // 1. Get Dialogflow conversation data if sessionId is provided and summary/transcription not supplied
    let dialogflowTranscript = '';
    let transcriptionWithAudio = '';
    let dialogflowDuration = '';

    if (sessionId && (!summary || !transcription)) {
      try {
        // Get GCP API credentials from environment variables
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (projectId && agentId) {
          try {
            console.log(`Fetching Dialogflow conversation data for session ID: ${sessionId}`);

            // Get access token for Dialogflow API
            const accessToken = await DialogflowAuthService.getAccessToken();

            if (accessToken) {
              // Fetch conversation data from Dialogflow API
              const conversation = await getDialogflowConversation({
                projectId,
                locationId,
                agentId,
                sessionId,
                accessToken,
              });

              // Extract transcript and summary
              dialogflowTranscript = extractTranscriptFromConversation(conversation);

              // Extract duration
              if (conversation.duration) {
                dialogflowDuration = formatDialogflowDuration(conversation.duration);
              }

              // Get transcription with audio
              try {
                const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;
                if (bucketName) {
                  const storageService = getGcpStorageService();
                  const interactions = await storageService.getTranscriptWithAudioRecords(
                    bucketName,
                    sessionId,
                    undefined,
                  );

                  if (interactions && interactions.length > 0) {
                    transcriptionWithAudio = JSON.stringify(interactions);
                  }
                }
              } catch (audioError) {
                console.warn(`Failed to get transcription with audio: ${audioError}`);
              }

              // If no duration from Dialogflow, try to calculate from transcript interactions
              if (!dialogflowDuration && transcriptionWithAudio) {
                const effectiveDurationSeconds = getEffectiveCallDuration({
                  duration: '',
                  transcriptionWithAudio,
                });

                if (effectiveDurationSeconds > 0) {
                  dialogflowDuration =
                    effectiveDurationSeconds < 60
                      ? `${effectiveDurationSeconds} sec`
                      : `${(effectiveDurationSeconds / 60).toFixed(1)} min`;

                  console.log(
                    `Calculated call duration from transcript: ${dialogflowDuration} (${effectiveDurationSeconds} seconds)`,
                  );
                }
              }
            }
          } catch (dialogflowError) {
            console.error('Failed to fetch Dialogflow conversation:', dialogflowError);
          }
        }
      } catch (error) {
        console.error('Error retrieving Dialogflow conversation data:', error);
      }
    }

    // 2. Determine call type from session if not provided
    let callType = type;
    if (sessionId && callType === undefined) {
      try {
        const session = await callSessionsService.getCallSessionBySessionId(sessionId);
        if (session && session.callType !== undefined) {
          callType = session.callType;
          console.log(`Using call type ${callType} from session ${sessionId}`);
        }
      } catch (error) {
        console.warn(`Failed to get call type from session ${sessionId}:`, error);
      }
    }

    // Prepare update data
    const updateData: Record<string, unknown> = {};

    // Only include defined fields in the update
    if (sessionId !== undefined) updateData.sessionId = sessionId;
    if (phoneNumber !== undefined) updateData.phoneNumber = phoneNumber;
    if (reason !== undefined) updateData.reason = reason;
    if (notes !== undefined) updateData.notes = notes;
    if (hasVoiceMail !== undefined) updateData.hasVoiceMail = hasVoiceMail;
    if (voicemailUrl !== undefined) updateData.voicemailUrl = voicemailUrl;
    if (callType !== undefined) updateData.type = callType;
    if (patientId !== undefined && patientId !== 'unknown') updateData.clientId = patientId;

    // Use Dialogflow data if provided and no manual data exists
    if (summary !== undefined) {
      updateData.summary = summary;
    }

    if (transcription !== undefined) {
      updateData.transcription = transcription;
    } else if (dialogflowTranscript) {
      updateData.transcription = dialogflowTranscript;
    }

    if (transcriptionWithAudio) {
      updateData.transcriptionWithAudio = transcriptionWithAudio;
    }

    if (dialogflowDuration) {
      updateData.duration = dialogflowDuration;
    }

    // Set call type based on conditions, similar to endConversationHandler
    if (hasVoiceMail) {
      updateData.type = CallType.VOICEMAIL;
    } else if (isRedirected) {
      updateData.type = CallType.TRANSFER_TO_HUMAN;
    }

    // Update the call record
    await callsService.updateCall(callId, updateData);

    // If sessionId is provided, update the call session as well
    if (sessionId && sessionId.trim() !== '') {
      try {
        const sessionUpdate: Record<string, unknown> = {};

        if (hasVoiceMail !== undefined) sessionUpdate.hasVoiceMail = hasVoiceMail;
        if (isRedirected !== undefined) sessionUpdate.isRedirected = isRedirected;

        // Set call type in session based on conditions
        if (hasVoiceMail) {
          sessionUpdate.callType = CallType.VOICEMAIL;
        } else if (isRedirected) {
          sessionUpdate.callType = CallType.TRANSFER_TO_HUMAN;
        } else if (callType !== undefined) {
          sessionUpdate.callType = callType;
        }

        if (patientId !== undefined && patientId !== 'unknown') sessionUpdate.patientId = patientId;

        // Always update the callId field with the current call ID for proper linking
        sessionUpdate.callId = callId;

        // Only update if we have data to update
        if (Object.keys(sessionUpdate).length > 0) {
          await callSessionsService.addOrUpdateCallSession(sessionId, sessionUpdate);
          console.log(`Updated associated call session ${sessionId} for call ${callId}`);
        }
      } catch (sessionError) {
        // Just log the error but don't fail the entire operation
        console.error(
          `Failed to update associated call session ${sessionId} for call ${callId}`,
          sessionError,
        );
      }
    }

    // Get the updated call to return
    const updatedCall = await callsService.getCallById(callId);

    return res.status(200).json({
      success: true,
      call: updatedCall,
    });
  } catch (error) {
    console.error('Error manually updating call from UI:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
