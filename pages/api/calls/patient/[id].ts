import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { callsService } from '@/utils/firestore';
import { providerRegistry } from '@/lib/external-api/v2';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { id } = req.query;

  // Ensure id is a string
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ message: 'Invalid call ID' });
  }

  // Only handle GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Get basic call info to access the phone number
    const call = await callsService.getCallById(id);

    if (!call) {
      return res.status(404).json({ message: 'Call not found' });
    }

    // Initialize patient to null
    let patient = null;
    let clientName = 'Patient';

    // Get the associated patient from Nextech by phone number
    if (call.phoneNumber && call.phoneNumber.trim() !== 'Unknown') {
      try {
        // Get the default provider (nextech)
        const provider = providerRegistry.getProvider();
        const patientService = provider.getPatientService();

        if (call.clientId && call.clientId !== 'unknown') {
          patient = await patientService.getPatientById(call.clientId);
        } else {
          // Get patient by phone number from Nextech
          patient = await patientService.getPatientByPhone(call.phoneNumber);
        }

        // Set client name if patient exists
        if (patient) {
          clientName = `${patient.firstName} ${patient.lastName}`;
        }
      } catch (error) {
        console.error(`Error fetching patient from Nextech for call ${id}:`, error);
        // Continue with patient as null
      }
    }

    return res.status(200).json({
      clientName,
      clientInfo: patient,
    });
  } catch (error) {
    console.error('Error fetching patient info:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
