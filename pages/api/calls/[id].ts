import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { callsService } from '@/utils/firestore';
import { getGcpStorageService } from '@/utils/gcp-storage';
import { CallType } from '@/models/CallTypes';
import {
  getEffectiveCallDuration,
  calculateDurationFromTranscript,
  shouldMarkAsDisconnected,
  parseDurationToSeconds,
} from '@/utils/call-duration-utils';
import { TransferClassificationService } from '@/utils/transfer-classification-service';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { id } = req.query;

  // Ensure id is a string
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ message: 'Invalid call ID' });
  }

  // Handle GET request to fetch a call
  if (req.method === 'GET') {
    try {
      const call = await callsService.getCallWithDetail(id);

      if (!call) {
        return res.status(404).json({ message: 'Call not found' });
      }

      // Check if transcriptionWithAudio is missing and sessionId is available
      let updatedCall = call;
      if (!call.transcriptionWithAudio && !call.transcription && call.sessionId) {
        try {
          // Get the bucket name from environment variables
          const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;

          if (bucketName) {
            // Get the GCP Storage service
            const storageService = getGcpStorageService();

            // Get transcript with audio records using the service method
            const interactions = await storageService.getTranscriptWithAudioRecords(
              bucketName,
              call.sessionId,
            );

            if (interactions && interactions.length > 0) {
              // Stringify the interactions array to store in the call record
              const transcriptionWithAudio = JSON.stringify(interactions);

              // Update the call detail in the database
              await callsService.updateCall(id, { transcriptionWithAudio });

              // Update the call object for the response
              updatedCall = {
                ...call,
                transcriptionWithAudio,
              };

              console.log(`Retrieved and added transcriptionWithAudio for call ${id}`);
            }
          }
        } catch (audioError) {
          console.warn(
            `Failed to get transcription with audio for call ${id}:`,
            audioError instanceof Error ? audioError.message : String(audioError),
          );
          // Continue without audio data - it's not critical
        }
      }

      // Enhanced fallback logic: Fix missing duration and call types
      let needsUpdate = false;
      const updateData: Record<string, unknown> = {};

      // Check if duration is missing or 0 and we have transcription data
      const currentDurationSeconds = parseDurationToSeconds(updatedCall.duration);
      if (currentDurationSeconds === 0 && updatedCall.transcriptionWithAudio) {
        const calculatedDuration = calculateDurationFromTranscript(
          updatedCall.transcriptionWithAudio,
        );
        if (calculatedDuration > 0) {
          // Format as "X sec" or "X.X min" for consistency
          const formattedDuration =
            calculatedDuration < 60
              ? `${calculatedDuration} sec`
              : `${(calculatedDuration / 60).toFixed(1)} min`;

          updateData.duration = formattedDuration;
          updatedCall.duration = formattedDuration;
          needsUpdate = true;

          console.log(
            `Calculated and set duration for call ${id}: ${formattedDuration} (${calculatedDuration}s)`,
          );
        }
      }

      // Check if call type needs classification or improvement
      const needsTypeClassification =
        !updatedCall.type ||
        (updatedCall.type as number) === CallType.OTHER ||
        (updatedCall.type === CallType.TRANSFER_TO_HUMAN &&
          getEffectiveCallDuration(updatedCall) > 0);

      if (needsTypeClassification) {
        let newCallType: CallType | null = null;

        // Try smart transfer classification if it's currently a transfer
        if (updatedCall.type === CallType.TRANSFER_TO_HUMAN) {
          newCallType = TransferClassificationService.classifyTransfer(updatedCall);

          if (newCallType !== CallType.TRANSFER_TO_HUMAN) {
            const reason = TransferClassificationService.getClassificationReason(
              newCallType,
              updatedCall,
            );
            console.log(
              `Smart transfer classification for call ${id}: ${CallType[newCallType]} - ${reason}`,
            );
          }
        }

        // Check if call should be marked as disconnected
        if (!newCallType && shouldMarkAsDisconnected(updatedCall)) {
          newCallType = CallType.DISCONNECTED;
          console.log(
            `Auto-marking call ${id} as DISCONNECTED - long duration with no meaningful type`,
          );
        }

        // Apply the new call type if determined
        if (newCallType && newCallType !== updatedCall.type) {
          updateData.type = newCallType;
          updatedCall.type = newCallType;
          needsUpdate = true;
        }
      }

      // Apply any updates to the database
      if (needsUpdate) {
        try {
          await callsService.updateCall(id, updateData);
          console.log(`Updated call ${id} with enhanced fallback data:`, updateData);
        } catch (updateError) {
          console.warn(`Failed to update call ${id} with enhanced data:`, updateError);
        }
      }

      // Check if call has voicemail but empty voicemailUrl and try to fill it
      if (
        updatedCall.hasVoiceMail &&
        (!updatedCall.voicemailUrl || updatedCall.voicemailUrl.trim() === '') &&
        updatedCall.sessionId
      ) {
        try {
          console.log(
            `Call ${id} has voicemail but empty voicemailUrl, attempting to retrieve from GCP Storage`,
          );

          // Get the bucket name from environment variables
          const bucketName = process.env.GCP_AUDIO_BUCKET_NAME;

          if (bucketName) {
            // Get the GCP Storage service
            const storageService = getGcpStorageService();

            // Get the latest audio file for the session (similar to end-conversation logic)
            const audioFile = await storageService.getLatestAudioFileForSession(
              bucketName,
              updatedCall.sessionId,
            );

            if (audioFile && audioFile.url) {
              // Update the call with the found voicemail URL
              await callsService.updateCall(id, { voicemailUrl: audioFile.url });

              // Update the call object for the response
              updatedCall = {
                ...updatedCall,
                voicemailUrl: audioFile.url,
              };

              console.log(
                `Successfully retrieved and updated voicemailUrl for call ${id}: ${audioFile.fileName}`,
              );
            } else {
              console.warn(
                `No audio files found for call ${id} with session ${updatedCall.sessionId}`,
              );

              // If we still don't have a voicemail URL, change the call type to OTHER
              if (!updatedCall.voicemailUrl || updatedCall.voicemailUrl.trim() === '') {
                await callsService.updateCall(id, { type: CallType.OTHER, hasVoiceMail: false });

                // Update the call object for the response
                updatedCall = {
                  ...updatedCall,
                  type: CallType.OTHER,
                  hasVoiceMail: false,
                };

                console.log(
                  `Changed call type to OTHER for call ${id} due to missing voicemail file`,
                );
              }
            }
          }
        } catch (voicemailError) {
          console.error(
            `Error retrieving voicemail for call ${id}:`,
            voicemailError instanceof Error ? voicemailError.message : String(voicemailError),
          );
        }
      }

      return res.status(200).json({
        ...updatedCall,
        dateTime: updatedCall.date,
        clientName: 'Patient', // Default name, will be updated from patient endpoint
        clientInfo: null, // Will be fetched separately
      });
    } catch (error) {
      console.error('Error fetching call:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  }

  // Handle PATCH request to update a call
  if (req.method === 'PATCH') {
    try {
      const call = await callsService.getCallById(id);

      if (!call) {
        return res.status(404).json({ message: 'Call not found' });
      }

      const { priorityScore, urgent } = req.body;

      // Update the call record in Firestore
      await callsService.updateCall(id, { priorityScore, urgent });

      // If the call is marked as urgent, simulate sending an alert
      if (urgent === true && call.urgent !== true) {
        console.log(`URGENT ALERT: Call ${call.id} has been marked urgent!`);

        // In a real app, you would integrate with an SMS/email service here
        // Return a special response to inform the client
        return res.status(200).json({
          success: true,
          updated: true,
          alertSent: true,
          message: 'Call updated and urgent alert sent',
        });
      }

      return res.status(200).json({
        success: true,
        updated: true,
        message: 'Call updated successfully',
      });
    } catch (error) {
      console.error('Error updating call:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to update call',
      });
    }
  }

  // If we get here, the method is not supported
  return res.status(405).json({ message: 'Method not allowed' });
}
