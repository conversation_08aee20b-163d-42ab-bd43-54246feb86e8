import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { callsService } from '@/utils/firestore';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import {
  withLocationContext,
  LocationAwareRequest,
  addLocationFilter,
} from '@/lib/middleware/locationAware';
import { NextApiResponse } from 'next';
import { CallType } from '@/models/CallTypes';

dayjs.extend(utc);

async function handler(req: LocationAwareRequest, res: NextApiResponse) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify Firebase authentication
    const user = await verifyAuthAndGetUser(req);

    if (!user) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    // Extract pagination parameters
    const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
    const startAfterId = req.query.startAfterId as string | undefined;

    // Parse date filters
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (req.query.startDate) {
      const parsedDate = dayjs.utc(req.query.startDate as string);
      if (parsedDate.isValid()) {
        startDate = parsedDate.toDate();
      }
    }

    if (req.query.endDate) {
      const parsedDate = dayjs.utc(req.query.endDate as string);
      if (parsedDate.isValid()) {
        // Set the time to end of day to include all records on that day
        endDate = parsedDate.toDate();
        console.log('🔍 API endDate parsed:', {
          original: req.query.endDate,
          parsed: endDate.toISOString(),
        });
      }
    }

    // Parse search parameters
    const searchTerm = req.query.search as string | undefined;

    // Parse call type filter
    let callType: CallType | undefined;
    if (req.query.callType && req.query.callType !== '') {
      const typeValue = parseInt(req.query.callType as string, 10);
      if (!isNaN(typeValue) && Object.values(CallType).includes(typeValue)) {
        callType = typeValue as CallType;
      }
    }

    // Parse priority filter
    let minPriority: number | undefined;
    let maxPriority: number | undefined;
    if (req.query.priority && req.query.priority !== '') {
      const priorityRange = (req.query.priority as string).split('-');
      if (priorityRange.length === 2) {
        const min = parseInt(priorityRange[0], 10);
        const max = parseInt(priorityRange[1], 10);
        if (!isNaN(min) && !isNaN(max)) {
          minPriority = min;
          maxPriority = max;
        }
      }
    }

    // Parse office hours filter
    const officeHoursOnly = req.query.officeHoursOnly === 'true';
    const callDirection: 'inbound' | 'outbound' | 'both' =
      (req.query.callDirection as 'inbound' | 'outbound' | 'both') || 'inbound';

    // Add location filtering to the query
    const queryParams = addLocationFilter(req, {
      limit,
      startAfterId,
      clinicId: req.user?.clinicId,
      startDate,
      endDate,
      searchTerm,
      callType,
      minPriority,
      maxPriority,
      officeHoursOnly,
      callDirection,
    });

    // Get paginated calls with client data, filtered by current location
    const result = await callsService.paginateCalls(
      queryParams as {
        limit: number;
        startAfterId?: string;
        clinicId?: number | null;
        locationId?: string;
        startDate?: Date;
        endDate?: Date;
        searchTerm?: string;
        callType?: CallType;
        minPriority?: number;
        maxPriority?: number;
        officeHoursOnly?: boolean;
        callDirection?: 'inbound' | 'outbound' | 'both';
      },
    );

    return res.status(200).json(result);
  } catch (error) {
    console.error('Error handling call records request:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

export default withLocationContext(handler);
