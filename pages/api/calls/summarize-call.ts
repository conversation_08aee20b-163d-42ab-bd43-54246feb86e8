import { NextApiRequest, NextApiResponse } from 'next';
import { CallSummarizationService } from '@/lib/services/call-summarization';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow PUT requests
  if (req.method !== 'PUT') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { callId } = req.body;
    if (!callId) {
      return res.status(400).json({ message: 'Call ID is required' });
    }

    // Use the common summarization service
    const summarizationService = CallSummarizationService.getInstance();
    const summary = await summarizationService.summarizeCallById(callId);

    return res.status(200).json({ summary });
  } catch (error) {
    console.error('Error handling call records request:', error);

    // Handle specific error cases
    if (error instanceof Error) {
      if (error.message === 'Call not found') {
        return res.status(404).json({ message: 'Call not found' });
      }
      if (error.message === 'Transcript is not available') {
        return res.status(400).json({ message: 'Transcript is not available' });
      }
      if (error.message === 'Failed to generate call summary') {
        return res.status(500).json({ message: 'Failed to generate call summary' });
      }
    }

    return res.status(500).json({ message: 'Internal server error' });
  }
}
