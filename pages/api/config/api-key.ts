import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  // Only return the API key if the user is authenticated
  // This keeps the API key secure and not exposed in client-side code
  return res.status(200).json({
    apiKey: process.env.EXTERNAL_SERVICE_API_KEY || '',
  });
}
