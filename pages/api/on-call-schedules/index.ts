import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallScheduleService } from '@/lib/services/on-call-schedule';
import { OnCallScheduleCreateRequest, OnCallScheduleFilters } from '@/models/OnCallSchedule';

interface ApiResponse {
  schedules?: unknown[];
  schedule?: unknown;
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/on-call-schedules:
 *   get:
 *     summary: Get on-call schedules
 *     tags: [On-Call Schedules]
 *     parameters:
 *       - name: locationId
 *         in: query
 *         description: Filter by location ID
 *         schema:
 *           type: string
 *       - name: doctorId
 *         in: query
 *         description: Filter by doctor ID
 *         schema:
 *           type: string
 *       - name: clinicId
 *         in: query
 *         description: Filter by clinic ID
 *         schema:
 *           type: number
 *       - name: startDate
 *         in: query
 *         description: Start date filter (YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: endDate
 *         in: query
 *         description: End date filter (YYYY-MM-DD)
 *         schema:
 *           type: string
 *       - name: isActive
 *         in: query
 *         description: Filter by active status
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: List of on-call schedules
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 schedules:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/OnCallSchedule'
 *       400:
 *         description: Bad request
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create new on-call schedule
 *     tags: [On-Call Schedules]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OnCallScheduleCreateRequest'
 *     responses:
 *       201:
 *         description: Schedule created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 schedule:
 *                   $ref: '#/components/schemas/OnCallSchedule'
 *       400:
 *         description: Validation error
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  try {
    switch (req.method) {
      case 'GET':
        await handleGet(req, res);
        break;
      case 'POST':
        await handlePost(req, res);
        break;
      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).json({ error: `Method ${req.method} not allowed` });
        break;
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Internal server error',
    });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse<ApiResponse>): Promise<void> {
  try {
    const { locationId, doctorId, clinicId, startDate, endDate, isActive } = req.query;

    // Build filters
    const filters: OnCallScheduleFilters = {};

    if (locationId && typeof locationId === 'string') {
      filters.locationId = locationId;
    }

    if (doctorId && typeof doctorId === 'string') {
      filters.doctorId = doctorId;
    }

    if (clinicId && typeof clinicId === 'string') {
      const clinicIdNum = parseInt(clinicId, 10);
      if (!isNaN(clinicIdNum)) {
        filters.clinicId = clinicIdNum;
      }
    }

    if (startDate && typeof startDate === 'string') {
      // Validate date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
        return res.status(400).json({ error: 'Invalid startDate format. Use YYYY-MM-DD' });
      }
      filters.startDate = startDate;
    }

    if (endDate && typeof endDate === 'string') {
      // Validate date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
        return res.status(400).json({ error: 'Invalid endDate format. Use YYYY-MM-DD' });
      }
      filters.endDate = endDate;
    }

    if (isActive && typeof isActive === 'string') {
      filters.isActive = isActive.toLowerCase() === 'true';
    }

    // Get schedules
    const schedules = await OnCallScheduleService.getSchedules(filters);

    res.status(200).json({ schedules });
  } catch (error) {
    console.error('Error fetching schedules:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch schedules',
    });
  }
}

async function handlePost(req: NextApiRequest, res: NextApiResponse<ApiResponse>): Promise<void> {
  try {
    const scheduleData: OnCallScheduleCreateRequest = req.body;

    // Validate required fields
    if (
      !scheduleData.doctorId ||
      !scheduleData.locationId ||
      !scheduleData.date ||
      !scheduleData.startTime ||
      !scheduleData.endTime
    ) {
      return res.status(400).json({
        error: 'Missing required fields: doctorId, locationId, date, startTime, endTime',
      });
    }

    // TODO: Get user ID from authentication
    const createdBy = 'system'; // This should come from authenticated user

    // Create schedule
    const schedule = await OnCallScheduleService.createSchedule(scheduleData, createdBy);

    res.status(201).json({
      message: 'On-call schedule created successfully',
      schedule,
    });
  } catch (error) {
    console.error('Error creating schedule:', error);

    // Handle validation errors
    if (error instanceof Error && error.message.includes('Validation failed')) {
      return res.status(400).json({ error: error.message });
    }

    // Handle conflict errors
    if (error instanceof Error && error.message.includes('conflicts detected')) {
      return res.status(409).json({ error: error.message });
    }

    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to create schedule',
    });
  }
}
