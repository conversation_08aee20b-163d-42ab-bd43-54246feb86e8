import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallScheduleService } from '@/lib/services/on-call-schedule';

interface ApiResponse {
  currentDoctor?: unknown;
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/on-call-schedules/current:
 *   get:
 *     summary: Get currently on-call doctor
 *     tags: [On-Call Schedules]
 *     parameters:
 *       - name: locationId
 *         in: query
 *         required: true
 *         description: Location ID to check for current on-call doctor
 *         schema:
 *           type: string
 *       - name: currentTime
 *         in: query
 *         description: Override current time for testing (ISO string)
 *         schema:
 *           type: string
 *           format: date-time
 *     responses:
 *       200:
 *         description: Current on-call doctor information (or null if none)
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 currentDoctor:
 *                   oneOf:
 *                     - $ref: '#/components/schemas/OnCallSchedule'
 *                     - type: null
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - missing locationId
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  try {
    if (req.method !== 'GET') {
      res.setHeader('Allow', ['GET']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }

    await handleGet(req, res);
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Internal server error',
    });
  }
}

async function handleGet(req: NextApiRequest, res: NextApiResponse<ApiResponse>): Promise<void> {
  try {
    const { locationId, currentTime } = req.query;

    // Validate required locationId parameter
    if (!locationId || typeof locationId !== 'string') {
      return res.status(400).json({
        error: 'locationId query parameter is required',
      });
    }

    // Parse optional currentTime parameter for testing
    let testCurrentTime: Date | undefined;
    if (currentTime && typeof currentTime === 'string') {
      testCurrentTime = new Date(currentTime);
      if (isNaN(testCurrentTime.getTime())) {
        return res.status(400).json({
          error: 'Invalid currentTime format. Use ISO date string',
        });
      }
    }

    // Get current on-call doctor
    const currentDoctor = await OnCallScheduleService.getCurrentOnCallDoctor(
      locationId,
      testCurrentTime,
    );

    if (currentDoctor) {
      res.status(200).json({
        currentDoctor,
        message: `Dr. ${currentDoctor.doctorName} is currently on-call`,
      });
    } else {
      res.status(200).json({
        currentDoctor: null,
        message: 'No doctor is currently on-call for this location',
      });
    }
  } catch (error) {
    console.error('Error fetching current on-call doctor:', error);

    // Handle location not found error
    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }

    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch current on-call doctor',
    });
  }
}
