import type { NextApiRequest, NextApiResponse } from 'next';
import { OnCallScheduleService } from '@/lib/services/on-call-schedule';
import { OnCallScheduleUpdateRequest } from '@/models/OnCallSchedule';

interface ApiResponse {
  schedule?: unknown;
  message?: string;
  error?: string;
}

/**
 * @swagger
 * /api/on-call-schedules/{id}:
 *   get:
 *     summary: Get specific on-call schedule
 *     tags: [On-Call Schedules]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Schedule ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Schedule details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 schedule:
 *                   $ref: '#/components/schemas/OnCallSchedule'
 *       404:
 *         description: Schedule not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update on-call schedule
 *     tags: [On-Call Schedules]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Schedule ID
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OnCallScheduleUpdateRequest'
 *     responses:
 *       200:
 *         description: Schedule updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 schedule:
 *                   $ref: '#/components/schemas/OnCallSchedule'
 *       400:
 *         description: Validation error
 *       404:
 *         description: Schedule not found
 *       409:
 *         description: Schedule conflict
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete on-call schedule (soft delete)
 *     tags: [On-Call Schedules]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Schedule ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Schedule deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Schedule not found
 *       500:
 *         description: Internal server error
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
): Promise<void> {
  try {
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Schedule ID is required' });
    }

    switch (req.method) {
      case 'GET':
        await handleGet(req, res, id);
        break;
      case 'PUT':
        await handlePut(req, res, id);
        break;
      case 'DELETE':
        await handleDelete(req, res, id);
        break;
      default:
        res.setHeader('Allow', ['GET', 'PUT', 'DELETE']);
        res.status(405).json({ error: `Method ${req.method} not allowed` });
        break;
    }
  } catch (error) {
    console.error('API Error:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Internal server error',
    });
  }
}

async function handleGet(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
  id: string,
): Promise<void> {
  try {
    const schedule = await OnCallScheduleService.getScheduleById(id);

    if (!schedule) {
      return res.status(404).json({ error: 'Schedule not found' });
    }

    res.status(200).json({ schedule });
  } catch (error) {
    console.error('Error fetching schedule:', error);
    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to fetch schedule',
    });
  }
}

async function handlePut(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
  id: string,
): Promise<void> {
  try {
    const updateData: OnCallScheduleUpdateRequest = req.body;

    // Validate that at least one field is being updated
    const updateFields = Object.keys(updateData);
    if (updateFields.length === 0) {
      return res.status(400).json({ error: 'At least one field must be provided for update' });
    }

    // TODO: Get user ID from authentication
    const updatedBy = 'system'; // This should come from authenticated user

    // Update schedule
    const schedule = await OnCallScheduleService.updateSchedule(id, updateData, updatedBy);

    res.status(200).json({
      message: 'Schedule updated successfully',
      schedule,
    });
  } catch (error) {
    console.error('Error updating schedule:', error);

    // Handle validation errors
    if (error instanceof Error && error.message.includes('Validation failed')) {
      return res.status(400).json({ error: error.message });
    }

    // Handle conflict errors
    if (error instanceof Error && error.message.includes('conflicts detected')) {
      return res.status(409).json({ error: error.message });
    }

    // Handle not found errors
    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }

    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to update schedule',
    });
  }
}

async function handleDelete(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>,
  id: string,
): Promise<void> {
  try {
    // TODO: Get user ID from authentication
    const deletedBy = 'system'; // This should come from authenticated user

    // Delete schedule (soft delete)
    await OnCallScheduleService.deleteSchedule(id, deletedBy);

    res.status(200).json({
      message: 'Schedule deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting schedule:', error);

    // Handle not found errors
    if (error instanceof Error && error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }

    res.status(500).json({
      error: error instanceof Error ? error.message : 'Failed to delete schedule',
    });
  }
}
