import { NextApiRequest, NextApiResponse } from 'next';
import { callsService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    console.log('🔧 TESTING MYSQL SYNTAX - NO AUTH REQUIRED');

    // Test with the exact parameters that were failing
    const params = {
      limit: 11,
      clinicId: 12,
      locationId: '118', // String version
      callDirection: 'both' as const,
      includeUnknownPhoneNumbers: false,
    };

    console.log('🔧 Testing paginateCalls with params:', params);

    const result = await callsService.paginateCalls(params);

    console.log('✅ MySQL query executed successfully!');
    console.log('📋 Results:', {
      callsCount: result.calls.length,
      isLastPage: result.isLastPage,
      lastDocId: result.lastDocId,
    });

    return res.status(200).json({
      success: true,
      message: 'MySQL syntax error is FIXED!',
      callsCount: result.calls.length,
      isLastPage: result.isLastPage,
    });
  } catch (error) {
    console.error('🚨 MySQL syntax error still exists:', error);

    const errorMessage = error instanceof Error ? error.message : String(error);
    const isSyntaxError = errorMessage.includes('syntax error') || errorMessage.includes('MySQL');

    return res.status(500).json({
      success: false,
      message: isSyntaxError ? 'MySQL syntax error still NOT fixed!' : 'Other error occurred',
      error: errorMessage,
      isSQLSyntaxError: isSyntaxError,
    });
  }
}
