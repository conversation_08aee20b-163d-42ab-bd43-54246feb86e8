import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { PracticeService } from '@/lib/services/practiceService';
import { Practice } from '@/models/Practice';
import { User, UserRole } from '@/models/auth';

/**
 * @swagger
 * /api/practices:
 *   get:
 *     summary: Get all practices for the authenticated user's clinic
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of practices
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 practices:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Practice'
 *                 total:
 *                   type: integer
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create a new practice
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Name of the practice
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Optional description of the practice
 *               isActive:
 *                 type: boolean
 *                 default: true
 *                 description: Whether the practice is active
 *     responses:
 *       201:
 *         description: Practice created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 practice:
 *                   $ref: '#/components/schemas/Practice'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Internal server error
 */

interface CreatePracticeRequest {
  name: string;
  description?: string;
  isActive?: boolean;
}

interface PracticesResponse {
  practices: Practice[];
  total: number;
}

interface CreatePracticeResponse {
  success: boolean;
  practice: Practice;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetPractices(req, res, user as User);
    } else if (req.method === 'POST') {
      return await handleCreatePractice(req, res, user as User);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in practices API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve all practices for the user's clinic
 */
async function handleGetPractices(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    const practices = await PracticeService.getPracticesByClinicId(user.clinicId!);

    const response: PracticesResponse = {
      practices,
      total: practices.length,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting practices:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve practices',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle POST request to create a new practice
 */
async function handleCreatePractice(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
): Promise<void> {
  try {
    // Validate user permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can create practices',
      });
    }

    // Validate request body
    const { name, description, isActive }: CreatePracticeRequest = req.body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Practice name is required',
      });
    }

    if (name.length > 100) {
      return res.status(400).json({
        success: false,
        message: 'Practice name must be 100 characters or less',
      });
    }

    if (description && typeof description !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Practice description must be a string',
      });
    }

    if (description && description.length > 500) {
      return res.status(400).json({
        success: false,
        message: 'Practice description must be 500 characters or less',
      });
    }

    if (isActive !== undefined && typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean',
      });
    }

    // Create practice data
    const practiceData = {
      clinicId: user.clinicId!,
      name: name.trim(),
      description: description?.trim() || undefined,
      isActive: isActive ?? true,
    };

    // Create the practice
    const newPractice = await PracticeService.createPractice(practiceData, user);

    const response: CreatePracticeResponse = {
      success: true,
      practice: newPractice,
      message: 'Practice created successfully',
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error creating practice:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create practice',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
