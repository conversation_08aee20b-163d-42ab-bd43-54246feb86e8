import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { PracticeService } from '@/lib/services/practiceService';
import { Practice } from '@/models/Practice';
import { User, UserRole } from '@/models/auth';

/**
 * @swagger
 * /api/practices/{id}:
 *   get:
 *     summary: Get a specific practice by ID
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Practice ID
 *     responses:
 *       200:
 *         description: Practice details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 practice:
 *                   $ref: '#/components/schemas/Practice'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Practice not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update a practice
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Practice ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 100
 *                 description: Name of the practice
 *               description:
 *                 type: string
 *                 maxLength: 500
 *                 description: Description of the practice
 *               isActive:
 *                 type: boolean
 *                 description: Whether the practice is active
 *     responses:
 *       200:
 *         description: Practice updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 practice:
 *                   $ref: '#/components/schemas/Practice'
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Practice not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete a practice (soft delete)
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Practice ID
 *     responses:
 *       200:
 *         description: Practice deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Practice not found
 *       409:
 *         description: Conflict - practice has active locations
 *       500:
 *         description: Internal server error
 */

interface UpdatePracticeRequest {
  name?: string;
  description?: string;
  isActive?: boolean;
}

interface PracticeDetailResponse {
  practice: Practice;
}

interface UpdatePracticeResponse {
  success: boolean;
  practice: Practice;
  message: string;
}

interface DeletePracticeResponse {
  success: boolean;
  message: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract practice ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid practice ID',
    });
  }

  try {
    if (req.method === 'GET') {
      return await handleGetPractice(req, res, user as User, id);
    } else if (req.method === 'PUT') {
      return await handleUpdatePractice(req, res, user as User, id);
    } else if (req.method === 'DELETE') {
      return await handleDeletePractice(req, res, user as User, id);
    } else {
      return res.status(405).json({
        success: false,
        message: 'Method not allowed',
      });
    }
  } catch (error) {
    console.error('Error in practice detail API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve a specific practice
 */
async function handleGetPractice(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  practiceId: string,
): Promise<void> {
  try {
    const practice = await PracticeService.getPracticeById(practiceId, user.clinicId!);

    if (!practice) {
      return res.status(404).json({
        success: false,
        message: 'Practice not found',
      });
    }

    const response: PracticeDetailResponse = {
      practice,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting practice:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve practice',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle PUT request to update a practice
 */
async function handleUpdatePractice(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  practiceId: string,
): Promise<void> {
  try {
    // Validate user permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can update practices',
      });
    }

    // Validate request body
    const { name, description, isActive }: UpdatePracticeRequest = req.body;

    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Practice name must be a non-empty string',
        });
      }

      if (name.length > 100) {
        return res.status(400).json({
          success: false,
          message: 'Practice name must be 100 characters or less',
        });
      }
    }

    if (description !== undefined && typeof description !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Practice description must be a string',
      });
    }

    if (description && description.length > 500) {
      return res.status(400).json({
        success: false,
        message: 'Practice description must be 500 characters or less',
      });
    }

    if (isActive !== undefined && typeof isActive !== 'boolean') {
      return res.status(400).json({
        success: false,
        message: 'isActive must be a boolean',
      });
    }

    // Prepare update data
    const updateData: Partial<Practice> = {};
    if (name !== undefined) updateData.name = name.trim();
    if (description !== undefined) updateData.description = description.trim() || undefined;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Check if there's anything to update
    if (Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields provided for update',
      });
    }

    // Update the practice
    const updatedPractice = await PracticeService.updatePractice(practiceId, updateData, user);

    const response: UpdatePracticeResponse = {
      success: true,
      practice: updatedPractice,
      message: 'Practice updated successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error updating practice:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('already exists')) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update practice',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle DELETE request to soft delete a practice
 */
async function handleDeletePractice(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  practiceId: string,
): Promise<void> {
  try {
    // Validate user permissions
    if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
      return res.status(403).json({
        success: false,
        message: 'Forbidden: Only clinic admins can delete practices',
      });
    }

    // Delete the practice
    await PracticeService.deletePractice(practiceId, user);

    const response: DeletePracticeResponse = {
      success: true,
      message: 'Practice deleted successfully',
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error deleting practice:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('active locations')) {
        return res.status(409).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to delete practice',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
