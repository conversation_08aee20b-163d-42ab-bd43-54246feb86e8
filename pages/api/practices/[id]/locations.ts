import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { PracticeService } from '@/lib/services/practiceService';
import { Location } from '@/models/Location';
import { User } from '@/models/auth';

/**
 * @swagger
 * /api/practices/{id}/locations:
 *   get:
 *     summary: Get all locations for a specific practice
 *     tags: [Practices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Practice ID
 *       - in: query
 *         name: includeInactive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Whether to include inactive locations
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 50
 *         description: Maximum number of locations to return
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Number of locations to skip
 *     responses:
 *       200:
 *         description: List of locations for the practice
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 locations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Location'
 *                 total:
 *                   type: integer
 *                   description: Total number of locations
 *                 practiceId:
 *                   type: string
 *                   description: Practice ID
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Practice not found
 *       500:
 *         description: Internal server error
 */

interface PracticeLocationsResponse {
  locations: Location[];
  total: number;
  practiceId: string;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow GET method
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  // Extract practice ID from query
  const { id } = req.query;
  if (!id || Array.isArray(id)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid practice ID',
    });
  }

  try {
    return await handleGetPracticeLocations(req, res, user as User, id);
  } catch (error) {
    console.error('Error in practice locations API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle GET request to retrieve all locations for a practice
 */
async function handleGetPracticeLocations(
  req: NextApiRequest,
  res: NextApiResponse,
  user: User,
  practiceId: string,
): Promise<void> {
  try {
    // Parse query parameters
    const includeInactive = req.query.includeInactive === 'true';
    const limit = Math.min(parseInt(req.query.limit as string) || 50, 100);
    const offset = Math.max(parseInt(req.query.offset as string) || 0, 0);

    // First, verify that the practice exists and belongs to the user's clinic
    const practice = await PracticeService.getPracticeById(practiceId, user.clinicId!);
    if (!practice) {
      return res.status(404).json({
        success: false,
        message: 'Practice not found',
      });
    }

    // Get all locations for the practice
    const allLocations = await PracticeService.getLocationsByPracticeId(
      practiceId,
      user.clinicId!,
      { includeInactive },
    );

    // Apply pagination
    const paginatedLocations = allLocations.slice(offset, offset + limit);

    const response: PracticeLocationsResponse = {
      locations: paginatedLocations,
      total: allLocations.length,
      practiceId,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting practice locations:', error);

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
        });
      }

      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to retrieve practice locations',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}
