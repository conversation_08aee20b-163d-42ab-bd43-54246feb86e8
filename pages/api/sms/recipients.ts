import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

import logger from '@/lib/external-api/v2/utils/logger';
import { URMA_LOMBARD_LOCATION_ID } from '@/app-config';
import {
  AppointmentStatus,
  BadRequestError,
  createApiHandler,
  ensureProvidersInitialized,
  getProviderFromRequest,
  IProvider,
} from '@/lib/external-api/v2';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

const smsRecipientsSchema = z.object({
  locationId: z.string().optional().default(URMA_LOMBARD_LOCATION_ID),
  getPatientsForTheLastNMonths: z.number().optional().default(1),
});

type Recipient = {
  phoneNumber: string;
  firstName: string;
  lastName: string;
};

const getRecipients = async (
  locationId: string,
  provider: IProvider,
  getPatientsForTheLastNMonths: number,
  appointmentOffset = 0,
): Promise<Recipient[]> => {
  logger.info(
    {
      context: 'sms.recipients',
      locationId,
      getPatientsForTheLastNMonths,
      appointmentOffset,
    },
    'Getting recipients',
  );

  const appointmentService = provider.getAppointmentService();
  const patientService = provider.getPatientService();

  const now = dayjs.utc();
  const startDate = now.subtract(getPatientsForTheLastNMonths, 'month').toISOString();
  const endDate = now.toISOString();

  const appointments = await appointmentService.getAppointmentsByStatus(
    AppointmentStatus.FULFILLED,
    {
      locationId,
      startLastUpdated: startDate,
      endLastUpdated: endDate,
      pagination: {
        limit: 50,
        offset: appointmentOffset,
      },
    },
  );

  const patientIds = new Set<string>();
  for (const appointment of appointments.items) {
    patientIds.add(appointment.patientId);
  }

  const patients = await Promise.all(
    Array.from(patientIds).map(id => patientService.getPatientByIdWithoutInsurance(id)),
  );

  const recipients = patients
    .filter(p => Boolean(p?.phoneNumber))
    .map(patient => ({
      phoneNumber: patient!.phoneNumber!,
      firstName: patient!.firstName,
      lastName: patient!.lastName,
    }));

  if (appointments.pagination.hasMore) {
    const nextAppointmentOffset = appointments.pagination.offset + appointments.items.length;
    const nextRecipients = await getRecipients(
      locationId,
      provider,
      getPatientsForTheLastNMonths,
      nextAppointmentOffset,
    );
    recipients.push(...nextRecipients);
  }

  // Order recipients by last name
  return recipients.sort((a, b) => a.lastName.localeCompare(b.lastName));
};

async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const validationResult = smsRecipientsSchema.safeParse(req.body);
  if (!validationResult.success) {
    logger.error(
      { context: 'sms.recipients', innerError: validationResult.error },
      'Invalid request body',
    );
    throw new BadRequestError('Invalid request body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const { locationId, getPatientsForTheLastNMonths } = validationResult.data;
  const provider = getProviderFromRequest(req);
  const recipients = await getRecipients(locationId, provider, getPatientsForTheLastNMonths);
  logger.info(
    {
      context: 'sms.recipients',
      recipientsCount: recipients.length,
    },
    'Recipients fetched',
  );

  res.status(200).json(recipients);
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [ensureProvidersInitialized],
});
