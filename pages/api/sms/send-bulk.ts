import { z } from 'zod';
import { NextApiRequest, NextApiResponse } from 'next';

import logger from '@/lib/external-api/v2/utils/logger';
import { BadRequestError } from '@/lib/external-api/v2';
import { smsService } from '@/lib/services/sms-service';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';

const sendBulkSmsSchema = z.object({
  toNumbers: z.array(z.string()).min(1),
  message: z.string(),
  dryRun: z.boolean().optional(),
});

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const validationResult = sendBulkSmsSchema.safeParse(req.body);
  if (!validationResult.success) {
    logger.error(
      { context: 'sendBulkSms', innerError: validationResult.error },
      'Invalid request body',
    );
    throw new BadRequestError('Invalid request body', {
      errors: validationResult.error.flatten().fieldErrors,
    });
  }

  const { toNumbers, message, dryRun } = validationResult.data;
  await smsService.sendBulkSms(toNumbers, message, { dryRun });
  res.status(200).json({ message: 'Ok' });
}
