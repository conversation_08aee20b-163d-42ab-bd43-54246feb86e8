import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { clientsService, callsService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  const { id } = req.query;

  // Ensure id is a string
  if (!id || Array.isArray(id)) {
    return res.status(400).json({ message: 'Invalid client ID' });
  }

  console.log('API route: fetching client with ID:', id);

  try {
    // Find the client in Firestore
    const client = await clientsService.getClientById(id);

    console.log('Client found in DB:', !!client);

    if (!client) {
      // If no client is found, check if a client exists in the 'patients' collection
      console.log('Client not found, checking patients collection');

      // Return a more descriptive error
      return res.status(404).json({
        message: 'Client not found',
        id,
        note: 'Try running the migration endpoint at /api/debug/migrate-patients first',
      });
    }

    // Find calls for this client
    const clientCalls = await callsService.getCallsByClientId(id);
    console.log(`Found ${clientCalls.length} calls for client`);

    // Format calls for frontend
    const formattedCalls = clientCalls.map(call => {
      // Use the actual duration if available, otherwise calculate it based on transcription length
      const duration =
        call.duration || Math.floor((call.transcription?.length || 0) / 100) + ' min';

      return {
        ...call,
        dateTime: call.date,
        duration,
      };
    });

    // Return client data with related calls, mapping fields for frontend consumption
    return res.status(200).json({
      ...client,
      name: client.fullName,
      birthDate: client.birthday,
      phone: client.phoneNumber,
      calls: formattedCalls,
    });
  } catch (error) {
    console.error('Error fetching client:', error);
    return res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
}
