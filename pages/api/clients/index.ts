import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { clientsService } from '@/utils/firestore';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Verify authentication using Firebase Auth
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({ message: 'Unauthorized' });
  }

  try {
    // Fetch all clients from Firestore
    const clients = await clientsService.getAllClients();

    // Format client data for frontend
    const formattedClients = clients.map(client => ({
      ...client,
      name: client.fullName,
      birthDate: client.birthday,
      phone: client.phoneNumber,
    }));

    return res.status(200).json(formattedClients);
  } catch (error) {
    console.error('Error fetching clients:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}
