import { NextApiRequest, NextApiResponse } from 'next';
import { verifyAuthAndGetUser } from '@/utils/firebase-admin';
import { UserLocationService } from '@/lib/services/userLocationService';
import { User, UserRole } from '@/models/auth';

/**
 * @swagger
 * /api/admin/bulk-assign-locations:
 *   post:
 *     summary: Bulk assign users to multiple locations
 *     tags: [Admin Bulk Operations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignments
 *             properties:
 *               assignments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - userId
 *                     - locationIds
 *                   properties:
 *                     userId:
 *                       type: string
 *                       description: ID of the user to assign locations to
 *                     locationIds:
 *                       type: array
 *                       items:
 *                         type: string
 *                       description: Array of location IDs to assign to the user
 *                 description: Array of user-location assignments
 *               validateOnly:
 *                 type: boolean
 *                 default: false
 *                 description: If true, only validate assignments without executing them
 *     responses:
 *       200:
 *         description: Bulk assignment completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 successful:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       user:
 *                         $ref: '#/components/schemas/User'
 *                 failed:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       error:
 *                         type: string
 *                 summary:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                     successful:
 *                       type: integer
 *                     failed:
 *                       type: integer
 *                 message:
 *                   type: string
 *       400:
 *         description: Bad request - validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - insufficient permissions
 *       500:
 *         description: Internal server error
 */

interface BulkAssignmentRequest {
  assignments: Array<{
    userId: string;
    locationIds: string[];
  }>;
  validateOnly?: boolean;
}

interface BulkAssignmentResponse {
  success: boolean;
  successful: Array<{ userId: string; user: User }>;
  failed: Array<{ userId: string; error: string }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
  message: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Only allow POST method
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed',
    });
  }

  // Authenticate user
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Authentication required',
    });
  }

  // Validate user permissions (only admins can perform bulk operations)
  if (user.role !== UserRole.CLINIC_ADMIN && user.role !== UserRole.SUPER_ADMIN) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: Only clinic admins can perform bulk location assignments',
    });
  }

  // Ensure user has a clinic ID
  if (!user.clinicId) {
    return res.status(403).json({
      success: false,
      message: 'Forbidden: User must belong to a clinic',
    });
  }

  try {
    return await handleBulkAssignment(req, res, user as User);
  } catch (error) {
    console.error('Error in bulk assignment API:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Handle POST request for bulk user-location assignments
 */
async function handleBulkAssignment(
  req: NextApiRequest,
  res: NextApiResponse,
  requestingUser: User,
): Promise<void> {
  try {
    // Validate request body
    const { assignments, validateOnly }: BulkAssignmentRequest = req.body;

    // Basic validation
    const validation = validateBulkAssignmentRequest(assignments);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors,
      });
    }

    // If validation only, return success
    if (validateOnly) {
      return res.status(200).json({
        success: true,
        message: 'Validation passed - assignments are valid',
        summary: {
          total: assignments.length,
          successful: 0,
          failed: 0,
        },
      });
    }

    // Perform bulk assignment
    const result = await UserLocationService.bulkAssignUsersToLocations(
      assignments,
      requestingUser,
    );

    const summary = {
      total: assignments.length,
      successful: result.successful.length,
      failed: result.failed.length,
    };

    const response: BulkAssignmentResponse = {
      success: true,
      successful: result.successful,
      failed: result.failed,
      summary,
      message: `Bulk assignment completed. ${summary.successful} successful, ${summary.failed} failed out of ${summary.total} total assignments.`,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error in bulk assignment:', error);

    // Handle specific validation errors
    if (error instanceof Error) {
      if (error.message.includes('permissions') || error.message.includes('Forbidden')) {
        return res.status(403).json({
          success: false,
          message: error.message,
        });
      }

      if (
        error.message.includes('outside your clinic') ||
        error.message.includes('does not belong')
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }
    }

    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk assignment',
      error: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Validate bulk assignment request structure
 */
function validateBulkAssignmentRequest(
  assignments: Array<{ userId: string; locationIds: string[] }>,
): ValidationResult {
  const errors: string[] = [];

  // Check if assignments array exists and is not empty
  if (!assignments || !Array.isArray(assignments)) {
    errors.push('assignments must be an array');
    return { isValid: false, errors };
  }

  if (assignments.length === 0) {
    errors.push('assignments array cannot be empty');
    return { isValid: false, errors };
  }

  // Validate maximum batch size
  if (assignments.length > 100) {
    errors.push('Maximum 100 assignments allowed per batch');
  }

  // Validate each assignment
  assignments.forEach((assignment, index) => {
    const prefix = `Assignment ${index + 1}`;

    // Validate userId
    if (!assignment.userId || typeof assignment.userId !== 'string') {
      errors.push(`${prefix}: userId is required and must be a string`);
    } else if (assignment.userId.trim().length === 0) {
      errors.push(`${prefix}: userId cannot be empty`);
    }

    // Validate locationIds
    if (!assignment.locationIds || !Array.isArray(assignment.locationIds)) {
      errors.push(`${prefix}: locationIds must be an array`);
    } else if (assignment.locationIds.length === 0) {
      errors.push(`${prefix}: locationIds array cannot be empty`);
    } else {
      // Validate each location ID
      assignment.locationIds.forEach((locationId, locIndex) => {
        if (!locationId || typeof locationId !== 'string') {
          errors.push(`${prefix}: locationIds[${locIndex}] must be a non-empty string`);
        } else if (locationId.trim().length === 0) {
          errors.push(`${prefix}: locationIds[${locIndex}] cannot be empty`);
        }
      });

      // Check for duplicate location IDs within the same assignment
      const uniqueLocationIds = new Set(assignment.locationIds);
      if (uniqueLocationIds.size !== assignment.locationIds.length) {
        errors.push(`${prefix}: duplicate location IDs found`);
      }

      // Validate maximum locations per user
      if (assignment.locationIds.length > 20) {
        errors.push(`${prefix}: maximum 20 locations allowed per user`);
      }
    }
  });

  // Check for duplicate user IDs across assignments
  const userIds = assignments.map(a => a.userId).filter(Boolean);
  const uniqueUserIds = new Set(userIds);
  if (uniqueUserIds.size !== userIds.length) {
    errors.push('Duplicate user IDs found across assignments');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
