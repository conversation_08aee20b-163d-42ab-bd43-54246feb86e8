import Head from 'next/head';
import { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import Link from 'next/link';

// Import the SwaggerUI component dynamically to avoid SSR issues
const SwaggerUI = dynamic(() => import('../components/SwaggerUI'), {
  ssr: false,
  loading: () => <div>Loading API documentation...</div>,
});

/**
 * Simple API Documentation page that uses Swagger UI with minimal styling
 */
function ApiDocsSimple() {
  const [isClient, setIsClient] = useState(false);
  const [spec, setSpec] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch the Swagger spec directly in the page component
  useEffect(() => {
    if (isClient) {
      setIsLoading(true);
      fetch('/api/swagger')
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to fetch spec: ${response.status} ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          setSpec(data);
          setIsLoading(false);
        })
        .catch(err => {
          console.error('Error fetching Swagger spec:', err);
          setError(err.message || 'Failed to fetch API specification');
          setIsLoading(false);
        });
    }
  }, [isClient]);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <>
      <Head>
        <title>Simple API Documentation - Front Desk Portal</title>
        <meta name="description" content="Simple API documentation for the Front Desk Portal" />
      </Head>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Simple API Documentation</h1>
        <div className="mb-4">
          <Link href="/api-docs" className="text-blue-500 hover:underline text-sm mr-4">
            View Full Documentation
          </Link>
        </div>

        {isLoading && <div>Loading API specification...</div>}

        {error && (
          <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
            <h3 className="font-bold">Error Loading API Documentation</h3>
            <p>{error}</p>
            <p className="mt-2">
              <button
                onClick={() => window.location.reload()}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Retry
              </button>
            </p>
          </div>
        )}

        {isClient && spec && <SwaggerUI spec={spec} />}
      </div>
    </>
  );
}

export default ApiDocsSimple;
