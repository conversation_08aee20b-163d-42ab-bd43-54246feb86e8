import React, { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/router';
import { Label, TextInput, Card, Alert } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import {
  loginWithEmailPassword,
  isAuthenticated,
  staffSignupWithCode,
  setupTokenRefresh,
} from '@/utils/auth';
import PasswordResetRequestForm from '@/components/PasswordResetRequestForm';
import Head from 'next/head';
import TwoFactorVerificationModal from '@/components/TwoFactorVerificationModal';

const LoginPage = () => {
  // Login form state
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  // Registration form state
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  const [inviteCode, setInviteCode] = useState('');

  // 2FA verification state
  const [show2FAModal, setShow2FAModal] = useState(false);
  const [loginAttemptEmail, setLoginAttemptEmail] = useState('');
  const [twoFASuccessMessage, setTwoFASuccessMessage] = useState('');

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState(0);

  const router = useRouter();

  // Get return URL from query parameters
  const returnUrl =
    typeof router.query.returnUrl === 'string' ? router.query.returnUrl : '/dashboard';

  // Setup token refresh mechanism
  useEffect(() => {
    setupTokenRefresh();
  }, []);

  useEffect(() => {
    // If already authenticated, redirect to return URL or dashboard
    if (isAuthenticated()) {
      router.push(returnUrl);
    }
  }, [router, returnUrl]);

  const handleLogin = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }

    setIsLoading(true);

    try {
      const shouldDo2FAResponse = await fetch('/api/auth/2fa/should-do-2fa-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!shouldDo2FAResponse.ok) {
        setError('An unexpected error occurred during login. Please try again.');
        setIsLoading(false);
        return;
      }

      const { isEligibleToLogin } = await shouldDo2FAResponse.json();
      if (!isEligibleToLogin) {
        const send2FAResponse = await fetch('/api/auth/2fa/send-2fa-verification-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
        });

        if (!send2FAResponse.ok) {
          const { message = 'An unexpected error occurred during login. Please try again.' } =
            await send2FAResponse.json();
          setError(message);
          setIsLoading(false);
          return;
        }

        // Show 2FA verification modal
        setLoginAttemptEmail(email);
        setShow2FAModal(true);
        setIsLoading(false);
        return;
      }

      const result = await loginWithEmailPassword(email, password);

      if (result.success) {
        // Redirect to return URL or dashboard
        router.push(returnUrl);
      } else {
        setError('Invalid email or password. Please try again.');
      }
    } catch (error) {
      setError('An unexpected error occurred during login. Please try again.');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerificationSuccess = async () => {
    try {
      // After successful 2FA verification, attempt to login
      const result = await loginWithEmailPassword(loginAttemptEmail, password);

      if (result.success) {
        // Close modal and redirect to return URL or dashboard
        setShow2FAModal(false);
        router.push(returnUrl);
      } else {
        // If login fails after verification, show error in the modal
        setTwoFASuccessMessage('');
        // The error will be shown in the modal through the onVerificationError callback
      }
    } catch (error) {
      console.error(error);
      // Handle error in the modal through the same mechanism
    }
  };

  const handleRegister = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!inviteCode) {
      setError('Please enter an invite code');
      return;
    }

    if (!name) {
      setError('Please enter your full name');
      return;
    }

    if (!registerEmail) {
      setError('Please enter your email');
      return;
    }

    if (!registerPassword) {
      setError('Please enter a password');
      return;
    }

    if (registerPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      const result = await staffSignupWithCode(inviteCode, registerEmail, registerPassword, name);

      if (result.success) {
        setSuccess(result.message);
        // Clear form
        setInviteCode('');
        setName('');
        setRegisterEmail('');
        setRegisterPassword('');
        setConfirmPassword('');
        // Redirect to return URL or dashboard since user is now logged in
        router.push(returnUrl);
      } else {
        setError(result.message || 'Registration failed. Please try again.');
      }
    } catch (error) {
      setError('An unexpected error occurred during registration. Please try again.');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Head>
        <title>Login - Front Desk Portal</title>
      </Head>

      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <Card className="w-full max-w-md">
          <div className="text-center mb-4">
            <div className="flex justify-center">
              {/* logo */}
              <div className="flex items-center relative text-3xl font-semibold my-4">
                <span className="logo-text">FrontDesk</span>
                <span className="relative top-[-5px]">AI</span>
              </div>
            </div>
          </div>

          {error && (
            <Alert color="failure" className="mb-4">
              {error}
            </Alert>
          )}
          {success && (
            <Alert color="success" className="mb-4">
              {success}
            </Alert>
          )}

          <div className="mb-4">
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`py-2 px-4 text-center ${activeTab === 0 ? 'text-blue-600 border-b-2 border-blue-600 active' : 'text-gray-500 hover:text-gray-600'}`}
                onClick={() => setActiveTab(0)}
              >
                Login
              </button>
              <button
                className={`py-2 px-4 text-center ${activeTab === 1 ? 'text-blue-600 border-b-2 border-blue-600 active' : 'text-gray-500 hover:text-gray-600'}`}
                onClick={() => setActiveTab(1)}
              >
                Register with Invite Code
              </button>
              <button
                className={`py-2 px-4 text-center ${activeTab === 2 ? 'text-blue-600 border-b-2 border-blue-600 active' : 'text-gray-500 hover:text-gray-600'}`}
                onClick={() => setActiveTab(2)}
              >
                Reset Password
              </button>
            </div>
          </div>

          {/* Two Factor Authentication Modal */}
          <TwoFactorVerificationModal
            show={show2FAModal}
            onClose={() => setShow2FAModal(false)}
            email={loginAttemptEmail}
            onVerifySuccess={handleVerificationSuccess}
            successMessage={twoFASuccessMessage}
          />

          {activeTab === 0 && (
            <form onSubmit={handleLogin} className="space-y-4 sm:space-y-6 mt-4">
              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="email" value="Email" />
                </div>
                <TextInput
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={e => setEmail(e.target.value)}
                  required
                />
              </div>
              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="password" value="Password" />
                </div>
                <TextInput
                  id="password"
                  type="password"
                  placeholder="••••••••"
                  value={password}
                  onChange={e => setPassword(e.target.value)}
                  required
                />
                <div className="flex justify-end mt-1">
                  <button
                    type="button"
                    className="text-sm text-blue-600 hover:underline bg-transparent border-none cursor-pointer"
                    onClick={() => setActiveTab(2)}
                  >
                    Forgot Password?
                  </button>
                </div>
              </div>

              <CustomButton type="submit" color="blue" className="w-full" disabled={isLoading}>
                {isLoading ? 'Logging in...' : 'Login'}
              </CustomButton>
            </form>
          )}

          {activeTab === 1 && (
            <form onSubmit={handleRegister} className="space-y-4 sm:space-y-6 mt-4">
              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="invite-code" value="Invite Code" />
                </div>
                <TextInput
                  id="invite-code"
                  type="text"
                  placeholder="Enter your invite code"
                  value={inviteCode}
                  onChange={e => setInviteCode(e.target.value)}
                  required
                />
              </div>

              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="name" value="Full Name" />
                </div>
                <TextInput
                  id="name"
                  type="text"
                  placeholder="John Doe"
                  value={name}
                  onChange={e => setName(e.target.value)}
                  required
                />
              </div>

              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="register-email" value="Email" />
                </div>
                <TextInput
                  id="register-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={registerEmail}
                  onChange={e => setRegisterEmail(e.target.value)}
                  required
                />
              </div>

              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="register-password" value="Password" />
                </div>
                <TextInput
                  id="register-password"
                  type="password"
                  placeholder="••••••••"
                  value={registerPassword}
                  onChange={e => setRegisterPassword(e.target.value)}
                  required
                />
              </div>

              <div>
                <div className="mb-1 sm:mb-2">
                  <Label htmlFor="confirm-password" value="Confirm Password" />
                </div>
                <TextInput
                  id="confirm-password"
                  type="password"
                  placeholder="••••••••"
                  value={confirmPassword}
                  onChange={e => setConfirmPassword(e.target.value)}
                  required
                />
              </div>

              <CustomButton type="submit" color="blue" className="w-full" disabled={isLoading}>
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </CustomButton>

              <p className="text-xs sm:text-sm text-gray-500 text-center mt-3 sm:mt-4">
                You need an invite code from your clinic admin to register.
              </p>
            </form>
          )}

          {activeTab === 2 && (
            <PasswordResetRequestForm
              onSuccess={() => {
                setActiveTab(0);
                setSuccess('Password reset email sent. Please check your inbox.');
              }}
            />
          )}
        </Card>
      </div>
    </>
  );
};

export default LoginPage;
