import type { NextPage } from 'next';
import { useRouter } from 'next/router';
import CustomButton from '@/components/CustomButton';
import Head from 'next/head';
import { useEffect } from 'react';
import { isAuthenticated } from '@/utils/auth';

const Home: NextPage = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard if already authenticated
    if (isAuthenticated()) {
      router.push('/dashboard');
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50">
      <Head>
        <title>Doctor Portal | Secure Client Call Management</title>
        <meta
          name="description"
          content="A secure portal for doctors to manage client calls and information"
        />
      </Head>

      <main className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-2xl p-8 bg-white rounded-lg shadow-lg text-center">
          <h1 className="text-3xl font-bold text-blue-600 mb-6">Welcome to Doctor Portal</h1>

          <p className="text-lg text-gray-700 mb-8">
            The secure way to manage your client communications and call records.
          </p>

          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <CustomButton size="lg" onClick={() => router.push('/login')}>
              Login
            </CustomButton>
            <CustomButton color="light" size="lg" onClick={() => router.push('/about')}>
              Learn More
            </CustomButton>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Secure Access</h2>
              <p className="text-gray-600">
                End-to-end encryption keeps all your client data safe and private.
              </p>
            </div>

            <div className="p-4 border border-gray-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Call Management</h2>
              <p className="text-gray-600">
                Record, transcribe, and organize all client calls in one place.
              </p>
            </div>

            <div className="p-4 border border-gray-200 rounded-lg">
              <h2 className="text-xl font-semibold mb-2">Client Portal</h2>
              <p className="text-gray-600">
                Give your clients a seamless experience with their own portal access.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Home;
