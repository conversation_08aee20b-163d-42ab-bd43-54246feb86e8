import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/router';
import { Label, TextInput, Card, Alert, Spinner } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { verifyResetCode, confirmResetPassword } from '@/utils/firebase';
import { HiCheck, HiExclamation } from 'react-icons/hi';
import Head from 'next/head';
import Image from 'next/image';

const ResetPasswordPage = () => {
  const router = useRouter();
  const { oobCode } = router.query; // Firebase sends the reset code as 'oobCode' in the URL

  const [email, setEmail] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Verify the reset code when the component mounts
  useEffect(() => {
    const verifyCode = async () => {
      if (!oobCode || typeof oobCode !== 'string') {
        return;
      }

      try {
        // Verify the code and get the associated email
        const emailFromCode = await verifyResetCode(oobCode);
        setEmail(emailFromCode);
        setError('');
      } catch (error) {
        console.error('Error verifying reset code:', error);
        setError('This password reset link is invalid or has expired. Please request a new one.');
      } finally {
        setIsLoading(false);
      }
    };

    if (oobCode) {
      verifyCode();
    } else {
      setIsLoading(false);
      setError('No reset code provided. Please use the link from your email.');
    }
  }, [oobCode]);

  // Password validation
  const validatePassword = (password: string): boolean => {
    // Password must be at least 8 characters long and contain at least one number
    return password.length >= 8 && /\d/.test(password);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!oobCode || typeof oobCode !== 'string') {
      setError('Invalid reset code. Please try again with a new reset link.');
      return;
    }

    // Validate passwords
    if (!validatePassword(newPassword)) {
      setError('New password must be at least 8 characters long and contain at least one number');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsSubmitting(true);

    try {
      await confirmResetPassword(oobCode, newPassword);
      setSuccess(
        'Password has been reset successfully. You can now log in with your new password.',
      );

      // Clear form
      setNewPassword('');
      setConfirmPassword('');

      // Redirect to login page after a delay
      setTimeout(() => {
        router.push('/login');
      }, 3000);
    } catch (error) {
      if (error instanceof Error) {
        setError(`Failed to reset password: ${error.message}`);
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
      console.error('Password reset error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Head>
        <title>Reset Password - Front Desk Portal</title>
      </Head>
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <Image src="/images/logo.svg" alt="Front Desk Portal" className="h-12" />
          </div>

          <Card>
            <h2 className="text-2xl font-bold text-center mb-4">Reset Your Password</h2>

            {isLoading ? (
              <div className="flex justify-center">
                <Spinner size="xl" />
              </div>
            ) : error ? (
              <div className="space-y-4">
                <Alert color="failure" icon={HiExclamation}>
                  {error}
                </Alert>
                <div className="text-center">
                  <CustomButton onClick={() => router.push('/login')} color="blue">
                    Back to Login
                  </CustomButton>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {success && (
                  <Alert color="success" icon={HiCheck}>
                    {success}
                  </Alert>
                )}

                <p className="text-sm text-gray-600">
                  Please create a new password for <strong>{email}</strong>
                </p>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <div className="mb-1">
                      <Label htmlFor="new-password" value="New Password" />
                    </div>
                    <TextInput
                      id="new-password"
                      type="password"
                      placeholder="••••••••"
                      value={newPassword}
                      onChange={e => setNewPassword(e.target.value)}
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Password must be at least 8 characters long and contain at least one number
                    </p>
                  </div>

                  <div>
                    <div className="mb-1">
                      <Label htmlFor="confirm-password" value="Confirm Password" />
                    </div>
                    <TextInput
                      id="confirm-password"
                      type="password"
                      placeholder="••••••••"
                      value={confirmPassword}
                      onChange={e => setConfirmPassword(e.target.value)}
                      required
                    />
                  </div>

                  <CustomButton
                    type="submit"
                    color="blue"
                    className="w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
                  </CustomButton>
                </form>
              </div>
            )}
          </Card>
        </div>
      </div>
    </>
  );
};

export default ResetPasswordPage;
