import { useState, useEffect } from 'react';
import { <PERSON>, Spinner, <PERSON><PERSON>, Accordion, <PERSON><PERSON>, Alert } from 'flowbite-react';
import {
  Hi<PERSON><PERSON>,
  HiPhone,
  HiLockClosed,
  Hi<PERSON>dentification,
  Hi<PERSON>he<PERSON>,
  HiExclamation,
} from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { fetchCurrentUser } from '@/utils/auth';
import { User } from '@/models/auth';
import PasswordChangeForm from '@/components/PasswordChangeForm';
import EmailChangeForm from '@/components/EmailChangeForm';
import CustomButton from '@/components/CustomButton';
import { getRoleDisplayName } from '@/utils/role-utils';
import { getAuth } from '@/utils/firebase';

const ProfilePage = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [emailVerified, setEmailVerified] = useState<boolean | null>(null);
  const [verifyingEmail, setVerifyingEmail] = useState(false);
  const [verifyEmailSuccess, setVerifyEmailSuccess] = useState('');
  const [verifyEmailError, setVerifyEmailError] = useState('');

  // Function to load user profile data
  const loadUserProfile = async () => {
    try {
      setLoading(true);

      // Force refresh the token to get the latest claims
      const currentUser = getAuth().currentUser;
      if (currentUser) {
        await currentUser.getIdToken(true); // Force token refresh
      }

      const userData = await fetchCurrentUser();
      setUser(userData);

      // Check if email is verified
      if (currentUser) {
        setEmailVerified(currentUser.emailVerified);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setError('Failed to load profile information. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Load user profile on component mount
  useEffect(() => {
    loadUserProfile();
  }, []);

  // Add a listener for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = getAuth().onAuthStateChanged(user => {
      if (user) {
        // Reload user data when auth state changes (e.g., after email verification)
        loadUserProfile();
      }
    });

    return () => unsubscribe(); // Clean up the listener on component unmount
  }, []);

  // Function to send verification email
  const handleSendVerificationEmail = async () => {
    setVerifyingEmail(true);
    setVerifyEmailSuccess('');
    setVerifyEmailError('');

    try {
      const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send verification email');
      }

      setVerifyEmailSuccess(
        'Verification email sent. Please check your inbox and click the verification link.',
      );

      // In a real app, you would send this email via your email service
      if (data.verificationLink) {
        console.log('Verification link:', data.verificationLink);
      }
    } catch (error) {
      console.error('Error sending verification email:', error);
      setVerifyEmailError('Failed to send verification email. Please try again.');
    } finally {
      setVerifyingEmail(false);
    }
  };

  return (
    <>
      <Head>
        <title>My Profile | Doctor Portal</title>
        <meta name="description" content="View and manage your doctor profile" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto py-8 px-4">
          {loading ? (
            <div className="flex justify-center">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-800 rounded-lg">{error}</div>
          ) : user ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Profile Card */}
              <Card className="max-w-sm mx-auto md:mx-0">
                <div className="flex flex-col items-center pb-10">
                  <Avatar
                    img={user.profilePicture}
                    size="xl"
                    rounded
                    className="mb-3"
                    alt="Profile picture"
                  />
                  <h5 className="mb-1 text-xl font-medium text-gray-900">{user.name}</h5>
                  <span className="text-sm text-gray-500">
                    {user.specialty || getRoleDisplayName(user.role)}
                  </span>
                </div>
              </Card>

              {/* Contact Information */}
              <Card className="col-span-1 md:col-span-2">
                <h5 className="text-xl font-bold mb-4">Contact Information</h5>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <HiPhone className="w-5 h-5 text-gray-500 mr-3" />
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p>{user.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <HiMail className="w-5 h-5 text-gray-500 mr-3" />
                    <div className="flex-grow">
                      <p className="text-sm text-gray-500">Email</p>
                      <div className="flex items-center">
                        <p>{user.email}</p>
                        {emailVerified !== null && (
                          <Badge color={emailVerified ? 'success' : 'warning'} className="ml-2">
                            {emailVerified ? 'Verified' : 'Not Verified'}
                          </Badge>
                        )}
                      </div>

                      {emailVerified === false && (
                        <div className="mt-2">
                          <CustomButton
                            size="xs"
                            color="blue"
                            onClick={handleSendVerificationEmail}
                            disabled={verifyingEmail}
                          >
                            {verifyingEmail ? 'Sending...' : 'Verify Email'}
                          </CustomButton>

                          {verifyEmailSuccess && (
                            <Alert color="success" className="mt-2" icon={HiCheck}>
                              {verifyEmailSuccess}
                            </Alert>
                          )}

                          {verifyEmailError && (
                            <Alert color="failure" className="mt-2" icon={HiExclamation}>
                              {verifyEmailError}
                            </Alert>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Card>

              {/* Account Settings */}
              <Card className="col-span-1 md:col-span-3 mt-4">
                <h5 className="text-xl font-bold mb-4">Account Settings</h5>
                <Accordion>
                  <Accordion.Panel>
                    <Accordion.Title>
                      <div className="flex items-center">
                        <HiLockClosed className="mr-2" />
                        Change Password
                      </div>
                    </Accordion.Title>
                    <Accordion.Content>
                      <PasswordChangeForm />
                    </Accordion.Content>
                  </Accordion.Panel>

                  <Accordion.Panel>
                    <Accordion.Title>
                      <div className="flex items-center">
                        <HiIdentification className="mr-2" />
                        Change Email Address
                      </div>
                    </Accordion.Title>
                    <Accordion.Content>
                      <EmailChangeForm currentEmail={user.email} onSuccess={loadUserProfile} />
                    </Accordion.Content>
                  </Accordion.Panel>
                </Accordion>
              </Card>
            </div>
          ) : (
            <div className="p-4 bg-gray-100 text-gray-800 rounded-lg">
              No profile data available.
            </div>
          )}
        </div>
      </DashboardLayout>
    </>
  );
};

export default ProfilePage;
