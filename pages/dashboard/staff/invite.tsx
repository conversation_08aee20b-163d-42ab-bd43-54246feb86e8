import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON><PERSON>, <PERSON>, Spinner, Alert, Table, Badge } from 'flowbite-react';
import { HiArrowLeft, HiC<PERSON>boardCopy, HiCheck, HiExclamation, HiRefresh } from 'react-icons/hi';
import { UserRole } from '@/models/auth';
import { getToken } from '@/utils/auth';

interface InviteCode {
  id: string;
  code: string;
  clinicId: number;
  used: boolean;
  expiresAt: string;
  createdAt: string;
  usedAt?: string;
}

interface CurrentUser {
  id: string;
  name: string;
  email: string;
  role?: UserRole;
  clinicId?: number | null;
}

const InviteStaffPage = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [currentUser, setCurrentUser] = useState<CurrentUser>({ id: '', name: '', email: '' });
  const [inviteCode, setInviteCode] = useState('');
  const [existingCodes, setExistingCodes] = useState<InviteCode[]>([]);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchExistingCodes = useCallback(async () => {
    if (!currentUser.clinicId) {
      console.log('No clinic ID available, cannot fetch codes');
      return;
    }

    try {
      setRefreshing(true);
      console.log('Fetching invite codes for clinic ID:', currentUser.clinicId);

      // Get auth token
      const token = getToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Call the API endpoint
      const response = await fetch(`/api/staff/invite-codes?clinicId=${currentUser.clinicId}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch invite codes: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API response:', data);

      if (data.success) {
        setExistingCodes(data.inviteCodes || []);
        console.log('Invite codes loaded:', data.inviteCodes.length);
      } else {
        throw new Error(data.message || 'Failed to fetch invite codes');
      }
    } catch (error) {
      console.error('Error fetching invite codes:', error);
      setError('Error loading invite codes. Please try again.');
    } finally {
      setRefreshing(false);
    }
  }, [currentUser.clinicId]);

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = getToken();
        const headers: HeadersInit = {};

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch('/api/staff/me', { headers });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch current user: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();
        console.log('Current user data:', data);

        setCurrentUser(data);

        if (data.clinicId) {
          fetchExistingCodes();
        } else {
          console.log('User has no clinic ID, skipping code fetch');
        }
      } catch (error) {
        console.error('Error fetching current user:', error);
        setError('Could not load your user information');
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
  }, [fetchExistingCodes]);

  const handleGoBack = () => {
    router.push('/dashboard/staff');
  };

  const handleGenerateCode = async () => {
    if (!currentUser.clinicId) {
      setError('No clinic ID found for your account');
      return;
    }

    try {
      setGenerating(true);
      setError('');

      // Get authentication token
      const token = getToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      console.log('Generating invite code for clinic:', currentUser.clinicId);

      // Call API endpoint to generate code
      const response = await fetch('/api/staff/generate-invite-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          clinicId: currentUser.clinicId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API error response:', errorData);
        throw new Error(
          `Failed to generate invite code: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();
      console.log('API success response:', data);

      if (data.success) {
        setInviteCode(data.code);

        // Refresh the list of codes
        await fetchExistingCodes();
      } else {
        throw new Error(data.message || 'Failed to generate invite code');
      }
    } catch (error) {
      console.error('Error generating invite code:', error);
      setError('Failed to generate invite code. Please try again.');
    } finally {
      setGenerating(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      },
      err => {
        console.error('Could not copy text: ', err);
      },
    );
  };

  // Format date to be more readable
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Check if a code is expired
  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  // Count number of valid (not used and not expired) codes
  const validCodesCount = existingCodes.filter(
    code => !code.used && !isExpired(code.expiresAt),
  ).length;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-full">
          <Spinner size="xl" />
        </div>
      </DashboardLayout>
    );
  }

  // Check if user is a clinic admin
  if (currentUser.role !== UserRole.CLINIC_ADMIN) {
    return (
      <DashboardLayout>
        <div className="p-4">
          <Button color="light" onClick={handleGoBack} className="mb-4">
            <HiArrowLeft className="mr-2 h-5 w-5" />
            Back to Staff List
          </Button>
          <Card>
            <div className="text-center py-10">
              <HiExclamation className="mx-auto mb-4 h-10 w-10 text-yellow-500" />
              <h5 className="text-lg font-bold text-gray-800 mb-2">Access Denied</h5>
              <p className="text-gray-700">
                Only clinic administrators can generate staff invite codes.
              </p>
              <Button color="primary" onClick={handleGoBack} className="mt-4">
                Return to Staff List
              </Button>
            </div>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4">
        <Button color="light" onClick={handleGoBack} className="mb-4">
          <HiArrowLeft className="mr-2 h-5 w-5" />
          Back to Staff List
        </Button>

        <div className="max-w-4xl mx-auto">
          <Card className="mb-6">
            <h5 className="text-xl font-bold text-gray-900 mb-4">Generate Staff Invite Code</h5>

            <p className="text-gray-700 mb-6">
              Generate a unique invite code that you can share with new staff members. This code
              will be valid for 24 hours and can only be used once.
              {validCodesCount > 0 && (
                <span className="ml-1 text-blue-600">
                  You have {validCodesCount} valid unused {validCodesCount === 1 ? 'code' : 'codes'}{' '}
                  available.
                </span>
              )}
            </p>

            {error && (
              <Alert color="failure" className="mb-4">
                <div className="font-medium">Error</div>
                {error}
              </Alert>
            )}

            {!inviteCode ? (
              <div className="flex justify-center">
                <Button color="primary" onClick={handleGenerateCode} disabled={generating}>
                  {generating ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Generating...
                    </>
                  ) : (
                    'Generate Invite Code'
                  )}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-4 bg-gray-100 rounded-lg">
                  <p className="text-sm text-gray-700 mb-1">Invite Code:</p>
                  <div className="flex items-center">
                    <code className="bg-gray-200 px-3 py-1 rounded text-lg font-mono flex-grow">
                      {inviteCode}
                    </code>
                    <Button
                      color="light"
                      size="sm"
                      onClick={() => copyToClipboard(inviteCode)}
                      className="ml-2"
                    >
                      {copied ? (
                        <HiCheck className="h-5 w-5" />
                      ) : (
                        <HiClipboardCopy className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-100">
                  <p className="text-gray-700">
                    <strong>Note:</strong> This code will expire in 24 hours and can only be used
                    once.
                  </p>
                </div>

                <div className="flex justify-end mt-4">
                  <Button onClick={handleGenerateCode}>Generate Another Code</Button>
                </div>
              </div>
            )}
          </Card>

          {/* Existing invite codes list */}
          <Card>
            <div className="flex justify-between items-center mb-4">
              <h5 className="text-xl font-bold text-gray-900">Existing Invite Codes</h5>
              <div className="flex space-x-2">
                <Button
                  color="light"
                  size="sm"
                  onClick={() => fetchExistingCodes()}
                  disabled={refreshing}
                >
                  {refreshing ? <Spinner size="sm" /> : <HiRefresh className="h-5 w-5" />}
                </Button>
              </div>
            </div>

            {existingCodes.length === 0 ? (
              <div className="text-center py-6 text-gray-500">
                No invite codes found. Generate your first code above.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table striped>
                  <Table.Head>
                    <Table.HeadCell>Code</Table.HeadCell>
                    <Table.HeadCell>Status</Table.HeadCell>
                    <Table.HeadCell>Created At</Table.HeadCell>
                    <Table.HeadCell>Expires At</Table.HeadCell>
                    <Table.HeadCell>Actions</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {existingCodes.map(code => {
                      const expired = isExpired(code.expiresAt);
                      let status = 'Active';
                      let statusColor = 'success';

                      if (code.used) {
                        status = 'Used';
                        statusColor = 'gray';
                      } else if (expired) {
                        status = 'Expired';
                        statusColor = 'failure';
                      }

                      return (
                        <Table.Row key={code.id} className="bg-white">
                          <Table.Cell className="font-medium">{code.code}</Table.Cell>
                          <Table.Cell>
                            <Badge color={statusColor as 'success' | 'gray' | 'failure'}>
                              {status}
                            </Badge>
                          </Table.Cell>
                          <Table.Cell>{formatDate(code.createdAt)}</Table.Cell>
                          <Table.Cell>{formatDate(code.expiresAt)}</Table.Cell>
                          <Table.Cell>
                            {!code.used && !expired && (
                              <Button size="xs" onClick={() => copyToClipboard(code.code)}>
                                Copy
                              </Button>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              </div>
            )}
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default InviteStaffPage;
