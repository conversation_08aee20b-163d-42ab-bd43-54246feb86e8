import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON><PERSON>, <PERSON>, Spinner, Badge, Table, Modal, Checkbox } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { HiArrowLeft, HiMail, HiPhone, HiOfficeBuilding } from 'react-icons/hi';
import { FiMapPin, FiEdit, FiPlus } from 'react-icons/fi';
import { UserRole } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import { getToken } from '@/utils/auth';
import { getRoleDisplayName } from '@/utils/role-utils';

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  phone?: string;
  clinicId: number | null;
  status?: string;
  locationIds?: string[];
  currentLocationId?: string;
}

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

const StaffDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState<StaffMember | null>(null);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [userLocations, setUserLocations] = useState<Location[]>([]);
  const [allLocations, setAllLocations] = useState<Location[]>([]);
  const [allPractices, setAllPractices] = useState<Practice[]>([]);
  const [showLocationModal, setShowLocationModal] = useState(false);
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch current user
        const currentUserResponse = await fetch('/api/staff/me', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const currentUserData = await currentUserResponse.json();
        setCurrentUser(currentUserData);

        // Fetch staff member
        const response = await fetch(`/api/staff/${id}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch staff member');
        }

        const data = await response.json();
        setStaff(data);

        // Fetch user's locations
        const locationsResponse = await fetch(`/api/users/${id}/locations`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (locationsResponse.ok) {
          const locationsData = await locationsResponse.json();
          setUserLocations(locationsData.locations || []);
        }

        // Fetch all locations for assignment
        const allLocationsResponse = await fetch('/api/locations', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (allLocationsResponse.ok) {
          const allLocationsData = await allLocationsResponse.json();
          setAllLocations(allLocationsData.locations || []);
        }

        // Fetch all practices for context
        const practicesResponse = await fetch('/api/practices', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (practicesResponse.ok) {
          const practicesData = await practicesResponse.json();
          setAllPractices(practicesData.practices || []);
        }
      } catch (error) {
        console.error('Error fetching staff member:', error);
        setError('Could not load staff member details');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const getPracticeName = (practiceId: string): string => {
    const practice = allPractices.find(p => p.id === practiceId);
    return practice?.name || 'Unknown Practice';
  };

  const handleLocationAssignment = async () => {
    if (!staff) return;

    try {
      setSubmitting(true);
      const response = await fetch(`/api/users/${staff.id}/locations`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({
          locationIds: selectedLocationIds,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Update user locations
        const updatedLocations = allLocations.filter(loc => selectedLocationIds.includes(loc.id));
        setUserLocations(updatedLocations);
        setShowLocationModal(false);
      } else {
        alert(data.message || 'Failed to update location assignments');
      }
    } catch (error) {
      console.error('Error updating location assignments:', error);
      alert('Failed to update location assignments');
    } finally {
      setSubmitting(false);
    }
  };

  const openLocationModal = () => {
    setSelectedLocationIds(userLocations.map(loc => loc.id));
    setShowLocationModal(true);
  };

  const handleLocationToggle = (locationId: string) => {
    setSelectedLocationIds(prev =>
      prev.includes(locationId) ? prev.filter(id => id !== locationId) : [...prev, locationId],
    );
  };

  const handleGoBack = () => {
    router.push('/dashboard/staff');
  };

  const canManageUsers =
    currentUser?.role === UserRole.CLINIC_ADMIN || currentUser?.role === UserRole.SUPER_ADMIN;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-full">
          <Spinner size="xl" />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !staff) {
    return (
      <DashboardLayout>
        <div className="p-4">
          <Button color="light" onClick={handleGoBack} className="mb-4">
            <HiArrowLeft className="mr-2 h-5 w-5" />
            Back to Staff List
          </Button>
          <Card>
            <div className="text-center py-10">
              <p className="text-lg text-gray-700">{error || 'Staff member not found'}</p>
              <Button color="primary" onClick={handleGoBack} className="mt-4">
                Return to Staff List
              </Button>
            </div>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4">
        <Button color="light" onClick={handleGoBack} className="mb-4">
          <HiArrowLeft className="mr-2 h-5 w-5" />
          Back to Staff List
        </Button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="lg:col-span-1">
            <Card>
              <div className="flex flex-col items-center pb-4">
                <div className="w-24 h-24 mb-3 rounded-full shadow-lg bg-gray-200 flex items-center justify-center text-gray-500">
                  <span className="text-2xl font-bold">{staff.name.charAt(0)}</span>
                </div>
                <h5 className="mb-1 text-xl font-medium text-gray-900">{staff.name}</h5>
                <div className="mt-2">
                  <Badge color={staff.role === UserRole.CLINIC_ADMIN ? 'success' : 'info'}>
                    {getRoleDisplayName(staff.role)}
                  </Badge>
                </div>
                {staff.status && (
                  <div className="mt-2">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        staff.status === 'active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {staff.status === 'active' ? 'Active' : 'Pending'}
                    </span>
                  </div>
                )}
              </div>
            </Card>
          </div>

          <div className="lg:col-span-2 space-y-4">
            {/* Contact Information */}
            <Card>
              <h5 className="text-xl font-bold tracking-tight text-gray-900 mb-4">
                Contact Information
              </h5>
              <div className="space-y-3">
                <div className="flex items-center">
                  <HiMail className="mr-2 h-5 w-5 text-gray-500" />
                  <span>{staff.email}</span>
                </div>
                {staff.phone && (
                  <div className="flex items-center">
                    <HiPhone className="mr-2 h-5 w-5 text-gray-500" />
                    <span>{staff.phone}</span>
                  </div>
                )}
                {staff.clinicId && (
                  <div className="flex items-center">
                    <HiOfficeBuilding className="mr-2 h-5 w-5 text-gray-500" />
                    <span>Clinic ID: {staff.clinicId}</span>
                  </div>
                )}
              </div>
            </Card>

            {/* Location Access */}
            <Card>
              <div className="flex justify-between items-center mb-4">
                <h5 className="text-xl font-bold tracking-tight text-gray-900">Location Access</h5>
                {canManageUsers && (
                  <CustomButton size="sm" onClick={openLocationModal}>
                    <FiEdit className="mr-2 h-4 w-4" />
                    Manage Access
                  </CustomButton>
                )}
              </div>

              {userLocations.length === 0 ? (
                <div className="text-center py-8">
                  <FiMapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">No location access assigned.</p>
                  {canManageUsers && (
                    <CustomButton onClick={openLocationModal} className="mt-4">
                      <FiPlus className="mr-2 h-4 w-4" />
                      Assign Locations
                    </CustomButton>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <Table.Head>
                      <Table.HeadCell>Location</Table.HeadCell>
                      <Table.HeadCell>Practice</Table.HeadCell>
                      <Table.HeadCell>Address</Table.HeadCell>
                    </Table.Head>
                    <Table.Body className="divide-y">
                      {userLocations.map(location => (
                        <Table.Row key={location.id} className="bg-white">
                          <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                            {location.name}
                            {location.id === staff.currentLocationId && (
                              <Badge color="success" className="ml-2">
                                Current
                              </Badge>
                            )}
                          </Table.Cell>
                          <Table.Cell>{getPracticeName(location.practiceId)}</Table.Cell>
                          <Table.Cell>{location.address}</Table.Cell>
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </Card>

            {/* Actions */}
            <div className="flex justify-end">
              <Button color="dark" onClick={() => router.push(`/dashboard/staff/${id}/edit`)}>
                Edit Staff Member
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Location Assignment Modal */}
      <Modal show={showLocationModal} onClose={() => setShowLocationModal(false)} size="lg">
        <Modal.Header>Manage Location Access</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Select the locations this user should have access to. Users can switch between their
              assigned locations.
            </p>

            <div className="max-h-96 overflow-y-auto space-y-2">
              {allLocations.map(location => (
                <div key={location.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                  <Checkbox
                    id={`location-${location.id}`}
                    checked={selectedLocationIds.includes(location.id)}
                    onChange={() => handleLocationToggle(location.id)}
                  />
                  <div className="flex-1">
                    <label
                      htmlFor={`location-${location.id}`}
                      className="block text-sm font-medium text-gray-900 cursor-pointer"
                    >
                      {location.name}
                    </label>
                    <p className="text-xs text-gray-500">{getPracticeName(location.practiceId)}</p>
                    {location.address && (
                      <p className="text-xs text-gray-400">{location.address}</p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <CustomButton onClick={handleLocationAssignment} disabled={submitting}>
            {submitting ? 'Updating...' : 'Update Access'}
          </CustomButton>
          <CustomButton color="light" onClick={() => setShowLocationModal(false)}>
            Cancel
          </CustomButton>
        </Modal.Footer>
      </Modal>
    </DashboardLayout>
  );
};

export default StaffDetail;
