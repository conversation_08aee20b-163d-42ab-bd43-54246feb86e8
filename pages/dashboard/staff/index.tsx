import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Table, Spinner, Badge } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { useRouter } from 'next/router';
import { UserRole } from '@/models/auth';
import { getRoleDisplayName } from '@/utils/role-utils';

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  status: 'active' | 'pending';
  clinicId: number | null;
}

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

const StaffManagement = () => {
  const [loading, setLoading] = useState(true);
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await fetch('/api/staff/me');
        const data = await response.json();
        setCurrentUser(data);
      } catch (error) {
        console.error('Error fetching current user:', error);
      }
    };

    const fetchStaff = async () => {
      try {
        // API will handle filtering based on user role
        const response = await fetch('/api/staff');
        const data = await response.json();

        if (data.success) {
          setStaff(data.staff || []);
        }
      } catch (error) {
        console.error('Error fetching staff:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
    fetchStaff();
  }, []);

  const isSuperAdmin = currentUser?.role === UserRole.SUPER_ADMIN;

  return (
    <DashboardLayout>
      <div className="px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <CustomButton
            onClick={() => router.push('/dashboard/staff/invite')}
            disabled={currentUser?.role !== UserRole.CLINIC_ADMIN}
            title={
              currentUser?.role !== UserRole.CLINIC_ADMIN
                ? 'Only clinic admins can invite staff members'
                : ''
            }
          >
            Invite Staff Member
          </CustomButton>
        </div>

        {loading ? (
          <div className="flex justify-center my-12">
            <Spinner size="xl" />
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Name</Table.HeadCell>
                <Table.HeadCell>Email</Table.HeadCell>
                <Table.HeadCell>Role</Table.HeadCell>
                {isSuperAdmin && <Table.HeadCell>Clinic ID</Table.HeadCell>}
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {staff.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={isSuperAdmin ? 6 : 5} className="text-center py-10">
                      No staff members found. Invite your first team member to get started.
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  staff.map(member => (
                    <Table.Row key={member.id} className="bg-white">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                        {member.name}
                      </Table.Cell>
                      <Table.Cell>{member.email}</Table.Cell>
                      <Table.Cell>
                        <Badge color={member.role === UserRole.CLINIC_ADMIN ? 'success' : 'info'}>
                          {getRoleDisplayName(member.role)}
                        </Badge>
                      </Table.Cell>
                      {isSuperAdmin && <Table.Cell>{member.clinicId}</Table.Cell>}
                      <Table.Cell>
                        <span
                          className={`px-2 py-1 rounded-full text-xs ${
                            member.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {member.status === 'active' ? 'Active' : 'Pending'}
                        </span>
                      </Table.Cell>
                      <Table.Cell>
                        <CustomButton
                          size="xs"
                          onClick={() => router.push(`/dashboard/staff/${member.id}`)}
                        >
                          View
                        </CustomButton>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default StaffManagement;
