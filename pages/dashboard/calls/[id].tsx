import { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import { useRouter } from 'next/router';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'flowbite-react';
import { HiOutlineUserCircle, HiClock, HiCalendar, HiOutlineTag } from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { getToken } from '@/utils/auth';
import Link from 'next/link';
import { Call } from '@/models/Call';
import SessionAudioPlayer from '@/components/SessionAudioPlayer';
import TranscriptWithAudio from '@/components/TranscriptWithAudio';
import CallTypeDisplay from '@/components/CallTypeDisplay';
import EmptyCallUpdater from '@/components/EmptyCallUpdater';
import StatusCard from '@/components/StatusCard';
import { CallType } from '@/models/CallTypes';
import { normalizeCallTypeSmartly } from '@/utils/call-type-utils';

// Extend Call but allow type to be a single CallType or an array of CallType values
type CallDetail = Omit<Call, 'type'> & {
  /** Primary or multiple call types */
  type?: CallType | CallType[];
  dateTime: string;
  duration: string;
  clientName: string;
  clientInfo: {
    id: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender?: string;
    email?: string;
    phoneNumber?: string;
    address?: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      postalCode: string;
      country: string;
    };
    providerInfo: {
      provider: string;
      externalId: string;
    };
    insurances?: Array<{
      companyName: string;
      memberId: string;
      groupNumber?: string;
      isPrimary?: boolean;
      patientId: string;
      providerInfo?: {
        provider: string;
        externalId: string;
      };
    }>;
  } | null;
  summary?: string;
  transcription?: string;
  transcriptionWithAudio?: string;
  voicemailSummary?: string;
};

// Patient info type for the separate endpoint
type PatientInfo = {
  clientName: string;
  clientInfo: CallDetail['clientInfo'];
};

// Determine if a call is likely empty/minimal based on content
const isEmptyCall = (call: CallDetail | null): boolean => {
  if (!call) return false;

  // Check for common indicators of an empty or minimal call record
  return (
    (call.type === CallType.OTHER || call.type === undefined) &&
    (!call.transcription || call.transcription.trim() === '' || !call.transcriptionWithAudio)
  );
};

const CallDetailPage = () => {
  const router = useRouter();
  const { id } = router.query;

  const [call, setCall] = useState<CallDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [patientLoading, setPatientLoading] = useState(false);
  const [error, setError] = useState('');
  const [priorityScore, setPriorityScore] = useState<number>(0);
  const [isUrgent, setIsUrgent] = useState(false);
  const [updateMessage, setUpdateMessage] = useState('');
  const [updateStatus, setUpdateStatus] = useState<'success' | 'error' | ''>('');
  const [isSaving, setIsSaving] = useState(false);
  const [audioError, setAudioError] = useState<string | null>(null);
  const [summarizing, setSummarizing] = useState(false);
  const [isTranscriptAvailable, setIsTranscriptAvailable] = useState(false);
  const [isSummaryAvailable, setIsSummaryAvailable] = useState(false);
  const [summarizingVoicemail, setSummarizingVoicemail] = useState(false);
  const [isVoicemailSummaryAvailable, setIsVoicemailSummaryAvailable] = useState(false);

  const canReadText = (value: string): boolean => {
    const text = value?.trim()?.toUpperCase();
    return Boolean(text) && text !== '' && text !== `DEFAULT` && text !== `NONE`;
  };

  useEffect(() => {
    if (!id) return;

    const fetchCallDetail = async () => {
      try {
        const response = await fetch(`/api/calls/${id}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch call details: ${response.status} ${response.statusText}`,
          );
        }

        const data = await response.json();
        setCall({
          ...data,
          type: normalizeCallTypeSmartly(data.callTypes, data.type),
        } as CallDetail);
        setIsTranscriptAvailable(!!data.transcriptionWithAudio);
        setIsSummaryAvailable(canReadText(data.summary));
        setIsVoicemailSummaryAvailable(canReadText(data.voicemailSummary));
        setPriorityScore(data.priorityScore || 0);
        setIsUrgent(data.urgent || false);

        // Now fetch patient info separately
        fetchPatientInfo();
      } catch (error) {
        console.error('Error fetching call details:', error);
        setError('Failed to load call details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    const fetchPatientInfo = async () => {
      if (!id) return;

      setPatientLoading(true);
      try {
        const response = await fetch(`/api/calls/patient/${id}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch patient info: ${response.status} ${response.statusText}`,
          );
        }

        const patientData: PatientInfo = await response.json();

        // Update the call state with patient info
        setCall(prevCall => {
          if (!prevCall) return null;

          return {
            ...prevCall,
            clientName: patientData.clientName,
            clientInfo: patientData.clientInfo,
          };
        });
      } catch (error) {
        console.error('Error fetching patient info:', error);
        // We don't set the main error state here as the call data is still valuable
      } finally {
        setPatientLoading(false);
      }
    };

    fetchCallDetail();
  }, [id]);

  const formatDate = (dateTimeString: string) => {
    if (!dateTimeString) return '';
    const dateTime = new Date(dateTimeString);
    return dateTime.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDateShort = (dateTimeString: string) => {
    if (!dateTimeString) return '';
    const dateTime = new Date(dateTimeString);
    const datePart = dateTime.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
    const timePart = dateTime.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
    return `${datePart} at ${timePart}`;
  };

  const getPriorityClass = (score: number) => {
    if (score >= 7) return 'priority-high';
    if (score >= 4) return 'priority-medium';
    return 'priority-low';
  };

  const handleUpdateCall = async () => {
    if (!call) return;

    setIsSaving(true);
    setUpdateMessage('');
    setUpdateStatus('');

    try {
      const response = await fetch(`/api/calls/${call.id}`, {
        method: 'PATCH',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priorityScore,
          urgent: isUrgent,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update call');
      }

      const data = await response.json();

      if (data.success) {
        setUpdateStatus('success');
        setUpdateMessage(
          data.alertSent
            ? 'Call updated successfully. Urgent alert has been sent.'
            : 'Call updated successfully.',
        );

        // Update local state to reflect changes
        setCall(prev => (prev ? { ...prev, priorityScore, urgent: isUrgent } : null));
      } else {
        throw new Error(data.message || 'Update failed');
      }
    } catch (error) {
      console.error('Error updating call:', error);
      setUpdateStatus('error');
      setUpdateMessage('Failed to update call. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSummarizeCall = async () => {
    if (!call?.id) {
      console.error(`Can't summarize call, no call ID found`);
      return;
    }

    setSummarizing(true);
    try {
      const response = await fetch('/api/calls/summarize-call', {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callId: call.id,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to summarize call');
      }

      const data = await response.json();

      // Update the call object with the new summary
      setCall(prev => (prev ? { ...prev, summary: data.summary } : null));
      setIsSummaryAvailable(canReadText(data.summary));
    } catch (error) {
      console.error('Error summarizing call:', error);
      setUpdateStatus('error');
      setUpdateMessage('Failed to summarize call. Please try again.');
    } finally {
      setSummarizing(false);
    }
  };

  const handleSummarizeVoicemail = async () => {
    if (!call?.id) {
      console.error(`Can't summarize voicemail, no call ID found`);
      return;
    }

    setSummarizingVoicemail(true);
    try {
      const response = await fetch('/api/calls/summarize-voicemail', {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          callId: call.id,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        const message = data?.message || 'Failed to summarize voicemail';
        throw new Error(message);
      }

      // Update the call object with the new voicemail summary
      setCall(prev => (prev ? { ...prev, voicemailSummary: data.voicemailSummary } : null));
      setIsVoicemailSummaryAvailable(canReadText(data.voicemailSummary));
    } catch (error) {
      console.error('Error summarizing voicemail:', error);
      setUpdateStatus('error');
      setUpdateMessage(
        (error as Error).message || 'Failed to summarize voicemail. Please try again.',
      );
    } finally {
      setSummarizingVoicemail(false);
    }
  };

  const handleUpdateSuccess = async () => {
    // Refresh the call data
    if (id) {
      setLoading(true);
      try {
        const response = await fetch(`/api/calls/${id}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch call details`);
        }

        const data = await response.json();
        setCall({
          ...data,
          type: normalizeCallTypeSmartly(data.callTypes, data.type),
        } as CallDetail);
        setIsTranscriptAvailable(!!data.transcriptionWithAudio);
        setIsSummaryAvailable(canReadText(data.summary));
        setIsVoicemailSummaryAvailable(canReadText(data.voicemailSummary));
        setPriorityScore(data.priorityScore || 0);
        setIsUrgent(data.urgent || false);
      } catch (error) {
        console.error('Error refreshing call data:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="p-4 bg-red-50 text-red-800 rounded-lg">{error}</div>
      </DashboardLayout>
    );
  }

  if (!call) {
    return (
      <DashboardLayout>
        <div className="p-4 bg-gray-50 text-gray-800 rounded-lg text-center">Call not found.</div>
      </DashboardLayout>
    );
  }

  const isEmpty = isEmptyCall(call);

  return (
    <>
      <Head>
        <title>Call Details | Doctor Portal</title>
        <meta name="description" content="View call details" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto">
          <div className="flex justify-end items-center mb-6">
            <Button onClick={() => router.back()}>Back to Calls</Button>
          </div>

          {updateStatus && (
            <Alert
              color={updateStatus === 'success' ? 'success' : 'failure'}
              className="mb-4"
              onDismiss={() => setUpdateStatus('')}
            >
              <span className="font-medium">{updateMessage}</span>
            </Alert>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            {/* Main call information */}
            <Card>
              <div className="flex justify-between items-start">
                <h2 className="text-2xl font-bold mb-4">Call Details</h2>

                {isEmpty && (
                  <EmptyCallUpdater
                    callId={call.id}
                    sessionId={call.sessionId}
                    onSuccess={handleUpdateSuccess}
                    buttonText="Complete Call Data"
                  />
                )}
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <HiCalendar className="w-5 h-5 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-500">Date & Time</div>
                    <div>{formatDate(call?.dateTime || '')}</div>
                  </div>
                </div>

                <div className="flex items-center">
                  <HiClock className="w-5 h-5 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-500">Duration</div>
                    <div>{call?.duration || ''}</div>
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500 mb-1">Call Type</div>
                  <div className="whitespace-nowrap">
                    <CallTypeDisplay type={call?.type} />
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500 mb-1">Phone Number</div>
                  <div className="whitespace-nowrap">
                    <div>{call?.phoneNumber || ''}</div>
                  </div>
                </div>

                <div>
                  <div className="text-sm text-gray-500 mb-1">Reason</div>
                  <div>{call?.reason || 'No reason provided'}</div>
                </div>

                {call?.tags && call.tags.length > 0 && (
                  <div>
                    <div className="text-sm text-gray-500 mb-1">Tags</div>
                    <div className="flex flex-wrap gap-2">
                      {call.tags.map((tag, index) => (
                        <Badge key={index} color="gray">
                          <HiOutlineTag className="w-3 h-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </Card>

            {/* Client Info Card */}
            <Card>
              <h2 className="text-xl font-bold mb-4">Patient Information</h2>

              <div className="space-y-4">
                {patientLoading ? (
                  <div className="flex justify-center items-center h-40">
                    <Spinner size="xl" />
                  </div>
                ) : call?.clientInfo ? (
                  <>
                    <div className="flex items-center mb-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <HiOutlineUserCircle className="w-6 h-6 text-blue-500" />
                      </div>
                      <div>
                        <Link
                          href={`/dashboard/client/${call.clientId}`}
                          className="text-blue-600 hover:underline font-medium"
                        >
                          {call.clientInfo.firstName} {call.clientInfo.lastName}
                        </Link>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {call.clientInfo.dateOfBirth && (
                        <div>
                          <div className="text-sm text-gray-500">Date of Birth</div>
                          <div>{call.clientInfo.dateOfBirth}</div>
                        </div>
                      )}

                      {call.clientInfo.phoneNumber && (
                        <div>
                          <div className="text-sm text-gray-500">Phone</div>
                          <div>{call.clientInfo.phoneNumber}</div>
                        </div>
                      )}

                      {call.clientInfo.email && (
                        <div>
                          <div className="text-sm text-gray-500">Email</div>
                          <div>{call.clientInfo.email}</div>
                        </div>
                      )}

                      {call.clientInfo.address && (
                        <div>
                          <div className="text-sm text-gray-500">Address</div>
                          <div className="text-sm">
                            {call.clientInfo.address.line1}
                            <br />
                            {call.clientInfo.address.line2 && (
                              <>
                                {call.clientInfo.address.line2}
                                <br />
                              </>
                            )}
                            {call.clientInfo.address.city}, {call.clientInfo.address.state}{' '}
                            {call.clientInfo.address.postalCode}
                          </div>
                        </div>
                      )}

                      {call.clientInfo.providerInfo && (
                        <div>
                          <div className="text-sm text-gray-500">Provider Info</div>
                          <div className="text-sm">
                            Provider: {call.clientInfo.providerInfo.provider}
                          </div>
                        </div>
                      )}

                      {call.lastAppointmentPractitionerName && call.lastAppointmentDate && (
                        <div>
                          <div className="text-sm text-gray-500">Last Appointment</div>
                          <div className="text-sm">
                            with {call.lastAppointmentPractitionerName} on{' '}
                            {formatDateShort(
                              typeof call.lastAppointmentDate === 'string'
                                ? call.lastAppointmentDate
                                : call.lastAppointmentDate instanceof Date
                                  ? call.lastAppointmentDate.toISOString()
                                  : String(call.lastAppointmentDate),
                            )}
                          </div>
                        </div>
                      )}

                      {call.clientInfo?.insurances && call.clientInfo?.insurances.length > 0 && (
                        <div>
                          <div className="text-sm text-gray-500 font-medium">
                            Insurance Information
                          </div>
                          {call.clientInfo?.insurances.map((insurance, index) => (
                            <div key={index} className="text-sm mt-2 p-2 bg-gray-50 rounded">
                              {insurance.isPrimary && (
                                <span className="inline-block mb-1">
                                  <Badge color="blue">Primary</Badge>
                                </span>
                              )}
                              <div>Provider: {insurance.companyName}</div>
                              <div>Member ID: {insurance.memberId}</div>
                              {insurance.groupNumber && <div>Group #: {insurance.groupNumber}</div>}
                              {index < (call.clientInfo?.insurances?.length || 0) - 1 && (
                                <hr className="my-2" />
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-gray-500">Patient information not available</div>
                )}
              </div>
            </Card>

            {/* Status Card - shows After-Hours Status for after-hours calls, Priority & Status for regular calls */}
            <StatusCard
              callId={call.id}
              callType={call.type}
              callTimestamp={call.dateTime}
              priorityScore={priorityScore}
              setPriorityScore={setPriorityScore}
              isUrgent={isUrgent}
              setIsUrgent={setIsUrgent}
              handleUpdateCall={handleUpdateCall}
              isSaving={isSaving}
              getPriorityClass={getPriorityClass}
            />
          </div>

          {/* Voice Mail Recording */}
          {!!call.hasVoiceMail && (
            <Card className="mb-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold">Voice Mail Recording</h2>
                {!!call.hasVoiceMail && !isVoicemailSummaryAvailable && (
                  <Button
                    size="sm"
                    onClick={handleSummarizeVoicemail}
                    disabled={summarizingVoicemail}
                    isProcessing={summarizingVoicemail}
                    processingSpinner={<Spinner size="sm" light={true} />}
                  >
                    Generate Summary
                  </Button>
                )}
              </div>
              {!!call.hasVoiceMail ? (
                <div>
                  {/* Use SessionAudioPlayer for GCP Storage integration */}
                  <div className="">
                    <SessionAudioPlayer
                      voicemailUrl={call.voicemailUrl || ''}
                      autoPlay={false}
                      onError={errorMsg => {
                        console.error('Audio player error:', errorMsg);
                        setAudioError(errorMsg);
                      }}
                    />
                    {audioError && (
                      <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                        Error: {audioError}
                      </div>
                    )}
                  </div>
                  {isVoicemailSummaryAvailable && (
                    <div className="p-4 bg-gray-50 rounded-lg text-gray-700">
                      <ReactMarkdown
                        components={{
                          ol: ({ children }) => <ol className="list-decimal ml-6">{children}</ol>,
                          ul: ({ children }) => (
                            <ul className="list-disc list-inside">{children}</ul>
                          ),
                          li: ({ children, ...props }) => {
                            return Array.isArray(children) && children.length > 0 ? (
                              <li className="mb-4" {...props}>
                                {children}
                              </li>
                            ) : (
                              <li {...props}>{children}</li>
                            );
                          },
                        }}
                      >
                        {call.voicemailSummary}
                      </ReactMarkdown>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-gray-500">
                  No Voice Mail Recording available for this call.
                </div>
              )}
            </Card>
          )}

          {/* Call Summary Card */}
          {isTranscriptAvailable && (
            <Card className="mb-6">
              <h2 className="text-xl font-bold mb-4">Call Summary</h2>
              {isSummaryAvailable ? (
                <div className="p-4 bg-gray-50 rounded-lg text-gray-700 whitespace-pre-line">
                  {call.summary}
                </div>
              ) : (
                <div className="p-4 bg-gray-50 rounded-lg text-gray-700">
                  <p className="mb-4">Click on button to generate summary for this call</p>
                  <Button
                    onClick={handleSummarizeCall}
                    disabled={summarizing}
                    isProcessing={summarizing}
                    processingSpinner={<Spinner size="sm" light={true} />}
                  >
                    Summarize Call
                  </Button>
                </div>
              )}
            </Card>
          )}

          {/* Call Transcript with Audio Card */}
          {call.transcriptionWithAudio && (
            <Card className="mb-6">
              <h2 className="text-xl font-bold mb-4">Call Transcript with Audio</h2>
              {call.transcriptionWithAudio ? (
                <TranscriptWithAudio transcriptionWithAudio={call.transcriptionWithAudio} />
              ) : (
                <div className="p-4 bg-gray-50 rounded-lg text-gray-700">
                  No transcript with audio available for this call.
                </div>
              )}
            </Card>
          )}

          {/* Call Transcript Card */}
          {call.transcription && !call.transcriptionWithAudio && (
            <Card className="mb-6">
              <h2 className="text-xl font-bold mb-4">Call Transcript</h2>
              <div className="p-4 bg-gray-50 rounded-lg text-gray-700 whitespace-pre-line">
                {call.transcription || 'No transcription available for this call.'}
              </div>
            </Card>
          )}

          {/* Call Notes Card */}
          <Card>
            <h2 className="text-xl font-bold mb-4">Notes</h2>
            <div className="p-4 bg-gray-50 rounded-lg text-gray-700 whitespace-pre-line">
              {call.notes || 'No notes available for this call.'}
            </div>
          </Card>
        </div>
      </DashboardLayout>
    </>
  );
};

export default CallDetailPage;
