import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, Spinner, Badge, Table, Modal, Select } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { FiArrowLeft, FiMapPin, FiPlus, FiEdit, FiTrash2 } from 'react-icons/fi';
import { UserRole } from '@/models/auth';
import { Practice } from '@/models/Practice';
import { Location } from '@/models/Location';
import { getToken } from '@/utils/auth';

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

interface PracticeDetail extends Practice {
  locationCount: number;
  userCount: number;
}

const PracticeDetail = () => {
  const router = useRouter();
  const { id } = router.query;
  const [loading, setLoading] = useState(true);
  const [practice, setPractice] = useState<PracticeDetail | null>(null);
  const [locations, setLocations] = useState<Location[]>([]);
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [showAddLocationModal, setShowAddLocationModal] = useState(false);
  const [selectedLocationId, setSelectedLocationId] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch current user
        const userResponse = await fetch('/api/staff/me', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const userData = await userResponse.json();
        setCurrentUser(userData);

        // Fetch practice details
        const practiceResponse = await fetch(`/api/practices/${id}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!practiceResponse.ok) {
          throw new Error('Failed to fetch practice details');
        }

        const practiceData = await practiceResponse.json();
        setPractice(practiceData.practice);

        // Fetch practice locations
        const locationsResponse = await fetch(`/api/practices/${id}/locations`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        let locationsData = { locations: [] };
        if (locationsResponse.ok) {
          locationsData = await locationsResponse.json();
          setLocations(locationsData.locations || []);
        }

        // Fetch all clinic locations to show available ones
        const allLocationsResponse = await fetch('/api/locations', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (allLocationsResponse.ok) {
          const allLocationsData = await allLocationsResponse.json();
          const practiceLocationIds = new Set(
            (locationsData?.locations || []).map((l: Location) => l.id),
          );
          setAvailableLocations(
            (allLocationsData.locations || []).filter(
              (l: Location) => !practiceLocationIds.has(l.id),
            ),
          );
        }
      } catch (error) {
        console.error('Error fetching practice details:', error);
        setError('Could not load practice details');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id]);

  const handleAddLocation = async () => {
    if (!selectedLocationId || !practice) return;

    try {
      setSubmitting(true);
      const response = await fetch(`/api/locations/${selectedLocationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({
          transferToPracticeId: practice.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Move location from available to practice locations
        const movedLocation = availableLocations.find(l => l.id === selectedLocationId);
        if (movedLocation) {
          setLocations(prev => [...prev, { ...movedLocation, practiceId: practice.id }]);
          setAvailableLocations(prev => prev.filter(l => l.id !== selectedLocationId));
        }
        setShowAddLocationModal(false);
        setSelectedLocationId('');
      } else {
        alert(data.message || 'Failed to add location to practice');
      }
    } catch (error) {
      console.error('Error adding location to practice:', error);
      alert('Failed to add location to practice');
    } finally {
      setSubmitting(false);
    }
  };

  const handleRemoveLocation = async () => {
    if (!practice || !confirm('Are you sure you want to remove this location from the practice?'))
      return;

    try {
      // For now, we'll just show an alert since removing locations requires careful handling
      alert(
        'Location removal functionality will be implemented in a future update. Please contact support if you need to remove a location from a practice.',
      );
    } catch (error) {
      console.error('Error removing location from practice:', error);
      alert('Failed to remove location from practice');
    }
  };

  const handleGoBack = () => {
    router.push('/dashboard/practices');
  };

  const canManagePractices =
    currentUser?.role === UserRole.CLINIC_ADMIN || currentUser?.role === UserRole.SUPER_ADMIN;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-full">
          <Spinner size="xl" />
        </div>
      </DashboardLayout>
    );
  }

  if (error || !practice) {
    return (
      <DashboardLayout>
        <div className="p-4">
          <CustomButton color="light" onClick={handleGoBack} className="mb-4">
            <FiArrowLeft className="mr-2 h-5 w-5" />
            Back to Practices
          </CustomButton>
          <Card>
            <div className="text-center py-10">
              <p className="text-lg text-gray-700">{error || 'Practice not found'}</p>
              <CustomButton onClick={handleGoBack} className="mt-4">
                Return to Practices
              </CustomButton>
            </div>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4">
        <CustomButton color="light" onClick={handleGoBack} className="mb-4">
          <FiArrowLeft className="mr-2 h-5 w-5" />
          Back to Practices
        </CustomButton>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Practice Info */}
          <div className="lg:col-span-1">
            <Card>
              <div className="text-center pb-4">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 flex items-center justify-center">
                  <FiMapPin className="h-8 w-8 text-blue-600" />
                </div>
                <h2 className="text-xl font-bold text-gray-900 mb-2">{practice.name}</h2>
                <Badge color={practice.isActive ? 'success' : 'failure'} className="mb-4">
                  {practice.isActive ? 'Active' : 'Inactive'}
                </Badge>
                {practice.description && (
                  <p className="text-gray-600 text-sm">{practice.description}</p>
                )}
              </div>

              <div className="border-t pt-4">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{locations.length}</div>
                    <div className="text-sm text-gray-500">Locations</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-green-600">{practice.userCount}</div>
                    <div className="text-sm text-gray-500">Users</div>
                  </div>
                </div>
              </div>

              {canManagePractices && (
                <div className="border-t pt-4 mt-4">
                  <CustomButton
                    onClick={() => router.push(`/dashboard/practices/${practice.id}/edit`)}
                    className="w-full"
                  >
                    <FiEdit className="mr-2 h-4 w-4" />
                    Edit Practice
                  </CustomButton>
                </div>
              )}
            </Card>
          </div>

          {/* Locations */}
          <div className="lg:col-span-2">
            <Card>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Practice Locations</h3>
                {canManagePractices && availableLocations.length > 0 && (
                  <CustomButton size="sm" onClick={() => setShowAddLocationModal(true)}>
                    <FiPlus className="mr-2 h-4 w-4" />
                    Add Location
                  </CustomButton>
                )}
              </div>

              {locations.length === 0 ? (
                <div className="text-center py-8">
                  <FiMapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p className="text-gray-500">No locations assigned to this practice yet.</p>
                  {canManagePractices && availableLocations.length > 0 && (
                    <CustomButton onClick={() => setShowAddLocationModal(true)} className="mt-4">
                      Add First Location
                    </CustomButton>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <Table.Head>
                      <Table.HeadCell>Location Name</Table.HeadCell>
                      <Table.HeadCell>Address</Table.HeadCell>
                      <Table.HeadCell>Phone</Table.HeadCell>
                      {canManagePractices && <Table.HeadCell>Actions</Table.HeadCell>}
                    </Table.Head>
                    <Table.Body className="divide-y">
                      {locations.map(location => (
                        <Table.Row key={location.id} className="bg-white">
                          <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                            {location.name}
                          </Table.Cell>
                          <Table.Cell>{location.address}</Table.Cell>
                          <Table.Cell>{location.phone || 'N/A'}</Table.Cell>
                          {canManagePractices && (
                            <Table.Cell>
                              <div className="flex space-x-2">
                                <CustomButton
                                  size="xs"
                                  onClick={() => router.push(`/dashboard/locations/${location.id}`)}
                                >
                                  View
                                </CustomButton>
                                <CustomButton
                                  size="xs"
                                  color="failure"
                                  onClick={handleRemoveLocation}
                                >
                                  <FiTrash2 className="h-3 w-3" />
                                </CustomButton>
                              </div>
                            </Table.Cell>
                          )}
                        </Table.Row>
                      ))}
                    </Table.Body>
                  </Table>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      {/* Add Location Modal */}
      <Modal show={showAddLocationModal} onClose={() => setShowAddLocationModal(false)}>
        <Modal.Header>Add Location to Practice</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label
                htmlFor="location-select"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Select Location
              </label>
              <Select
                id="location-select"
                value={selectedLocationId}
                onChange={e => setSelectedLocationId(e.target.value)}
                required
              >
                <option value="">Choose a location...</option>
                {availableLocations.map(location => (
                  <option key={location.id} value={location.id}>
                    {location.name} - {location.address}
                  </option>
                ))}
              </Select>
            </div>
            <p className="text-sm text-gray-600">
              This will transfer the selected location to this practice. The location will no longer
              be available to other practices.
            </p>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <CustomButton onClick={handleAddLocation} disabled={submitting || !selectedLocationId}>
            {submitting ? 'Adding...' : 'Add Location'}
          </CustomButton>
          <CustomButton color="light" onClick={() => setShowAddLocationModal(false)}>
            Cancel
          </CustomButton>
        </Modal.Footer>
      </Modal>
    </DashboardLayout>
  );
};

export default PracticeDetail;
