import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Table, Spinner, Badge, Modal, TextInput, Textarea } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { useRouter } from 'next/router';
import { UserRole } from '@/models/auth';
import { Practice } from '@/models/Practice';
import { getToken } from '@/utils/auth';
import { FiPlus, FiEdit, FiMapPin } from 'react-icons/fi';

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

interface PracticeWithStats extends Practice {
  locationCount: number;
  userCount: number;
}

const PracticeManagement = () => {
  const [loading, setLoading] = useState(true);
  const [practices, setPractices] = useState<PracticeWithStats[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingPractice, setEditingPractice] = useState<Practice | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });
  const [submitting, setSubmitting] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const response = await fetch('/api/staff/me', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const data = await response.json();
        setCurrentUser(data);
      } catch (error) {
        console.error('Error fetching current user:', error);
      }
    };

    const fetchPractices = async () => {
      try {
        const response = await fetch('/api/practices', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const data = await response.json();

        if (data.success) {
          setPractices(data.practices || []);
        }
      } catch (error) {
        console.error('Error fetching practices:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrentUser();
    fetchPractices();
  }, []);

  const handleCreatePractice = async () => {
    if (!formData.name.trim()) return;

    try {
      setSubmitting(true);
      const response = await fetch('/api/practices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPractices(prev => [...prev, { ...data.practice, locationCount: 0, userCount: 0 }]);
        setShowCreateModal(false);
        setFormData({ name: '', description: '' });
      } else {
        alert(data.message || 'Failed to create practice');
      }
    } catch (error) {
      console.error('Error creating practice:', error);
      alert('Failed to create practice');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditPractice = async () => {
    if (!editingPractice || !formData.name.trim()) return;

    try {
      setSubmitting(true);
      const response = await fetch(`/api/practices/${editingPractice.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPractices(prev =>
          prev.map(p =>
            p.id === editingPractice.id
              ? { ...p, name: data.practice.name, description: data.practice.description }
              : p,
          ),
        );
        setShowEditModal(false);
        setEditingPractice(null);
        setFormData({ name: '', description: '' });
      } else {
        alert(data.message || 'Failed to update practice');
      }
    } catch (error) {
      console.error('Error updating practice:', error);
      alert('Failed to update practice');
    } finally {
      setSubmitting(false);
    }
  };

  const openCreateModal = () => {
    setFormData({ name: '', description: '' });
    setShowCreateModal(true);
  };

  const openEditModal = (practice: Practice) => {
    setEditingPractice(practice);
    setFormData({
      name: practice.name,
      description: practice.description || '',
    });
    setShowEditModal(true);
  };

  const canManagePractices =
    currentUser?.role === UserRole.CLINIC_ADMIN || currentUser?.role === UserRole.SUPER_ADMIN;

  return (
    <DashboardLayout>
      <div className="px-4 py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Practice Management</h1>
            <p className="text-gray-600 mt-1">
              Manage practices and their locations within your clinic
            </p>
          </div>
          <CustomButton
            onClick={openCreateModal}
            disabled={!canManagePractices}
            title={!canManagePractices ? 'Only clinic admins can manage practices' : ''}
          >
            <FiPlus className="mr-2 h-4 w-4" />
            Create Practice
          </CustomButton>
        </div>

        {loading ? (
          <div className="flex justify-center my-12">
            <Spinner size="xl" />
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Practice Name</Table.HeadCell>
                <Table.HeadCell>Description</Table.HeadCell>
                <Table.HeadCell>Locations</Table.HeadCell>
                <Table.HeadCell>Users</Table.HeadCell>
                <Table.HeadCell>Status</Table.HeadCell>
                <Table.HeadCell>Actions</Table.HeadCell>
              </Table.Head>
              <Table.Body className="divide-y">
                {practices.length === 0 ? (
                  <Table.Row>
                    <Table.Cell colSpan={6} className="text-center py-10">
                      No practices found. Create your first practice to get started.
                    </Table.Cell>
                  </Table.Row>
                ) : (
                  practices.map(practice => (
                    <Table.Row key={practice.id} className="bg-white">
                      <Table.Cell className="whitespace-nowrap font-medium text-gray-900">
                        {practice.name}
                      </Table.Cell>
                      <Table.Cell className="max-w-xs truncate">
                        {practice.description || 'No description'}
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center">
                          <FiMapPin className="mr-1 h-4 w-4 text-gray-500" />
                          {practice.locationCount}
                        </div>
                      </Table.Cell>
                      <Table.Cell>{practice.userCount}</Table.Cell>
                      <Table.Cell>
                        <Badge color={practice.isActive ? 'success' : 'failure'}>
                          {practice.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex space-x-2">
                          <CustomButton
                            size="xs"
                            onClick={() => router.push(`/dashboard/practices/${practice.id}`)}
                          >
                            View
                          </CustomButton>
                          {canManagePractices && (
                            <CustomButton
                              size="xs"
                              color="light"
                              onClick={() => openEditModal(practice)}
                            >
                              <FiEdit className="h-3 w-3" />
                            </CustomButton>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  ))
                )}
              </Table.Body>
            </Table>
          </div>
        )}
      </div>

      {/* Create Practice Modal */}
      <Modal show={showCreateModal} onClose={() => setShowCreateModal(false)}>
        <Modal.Header>Create New Practice</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Practice Name *
              </label>
              <TextInput
                id="name"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter practice name"
                required
              />
            </div>
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter practice description (optional)"
                rows={3}
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <CustomButton
            onClick={handleCreatePractice}
            disabled={submitting || !formData.name.trim()}
          >
            {submitting ? 'Creating...' : 'Create Practice'}
          </CustomButton>
          <CustomButton color="light" onClick={() => setShowCreateModal(false)}>
            Cancel
          </CustomButton>
        </Modal.Footer>
      </Modal>

      {/* Edit Practice Modal */}
      <Modal show={showEditModal} onClose={() => setShowEditModal(false)}>
        <Modal.Header>Edit Practice</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <div>
              <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                Practice Name *
              </label>
              <TextInput
                id="edit-name"
                value={formData.name}
                onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter practice name"
                required
              />
            </div>
            <div>
              <label
                htmlFor="edit-description"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Description
              </label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter practice description (optional)"
                rows={3}
              />
            </div>
          </div>
        </Modal.Body>
        <Modal.Footer>
          <CustomButton onClick={handleEditPractice} disabled={submitting || !formData.name.trim()}>
            {submitting ? 'Updating...' : 'Update Practice'}
          </CustomButton>
          <CustomButton color="light" onClick={() => setShowEditModal(false)}>
            Cancel
          </CustomButton>
        </Modal.Footer>
      </Modal>
    </DashboardLayout>
  );
};

export default PracticeManagement;
