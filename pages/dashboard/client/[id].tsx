import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import { <PERSON>, Spinner, Badge, Table, Button } from 'flowbite-react';
import {
  HiOutlineUserCircle,
  HiPhone,
  HiMail,
  HiCalendar,
  HiClipboardList,
  HiExternalLink,
} from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { getToken, fetchCurrentUser } from '@/utils/auth';
import { Call } from '@/models/Call';
import { Client } from '@/models/Client';

// Extended client type with UI-specific fields
type ClientWithCalls = Client & {
  // Frontend mapping fields added by API
  name?: string;
  birthDate?: string;
  phone?: string;
  email?: string;
  medicalHistory?: string;
  recentNotes?: string;
  calls: (Call & {
    dateTime: string; // Formatted date
    duration: string;
  })[];
};

const ClientPage = () => {
  const router = useRouter();
  const { id } = router.query;

  const [client, setClient] = useState<ClientWithCalls | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Define fetchClientData function with useCallback so it's not recreated on every render
  const fetchClientData = useCallback(
    async (userClinicId: number | null) => {
      if (!id) return;

      try {
        let data;

        // Use API v2 if clinic ID is greater than 2
        if (userClinicId && userClinicId > 2) {
          console.log('Using API v2 for patient data, clinic ID:', userClinicId);
          // Get the API key from the server-side API
          const apiKeyResponse = await fetch('/api/config/api-key', {
            headers: {
              Authorization: `Bearer ${getToken()}`,
            },
          });

          if (!apiKeyResponse.ok) {
            throw new Error(
              `Failed to get API key: ${apiKeyResponse.status} ${apiKeyResponse.statusText}`,
            );
          }

          const { apiKey } = await apiKeyResponse.json();

          const response = await fetch(`/api/external-api/v2/patients?id=${id}`, {
            headers: {
              Authorization: `Bearer ${getToken()}`,
              'x-api-key': apiKey,
              'x-provider': 'nextech',
            },
          });

          if (!response.ok) {
            throw new Error(
              `Failed to fetch patient data from API v2: ${response.status} ${response.statusText}`,
            );
          }

          const patientData = await response.json();

          // Map API v2 patient data to client format
          data = {
            id: patientData.id,
            fullName: `${patientData.firstName} ${patientData.lastName}`,
            birthday: patientData.dateOfBirth,
            phoneNumber: patientData.phoneNumber || '',
            email: patientData.email || '',
            medicalHistory: '',
            recentNotes: '',
            insuranceCompany: '',
            insuranceGroupNumber: '',
            subscriberName: '',
            calls: [], // Initialize with empty calls array
          };

          // Fetch calls separately if needed
          // This would require additional implementation
        } else {
          console.log('Using original API for client data, clinic ID:', userClinicId);
          const response = await fetch(`/api/clients/${id}`, {
            headers: {
              Authorization: `Bearer ${getToken()}`,
            },
          });

          if (!response.ok) {
            throw new Error(
              `Failed to fetch client data: ${response.status} ${response.statusText}`,
            );
          }

          data = await response.json();
        }

        setClient(data);
      } catch (error) {
        console.error('Error fetching client data:', error);
        setError('Failed to load patient information. Please try again later.');
      } finally {
        setLoading(false);
      }
    },
    [id],
  );

  // Fetch user data and then client data
  useEffect(() => {
    if (!id) return;

    const loadData = async () => {
      try {
        // First get the user's clinic ID
        const userData = await fetchCurrentUser();
        const userClinicId = userData.clinicId;

        // Then fetch client data with the clinic ID
        await fetchClientData(userClinicId);
      } catch (error) {
        console.error('Error loading data:', error);
        setError('Failed to load patient information. Please try again later.');
        setLoading(false);
      }
    };

    loadData();
  }, [id, fetchClientData]);

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const calculateAge = (birthDateString: string) => {
    if (!birthDateString) return '';
    const birthDate = new Date(birthDateString);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const formatCallDateTime = (dateTimeString: string) => {
    if (!dateTimeString) return '';
    const dateTime = new Date(dateTimeString);
    return dateTime.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPriorityClass = (score: number) => {
    if (score >= 7) return 'priority-high';
    if (score >= 4) return 'priority-medium';
    return 'priority-low';
  };

  return (
    <>
      <Head>
        <title>Client Details | Doctor Portal</title>
        <meta name="description" content="View client information and call history" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto">
          <div className="flex justify-end items-center mb-6">
            <Button onClick={() => router.back()}>Back</Button>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-800 rounded-lg">{error}</div>
          ) : client ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Client Info Card */}
              <Card className="md:col-span-1">
                <div className="flex flex-col items-center text-center mb-4">
                  <div className="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                    <HiOutlineUserCircle className="w-12 h-12 text-blue-500" />
                  </div>
                  <h2 className="text-xl font-bold">{client.fullName}</h2>
                  <p className="text-gray-500">
                    {calculateAge(client.birthday ? client.birthday.toString() : '')} years old
                  </p>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <HiCalendar className="w-5 h-5 mr-2 text-gray-500" />
                    <div>
                      <div className="text-sm text-gray-500">Date of Birth</div>
                      <div>{formatDate(client.birthday ? client.birthday.toString() : '')}</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <HiPhone className="w-5 h-5 mr-2 text-gray-500" />
                    <div>
                      <div className="text-sm text-gray-500">Phone</div>
                      <div>{client.phoneNumber}</div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <HiMail className="w-5 h-5 mr-2 text-gray-500" />
                    <div>
                      <div className="text-sm text-gray-500">Email</div>
                      <div>{client.email || 'No email available'}</div>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Medical History Card */}
              <Card className="md:col-span-2">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-2 flex items-center">
                    <HiClipboardList className="w-5 h-5 mr-2 text-gray-500" />
                    Medical History
                  </h3>
                  <p className="text-gray-700 whitespace-pre-line">
                    {client.medicalHistory || 'No medical history recorded.'}
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2 flex items-center">
                    <HiClipboardList className="w-5 h-5 mr-2 text-gray-500" />
                    Recent Notes
                  </h3>
                  <p className="text-gray-700 whitespace-pre-line">
                    {client.recentNotes || 'No recent notes available.'}
                  </p>
                </div>
              </Card>

              {/* Insurance Card */}
              <Card className="md:col-span-3">
                <h3 className="text-lg font-semibold mb-4">Insurance Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Insurance Company</p>
                    <p>{client.insuranceCompany || 'Unknown'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Group Number</p>
                    <p>{client.insuranceGroupNumber || 'Unknown'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Subscriber Name</p>
                    <p>{client.subscriberName || client.fullName || 'Unknown'}</p>
                  </div>
                </div>
              </Card>

              {/* Call History Card */}
              <Card className="md:col-span-3">
                <h3 className="text-lg font-semibold mb-4">Call History</h3>

                {client.calls && client.calls.length > 0 ? (
                  <div className="overflow-x-auto">
                    <Table striped>
                      <Table.Head>
                        <Table.HeadCell>Date & Time</Table.HeadCell>
                        <Table.HeadCell>Reason</Table.HeadCell>
                        <Table.HeadCell>Duration</Table.HeadCell>
                        <Table.HeadCell>Priority</Table.HeadCell>
                        <Table.HeadCell>Action</Table.HeadCell>
                      </Table.Head>
                      <Table.Body>
                        {client.calls.map(call => (
                          <Table.Row key={call.id} className="bg-white">
                            <Table.Cell>
                              {formatCallDateTime(
                                call.dateTime || (call.date ? call.date.toString() : ''),
                              )}
                            </Table.Cell>
                            <Table.Cell>{call.reason || 'N/A'}</Table.Cell>
                            <Table.Cell>{call.duration}</Table.Cell>
                            <Table.Cell>
                              <div className="flex items-center">
                                <Badge className={getPriorityClass(call.priorityScore || 0)}>
                                  {call.priorityScore || 0}/10
                                </Badge>
                                {call.urgent && (
                                  <Badge color="red" className="ml-2">
                                    Urgent
                                  </Badge>
                                )}
                              </div>
                            </Table.Cell>
                            <Table.Cell>
                              <Button
                                size="xs"
                                onClick={() => router.push(`/dashboard/calls/${call.id}`)}
                              >
                                <HiExternalLink className="mr-1" />
                                View
                              </Button>
                            </Table.Cell>
                          </Table.Row>
                        ))}
                      </Table.Body>
                    </Table>
                  </div>
                ) : (
                  <div className="p-4 bg-gray-50 text-gray-800 rounded-lg text-center">
                    No call records found for this client.
                  </div>
                )}
              </Card>
            </div>
          ) : (
            <div className="p-4 bg-gray-50 text-gray-800 rounded-lg text-center">
              Client not found.
            </div>
          )}
        </div>
      </DashboardLayout>
    </>
  );
};

export default ClientPage;
