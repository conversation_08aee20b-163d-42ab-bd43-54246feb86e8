import React, { useState } from 'react';
import useS<PERSON> from 'swr';
import Head from 'next/head';
import { <PERSON>Phone, <PERSON><PERSON>lock, HiCalculator, HiCalendar, HiChevronDown } from 'react-icons/hi';
import dayjs from 'dayjs';

import DashboardLayout from '@/components/DashboardLayout';
import { DashboardSummary } from '@/lib/services/dashboard-metrics-service';
import StatCard from '@/components/analytics/StatCard';
import TimeRangeSelector, { TimeRange } from '@/components/analytics/TimeRangeSelector';
import DateRangeSelector from '@/components/analytics/DateRangeSelector';
import IssuesSplitChart from '@/components/analytics/IssuesSplitChart';
import AppointmentSplitChart from '@/components/analytics/AppointmentSplitChart';
import { getToken } from '@/utils/auth';

// Fetcher helper with auth
const fetcher = (url: string) => {
  const token = getToken();
  return fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  }).then(res => {
    if (!res.ok) {
      throw new Error('Failed to fetch data');
    }
    return res.json();
  });
};

function AnalyticsPage() {
  // Set default to last 30 days
  const defaultStartDate = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
  const defaultEndDate = dayjs().format('YYYY-MM-DD');

  const [range, setRange] = useState<TimeRange>('custom');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [customStartDate, setCustomStartDate] = useState(defaultStartDate);
  const [customEndDate, setCustomEndDate] = useState(defaultEndDate);
  const [excludeZeroDuration, setExcludeZeroDuration] = useState(true);
  const [excludeDisconnected, setExcludeDisconnected] = useState(false);

  // Build API URL based on range type
  const getApiUrl = () => {
    const params = new URLSearchParams();

    if (range === 'custom') {
      params.set('range', 'custom');
      params.set('startDate', customStartDate);
      params.set('endDate', customEndDate);
    } else {
      params.set('range', range);
    }

    // Add filter parameters
    if (excludeZeroDuration) {
      params.set('excludeZeroDuration', 'true');
    }
    if (excludeDisconnected) {
      params.set('excludeDisconnected', 'true');
    }

    return `/api/dashboard/summary?${params.toString()}`;
  };

  const { data, error, isLoading } = useSWR<DashboardSummary>(
    [getApiUrl(), excludeZeroDuration, excludeDisconnected],
    () => fetcher(getApiUrl()),
  );

  // Helper functions for formatting
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  const formatAvgTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (remainingSeconds > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${minutes}m`;
  };

  // Handle range changes
  const handleRangeChange = (newRange: TimeRange) => {
    setRange(newRange);
    if (newRange === 'custom') {
      setShowDatePicker(true);
    } else {
      setShowDatePicker(false);
    }
  };

  // Handle custom date range changes
  const handleDateRangeChange = (startDate: string, endDate: string) => {
    setCustomStartDate(startDate);
    setCustomEndDate(endDate);
    setRange('custom');
    setShowDatePicker(false);
  };

  // Format display text for current range
  const getRangeDisplayText = () => {
    if (range === 'custom') {
      // Check if this is the default "Last 30 days" range
      const today = dayjs();
      const thirtyDaysAgo = today.subtract(30, 'day');

      if (
        dayjs(customStartDate).isSame(thirtyDaysAgo, 'day') &&
        dayjs(customEndDate).isSame(today, 'day')
      ) {
        return 'Last 30 days';
      }

      // Check if this is June 2025 preset
      if (customStartDate === '2025-06-01' && customEndDate === '2025-06-30') {
        return 'June 2025';
      }

      // Check other common ranges
      const sevenDaysAgo = today.subtract(7, 'day');
      const ninetyDaysAgo = today.subtract(90, 'day');

      if (
        dayjs(customStartDate).isSame(sevenDaysAgo, 'day') &&
        dayjs(customEndDate).isSame(today, 'day')
      ) {
        return 'Last 7 days';
      }

      if (
        dayjs(customStartDate).isSame(ninetyDaysAgo, 'day') &&
        dayjs(customEndDate).isSame(today, 'day')
      ) {
        return 'Last 90 days';
      }

      // Default custom range display
      return `${dayjs(customStartDate).format('MMM D')} - ${dayjs(customEndDate).format('MMM D, YYYY')}`;
    }
    const option = timeRangeOptions.find(opt => opt.value === range);
    return option?.label || 'Today';
  };

  const timeRangeOptions = [
    { label: 'Today', value: 'today' as TimeRange },
    { label: 'Yesterday', value: 'yesterday' as TimeRange },
    { label: 'This Week', value: 'week' as TimeRange },
    { label: 'This Month', value: 'month' as TimeRange },
  ];

  const content = (
    <div className="space-y-6">
      <div className="flex flex-col gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-semibold text-gray-900">Analytics Dashboard</h1>
          <div className="flex items-center gap-4">
            <TimeRangeSelector selectedRange={range} onRangeChange={handleRangeChange} />
            <div className="relative">
              <button
                onClick={() => setShowDatePicker(!showDatePicker)}
                className="flex items-center gap-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <HiCalendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">
                  {range === 'custom' ? getRangeDisplayText() : 'Custom Range'}
                </span>
                <HiChevronDown className="h-4 w-4 text-gray-500" />
              </button>
              {showDatePicker && (
                <div className="absolute right-0 top-full mt-2 z-10">
                  <DateRangeSelector
                    startDate={customStartDate}
                    endDate={customEndDate}
                    onDateRangeChange={handleDateRangeChange}
                    onClose={() => setShowDatePicker(false)}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="excludeZeroDuration"
              checked={excludeZeroDuration}
              onChange={e => setExcludeZeroDuration(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="excludeZeroDuration" className="text-sm font-medium text-gray-700">
              Exclude calls with 0 duration
            </label>
          </div>
          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="excludeDisconnected"
              checked={excludeDisconnected}
              onChange={e => setExcludeDisconnected(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="excludeDisconnected" className="text-sm font-medium text-gray-700">
              Exclude disconnected calls
            </label>
          </div>
        </div>
      </div>

      {isLoading && (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">Error loading analytics data. Please try again.</p>
        </div>
      )}

      {data && (
        <div className="space-y-6">
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatCard
              title="Total Calls"
              value={data.totalCalls.toLocaleString()}
              icon={HiPhone}
              iconColor="text-blue-600"
              iconBgColor="bg-blue-100"
            />
            <StatCard
              title="Total Time Saved"
              value={formatTime(data.totalTimeSavedSeconds)}
              icon={HiClock}
              iconColor="text-green-600"
              iconBgColor="bg-green-100"
            />
            <StatCard
              title="Average Time Saved (per call)"
              value={formatAvgTime(data.avgTimeSavedSeconds)}
              icon={HiCalculator}
              iconColor="text-purple-600"
              iconBgColor="bg-purple-100"
            />
            <StatCard
              title="Patient Issues Handled"
              value={(
                data.appointments.reschedule +
                data.appointments.newAppointment +
                data.appointments.cancel
              ).toLocaleString()}
              icon={HiCalendar}
              iconColor="text-orange-600"
              iconBgColor="bg-orange-100"
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <IssuesSplitChart
              data={data.issues}
              overrideTotal={
                data.appointments.reschedule +
                data.appointments.newAppointment +
                data.appointments.cancel
              }
            />
            <AppointmentSplitChart data={data.appointments} />
          </div>
        </div>
      )}
    </div>
  );

  return (
    <>
      <Head>
        <title>Analytics Dashboard | FrontDesk</title>
      </Head>
      <DashboardLayout>{content}</DashboardLayout>
    </>
  );
}

export default AnalyticsPage;
