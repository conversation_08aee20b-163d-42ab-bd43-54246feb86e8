import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { <PERSON>, Button } from 'flowbite-react';
import { HiOfficeBuilding, HiUserGroup, HiLocationMarker, HiUsers, HiMail } from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { fetchCurrentUser } from '@/utils/auth';
import { User, UserRole } from '@/models/auth';
import { useLocationContext } from '@/components/LocationContext';

const AdminToolsPage = () => {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { currentLocation } = useLocationContext();

  useEffect(() => {
    const loadUser = async () => {
      try {
        const userData = await fetchCurrentUser();
        setUser(userData);

        // Check if user has admin permissions
        if (userData.role !== UserRole.SUPER_ADMIN && userData.role !== UserRole.CLINIC_ADMIN) {
          router.push('/dashboard');
          return;
        }
      } catch (error) {
        console.error('Error loading user:', error);
        router.push('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, [router]);

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!user || (user.role !== UserRole.SUPER_ADMIN && user.role !== UserRole.CLINIC_ADMIN)) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to access admin tools.</p>
        </div>
      </DashboardLayout>
    );
  }

  const adminTools = [
    {
      title: 'Practice Management',
      description: 'Create, edit, and manage practices within your clinic',
      icon: HiOfficeBuilding,
      path: '/dashboard/practices',
      color: 'bg-blue-500',
    },
    {
      title: 'Staff Management',
      description: 'Manage staff members and their permissions',
      icon: HiUserGroup,
      path: '/dashboard/staff',
      color: 'bg-green-500',
    },
    {
      title: 'Bulk User Assignment',
      description: 'Assign multiple users to multiple locations efficiently',
      icon: HiUsers,
      path: '/dashboard/admin/bulk-assign',
      color: 'bg-purple-500',
    },
    {
      title: 'Send Bulk SMS',
      description: 'Send SMS messages to multiple users and staff members',
      icon: HiMail,
      path: '/dashboard/admin/bulk-sms',
      color: 'bg-red-500',
    },
    {
      title: 'Location Management',
      description: 'View and manage all locations across practices',
      icon: HiLocationMarker,
      path: '/dashboard/practices', // Will redirect to practice management for location management
      color: 'bg-orange-500',
    },
  ];

  return (
    <>
      <Head>
        <title>Admin Tools | Doctor Portal</title>
        <meta name="description" content="Administrative tools for clinic management" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto">
          {/* Location Context Header */}
          {currentLocation && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <HiLocationMarker className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <h2 className="text-lg font-semibold text-blue-900">
                    Current Location: {currentLocation.name}
                  </h2>
                  <p className="text-sm text-blue-700">
                    Administrative tools will operate within your clinic scope
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Tools</h1>
            <p className="text-gray-600">
              Manage your clinic&apos;s practices, locations, and staff assignments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            {adminTools.map((tool, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg ${tool.color}`}>
                    <tool.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{tool.title}</h3>
                    <p className="text-gray-600 mb-4">{tool.description}</p>
                    <Button color="blue" size="sm" onClick={() => router.push(tool.path)}>
                      Open Tool
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Quick Stats */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Overview</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <div className="text-center">
                  <HiOfficeBuilding className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Practices</h3>
                  <p className="text-gray-600">Manage clinic practices</p>
                </div>
              </Card>
              <Card>
                <div className="text-center">
                  <HiLocationMarker className="h-8 w-8 text-green-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Locations</h3>
                  <p className="text-gray-600">Organize by location</p>
                </div>
              </Card>
              <Card>
                <div className="text-center">
                  <HiUserGroup className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                  <h3 className="text-lg font-semibold text-gray-900">Staff</h3>
                  <p className="text-gray-600">Manage user access</p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </>
  );
};

export default AdminToolsPage;
