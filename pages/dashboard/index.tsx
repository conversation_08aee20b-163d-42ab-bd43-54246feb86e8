import { useEffect } from 'react';
import { useRouter } from 'next/router';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';

const Dashboard = () => {
  const router = useRouter();

  useEffect(() => {
    // Redirect to calls page
    router.push('/dashboard/calls');
  }, [router]);

  return (
    <>
      <Head>
        <title>Dashboard | Doctor Portal</title>
        <meta name="description" content="Doctor Portal Dashboard" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      <DashboardLayout>
        <div className="flex items-center justify-center h-full px-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-10 w-10 md:h-12 md:w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-3 md:mt-4 text-sm md:text-base">Redirecting to calls dashboard...</p>
          </div>
        </div>
      </DashboardLayout>
    </>
  );
};

export default Dashboard;
