import Head from 'next/head';
import { useState, useEffect } from 'react';
import { Card, ToggleSwitch, Spinner } from 'flowbite-react';

import DashboardLayout from '@/components/DashboardLayout';
import CustomButton from '@/components/CustomButton';

type SettingsPayload = {
  isAppointmentNotificationsEnabled?: boolean;
  isIncomingCallNotificationsEnabled?: boolean;
  isVoiceMailNotificationsEnabled?: boolean;
  isDailyMonitoringNotificationsEnabled?: boolean;
  isHourlyMonitoringNotificationsEnabled?: boolean;
};

const SettingsPage = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [savingSettings, setSavingSettings] = useState(false);
  const [newVoicemailAlerts, setNewVoicemailAlerts] = useState(false);
  const [incomingCallAlerts, setIncomingCallAlerts] = useState(false);
  const [appointmentUpdates, setAppointmentUpdates] = useState(false);
  const [dailyMonitoringAlerts, setDailyMonitoringAlerts] = useState(false);
  const [hourlyMonitoringAlerts, setHourlyMonitoringAlerts] = useState(false);

  // Function to fetch user settings
  const fetchUserSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/staff/me/settings');

      if (!response.ok) {
        throw new Error('Failed to fetch user settings');
      }

      const data: SettingsPayload = await response.json();

      // Initialize state based on API response
      setNewVoicemailAlerts(Boolean(data.isVoiceMailNotificationsEnabled));
      setIncomingCallAlerts(Boolean(data.isIncomingCallNotificationsEnabled));
      setAppointmentUpdates(Boolean(data.isAppointmentNotificationsEnabled));
      setDailyMonitoringAlerts(Boolean(data.isDailyMonitoringNotificationsEnabled));
      setHourlyMonitoringAlerts(Boolean(data.isHourlyMonitoringNotificationsEnabled));
      setError('');
    } catch (error) {
      console.error('Error fetching user settings:', error);
      setError('Failed to load settings. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Fetch settings on component mount
  useEffect(() => {
    fetchUserSettings();
  }, []);

  const handleSave = async () => {
    try {
      if (savingSettings) {
        console.log('Saving setting is already in progress...');
        return;
      }

      setError('');
      setSavingSettings(true);

      const payload: SettingsPayload = {
        isVoiceMailNotificationsEnabled: newVoicemailAlerts,
        isIncomingCallNotificationsEnabled: incomingCallAlerts,
        isAppointmentNotificationsEnabled: appointmentUpdates,
        isDailyMonitoringNotificationsEnabled: dailyMonitoringAlerts,
        isHourlyMonitoringNotificationsEnabled: hourlyMonitoringAlerts,
      };

      const response = await fetch('/api/staff/me/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Failed to save settings');
      }

      console.log('Notification preferences saved:', {
        newVoicemailAlerts,
        incomingCallAlerts,
        appointmentUpdates,
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setError('Failed to save settings. Please try again later.');
    } finally {
      setSavingSettings(false);
    }
  };

  return (
    <>
      <Head>
        <title>Settings | Frontdesk Portal</title>
        <meta name="description" content="Manage your notification preferences" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto py-8 px-4">
          {loading ? (
            <div className="flex justify-center">
              <Spinner size="xl" />
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-800 rounded-lg">{error}</div>
          ) : (
            <Card className="max-w-3xl mx-auto">
              <h2 className="text-2xl font-bold mb-2">Notification Preferences</h2>
              <p className="text-gray-500 mb-6">
                Choose how you&apos;d like to be notified about important updates from Frontdesk.
              </p>

              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <ToggleSwitch
                    checked={newVoicemailAlerts}
                    onChange={() => setNewVoicemailAlerts(!newVoicemailAlerts)}
                  />
                  <div
                    className="cursor-pointer"
                    onClick={() => setNewVoicemailAlerts(!newVoicemailAlerts)}
                  >
                    <h3 className="font-medium">New Voicemail Alerts</h3>
                    <p className="text-gray-500 text-sm">
                      Get notified when a new voicemail is received from a patient.
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <ToggleSwitch
                    checked={incomingCallAlerts}
                    onChange={() => setIncomingCallAlerts(!incomingCallAlerts)}
                  />
                  <div
                    className="cursor-pointer"
                    onClick={() => setIncomingCallAlerts(!incomingCallAlerts)}
                  >
                    <h3 className="font-medium">Incoming Call Alerts</h3>
                    <p className="text-gray-500 text-sm">
                      Receive alerts when a patient calls your clinic through the Frontdesk system.
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <ToggleSwitch
                    checked={appointmentUpdates}
                    onChange={() => setAppointmentUpdates(!appointmentUpdates)}
                  />
                  <div
                    className="cursor-pointer"
                    onClick={() => setAppointmentUpdates(!appointmentUpdates)}
                  >
                    <h3 className="font-medium">Appointment Updates</h3>
                    <p className="text-gray-500 text-sm">
                      Be notified when an appointment is booked, changed, or cancelled.
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <ToggleSwitch
                    checked={dailyMonitoringAlerts}
                    onChange={() => setDailyMonitoringAlerts(!dailyMonitoringAlerts)}
                  />
                  <div
                    className="cursor-pointer"
                    onClick={() => setDailyMonitoringAlerts(!dailyMonitoringAlerts)}
                  >
                    <h3 className="font-medium">Daily Monitoring Notifications</h3>
                    <p className="text-gray-500 text-sm">
                      Get daily monitoring notifications for your clinic.
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <ToggleSwitch
                    checked={hourlyMonitoringAlerts}
                    onChange={() => setHourlyMonitoringAlerts(!hourlyMonitoringAlerts)}
                  />
                  <div
                    className="cursor-pointer"
                    onClick={() => setHourlyMonitoringAlerts(!hourlyMonitoringAlerts)}
                  >
                    <h3 className="font-medium">Hourly Monitoring Notifications</h3>
                    <p className="text-gray-500 text-sm">
                      Get hourly monitoring notifications for your clinic.
                    </p>
                  </div>
                </div>

                <div className="pt-4 border-t flex justify-end">
                  <CustomButton color="blue" onClick={handleSave} disabled={savingSettings}>
                    {savingSettings && <Spinner size="sm" className="mr-2" />}
                    Save
                  </CustomButton>
                </div>
              </div>
            </Card>
          )}
        </div>
      </DashboardLayout>
    </>
  );
};

export default SettingsPage;
