import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import CreatableSelect from 'react-select/creatable';
import { Card, Button, Textarea, Alert, Spinner } from 'flowbite-react';
import {
  HiMail,
  HiArrowLeft,
  HiCheck,
  HiExclamation,
  HiChevronDown,
  HiUsers,
  HiTrash,
} from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { fetchCurrentUser, getToken } from '@/utils/auth';
import { User, UserRole } from '@/models/auth';
import { useLocationContext } from '@/components/LocationContext';
import { components, OptionProps } from 'react-select';

interface ApiError {
  message: string;
  errors?: Record<string, string[]>;
}

interface PhoneNumberOption {
  label: string;
  value: string;
}

interface Recipient {
  phoneNumber: string;
  firstName: string;
  lastName: string;
}

const BulkSMSPage = () => {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { currentLocation } = useLocationContext();

  // SMS form state
  const [message, setMessage] = useState('');
  const [recipients, setRecipients] = useState<Recipient[]>([]);
  const [dryRun, setDryRun] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFetchingRecipients, setIsFetchingRecipients] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
  }>({ type: null, message: '' });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const userData = await fetchCurrentUser();
        setUser(userData);

        // Check if user has admin permissions
        if (userData.role !== UserRole.SUPER_ADMIN && userData.role !== UserRole.CLINIC_ADMIN) {
          router.push('/dashboard');
          return;
        }
      } catch (error) {
        console.error('Error loading user:', error);
        router.push('/dashboard');
      } finally {
        setLoading(false);
      }
    };

    loadUser();
  }, [router]);

  const validatePhoneNumber = (inputValue: string): boolean => {
    // Basic phone number validation (adjust regex as needed)
    return /^[\+]?[1-9][\d]{0,15}$/.test(inputValue);
  };

  const handleCreateOption = (inputValue: string) => {
    const cleanedNumber = inputValue
      .trim()
      .replace(/\u202c|\u202d/g, '')
      .replace(/[\s\-\(\)\n\r]/g, '');

    if (
      validatePhoneNumber(cleanedNumber) &&
      !recipients.some(r => r.phoneNumber === cleanedNumber)
    ) {
      const newRecipients = [
        { phoneNumber: cleanedNumber, firstName: '', lastName: '' },
        ...recipients,
      ];
      setRecipients(newRecipients);
    }
  };

  const handleRemoveOption = (inputValue: string) => {
    const newRecipients = recipients.filter(r => r.phoneNumber !== inputValue);
    setRecipients(newRecipients);
  };

  const fetchRecipients = async () => {
    setIsFetchingRecipients(true);
    try {
      const response = await fetch('/api/sms/recipients', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          locationId: currentLocation?.id,
        }),
      });

      if (!response.ok) {
        const errorData: ApiError = await response.json();
        throw new Error(errorData.message || 'Failed to fetch recipients');
      }

      const fetchedRecipients: Recipient[] = await response.json();
      // Merge with existing numbers, avoiding duplicates
      const uniqueRecipients = fetchedRecipients.filter(
        r => !recipients.some(r2 => r2.phoneNumber === r.phoneNumber),
      );

      setRecipients([...recipients, ...uniqueRecipients]);
      setSubmitStatus({
        type: 'success',
        message: `Loaded ${uniqueRecipients.length} recipient phone numbers from recent appointments`,
      });
    } catch (error) {
      console.error('Error fetching recipients:', error);
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to fetch recipients',
      });
    } finally {
      setIsFetchingRecipients(false);
    }
  };

  const validateForm = () => {
    return message.trim().length > 0 && recipients.length > 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus({ type: null, message: '' });
    const uniqueNumbers = Array.from(new Set(recipients.map(r => r.phoneNumber)));

    try {
      const response = await fetch('/api/sms/send-bulk', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toNumbers: uniqueNumbers,
          message: message.trim(),
          dryRun,
        }),
      });

      if (!response.ok) {
        const errorData: ApiError = await response.json();
        throw new Error(errorData.message || 'Failed to send SMS');
      }

      await response.json();
      setSubmitStatus({
        type: 'success',
        message: `SMS sent successfully to ${recipients.length} number(s)!`,
      });
    } catch (error) {
      console.error('Error sending SMS:', error);
      setSubmitStatus({
        type: 'error',
        message: error instanceof Error ? error.message : 'Failed to send SMS',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!user || (user.role !== UserRole.SUPER_ADMIN && user.role !== UserRole.CLINIC_ADMIN)) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">You don&apos;t have permission to access this feature.</p>
        </div>
      </DashboardLayout>
    );
  }

  const Svg = (p: JSX.IntrinsicElements['svg']) => (
    <svg width="24" height="24" viewBox="0 0 24 24" focusable="false" role="presentation" {...p} />
  );
  const DropdownIndicator = () => (
    <div className="flex items-center pr-2">
      <div className="text-blue-500" style={{ height: 24 }}>
        <Svg>
          <path
            d="M16.436 15.085l3.94 4.01a1 1 0 0 1-1.425 1.402l-3.938-4.006a7.5 7.5 0 1 1 1.423-1.406zM10.5 16a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11z"
            fill="currentColor"
            fillRule="evenodd"
          />
        </Svg>
      </div>
      {recipients.length > 0 && (
        <button
          type="button"
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
            setRecipients([]);
          }}
          className="ml-1 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors duration-150"
          title="Clear all"
        >
          <HiTrash className="h-4 w-4" />
        </button>
      )}
    </div>
  );
  const Option = (props: OptionProps<PhoneNumberOption>) => (
    <components.Option {...props}>
      <div className="flex items-center justify-between w-full">
        <span className="flex-1">{props.data.label}</span>
        {!props.label?.startsWith('Add ') && (
          <button
            type="button"
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              handleRemoveOption(props.data.value);
            }}
            className="ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors duration-150"
            title="Remove this phone number"
          >
            <HiTrash className="h-4 w-4" />
          </button>
        )}
      </div>
    </components.Option>
  );

  return (
    <>
      <Head>
        <title>Send Bulk SMS | Doctor Portal</title>
        <meta name="description" content="Send SMS messages to multiple users and staff members" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto max-w-4xl">
          {/* Header */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <Button
                color="gray"
                size="sm"
                onClick={() => router.push('/dashboard/admin-tools')}
                className="mr-4"
              >
                <HiArrowLeft className="h-4 w-4 mr-2" />
                Back to Admin Tools
              </Button>
            </div>

            <div className="flex items-center mb-2">
              <HiMail className="h-8 w-8 text-red-500 mr-3" />
              <h1 className="text-3xl font-bold text-gray-900">Send Bulk SMS</h1>
            </div>
            <p className="text-gray-600">
              Send SMS messages to multiple users and staff members across your clinic
            </p>
          </div>

          {/* Location Context */}
          {currentLocation && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center">
                <HiMail className="h-5 w-5 text-blue-600 mr-2" />
                <div>
                  <h2 className="text-lg font-semibold text-blue-900">
                    Current Location: {currentLocation.name}
                  </h2>
                  <p className="text-sm text-blue-700">
                    SMS messages will be sent from this location context
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Status Messages */}
          {submitStatus.type && (
            <div className="mb-6">
              <Alert
                color={submitStatus.type === 'success' ? 'success' : 'failure'}
                icon={submitStatus.type === 'success' ? HiCheck : HiExclamation}
              >
                {submitStatus.message}
              </Alert>
            </div>
          )}

          {/* SMS Form */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Message Composer */}
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  <HiMail className="inline h-5 w-5 mr-2" />
                  Message Composer
                </h3>
                <div className="space-y-4">
                  <div>
                    <label
                      htmlFor="message"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      SMS Message
                    </label>
                    <Textarea
                      id="message"
                      placeholder="Enter your SMS message here..."
                      value={message}
                      onChange={e => setMessage(e.target.value)}
                      rows={4}
                      className="w-full"
                    />
                    <div className="mt-2 text-sm text-gray-500">
                      Characters: {message.length} / 160 (recommended)
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Phone Numbers Management */}
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  <HiUsers className="inline h-5 w-5 mr-2" />
                  Phone Numbers Management
                </h3>

                {/* Fetch Recipients Button */}
                <div className="mb-4">
                  <Button
                    color="blue"
                    size="sm"
                    onClick={fetchRecipients}
                    disabled={isFetchingRecipients}
                    className="w-full"
                  >
                    {isFetchingRecipients ? (
                      <>
                        <Spinner aria-label="Loading recipients" size="sm" light />
                        <span className="ml-2">Loading Recipients...</span>
                      </>
                    ) : (
                      <>
                        <HiUsers className="h-4 w-4 mr-2" />
                        Load Recipients from Recent Appointments
                        {currentLocation && (
                          <span className="ml-1 text-xs">({currentLocation.name})</span>
                        )}
                      </>
                    )}
                  </Button>
                </div>

                {/* Recipients Dropdown */}
                <div className="relative">
                  <button
                    type="button"
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsDropdownOpen(!isDropdownOpen);
                    }}
                    className="w-full flex items-center justify-between px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <div className="flex items-center">
                      <HiUsers className="h-4 w-4 mr-2" />
                      <span>
                        {recipients.length === 0
                          ? 'No recipients selected'
                          : `${recipients.length} recipient${recipients.length !== 1 ? 's' : ''} selected`}
                      </span>
                    </div>
                    <HiChevronDown
                      className={`h-4 w-4 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
                    />
                  </button>

                  {/* Dropdown Content */}
                  {isDropdownOpen && (
                    <div className="absolute z-10 p-1 w-full">
                      <CreatableSelect
                        autoFocus
                        menuIsOpen
                        controlShouldRenderValue={false}
                        closeMenuOnSelect={false}
                        hideSelectedOptions={false}
                        isOptionSelected={() => false}
                        options={recipients.map(r => {
                          const firstName = r.firstName ? ` ${r.firstName}` : '';
                          const fullName = r.lastName ? `${r.lastName}${firstName} ` : '';

                          return {
                            label: `${fullName}${r.phoneNumber}`,
                            value: r.phoneNumber,
                          };
                        })}
                        onBlur={() => setIsDropdownOpen(false)}
                        onCreateOption={handleCreateOption}
                        tabSelectsValue={false}
                        placeholder="Search..."
                        noOptionsMessage={() => 'Type a phone number and press Enter to add'}
                        formatCreateLabel={inputValue => `Add "${inputValue}"`}
                        createOptionPosition="first"
                        isClearable
                        classNamePrefix="select"
                        components={{
                          DropdownIndicator,
                          Option,
                          IndicatorSeparator: null,
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* Send Button */}
          <div className="mt-8 text-center">
            {/* Dry Run Checkbox */}
            <div className="mb-4 flex items-center justify-center">
              <input
                id="dryRun"
                type="checkbox"
                checked={dryRun}
                onChange={e => setDryRun(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="dryRun" className="ml-2 text-sm font-medium text-gray-700">
                Dry Run (test mode - no SMS will be sent)
              </label>
            </div>

            <Button
              color="red"
              size="lg"
              onClick={handleSubmit}
              disabled={!validateForm() || isSubmitting}
              className="px-8 py-3"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {dryRun ? 'Testing...' : 'Sending...'}
                </>
              ) : (
                <>
                  <HiMail className="h-5 w-5 mr-2" />
                  {dryRun ? 'Test SMS to' : 'Send SMS to'} {recipients.length} Recipient
                  {recipients.length !== 1 ? 's' : ''}
                </>
              )}
            </Button>

            {!validateForm() && (
              <p className="mt-2 text-sm text-gray-500">
                {!message.trim() && !recipients.length
                  ? 'Please enter a message and add phone numbers'
                  : !message.trim()
                    ? 'Please enter a message'
                    : 'Please add phone numbers'}
              </p>
            )}
          </div>
        </div>
      </DashboardLayout>
    </>
  );
};

export default BulkSMSPage;
