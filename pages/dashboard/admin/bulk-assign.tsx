import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, Spinner, Table, Checkbox, Select, Badge } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { UserRole } from '@/models/auth';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import { getToken } from '@/utils/auth';
import { getRoleDisplayName } from '@/utils/role-utils';
import { FiUsers, FiMapPin, FiCheck } from 'react-icons/fi';

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  locationIds?: string[];
}

interface CurrentUser {
  id: string;
  name: string;
  role: UserRole;
  clinicId: number | null;
}

const BulkAssignLocations = () => {
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [practices, setPractices] = useState<Practice[]>([]);
  const [currentUser, setCurrentUser] = useState<CurrentUser | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedLocations, setSelectedLocations] = useState<string[]>([]);
  const [filterPractice, setFilterPractice] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch current user
        const userResponse = await fetch('/api/staff/me', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const userData = await userResponse.json();
        setCurrentUser(userData);

        // Fetch all users
        const usersResponse = await fetch('/api/staff', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const usersData = await usersResponse.json();
        if (usersData.success) {
          setUsers(usersData.staff || []);
        }

        // Fetch all locations
        const locationsResponse = await fetch('/api/locations', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const locationsData = await locationsResponse.json();
        if (locationsData.success) {
          setLocations(locationsData.locations || []);
        }

        // Fetch all practices
        const practicesResponse = await fetch('/api/practices', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });
        const practicesData = await practicesResponse.json();
        if (practicesData.success) {
          setPractices(practicesData.practices || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleUserToggle = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId) ? prev.filter(id => id !== userId) : [...prev, userId],
    );
  };

  const handleLocationToggle = (locationId: string) => {
    setSelectedLocations(prev =>
      prev.includes(locationId) ? prev.filter(id => id !== locationId) : [...prev, locationId],
    );
  };

  const handleSelectAllUsers = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.id));
    }
  };

  const handleSelectAllLocations = () => {
    const filteredLocations = filterPractice
      ? locations.filter(loc => loc.practiceId === filterPractice)
      : locations;

    if (selectedLocations.length === filteredLocations.length) {
      setSelectedLocations([]);
    } else {
      setSelectedLocations(filteredLocations.map(loc => loc.id));
    }
  };

  const handleBulkAssign = async () => {
    if (selectedUsers.length === 0 || selectedLocations.length === 0) {
      alert('Please select at least one user and one location');
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch('/api/admin/bulk-assign-locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({
          userIds: selectedUsers,
          locationIds: selectedLocations,
          operation: 'assign', // or 'replace' to replace existing assignments
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(
          `Successfully assigned ${selectedLocations.length} locations to ${selectedUsers.length} users`,
        );
        setSelectedUsers([]);
        setSelectedLocations([]);

        // Clear success message after 5 seconds
        setTimeout(() => setSuccessMessage(''), 5000);
      } else {
        alert(data.message || 'Failed to assign locations');
      }
    } catch (error) {
      console.error('Error assigning locations:', error);
      alert('Failed to assign locations');
    } finally {
      setSubmitting(false);
    }
  };

  const getPracticeName = (practiceId: string): string => {
    const practice = practices.find(p => p.id === practiceId);
    return practice?.name || 'Unknown Practice';
  };

  const filteredLocations = filterPractice
    ? locations.filter(loc => loc.practiceId === filterPractice)
    : locations;

  const canManageUsers =
    currentUser?.role === UserRole.CLINIC_ADMIN || currentUser?.role === UserRole.SUPER_ADMIN;

  if (!canManageUsers) {
    return (
      <DashboardLayout>
        <div className="p-4">
          <Card>
            <div className="text-center py-10">
              <p className="text-lg text-gray-700">
                Access denied. Only clinic admins can manage user assignments.
              </p>
            </div>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-4">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Bulk Location Assignment</h1>
          <p className="text-gray-600 mt-1">
            Assign multiple users to multiple locations efficiently
          </p>
        </div>

        {successMessage && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg flex items-center">
            <FiCheck className="mr-2 h-5 w-5" />
            {successMessage}
          </div>
        )}

        {loading ? (
          <div className="flex justify-center my-12">
            <Spinner size="xl" />
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Users Selection */}
            <Card>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FiUsers className="mr-2 h-5 w-5" />
                  Select Users ({selectedUsers.length} selected)
                </h3>
                <CustomButton size="sm" onClick={handleSelectAllUsers}>
                  {selectedUsers.length === users.length ? 'Deselect All' : 'Select All'}
                </CustomButton>
              </div>

              <div className="max-h-96 overflow-y-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell className="w-8"></Table.HeadCell>
                    <Table.HeadCell>Name</Table.HeadCell>
                    <Table.HeadCell>Role</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {users.map(user => (
                      <Table.Row key={user.id} className="bg-white">
                        <Table.Cell>
                          <Checkbox
                            checked={selectedUsers.includes(user.id)}
                            onChange={() => handleUserToggle(user.id)}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          <div>
                            <div className="font-medium text-gray-900">{user.name}</div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          <Badge color={user.role === UserRole.CLINIC_ADMIN ? 'success' : 'info'}>
                            {getRoleDisplayName(user.role)}
                          </Badge>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </Card>

            {/* Locations Selection */}
            <Card>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <FiMapPin className="mr-2 h-5 w-5" />
                  Select Locations ({selectedLocations.length} selected)
                </h3>
                <CustomButton size="sm" onClick={handleSelectAllLocations}>
                  {selectedLocations.length === filteredLocations.length
                    ? 'Deselect All'
                    : 'Select All'}
                </CustomButton>
              </div>

              <div className="mb-4">
                <label
                  htmlFor="practice-filter"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Filter by Practice
                </label>
                <Select
                  id="practice-filter"
                  value={filterPractice}
                  onChange={e => {
                    setFilterPractice(e.target.value);
                    setSelectedLocations([]); // Clear selections when filter changes
                  }}
                >
                  <option value="">All Practices</option>
                  {practices.map(practice => (
                    <option key={practice.id} value={practice.id}>
                      {practice.name}
                    </option>
                  ))}
                </Select>
              </div>

              <div className="max-h-96 overflow-y-auto">
                <Table>
                  <Table.Head>
                    <Table.HeadCell className="w-8"></Table.HeadCell>
                    <Table.HeadCell>Location</Table.HeadCell>
                    <Table.HeadCell>Practice</Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="divide-y">
                    {filteredLocations.map(location => (
                      <Table.Row key={location.id} className="bg-white">
                        <Table.Cell>
                          <Checkbox
                            checked={selectedLocations.includes(location.id)}
                            onChange={() => handleLocationToggle(location.id)}
                          />
                        </Table.Cell>
                        <Table.Cell>
                          <div>
                            <div className="font-medium text-gray-900">{location.name}</div>
                            {location.address && (
                              <div className="text-sm text-gray-500">{location.address}</div>
                            )}
                          </div>
                        </Table.Cell>
                        <Table.Cell>
                          <span className="text-sm text-gray-600">
                            {getPracticeName(location.practiceId)}
                          </span>
                        </Table.Cell>
                      </Table.Row>
                    ))}
                  </Table.Body>
                </Table>
              </div>
            </Card>
          </div>
        )}

        {/* Action Panel */}
        <Card className="mt-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-lg font-medium text-gray-900">Assignment Summary</h4>
              <p className="text-sm text-gray-600">
                Assign {selectedLocations.length} location(s) to {selectedUsers.length} user(s)
              </p>
            </div>
            <CustomButton
              onClick={handleBulkAssign}
              disabled={submitting || selectedUsers.length === 0 || selectedLocations.length === 0}
              className="px-6"
            >
              {submitting ? 'Assigning...' : 'Assign Locations'}
            </CustomButton>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default BulkAssignLocations;
