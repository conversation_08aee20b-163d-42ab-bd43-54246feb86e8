import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/router';
import {
  <PERSON>,
  <PERSON>ge,
  Spin<PERSON>,
  <PERSON>ton,
  ButtonGroup,
  Card,
  Modal,
  TextInput,
  Select,
} from 'flowbite-react';
import { Hi<PERSON>ale<PERSON><PERSON>, HiSearch } from 'react-icons/hi';
import DashboardLayout from '@/components/DashboardLayout';
import Head from 'next/head';
import { getToken } from '@/utils/auth';
import { Call } from '@/models/Call';
import { CallType, CallTypeInfo } from '@/models/CallTypes';
import CallTypeDisplay from '@/components/CallTypeDisplay';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { Location } from '@/models/Location';
import { useLocationContext } from '@/components/LocationContext';
import { normalizeCallTypeSmartly } from '@/utils/call-type-utils';

// Extend dayjs with UTC plugin to allow converting local times to UTC strings
dayjs.extend(utc);

// Extend Call but allow type to be a single CallType or an array of CallType values
type CallWithClient = Omit<Call, 'type'> & {
  /** Primary or multiple call types */
  type?: CallType | CallType[];
  dateTime: string; // Formatted date
  duration: string;
  clientName: string;
};

// Type for raw call data from API
type ApiCall = Omit<Partial<Call>, 'type'> & {
  /** Primary or multiple call types */
  type?: CallType | CallType[];
  date?: string | Date;
  dateTime?: string;
  duration?: string;
  clientId: string;
  clientName?: string;
  transcription?: string;
  phoneNumber?: string;
  callTypes?: CallType | CallType[];
};

// Type for pagination data
type PaginationData = {
  totalCount: number;
  limit: number;
  totalPages: number;
  lastDocId?: string;
  isLastPage: boolean;
};

// Type for location data
type LocationData = Location & {
  compactOfficeHours?: string;
};

// Tab options
type TabOption = 'all' | 'office-hour' | 'answering-service' | 'no-show';

const CallsPage = () => {
  const [calls, setCalls] = useState<CallWithClient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pageSize] = useState(10);
  const [lastDocId, setLastDocId] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [activeTab, setActiveTab] = useState<TabOption>('office-hour');
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);
  const [startTempDate, setStartTempDate] = useState<string | null>(null);
  const [endTempDate, setEndTempDate] = useState<string | null>(null);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [locationData, setLocationData] = useState<LocationData | null>(null);
  const [locationLoading, setLocationLoading] = useState(true);
  const [callDirection, setCallDirection] = useState<'inbound' | 'outbound' | 'both'>('inbound');

  // New filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCallType, setSelectedCallType] = useState<CallType | ''>('');
  const [selectedPriority, setSelectedPriority] = useState<string>('');

  const router = useRouter();

  // Location context is available if needed in the future
  useLocationContext();

  // Function to fetch location details
  const fetchLocationDetails = useCallback(async () => {
    try {
      setLocationLoading(true);
      const response = await fetch('/api/clinics/locations/details', {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch location details');
      }

      const data = await response.json();
      setLocationData(data);
    } catch (error) {
      console.error('Error fetching location details:', error);
      // We'll continue without location data
    } finally {
      setLocationLoading(false);
    }
  }, []);

  // Function to fetch calls with pagination and location filtering
  const fetchCalls = useCallback(
    async (startAfterId?: string, append: boolean = false) => {
      try {
        if (append) {
          setLoadingMore(true);
        } else {
          setLoading(true);
        }

        // Construct the URL with pagination and filter parameters
        const queryParams = new URLSearchParams();
        queryParams.set('limit', pageSize.toString());
        queryParams.set('callDirection', callDirection);
        if (startAfterId) {
          queryParams.set('startAfterId', startAfterId);
        }

        // Add date range filters if they exist
        if (startDate) {
          queryParams.set('startDate', startDate);
        }
        if (endDate) {
          queryParams.set('endDate', endDate);
        }

        // Add search filter
        if (searchTerm && searchTerm.trim() !== '') {
          queryParams.set('search', searchTerm.trim());
        }

        // Add call type filter based on active tab and user selection
        if (activeTab === 'answering-service') {
          // For answering service tab, always filter by after-hours calls
          queryParams.set('callType', CallType.AFTER_HOURS.toString());
        } else if (selectedCallType !== '') {
          // For other tabs, use the user-selected call type filter
          queryParams.set('callType', selectedCallType.toString());
        }

        // Add priority filter
        if (selectedPriority !== '') {
          queryParams.set('priority', selectedPriority);
        }

        // Add office hours filter based on active tab
        if (activeTab === 'office-hour') {
          queryParams.set('officeHoursOnly', 'true');
        }

        const apiUrl = `/api/calls?${queryParams}`;
        const response = await fetch(apiUrl, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch calls');
        }

        const data = await response.json();

        // Extract the calls array and pagination data from the response
        if (data.calls && Array.isArray(data.calls)) {
          // Process each call to ensure it has the required UI fields
          const processedCalls = data.calls.map((call: ApiCall) => {
            // Ensure dateTime field exists (fallback to date field)
            const dateTime =
              call.dateTime || (call.date ? String(call.date) : new Date().toISOString());

            // Use the actual duration if available, otherwise calculate it based on transcription length
            const duration =
              call.duration ||
              (call.transcription ? `${Math.floor(call.transcription.length / 100)} min` : 'N/A');

            return {
              ...call,
              dateTime,
              duration,
              // Ensure clientName exists
              clientName: call.phoneNumber || call.clientName || 'Patient',
              type: normalizeCallTypeSmartly(call.callTypes, call.type),
            } as CallWithClient;
          }); // Duration filtering is now handled server-side

          // Either append to existing calls or replace them
          if (append) {
            setCalls(prevCalls => [...prevCalls, ...processedCalls]);
          } else {
            setCalls(processedCalls);
          }

          // Update pagination state based on the new pagination model
          setLastDocId(data.lastDocId || null);

          // Update hasMore based on isLastPage or the presence of lastDocId
          if (data.isLastPage !== undefined) {
            setHasMore(!data.isLastPage);
          } else if (data.pagination) {
            // Fallback to old pagination format if available
            const pagination = data.pagination as PaginationData;
            setHasMore(!pagination.isLastPage && !!pagination.lastDocId);
            setLastDocId(pagination.lastDocId || null);
          } else {
            // If we don't have explicit pagination info, base hasMore on whether we got records
            setHasMore(processedCalls.length === pageSize);
          }
        } else {
          console.error('Invalid call data format:', data);
          setError('Received invalid data format from server');
        }
      } catch (error) {
        console.error('Error fetching calls:', error);
        setError('Failed to load call records. Please try again later.');
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [
      pageSize,
      startDate,
      endDate,
      searchTerm,
      selectedCallType,
      selectedPriority,
      activeTab,
      callDirection,
    ],
  );

  // Function to load more calls
  const loadMore = () => {
    if (lastDocId && hasMore && !loadingMore) {
      fetchCalls(lastDocId, true);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchLocationDetails();
    fetchCalls();
  }, [fetchLocationDetails]); // don't add fetchCalls to deps to avoid extra requests

  const getPriorityClass = (score: number) => {
    if (score >= 7) return 'priority-high';
    if (score >= 4) return 'priority-medium';
    return 'priority-low';
  };

  const formatDate = (dateTimeString: string | Date) => {
    const dateTime = typeof dateTimeString === 'string' ? new Date(dateTimeString) : dateTimeString;
    return dateTime.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Handle tab change
  const handleTabChange = (
    tab: TabOption,
    params: { callDirection: 'inbound' | 'outbound' | 'both' },
  ) => {
    setActiveTab(tab);
    // Reset pagination when changing tabs
    setLastDocId(null);
    setHasMore(true);
    setCallDirection(params.callDirection);
  };

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('');
    setSelectedCallType('');
    setSelectedPriority('');
    setStartDate(null);
    setEndDate(null);
    setStartTempDate(null);
    setEndTempDate(null);
    // Reset pagination
    setLastDocId(null);
    setHasMore(true);
    // Remove direct fetchCalls call - let useEffect handle it
  };

  // Refetch data when any filters change
  useEffect(() => {
    // Reset pagination when filters change
    setLastDocId(null);
    setHasMore(true);
    // Fetch fresh data with current filters
    fetchCalls();
  }, [
    startDate,
    endDate,
    searchTerm,
    selectedCallType,
    selectedPriority,
    activeTab,
    callDirection,
    fetchCalls,
  ]);

  // Fetch more data when we have fewer filtered results than the page size
  // This helps ensure we have enough data to display after client-side filtering
  useEffect(() => {
    if (
      !loading &&
      !loadingMore &&
      calls.length < pageSize &&
      hasMore &&
      calls.length > 0 &&
      calls.length < calls.length
    ) {
      // If we have unfiltered data but not enough filtered results, try to load more
      if (lastDocId) {
        fetchCalls(lastDocId, true);
      }
    }
  }, [calls.length, hasMore, lastDocId, loading, loadingMore, pageSize, fetchCalls]);

  // Open date picker modal
  const openDatePicker = () => {
    // Initialize temp dates with current filter values
    setStartTempDate(startDate);
    setEndTempDate(endDate);
    setIsDatePickerOpen(true);
  };

  // Close date picker modal
  const closeDatePicker = () => {
    setIsDatePickerOpen(false);
  };

  // Validate date range
  const isDateRangeValid = () => {
    if (!startTempDate || !endTempDate) {
      return true; // Allow partial date ranges
    }
    return new Date(startTempDate) <= new Date(endTempDate);
  };

  // Apply date filter
  const applyDateFilter = () => {
    // Validate date range before applying
    if (!isDateRangeValid()) {
      return; // Don't apply if invalid
    }

    // Convert chosen local datetimes to UTC ISO strings before storing
    const convertedStart = startTempDate ? dayjs(startTempDate).utc().format() : null;
    const convertedEnd = endTempDate ? dayjs(endTempDate).utc().format() : null;

    setStartDate(convertedStart);
    setEndDate(convertedEnd);

    // Reset pagination state
    setLastDocId(null);
    setHasMore(true);

    // Remove direct fetchCalls call - let useEffect handle it
    closeDatePicker();
  };

  // Clear date filter
  const clearDateFilter = () => {
    setStartTempDate(null);
    setEndTempDate(null);
    setStartDate(null);
    setEndDate(null);

    // Reset pagination state
    setLastDocId(null);
    setHasMore(true);

    // Remove direct fetchCalls call - let useEffect handle it
  };

  // Format date range for display
  const getFormattedDateRange = () => {
    if (!startDate && !endDate) {
      return locationData?.compactOfficeHours || 'All Time';
    }

    const formatDate = (dateString: string | null) => {
      if (!dateString) return '';
      return dayjs(dateString).format('MMM D, YYYY h:mm A');
    };

    if (startDate && endDate) {
      return `${formatDate(startDate)} - ${formatDate(endDate)}`;
    } else if (startDate) {
      return `From ${formatDate(startDate)}`;
    } else {
      return `Until ${formatDate(endDate)}`;
    }
  };

  // Check if all data is loaded
  const isAllDataLoading = loading || locationLoading;

  // Render content based on active tab
  const renderContent = () => {
    if (isAllDataLoading && !loadingMore) {
      return (
        <div className="flex justify-center items-center h-64">
          <Spinner size="xl" />
        </div>
      );
    }

    if (error) {
      return <div className="p-4 bg-red-50 text-red-800 rounded-lg">{error}</div>;
    }

    if (calls.length > 0) {
      return (
        <Card>
          <div className="overflow-x-auto">
            <Table striped>
              <Table.Head>
                <Table.HeadCell>Date & Time</Table.HeadCell>
                <Table.HeadCell>Patient Phone</Table.HeadCell>
                <Table.HeadCell>Type</Table.HeadCell>
                <Table.HeadCell>Duration</Table.HeadCell>
                <Table.HeadCell>Priority</Table.HeadCell>
              </Table.Head>
              <Table.Body>
                {calls.map(call => (
                  <Table.Row
                    key={call.id}
                    className="bg-white hover:bg-gray-100 cursor-pointer"
                    onClick={() => router.push(`/dashboard/calls/${call.id}`)}
                  >
                    <Table.Cell>{formatDate(call.dateTime)}</Table.Cell>
                    <Table.Cell>
                      {call.phoneNumber && call.phoneNumber !== 'Unknown' ? (
                        <span
                          className="px-2.5 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer"
                          onClick={e => {
                            e.stopPropagation();

                            if (call.clientId === 'unknown') {
                              return;
                            }

                            router.push(`/dashboard/client/${call.clientId}`);
                          }}
                        >
                          {call.phoneNumber}
                        </span>
                      ) : (
                        'N/A'
                      )}
                    </Table.Cell>
                    <Table.Cell className="whitespace-nowrap">
                      <CallTypeDisplay type={call.type} />
                    </Table.Cell>
                    <Table.Cell>{call.duration}</Table.Cell>
                    <Table.Cell>
                      <div className="flex items-center">
                        <Badge className={getPriorityClass(call.priorityScore || 0)}>
                          {call.priorityScore || 0}/10
                        </Badge>
                        {call.urgent && (
                          <Badge color="red" className="ml-2">
                            Urgent
                          </Badge>
                        )}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>

            {/* Load More Button */}
            {hasMore && calls.length > 0 && (
              <div className="flex justify-center mt-4 mb-2">
                <Button color="light" onClick={loadMore} disabled={loadingMore}>
                  {loadingMore ? (
                    <>
                      <Spinner size="sm" className="mr-2" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </div>
        </Card>
      );
    }

    return (
      <div className="p-8 bg-gray-50 text-gray-800 rounded-lg text-center">
        <div className="mb-3 text-2xl font-semibold">No call records found</div>
        {searchTerm ||
        selectedCallType !== '' ||
        selectedPriority !== '' ||
        startDate ||
        endDate ? (
          <div className="space-y-2">
            <p>No calls match your current filters:</p>
            <div className="text-sm text-gray-600">
              {searchTerm && <div>• Search: &ldquo;{searchTerm}&rdquo;</div>}
              {selectedCallType !== '' && (
                <div>• Call Type: {CallTypeInfo[selectedCallType as CallType]?.name}</div>
              )}
              {selectedPriority !== '' && (
                <div>• Priority: {selectedPriority.replace('-', ' to ')}</div>
              )}
              {(startDate || endDate) && <div>• Date Range: {getFormattedDateRange()}</div>}
              {activeTab === 'office-hour' && <div>• During office hours only</div>}
              {activeTab === 'answering-service' && <div>• After hours calls only</div>}
            </div>
            <p className="mt-3">
              Try adjusting your filters or{' '}
              <button onClick={clearAllFilters} className="text-blue-600 hover:underline">
                clear all filters
              </button>
            </p>
          </div>
        ) : activeTab === 'office-hour' && calls.length > 0 ? (
          <p>No calls found during office hours. Switch to &ldquo;All&rdquo; to see all calls.</p>
        ) : (
          <p>New calls will appear here when they are received</p>
        )}
      </div>
    );
  };

  const buildCountBadge = (tab: TabOption) => {
    if (!loading && tab === activeTab) {
      return ` (${calls.length})`;
    }
    return '';
  };

  return (
    <>
      <Head>
        <title>Call Records | Doctor Portal</title>
        <meta name="description" content="View and manage patient call records" />
      </Head>
      <DashboardLayout>
        <div className="container mx-auto">
          {/* Header with Tabs */}
          <div className="flex flex-col gap-4 mb-6">
            {/* Top row: Tab selector */}
            <div className="flex justify-start">
              <ButtonGroup className="w-full sm:w-auto">
                <Button
                  color={activeTab === 'all' ? 'blue' : 'gray'}
                  disabled={isAllDataLoading && activeTab !== 'answering-service'}
                  onClick={() => handleTabChange('all', { callDirection: 'both' })}
                  className="flex-1 sm:flex-none"
                >
                  <span className="hidden sm:inline">All{buildCountBadge('all')}</span>
                  <span className="sm:hidden">All{buildCountBadge('all')}</span>
                </Button>
                <Button
                  color={activeTab === 'office-hour' ? 'blue' : 'gray'}
                  disabled={isAllDataLoading && activeTab !== 'answering-service'}
                  onClick={() => handleTabChange('office-hour', { callDirection: 'inbound' })}
                  className="flex-1 sm:flex-none"
                >
                  <span className="hidden sm:inline">
                    Office Hour{buildCountBadge('office-hour')}
                  </span>
                  <span className="sm:hidden">Office{buildCountBadge('office-hour')}</span>
                </Button>
                <Button
                  color={activeTab === 'answering-service' ? 'blue' : 'gray'}
                  disabled={isAllDataLoading && activeTab !== 'answering-service'}
                  onClick={() => handleTabChange('answering-service', { callDirection: 'inbound' })}
                  className="flex-1 sm:flex-none"
                >
                  <span className="hidden sm:inline">Answering Service</span>
                  <span className="sm:hidden">Service</span>
                </Button>
                <Button
                  color={activeTab === 'no-show' ? 'blue' : 'gray'}
                  disabled={isAllDataLoading && activeTab !== 'no-show'}
                  onClick={() => handleTabChange('no-show', { callDirection: 'outbound' })}
                >
                  <span className="hidden sm:inline">
                    No-Show Outreach{buildCountBadge('no-show')}
                  </span>
                  <span className="sm:hidden">No-Show Outreach{buildCountBadge('no-show')}</span>
                </Button>
              </ButtonGroup>
            </div>

            {/* Bottom row: Filters */}
            <div className="flex flex-col lg:flex-row gap-3">
              {/* Search Input */}
              <div className="w-full lg:w-64">
                <TextInput
                  icon={HiSearch}
                  placeholder="Search by phone number"
                  value={searchTerm}
                  onChange={e => setSearchTerm(e.target.value)}
                  className="h-10"
                />
              </div>

              {/* Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-3 flex-1">
                {/* Call Type Filter */}
                <div className="w-full sm:w-48">
                  <Select
                    value={selectedCallType}
                    onChange={e => setSelectedCallType(e.target.value as CallType | '')}
                    className="h-10"
                  >
                    <option value="">All Call Types</option>
                    {Object.entries(CallTypeInfo).map(([value, info]) => (
                      <option key={value} value={value}>
                        {info.name}
                      </option>
                    ))}
                  </Select>
                </div>

                {/* Priority Filter */}
                <div className="w-full sm:w-40">
                  <Select
                    value={selectedPriority}
                    onChange={e => setSelectedPriority(e.target.value)}
                    className="h-10"
                  >
                    <option value="">All Priorities</option>
                    <option value="7-10">High Priority (7-10)</option>
                    <option value="4-6">Medium Priority (4-6)</option>
                    <option value="0-3">Low Priority (0-3)</option>
                  </Select>
                </div>

                {/* Date Range Picker */}
                <div className="w-full sm:w-auto sm:min-w-[200px] sm:max-w-[280px]">
                  <div
                    className={`flex items-center border h-10 ${
                      startDate || endDate ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
                    } rounded-lg px-3 cursor-pointer hover:bg-gray-50 w-full`}
                    onClick={openDatePicker}
                  >
                    <HiCalendar
                      className={`mr-2 flex-shrink-0 ${
                        startDate || endDate ? 'text-blue-600' : 'text-gray-600'
                      }`}
                    />
                    <div
                      className={`text-sm truncate ${
                        startDate || endDate ? 'text-blue-800' : 'text-gray-800'
                      }`}
                    >
                      {startDate || endDate ? (
                        <span className="truncate">{getFormattedDateRange()}</span>
                      ) : (
                        <span>Select date range</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Clear Filters Button */}
                <div className="w-full sm:w-auto">
                  <Button
                    color="gray"
                    onClick={clearAllFilters}
                    className="w-full h-10 flex items-center justify-center whitespace-nowrap"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Date Range Picker Modal */}
          <Modal show={isDatePickerOpen} onClose={closeDatePicker} size="md" dismissible={true}>
            <Modal.Header>Select Date Range</Modal.Header>
            <Modal.Body>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date and Time
                  </label>
                  <input
                    type="datetime-local"
                    className={`w-full p-2 border rounded-md ${
                      !isDateRangeValid() ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    value={startTempDate ?? ''}
                    onChange={e => setStartTempDate(e.target.value ? e.target.value : null)}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date and Time
                  </label>
                  <input
                    type="datetime-local"
                    className={`w-full p-2 border rounded-md ${
                      !isDateRangeValid() ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    value={endTempDate ?? ''}
                    onChange={e => setEndTempDate(e.target.value ? e.target.value : null)}
                  />
                </div>

                {/* Validation Error Message */}
                {!isDateRangeValid() && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-800">⚠️ End date must be after start date</p>
                  </div>
                )}
              </div>
            </Modal.Body>
            <Modal.Footer>
              <div className="flex justify-between w-full">
                <Button color="gray" onClick={clearDateFilter}>
                  Clear
                </Button>
                <div className="space-x-2">
                  <Button color="blue" onClick={applyDateFilter} disabled={!isDateRangeValid()}>
                    Apply
                  </Button>
                </div>
              </div>
            </Modal.Footer>
          </Modal>

          {/* Content based on selected tab */}
          {renderContent()}
        </div>
      </DashboardLayout>
    </>
  );
};

export default CallsPage;
