import { useState, useEffect } from 'react';
import { Card, Alert, TextInput, Label } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import Head from 'next/head';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { HiCheck, HiExclamation } from 'react-icons/hi';

/**
 * This is a development-only page to help test email verification links
 * In a production app, these links would be sent via email
 */
const DevEmailHandler = () => {
  const [link, setLink] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const router = useRouter();

  // Extract mode and oobCode from URL if present
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const mode = urlParams.get('mode');
      const oobCode = urlParams.get('oobCode');
      const continueUrl = urlParams.get('continueUrl');

      if (mode && oobCode) {
        setSuccess(`Detected email action: ${mode} with code ${oobCode}`);
        if (continueUrl) {
          setSuccess(prev => `${prev}\nContinue URL: ${continueUrl}`);
        }
      }
    }
  }, []);

  const handleOpenLink = () => {
    if (!link) {
      setError('Please enter a verification link');
      return;
    }

    try {
      // Open the link in a new tab
      window.open(link, '_blank');
      setSuccess('Link opened in a new tab');
    } catch (error) {
      setError('Failed to open link');
      console.error('Error opening link:', error);
    }
  };

  return (
    <>
      <Head>
        <title>Development Email Handler | Front Desk Portal</title>
      </Head>
      <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 px-4">
        <div className="w-full max-w-md">
          <div className="flex justify-center mb-6">
            <Image
              src="/images/logo.svg"
              alt="Front Desk Portal"
              width={48}
              height={48}
              className="h-12"
            />
          </div>

          <Card>
            <h2 className="text-2xl font-bold text-center mb-4">Development Email Handler</h2>
            <p className="text-sm text-gray-600 mb-4">
              This page is for development purposes only. It helps test email verification links
              that would normally be sent via email.
            </p>

            {error && (
              <Alert color="failure" icon={HiExclamation} className="mb-4">
                {error}
              </Alert>
            )}

            {success && (
              <Alert color="success" icon={HiCheck} className="mb-4">
                <pre className="whitespace-pre-wrap">{success}</pre>
              </Alert>
            )}

            <div className="space-y-4">
              <div>
                <div className="mb-1">
                  <Label htmlFor="verification-link" value="Verification Link" />
                </div>
                <TextInput
                  id="verification-link"
                  type="text"
                  placeholder="Paste verification link here"
                  value={link}
                  onChange={e => setLink(e.target.value)}
                />
              </div>

              <div className="flex space-x-2">
                <CustomButton color="blue" onClick={handleOpenLink} className="flex-1">
                  Open Link
                </CustomButton>

                <CustomButton
                  color="light"
                  onClick={() => router.push('/dashboard')}
                  className="flex-1"
                >
                  Back to Dashboard
                </CustomButton>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </>
  );
};

export default DevEmailHandler;
