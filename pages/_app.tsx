import '@/styles/globals.css';
import { AppProps } from 'next/app';
import { useEffect } from 'react';
import { setupTokenRefresh } from '@/utils/auth';
import { LocationProvider } from '@/components/LocationContext';

export default function App({ Component, pageProps }: AppProps) {
  // Set up Firebase token refresh mechanism when app first loads
  useEffect(() => {
    // Initialize token refresh mechanism
    setupTokenRefresh();
    console.log('Token refresh mechanism initialized');
  }, []);

  return (
    <LocationProvider>
      <Component {...pageProps} />
    </LocationProvider>
  );
}
