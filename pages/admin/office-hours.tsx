import React from 'react';
import Head from 'next/head';

import DashboardLayout from '@/components/DashboardLayout';
import OfficeHoursCalendar from '@/components/admin/OfficeHoursCalendar';
import { useLocationContext } from '@/components/LocationContext';

export default function OfficeHoursPage() {
  // Pull locations from global context so we always have the latest list
  const { currentLocation, loading } = useLocationContext();

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-8 text-center text-gray-600">Loading locations...</div>
      </DashboardLayout>
    );
  }

  const handleLocationUpdate = async (locationId: string, updates: Record<string, unknown>) => {
    try {
      const response = await fetch(`/api/locations/${locationId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update location');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating location:', error);
      throw error;
    } finally {
      // Loading state removed for simplicity
    }
  };

  return (
    <>
      <Head>
        <title>Office Hours & On-Call Management - Admin Portal</title>
        <meta
          name="description"
          content="Manage location office hours and on-call doctor schedules"
        />
      </Head>

      <DashboardLayout>
        <div className="min-h-screen bg-gray-50">
          <OfficeHoursCalendar currentLocation={currentLocation} onUpdate={handleLocationUpdate} />
        </div>
      </DashboardLayout>
    </>
  );
}
