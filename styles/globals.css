@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: Mulish, system-ui, sans-serif;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 250, 250, 250;
  --background-end-rgb: 255, 255, 255;
  --font-mulish: 'Mulish', sans-serif;
}

html {
  font-family: var(--font-mulish);
}

body {
  font-family: var(--font-mulish);
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

.priority-high {
  @apply bg-red-100 text-red-800;
}

.priority-medium {
  @apply bg-yellow-100 text-yellow-800;
}

.priority-low {
  @apply bg-green-100 text-green-800;
}

.urgent-badge {
  @apply bg-red-500 text-white text-xs font-semibold px-2.5 py-0.5 rounded;
}

/* Custom button styles to override Flowbite defaults */
.flowbite-button {
  @apply bg-frontdesk-button hover:bg-frontdesk-button/90 text-white focus:ring-4 focus:ring-frontdesk-button/50;
}

/* Override default Flowbite button styles */
[data-testid="flowbite-button"] {
  @apply bg-frontdesk-button hover:bg-frontdesk-button/90 text-white focus:ring-4 focus:ring-frontdesk-button/50;
}

/* Keep light button variant */
[data-testid="flowbite-button"][color="light"] {
  @apply bg-white border border-gray-200 hover:bg-gray-100 text-gray-900 focus:ring-4 focus:ring-gray-200;
}

/* Keep other color variants */
[data-testid="flowbite-button"][color="green"] {
  @apply bg-green-500 hover:bg-green-600 text-white focus:ring-4 focus:ring-green-300;
}

[data-testid="flowbite-button"][color="red"] {
  @apply bg-red-500 hover:bg-red-600 text-white focus:ring-4 focus:ring-red-300;
}

[data-testid="flowbite-button"][color="yellow"] {
  @apply bg-yellow-400 hover:bg-yellow-500 text-white focus:ring-4 focus:ring-yellow-300;
}

[data-testid="flowbite-button"][color="purple"] {
  @apply bg-purple-500 hover:bg-purple-600 text-white focus:ring-4 focus:ring-purple-300;
}