# A workflow to validate changes before merging into main (excluding draft PRs)
name: Validate Pull Request

on:
  pull_request:
    branches:
      - main
      - develop
    types: [opened, synchronize, reopened, ready_for_review]

jobs:
  validate:
    name: Validate Changes
    # Skip validation for draft PRs
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
          run_install: false

      - name: Read .nvmrc
        run: echo "NVMRC=$(cat .nvmrc)" >> $GITHUB_OUTPUT
        id: nvm

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '${{ steps.nvm.outputs.NVMRC }}'

      - name: Restore cached dependencies
        id: cache-node-modules-restore
        uses: actions/cache/restore@v4
        with:
          key: ${{ runner.os }}-build-cache-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
          path: |
            **/node_modules
          restore-keys: |
            ${{ runner.os }}-build-cache-node-modules-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install dependencies
        run: pnpm install

      - name: Run type checking
        run: pnpm type

      - name: Run linting
        run: pnpm lint:ci

      - name: Run tests
        run: pnpm test:ci
