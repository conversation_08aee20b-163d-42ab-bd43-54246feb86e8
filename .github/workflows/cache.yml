# A workflow to cache dependencies on the default branch level.
name: <PERSON><PERSON> Dependencies

on:
  push:
    branches:
      - main
      - develop
    paths:
      - '**/package.json'

jobs:
  cache:
    name: Cache Dependencies
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
          run_install: false

      - name: Read .nvmrc
        run: echo "NVMRC=$(cat .nvmrc)" >> $GITHUB_OUTPUT
        id: nvm

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '${{ steps.nvm.outputs.NVMRC }}'

      - name: Restore cached dependencies
        id: cache-node-modules-restore
        uses: actions/cache/restore@v4
        with:
          key: ${{ runner.os }}-build-cache-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
          path: |
            **/node_modules
          restore-keys: |
            ${{ runner.os }}-build-cache-node-modules-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install dependencies
        if: steps.cache-node-modules-restore.outputs.cache-hit != 'true'
        run: pnpm install

      - name: Cache dependencies
        id: cache-node-modules-save
        if: always() && steps.cache-node-modules-restore.outputs.cache-hit != 'true'
        uses: actions/cache/save@v4
        with:
          key: ${{ steps.cache-node-modules-restore.outputs.cache-primary-key }}
          path: |
            **/node_modules
