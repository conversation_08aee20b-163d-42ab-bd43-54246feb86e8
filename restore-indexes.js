const fs = require('fs');

console.log('🔄 RESTORING COMPLETE INDEXES FILE');
console.log('==================================');

const backupFile = 'firestore.indexes.backup-2025-05-30T07-57-05.json';
if (fs.existsSync(backupFile)) {
  fs.copyFileSync(backupFile, 'firestore.indexes.json');
  console.log('✅ Restored complete firestore.indexes.json');
  console.log('🧹 Cleaning up backup file...');
  fs.unlinkSync(backupFile);
  console.log('✨ All done! Your indexes file is back to normal.');
} else {
  console.error('❌ Backup file not found:', backupFile);
}