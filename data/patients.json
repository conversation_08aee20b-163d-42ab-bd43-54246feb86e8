[{"id": "2f9e775c-63df-4d5b-a1e5-af1d6470f3d8", "fullName": "<PERSON>", "birthday": "1985-03-22T00:00:00.000Z", "phoneNumber": "+****************", "email": "<EMAIL>", "medicalHistory": "Hypertension, managed with medication. No known allergies.", "recentNotes": "Patient reported occasional chest discomfort, especially after physical activity.", "insuranceCompany": "BlueCross BlueShield", "insuranceGroupNumber": "BC123456789", "subscriberName": "<PERSON>", "listOfCalls": ["ca87b856-2914-48cb-bded-542a0551f62e"], "clinicId": 1}, {"id": "7265e3a0-d0d5-4a6e-9142-f5eb8f8f6bb6", "fullName": "<PERSON>", "birthday": "1992-07-15T00:00:00.000Z", "phoneNumber": "+****************", "email": "<EMAIL>", "medicalHistory": "Migraines, managed with preventative medication. Allergic to penicillin.", "recentNotes": "Patient reports improved frequency of migraines after medication adjustment.", "insuranceCompany": "<PERSON><PERSON><PERSON>", "insuranceGroupNumber": "AE987654321", "subscriberName": "<PERSON>", "listOfCalls": ["37a19e4c-9bbf-4f3a-bf6c-2b297f564df9"], "clinicId": 1}, {"id": "9ed6a1e3-6379-4ad2-b6d0-b630f0f8674a", "fullName": "<PERSON>", "birthday": "1978-11-30T00:00:00.000Z", "phoneNumber": "+****************", "email": "<EMAIL>", "medicalHistory": "Type 2 diabetes diagnosed in 2015. No known allergies.", "recentNotes": "Recent lab work shows improved A1C levels. Continue current management plan.", "insuranceCompany": "UnitedHealthcare", "insuranceGroupNumber": "UH456789123", "subscriberName": "<PERSON>", "listOfCalls": ["4fc92f0c-9e14-4ce4-8428-e2e141d63282"], "clinicId": 1}, {"id": "d0c7fdb0-7645-4d26-b3e4-0b9b5c3c7d3b", "fullName": "<PERSON>", "birthday": "2010-05-12T00:00:00.000Z", "phoneNumber": "+****************", "email": "<EMAIL>", "medicalHistory": "Asthma since age 4. Allergic to peanuts and dust mites.", "recentNotes": "Seasonal asthma exacerbation. Adjustment to medication recommended.", "insuranceCompany": "<PERSON><PERSON><PERSON>", "insuranceGroupNumber": "CI234567891", "subscriberName": "<PERSON>", "listOfCalls": ["f1a3c5b9-7d8e-4f2c-9b6a-e0d4c8b2a6f3"], "clinicId": 1}]