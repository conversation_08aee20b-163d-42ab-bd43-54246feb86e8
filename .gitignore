# OS Files
## macOS
.DS_Store
.AppleDouble
.LSOverride
._*
.Spotlight-V100
.Trashes
Icon?

## Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# IDE and Editor Files
## Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

## JetBrains IDEs
.idea/
*.iml
*.iws
*.ipr
.idea_modules/

## Sublime Text
*.sublime-project
*.sublime-workspace

## Vim
[._]*.s[a-v][a-z]
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Language & Framework Files
## Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

## Python
__pycache__/
*.py[cod]
*$py.class
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

## Java/Maven/Gradle
*.class
*.jar
*.war
*.ear
*.logs
*.ctxt
.mtj.tmp/
target/
.gradle/
build/
out/

# Dependency directories
jspm_packages/
bower_components/

# Build outputs
dist/
build/
out/
coverage/
.next/
.nuxt/
.vuepress/dist
.serverless/
.fusebox/

# Environment variables and secrets
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local
*.env
*.pem
.vercel

# Logs
logs
*.log

# Local development
.localdevserver/
.docusaurus
.cache/
.parcel-cache/
.temp
.cache
.tmp

# Misc
.eslintcache
.stylelintcache
*.tsbuildinfo
.tern-port

# Private individual user cursor rules
.cursor/rules/_*.mdc
