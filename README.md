# Doctor/Staff Portal

A Next.js (TypeScript) application that provides a secure portal for doctors and staff to manage patient call records and information. Built with Flowbite React components and Tailwind CSS.

## Features

- Authentication with Firebase Authentication
  - Email/password login
  - Password change functionality
  - Password reset functionality
  - Email change functionality
  - Email verification
- Multi-level user roles (Super Admin, Doctor, Staff)
- Dashboard with sidebar navigation
- Call records listing with priority scores and urgency indicators
- Detailed call view with audio playback, transcription, and AI summary
- User profile management
- Clinic management
- Staff management
- Patient information page with medical history and call records
- Urgency alerts system (mocked for demo)
- External API v2 with Nextech provider integration for appointment management
- Admin tools for user management

## Tech Stack

- **Next.js**: React framework for server-rendered applications
- **TypeScript**: Static typing for safer code
- **Tailwind CSS**: Utility-first CSS framework
- **Flowbite React**: UI component library based on Tailwind
- **Firebase Authentication**: For secure user authentication
- **React Icons**: Icon library
- **Firebase Firestore**: NoSQL database for storing patient, call, and user data
- **UUID**: For generating unique identifiers
- **Nextech Practice+ API**: Integration for real medical practice management

## Project Structure

- **pages**: All Next.js pages and API routes
  - **api**: Backend API endpoints
    - **auth**: Authentication endpoints
    - **calls**: Call record management
    - **clinics**: Clinic management
    - **patients**: Patient data management
    - **staff**: Staff management
    - **external-api**: External API endpoints for third-party integration (v1 and v2)
  - **dashboard**: Protected dashboard pages
  - **super-admin**: Super admin functionality
- **components**: Reusable React components
- **utils**: Utility functions including authentication and Firestore service
- **models**: TypeScript interfaces for data models
- **data**: Mock JSON data for demonstration
- **styles**: Global CSS including Tailwind imports
- **scripts**: Utility scripts for database seeding and management
- **lib**: Core library code including External API implementations
  - **external-api**: External API implementation
    - **v1**: Legacy external API implementation
    - **v2**: New provider-based external API with Nextech integration

## Getting Started

### Prerequisites

- Node.js version - check `.nvmrc` file
- [pnpm](https://pnpm.io/installation)
- Firebase project with Firebase Authentication and Firestore database
- Nextech Practice+ API credentials (for External API v2)

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd doctor-staff-portal
   ```

2. Install dependencies:
   ```
   pnpm install
   ```

3. Set up Firebase:
   - Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/)
   - Enable Firebase Authentication with email/password provider
   - Enable Firestore database in your project
   - Generate a new private key for your service account:
     - Go to Project Settings > Service accounts
     - Click "Generate new private key"
     - Save the JSON file securely

4. Configure environment variables:
   - Copy `.env.example` to `.env.local`:
     ```
     cp .env.example .env.local
     ```
   - Update `.env.local` with your Firebase credentials from the downloaded JSON file for server-side API usage
   - Update the public Firebase configuration for client-side authentication
   - Add Nextech API credentials for External API v2 (if needed)
   - Configure GCP Storage for audio feature:
     ```
     # GCP Storage Configuration
     GCP_PROJECT_ID="your-project-id"
     GCP_SERVICE_ACCOUNT_CLIENT_EMAIL="<EMAIL>"
     GCP_SERVICE_ACCOUNT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key content here...\n-----END PRIVATE KEY-----"
     GCP_AUDIO_BUCKET_NAME="your-audio-bucket-name"
     ```
   - Note: The application uses request headers to determine URLs for email verification links, so you don't need to configure a `NEXT_PUBLIC_APP_URL` environment variable
     ```
     NEXTECH_BASE_URL=https://api.pm.nextech.com/api
     NEXTECH_CLIENT_ID=your-client-id
     NEXTECH_CLIENT_SECRET=your-client-secret
     NEXTECH_PRACTICE_ID=your-practice-id
     ```

5. Seed your Firestore database with initial data:
   ```
   pnpm seed
   ```
   This uploads the mock patient and call data to your Firestore database.

6. Run the development server:
   ```
   pnpm dev
   ```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

### Development Tools

#### Email Verification Testing

For testing email verification links during development:

1. **Use the Dev Email Handler**:
   - Access the page at [http://localhost:3000/dev-email-handler](http://localhost:3000/dev-email-handler)
   - Paste verification links from console logs to test them

2. **Use the Email Verification Script**:
   ```bash
   node scripts/test-email-verification.js
   ```
   - Paste the verification link when prompted
   - The script will analyze the link and provide testing instructions

#### Admin Email Management

For managing mock users with fake emails:

1. **Access the Admin Tools**:
   - Log in as an admin user
   - Navigate to [http://localhost:3000/dashboard/admin-tools](http://localhost:3000/dashboard/admin-tools)

2. **Use the Email Change Tool**:
   - Search for users by email
   - Update email addresses directly without verification
   - Emails are automatically marked as verified

### Development Workflow

The project is set up with Git hooks using Husky to ensure code quality:

1. **Pre-commit Hook**: Before each commit, the pre-commit hook automatically runs:
   - All tests to ensure no breaking changes
   - ESLint on all staged JavaScript and TypeScript files
   - Fixes auto-fixable issues
   - Prevents commits if there are failing tests or linting errors that can't be auto-fixed

If you need to bypass the pre-commit hook in exceptional cases, you can use:
```
git commit -m "Your message" --no-verify
```
However, this should be avoided whenever possible to maintain code quality.

### Testing

The project has comprehensive test coverage for all API endpoints and critical functionality:

#### Test Structure
- **Unit Tests**: Located in `__tests__` directory, matching the source file structure
- **API Tests**: Comprehensive coverage of all external API endpoints
- **Integration Tests**: Testing service interactions and data flow

#### Running Tests
```bash
# Run all tests
pnpm test

# Run tests in watch mode during development
pnpm test:watch

# Run tests with coverage report
pnpm test:coverage
```

#### Test Coverage Requirements
- Minimum 80% code coverage required
- All API endpoints must have comprehensive test coverage
- Critical business logic must have dedicated test cases
- Edge cases and error scenarios must be tested

#### External API Test Coverage
All external API endpoints have comprehensive test coverage:

1. **External API v1**:
   - User Specialties (`/user-specialties`)
   - Clinics (`/clinics/by-phone`)
   - Appointments (`/appointments/active`)
   - Calendar (`/calendar/*`)

2. **External API v2**:
   - Patients (GET, POST)
   - Appointments (GET, POST, PATCH, DELETE)
   - Appointment Types
   - Appointment Availability
   - Locations
   - Clinics
   - Users/Practitioners

## Authentication

The application uses Firebase Authentication for secure user authentication:

- Email/Password authentication for staff and admin users
- Phone number authentication with SMS verification (mocked for demo)
- Custom tokens for phone auth verification
- Token refreshing is handled automatically

## Role-Based Access

The application supports multiple user roles:

- **Super Admin**: Can manage clinics, staff, and system-wide settings
- **Doctor**: Can view and manage patient call records and information
- **Staff**: Limited access to patient information and call records

## API Structure

The project includes the following API endpoints:

- **/api/auth**: Authentication endpoints
- **/api/clinics**: Clinic management
- **/api/clients**: Client data management
- **/api/calls**: Call logs and recordings management
- **/api/staff**: Staff management
- **/api/storage**: Audio files and session recordings from GCP Storage
- **/api/external-api/v1**: Legacy external API endpoints for third-party integration
- **/api/external-api/v2**: New provider-based API with Nextech integration

### Audio API Endpoints

The application provides API endpoints for accessing audio files from Google Cloud Storage:

#### General Audio Files API

```
GET /api/storage/audio-files
```

**Query Parameters:**
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `prefix` (optional): Filter files by prefix
- `fileName` (optional): Get a signed URL for a specific file
- `expiresInMinutes` (optional): Expiration time for signed URLs (default: 15)
- `audioOnly` (optional): Filter for audio files only (default: false)

**Examples:**

List all files in the default bucket:
```
GET /api/storage/audio-files
```

List audio files with a specific prefix:
```
GET /api/storage/audio-files?prefix=calls/2023/&audioOnly=true
```

Get a signed URL for a specific file:
```
GET /api/storage/audio-files?fileName=calls/recording123.mp3
```

#### Session Audio API

Retrieve the latest audio file for a specific session ID:

```
GET /api/storage/session-audio
```

**Query Parameters:**
- `sessionId` (required): The session ID to find audio for
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `minTimestamp` (optional): Only return files with timestamps after this value (in milliseconds)

**Example:**

Get the latest audio file for a session:
```
GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA
```

Get audio files after a specific timestamp:
```
GET /api/storage/session-audio?sessionId=0650dnenrFtRsK9R-fHCONQCA&minTimestamp=1744269450000000
```

#### Session Audio All API

Retrieve all audio files for a specific session ID as an array of interactions:

```
GET /api/storage/session-audio-all
```

**Query Parameters:**
- `sessionId` (required): The session ID to find audio for
- `bucket` (optional): The name of the bucket (defaults to GCP_AUDIO_BUCKET_NAME)
- `minTimestamp` (optional): Only return files with timestamps after this value (in milliseconds)

**Example:**

Get all audio files for a session:
```
GET /api/storage/session-audio-all?sessionId=0650dnenrFtRsK9R-fHCONQCA
```

**Response Format:**
```json
[
  {
    "text": "Patient: I need to schedule an appointment",
    "recordUrl": "gcp://bucket-name/audio/session123_1609545600000.mp3"
  },
  {
    "text": "Agent: I can help you with that. What day works for you?",
    "recordUrl": "gcp://bucket-name/audio/session123_1609545660000.mp3"
  }
]
```

**Note:** The `recordUrl` is in the format `gcp://bucket-name/file-path` which can be passed directly to the `/api/storage/convert-audio` endpoint for conversion to a web-compatible format.

## External API v2

The new External API v2 introduces a provider-based architecture for integrating with various practice management systems, with the first implementation being Nextech Practice+:

### Key Components

- **Provider-based Architecture**: Modular approach to support multiple practice management systems
- **Nextech Integration**: Complete integration with Nextech Practice+ API
- **Appointment Management**: Comprehensive appointment CRUD operations
- **Patient Management**: Patient search, creation, and information retrieval
- **Location and Clinic Support**: Access to clinic and location information
- **User/Practitioner Data**: Retrieve practitioner information and specialties

### API Features

- **Authentication**: OAuth 2.0 authentication with token caching and automatic refresh
- **Error Handling**: Comprehensive error handling with detailed error messages
- **Validation**: Zod schemas for request validation
- **Rate Limiting**: Built-in rate limit handling for provider APIs
- **Pagination**: Support for paginated results from provider APIs

## API Documentation

API documentation is available using Redoc, a modern, responsive, and customizable OpenAPI/Swagger documentation UI. Access it at:

- **Development**: [http://localhost:3000/api-docs](http://localhost:3000/api-docs)
- **Production**: [https://your-production-domain.com/api-docs](https://your-production-domain.com/api-docs)

The API documentation is generated at build time using `next-swagger-doc` to create the OpenAPI specification from JSDoc annotations in the API route files. The raw Swagger JSON is available at `/api/swagger`.

### External API Access

External API endpoints are secured with API key authentication:

- All external API requests must include an `x-api-key` header with a valid API key
- API keys should be stored securely in environment variables
- To configure an API key, add it to your `.env.local` file as `EXTERNAL_SERVICE_API_KEY`

## Future Enhancements

- Email notifications for urgent calls
- Audio transcription service
- AI-powered call analysis
- Advanced reporting and analytics
- Mobile application
- Support for additional practice management system providers

## New GCP conversationa agent setup on GCP side

1. Export prod GCP Conversation Agent as a JSON file, use Draft environment
2. Create a new conversation agent in GCP and import the JSON file
3. Update endSession Webhook URL to dev api url `https://fdk-dev-frontdesk-staffportal-service-1050829356759.us-east1.run.app/api/external-api/v2/calls/end-conversation`
4. Update setupSession Webhook URL to dev api url `https://fdk-dev-frontdesk-staffportal-service-1050829356759.us-east1.run.app/api/external-api/v2/calls/add-or-update-call-session`
5. Update manage_appointment_tool_v2 URL to dev api url `https://fdk-dev-frontdesk-staffportal-service-1050829356759.us-east1.run.app`
6. Update sms_confirmation tool URL to dev api url `https://fdk-dev-frontdesk-staffportal-service-1050829356759.us-east1.run.app`
7. Update manage_patients_tool URL to dev api url `https://fdk-dev-frontdesk-staffportal-service-1050829356759.us-east1.run.app`

## License

This project is licensed under the MIT License.

## Development Guidelines

This project follows specific development standards to ensure consistency and optimal implementation:

- [Technology Selection Standards](.cursor/rules/core-rules/tech-selection-standards.mdc) - Guidelines for choosing technologies that integrate well with the stack
- [Quick Reference: Optimal Tech Selection](.cursor/rules/core-rules/tech-selection-quick-ref.mdc) - A quick checklist for developers
- [Lint Check Required](.cursor/rules/core-rules/lint-check-required.mdc) - Always check and fix lint errors before considering a task complete
- [Quick Reference: Lint Checking](.cursor/rules/core-rules/lint-check-quick-ref.mdc) - Quick guide for fixing common lint errors
- [Test Verification Standard](.cursor/rules/test-verification-auto.mdc) - Guidelines for test coverage and quality
- [Quick Reference: Test Verification](.cursor/rules/test-verification-quick-ref.mdc) - Quick guide for writing and maintaining tests

Always prefer framework-specific solutions (like `next-swagger-doc` for API documentation) over generic alternatives to minimize code and maximize maintainability.
