const flowbite = require('flowbite-react/tailwind');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './app/**/*.{js,ts,jsx,tsx}',
    flowbite.content(),
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: [
          'Mulish',
          'ui-sans-serif',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Helvetica Neue',
          'Arial',
          'sans-serif',
        ],
      },
      colors: {
        frontdesk: {
          dark: '#1e1e2d', // Dark purple/navy for sidebar
          primary: '#18163F', // Primary dark purple for sidebar and header
          light: '#f5f8fa', // Light background color
          accent: '#6366f1', // Accent color for active elements
          button: '#1724C9', // Button background color
          hover: '#1724C9', // Hover state for sidebar items
          'priority-pending': '#dc2626', // Red for pending priority badges
        },
      },
    },
  },
  plugins: [flowbite.plugin()],
};
