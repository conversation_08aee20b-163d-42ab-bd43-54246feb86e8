module.exports = {
  // For TypeScript files, use the same settings as our manual lint command
  "**/*.{ts,tsx}": ["eslint --fix"],

  // For script files - use a more permissive configuration
  "scripts/**/*.js": [
    'eslint --fix --rule "@typescript-eslint/no-require-imports:off" --rule "@typescript-eslint/no-unused-vars:warn"',
  ],

  // Add an ignore for markdown files that might get picked up
  "**/*.md": ["echo 'Skipping markdown files'"],
};
