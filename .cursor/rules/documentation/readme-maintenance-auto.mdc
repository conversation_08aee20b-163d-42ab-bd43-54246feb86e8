# README Maintenance Standard

## Core Rule
The README.md file must be kept up-to-date with all significant changes to the project. This includes new features, API changes, configuration updates, and development workflow modifications.

## When to Update README

1. **New Features**
   - Add feature description to the Features section
   - Update Tech Stack if new technologies are introduced
   - Add any new environment variables to the configuration section

2. **API Changes**
   - Document new endpoints in the API Structure section
   - Update API Documentation section if endpoints are modified
   - Add new authentication or authorization requirements

3. **Development Workflow**
   - Update development prerequisites if new tools are required
   - Document new scripts or commands
   - Update testing information if test requirements change

4. **Project Structure**
   - Add new directories or important files
   - Update directory descriptions
   - Document new configuration files

## README Sections to Check

### Essential Sections
- [ ] Features
- [ ] Tech Stack
- [ ] Project Structure
- [ ] Getting Started
- [ ] Development Workflow
- [ ] API Structure
- [ ] Testing
- [ ] Development Guidelines

### Update Checklist
1. **Feature Updates**
   ```markdown
   ## Features
   - Feature name: Brief description
   - Impact on existing features
   - Dependencies or requirements
   ```

2. **API Documentation**
   ```markdown
   ## API Structure
   - /api/path: Purpose and functionality
   - Authentication requirements
   - Request/Response examples if needed
   ```

3. **Development Changes**
   ```markdown
   ### Development Workflow
   1. New tools or requirements
   2. Updated scripts or commands
   3. Modified development practices
   ```

## Examples

### Good README Update
```markdown
## Features
- AI-powered Call Analysis: Automatically analyze call transcripts for sentiment and urgency using OpenAI's GPT-4
- Real-time Notifications: Instant alerts for urgent calls using WebSocket connections
- Enhanced Search: Elasticsearch integration for full-text search across call transcripts

## Tech Stack
- Added OpenAI GPT-4 for call analysis
- Added Socket.io for real-time notifications
- Added Elasticsearch for search functionality

## Configuration
New environment variables required:
- OPENAI_API_KEY: Your OpenAI API key
- ELASTICSEARCH_URL: Elasticsearch instance URL
```

### Bad README Update
```markdown
Added some new stuff and changed some APIs
```

## Common Mistakes to Avoid

1. **Incomplete Updates**
   - Missing new environment variables
   - Undocumented breaking changes
   - Incomplete setup instructions

2. **Outdated Information**
   - Old configuration examples
   - Removed features still listed
   - Incorrect version numbers

3. **Poor Organization**
   - Random placement of new information
   - Inconsistent formatting
   - Missing section hierarchy

## Best Practices

1. **Keep It Current**
   - Update README as part of the feature development
   - Don't wait until release time
   - Remove outdated information

2. **Maintain Structure**
   - Follow existing section organization
   - Use consistent formatting
   - Keep related information together

3. **Be Comprehensive**
   - Include all necessary setup steps
   - Document all configuration options
   - Provide clear examples

## Remember
- README is often the first thing new developers see
- Clear documentation saves time and reduces confusion
- Keep it professional and well-organized
- Use proper markdown formatting
- Include links to additional resources when relevant 