# Quick Reference: README Maintenance

## Quick Checklist
✅ Update Features section if adding new functionality
✅ Update Tech Stack if adding new technologies
✅ Update API Structure if modifying endpoints
✅ Update Testing section if changing test requirements
✅ Update Development Workflow if changing processes
✅ Add new environment variables to configuration
✅ Update Project Structure for new directories

## Common Commands
```bash
# View current README
cat README.md

# Check markdown formatting
npx markdownlint README.md

# Preview README (if using VS Code)
Ctrl/Cmd + Shift + V
```

## Section Templates

### New Feature
```markdown
## Features
- **Feature Name**: Brief description
  - Key functionality
  - Requirements
  - Dependencies
```

### New API Endpoint
```markdown
## API Structure
- **/api/path**: Description
  - Method: GET/POST/etc.
  - Authentication: Required/Optional
  - Parameters: List key parameters
```

### New Environment Variable
```markdown
## Configuration
- `VARIABLE_NAME`: Description
  Default: `default_value`
  Required: Yes/No
```

### New Test Requirement
```markdown
## Testing
- **Test Type**: Description
  - Coverage requirement
  - Special considerations
  - How to run
```

## Quick Tips
1. Update README before creating PR
2. Use consistent formatting
3. Keep descriptions clear and concise
4. Include examples for complex features
5. Link to additional documentation

## Remember
- README = First impression
- Keep it current
- Be comprehensive
- Stay organized 