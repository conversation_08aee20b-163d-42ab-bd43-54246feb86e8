---
description: Standards for documentation formats across different document types and code
globs: 
alwaysApply: false
---
# Documentation Standards

## Core Principles
- Documentation should be clear, concise, and complete
- Keep documentation close to the code it describes
- Maintain consistent formats across similar document types
- Document the "why" more than the "what"

## Code Documentation
Use JSDoc format for all TypeScript/JavaScript documentation:

```typescript
/**
 * Authenticates a user against the database
 *
 * @param {string} username - The user's username or email
 * @param {string} password - The user's password
 * @returns {Promise<User>} The authenticated user object
 * @throws {AuthError} When authentication fails
 */
```

## Project Documentation
Every project and significant module should have a README.md following a consistent structure:

```markdown
# Project Name

Brief description of the project.

## Features

* Feature 1
* Feature 2
* Feature 3

## Installation

```bash
pnpm install
```

## Usage

Brief overview of how to use the project.
```

## Examples

### Good JSDoc
```typescript
/**
 * Creates a new user account
 * 
 * @param {UserInput} userData - User registration information
 * @returns {Promise<User>} Newly created user
 * @throws {ValidationError} When input data is invalid
 * @throws {DuplicateError} When email already exists
 */
async function createUser(userData: UserInput): Promise<User> {
  // Implementation
}
```

### Bad JSDoc
```typescript
/**
 * This function creates a user
 * 
 * @param userData The data
 */
async function createUser(userData) {
  // Implementation
}
``` 