---
description: Enforce 80% code coverage and testing standards for the project
globs: 
alwaysApply: false
---
# Testing Standards

## Core Coverage Requirement
- **Minimum 80% code coverage** required for all production code
- Coverage must be verified during CI pipeline

## Testing Framework & Tools
- Jest as primary testing framework
- React Testing Library for UI components
- Cypress for end-to-end testing
- Istanbul/nyc for coverage reporting

## Coverage Rules
- Functions/methods: 80% minimum
- Branches: 80% minimum
- Lines: 80% minimum
- Statements: 80% minimum

## Priority Testing Areas
1. Business logic and domain models (100% coverage goal)
2. API endpoints and data fetching (90% coverage goal)
3. Authentication and authorization flows (95% coverage goal)
4. Form validation and submission (85% coverage goal)
5. State management (Redux/Context) (85% coverage goal)
6. UI components (80% coverage goal)

## Examples

### Good Test Structure
```typescript
describe('UserAuthentication', () => {
  describe('login', () => {
    it('should return token when credentials are valid', () => {
      // Arrange
      const credentials = { email: '<EMAIL>', password: 'password' };
      mockUserService.findByEmail.mockResolvedValue(testUser);
      
      // Act
      const result = await authenticateUser(credentials);
      
      // Assert
      expect(result).toHaveProperty('token');
      expect(result.user).toEqual(testUser);
    });
  });
});
```

### Bad Test Structure
```typescript
test('login works', async () => {
  const result = await authenticateUser({ email: '<EMAIL>', password: 'password' });
  expect(result).toBeDefined();
});
``` 