# Test Verification Standard

## Core Rule
All code changes must be accompanied by passing tests. This rule ensures that no task is considered complete until all tests pass successfully.

## Pre-Completion Checklist

1. **Run All Tests**
   ```bash
   pnpm test
   ```
   - All test suites must pass
   - No tests should be skipped without explicit justification
   - Test coverage should meet minimum requirements (80%)

2. **Verify Test Coverage**
   - New code must have corresponding test coverage
   - Modified code must maintain or improve existing test coverage
   - Critical paths must have comprehensive test coverage

3. **Test Quality Checks**
   - Tests should be meaningful and not just for coverage
   - Edge cases should be covered
   - Error scenarios should be tested
   - Mocks and stubs should be properly implemented

## Test Categories to Verify

### API Tests
- Endpoint validation
- Success scenarios
- Error handling
- Edge cases
- Authentication/Authorization

### Unit Tests
- Function behavior
- Input validation
- Error conditions
- Edge cases

### Integration Tests
- Component interactions
- Data flow
- Service integration
- State management

## Examples

### Good Test Verification
```typescript
describe("API Endpoint", () => {
  beforeEach(() => {
    // Proper test setup
    jest.clearAllMocks();
  });

  it("handles successful requests", async () => {
    // Arrange
    const input = { /* test data */ };
    
    // Act
    const result = await handler(input);
    
    // Assert
    expect(result).toBeDefined();
    expect(result.status).toBe(200);
  });

  it("handles error cases", async () => {
    // Error scenario testing
  });

  it("validates input", async () => {
    // Input validation testing
  });
});
```

### Bad Test Verification
```typescript
describe("API Endpoint", () => {
  it("works", () => {
    const result = handler({});
    expect(result).toBeTruthy();
  });
});
```

## Pre-Commit Hook
The repository includes a pre-commit hook that automatically runs commands:
```bash
#!/bin/sh
. "$(dirname "$0")/_/husky.sh"
```

## Error Messages and Solutions

### Common Test Failures
1. **Test Timeout**
   - Solution: Check for async operations not being properly handled
   - Verify test cleanup in afterEach/afterAll blocks

2. **Mock Failures**
   - Solution: Ensure mock implementations match expected behavior
   - Verify mock reset in beforeEach blocks

3. **Type Errors**
   - Solution: Check type definitions in test files
   - Verify proper typing of mock data

## Remember
- Never skip tests to complete a task faster
- Failed tests indicate potential issues that need attention
- Test quality is as important as code quality
- Tests are documentation for future developers 