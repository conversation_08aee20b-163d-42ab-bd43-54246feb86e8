---
description: Protocol for conducting deep research using date/time context and Tavily search
globs: 
alwaysApply: false
---
# Deep Research Protocol

## Trigger Conditions
- User explicitly requests "deep research" on a specific topic
- User asks for comprehensive analysis requiring external information
- Research questions that need up-to-date information

## Required Protocol Steps

### 1. Inject Current Date/Time
Always begin by noting the current system date and time to establish temporal context for the research:

```
Research conducted at [SYSTEM_DATE_TIME]
```

### 2. Use Tavily Search for Current Information
Immediately follow with <PERSON><PERSON> search to gather relevant, up-to-date information:

```
<function_calls>
<invoke name="mcp_tavily_mcp_tavily_search">
<parameter name="query">[RESEARCH_TOPIC]</parameter>
<parameter name="search_depth">advanced</parameter>
<parameter name="max_results">10</parameter>
</invoke>
</function_calls>
```

## Examples

### Good Research Response
```
Research conducted at 2023-06-15 14:30 UTC

I've conducted thorough research on quantum computing advancements in 2023 using the <PERSON><PERSON> search tool. The findings indicate several major breakthroughs:

1. IBM unveiled a 433-qubit quantum processor called "O<PERSON>rey" in November
2. Google achieved quantum error correction using logical qubits
3. Microsoft demonstrated topological qubits for more stable quantum computing
...
```

### Bad Research Response
```
Quantum computing is making rapid advances in recent years. Some companies working on it include IBM, Google, and Microsoft.

[No date/time context, no indication of using search tools, potentially outdated information]
``` 