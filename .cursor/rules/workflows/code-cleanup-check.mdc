# Code Cleanup Check

## Context

This rule should be applied after completing any implementation task to ensure code quality and maintainability.

## Critical Rules

- Identify any unused or redundant code that was created during implementation
- Check for deprecated code paths that are no longer needed
- Verify that temporary code or debugging statements have been removed
- Ensure no dead code exists (code that can never be executed)
- Check for duplicate functionality
- Verify that all imports are necessary and used

## Implementation Checklist

After completing any implementation task, check the following:

1. **Unused Variables**: Are there any variables, functions, or classes that are defined but never used?

2. **Commented-Out Code**: Are there blocks of commented code that should be removed?

3. **Duplicate Logic**: Is there any logic that's duplicated that could be refactored into a shared function?

4. **Unused Imports**: Are there any imports that are no longer needed?

5. **Deprecated Routes**: Have old API routes been properly removed or redirected?

6. **Test Code**: Has any code used temporarily for testing been removed?

7. **Console Logs**: Have debugging console.log statements been removed?

8. **Orphaned Files**: Are there any files that are no longer referenced anywhere?

## Process

When you've completed an implementation:

1. Review all changed files for the items in the checklist
2. Use code analysis tools if available
3. Propose removal of identified redundant code
4. Ask for approval before deleting significant portions of code

## Examples

### Types of Redundant Code to Remove

- Functions that are never called
- Variables that are assigned but never read
- Imports that are never used
- Routes that are no longer accessed
- Commented-out code blocks
- Duplicate utility functions
- Debug console.log statements
- Empty files or directories 