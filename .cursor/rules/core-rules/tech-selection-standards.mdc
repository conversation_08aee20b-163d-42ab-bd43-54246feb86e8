# Technology Selection Standards

## Context

- When selecting technologies, libraries, or frameworks for implementation
- When evaluating multiple approaches to solving a technical problem
- When adding new dependencies to the project
- When implementing features that could use framework-specific solutions

## Critical Rules

- Always prefer framework-specific solutions that integrate natively with the stack
- Analyze the project stack before proposing technologies or approaches
- Minimize dependencies while maximizing integration with existing tools
- Select solutions that reduce boilerplate code and maintenance burden
- For Next.js projects, prioritize Next.js-specific packages over generic alternatives

## Tech Selection Process

### 1. Analyze Project Stack
Before implementing any solution:
- Examine `package.json` to understand existing dependencies
- Identify the framework version and its core features
- Review related files to understand established project patterns
- Check for specialized configurations in project setup files

### 2. Research Framework-Specific Solutions
For any task, prioritize in this order:
1. Built-in framework features (Next.js APIs, React hooks, etc.)
2. Framework-specific packages (next-swagger-doc vs generic swagger-jsdoc)
3. Popular community solutions with good framework integration
4. Generic solutions only when specialized options don't exist

### 3. Evaluate Integration Costs
Consider:
- Setup complexity vs long-term maintenance
- Amount of boilerplate/configuration code required
- Compatibility with future framework versions
- Alignment with project conventions

## Examples

### ✅ Good Practices

#### API Documentation
```typescript
// Using next-swagger-doc for Next.js projects
import { createSwaggerSpec } from 'next-swagger-doc';

export const getSwaggerSpec = () => {
  return createSwaggerSpec({
    definition: apiConfig,
    apiFolder: 'pages/api' // Next.js specific structure
  });
};
```

#### Form Handling
```typescript
// Using react-hook-form instead of a generic solution
import { useForm } from 'react-hook-form';

function MyForm() {
  const { register, handleSubmit, errors } = useForm();
  // Implementation using React-specific form library
}
```

### ❌ Poor Practices

#### API Documentation
```typescript
// Using generic swagger-jsdoc requiring more configuration
import swaggerJsdoc from 'swagger-jsdoc';

const options: swaggerJsdoc.Options = {
  // Extensive configuration required
  // No Next.js-specific integration
};
const swaggerSpec = swaggerJsdoc(options);
```

#### Authentication
```typescript
// Manual JWT implementation instead of using NextAuth.js
// Requires significantly more code and maintenance
import jwt from 'jsonwebtoken';

// Custom implementation with more potential bugs and security issues
```

## Technology-Specific Guidelines

### Next.js Projects
- Use Next.js API routes instead of external API servers when possible
- Leverage Next.js data fetching methods (getServerSideProps, getStaticProps)
- Prefer nextjs-specific packages:
  - `next-swagger-doc` over `swagger-jsdoc`
  - `next-auth` over custom auth solutions
  - `next-i18next` over generic i18n libraries

### Firebase Projects
- Use Firebase Admin SDK server-side and Firebase SDK client-side
- Leverage Firebase Extensions over custom implementations
- Adopt Firestore data modeling patterns appropriate for NoSQL

### React Projects
- Use React Query for data fetching over custom fetch implementations
- Prefer React-specific form libraries (react-hook-form, formik)
- Use React context or specialized state management (Redux Toolkit, Zustand)

## Decision Process Questions
Before implementing any technical solution, ask:

1. Does Next.js/React have a built-in solution for this problem?
2. Is there a framework-specific package designed for this use case?
3. What solution requires the least configuration while maintaining quality?
4. What solution aligns best with existing project patterns?
5. Will this approach scale with the project's future needs?
6. Does this solution minimize boilerplate code and maintenance burden? 