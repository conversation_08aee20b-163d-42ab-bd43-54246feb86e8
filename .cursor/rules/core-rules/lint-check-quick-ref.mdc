description: 
globs: *.ts, *.tsx
alwaysApply: true
---

# Quick Reference: Lint Checking

## Context
- When completing any code changes
- When preparing to commit code

## Critical Rules
- Always check for lint errors before considering a task complete
- Fix all lint errors or provide clear reasons for disabling specific rules

## Quick Workflow

1. **Write/Modify Code** → Complete your implementation
2. **Run Lint** → `pnpm lint`
3. **Fix Errors** → Address all issues found
4. **Verify** → Run lint again to confirm all errors are fixed
5. **Only then** → Consider the task complete

## Common Fixes

| Error | Fix |
|-------|-----|
| Unused import | Remove the import |
| Unused variable | Remove or use it (or disable with comment) |
| Missing return type | Add explicit return type |
| Missing dependency in useEffect | Add to dependency array or explain why excluded |

## Disabling Rules (When Necessary)

```typescript
// Single line
// eslint-disable-next-line rule-name
const someValue = getValue();

// Block of code
/* eslint-disable rule-name */
// Code with intentional lint issues
/* eslint-enable rule-name */

// File level (at top of file)
/* eslint-disable rule-name */
```

Always add a comment explaining why the rule is disabled. 