description: apply this rule when your are making a tech selection decision
globs: *.ts, *.tsx
alwaysApply: false
---

# Quick Reference: Optimal Tech Selection

## Context

- When making quick decisions about which technology to use
- When needing a fast checklist for evaluating technology options
- When implementing features in a Next.js or React-based project

## Critical Rules

- Always check the existing project stack before making technology decisions
- Choose framework-specific solutions over generic packages whenever possible
- Prioritize solutions with minimal configuration and boilerplate code
- Use the provided checklists to evaluate each potential solution

## Before Implementing Any Solution

1. **Check the stack first**
   - Review `package.json` and project structure
   - Identify the framework and version

2. **Look for specialized solutions**
   - Framework-specific > Generic packages
   - Example: `next-swagger-doc` > `swagger-jsdoc`
   - Example: `next-auth` > custom JWT implementation

3. **Evaluate code efficiency**
   - Less configuration = Better
   - Less boilerplate = Better
   - Better framework integration = Better

## Next.js Project Checklist

- [ ] Does Next.js have a built-in way to do this?
- [ ] Is there a Next.js-specific package for this?
- [ ] Will this work well with Next.js API routes?
- [ ] Is this compatible with Next.js data fetching methods?

## Red Flags

- Using generic packages when framework-specific ones exist
- Complex configuration for something that should be simple
- Reinventing features that are built into the framework
- Excessive boilerplate code

## Common Next.js Optimizations

| Need | Optimal Solution | Avoid |
|------|-----------------|-------|
| API Docs | next-swagger-doc | swagger-jsdoc + UI manually |
| Auth | next-auth | custom JWT handling |
| Forms | react-hook-form | vanilla handlers |
| Data Fetching | SWR/React Query | custom fetch wrappers |
| Routing | Next.js built-in | custom router solutions |
| Internationalization | next-i18next | generic i18n libraries | 