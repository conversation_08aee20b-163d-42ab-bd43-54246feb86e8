# Lint Check Required Before Task Completion

## Context
- When implementing new features
- When modifying existing code
- When reviewing code before completion
- When preparing code for submission or pull request

## Critical Rules
- Always run lint checks after writing or modifying code
- Fix all linting errors before considering a task complete
- Prefer fixing the code over disabling lint rules
- Only disable lint rules when absolutely necessary and with clear comments
- Verify all lint errors are fixed by running the linter again after making changes

## Lint Check Process

### 1. Implement Required Code Changes
Complete your code changes as needed for the task.

### 2. Run Lint Check
```bash
pnpm lint
```

### 3. Fix Any Errors Found
Address all errors using one of these approaches (in order of preference):
1. Modify code to comply with lint rules
2. Refactor problematic sections
3. If needed, disable specific lint rules with clear reasoning:
   ```typescript
   // eslint-disable-next-line @typescript-eslint/no-unused-vars
   const unusedVar = 'only needed for future implementation';
   ```

### 4. Verify Fixes with Another Lint Check
Always run the linter again after making changes to verify all issues are resolved.

## Common Lint Error Examples and Solutions

### Unused Variables or Imports

#### Problem:
```typescript
import { useState, useEffect } from 'react'; // Error: 'useEffect' is defined but never used
const [value, setValue] = useState(''); // Error: 'setValue' is defined but never used
```

#### Solutions:

1. Remove unused imports/variables:
```typescript
import { useState } from 'react';
const [value] = useState('');
```

2. For variables intended for future use, disable the rule with explanation:
```typescript
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const [value, setValue] = useState(''); // Will be used in upcoming feature
```

### No Explicit Return Type

#### Problem:
```typescript
// Error: Missing return type on function
function processData(input: string) {
  return input.toUpperCase();
}
```

#### Solution:
```typescript
function processData(input: string): string {
  return input.toUpperCase();
}
```

## Task Completion Checklist

Before marking a task as complete, ensure:

- [ ] Code implements all required functionality
- [ ] All lint errors are fixed (verified with a final lint check)
- [ ] No unnecessary lint rule disables
- [ ] Clear comments for any necessary lint rule disables
- [ ] Code follows the project's style guide and naming conventions 