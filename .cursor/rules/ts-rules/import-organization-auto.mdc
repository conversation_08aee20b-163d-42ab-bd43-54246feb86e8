---
description: 
globs: *.ts, *.tsx
alwaysApply: false
---
# Import Organization Standards

## Core Rule
All TypeScript and React files must organize imports in consistent groups, sorted alphabetically within each group.

## Import Groups Order
1. Built-in Node modules
2. External third-party packages/libraries
3. Internal absolute imports (project modules)
4. Internal relative parent imports (`../`)
5. Internal relative sibling imports (`./`)
6. Type imports
7. Asset imports (CSS, images, etc.)

## Formatting Requirements
- Add a blank line between each import group
- Sort imports alphabetically within each group
- No blank lines within a group
- Use consistent quote style (prefer single quotes)

## Examples

### Good Import Organization
```typescript
import React, { useEffect, useState } from 'react';

import { AxiosError } from 'axios';
import classNames from 'classnames';

import { ErrorBoundary } from 'components/common';
import { useAppDispatch } from 'store/hooks';

import { fetchUserData } from '../actions';

import { UserAvatar } from './UserAvatar';
import { UserInfo } from './UserInfo';

import type { UserProfile } from 'types/user';

import './UserProfile.css';
```

### Bad Import Organization
```typescript
import React from 'react';
import './styles.css';  // Asset import mixed with package import
import { useEffect, useState } from 'react';
import { UserData } from 'types';
import { Button } from '@material-ui/core';
import { fetchData } from '../api';
``` 