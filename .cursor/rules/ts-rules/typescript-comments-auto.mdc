---
description: 
globs: *.ts, *.tsx
alwaysApply: false
---
# TypeScript Commenting Standard

## Core Philosophy
- Comments should explain *why*, not *what* (code shows what happens)
- Be thorough at critical junctures, brief elsewhere
- Omit comments for self-documenting code

## File Headers
```typescript
/**
 * @file Brief description of file purpose
 * <AUTHOR> author name
 */
```

## Function/Method Comments
```typescript
/**
 * Brief description of function's purpose
 * @param paramName Description of parameter
 * @returns Description of return value
 * @throws Description of potential errors (when applicable)
 */
```

## Class Comments
```typescript
/**
 * Brief description of class purpose
 * @implements Interfaces implemented (when applicable)
 * @extends Classes extended (when applicable)
 */
```

## Interface/Type Comments
```typescript
/**
 * Brief description of interface/type purpose
 */
```

## Property Comments
- Use single-line comments for class properties:
```typescript
/** Description of property's purpose */
```

## Inline Comments
- Use sparingly for non-obvious code:
```typescript
// Explanation of complex logic or notable edge case
```

## Examples

### Good
```typescript
/**
 * Processes user authentication request
 * @param credentials User login information
 * @returns Authentication token and user profile
 * @throws AuthError if credentials are invalid
 */
async function authenticateUser(credentials: UserCredentials): Promise<AuthResult> {
  // Skip validation for admin users
  if (credentials.isAdmin) {
    return adminAuthFlow(credentials);
  }
  
  const user = await userRepository.findByEmail(credentials.email);
  
  // TODO(alice): Add multi-factor authentication [AUTH-456]
  
  return generateAuthToken(user);
}
```

### Bad
```typescript
/**
 * This function authenticates the user
 * It takes the user credentials
 * Then it checks if the user exists
 * Then it validates the password
 * Then it returns an auth token
 * @param credentials The credentials
 * @returns The auth result
 */
async function authenticateUser(credentials: UserCredentials): Promise<AuthResult> {
  // Get the user
  const user = await userRepository.findByEmail(credentials.email);
  // Check if user exists
  if (!user) {
    // If user doesn't exist, throw error
    throw new Error("User not found");
  }
  // Return the token
  return generateAuthToken(user);
}
``` 