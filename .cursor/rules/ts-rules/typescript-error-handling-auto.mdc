---
description: 
globs: *.ts, *.tsx
alwaysApply: false
---
# TypeScript Error Handling Standards

## Core Requirements
- All functions that can fail must implement proper error handling
- Error types must be specific and descriptive
- Errors must be propagated or handled in a user-friendly way
- Asynchronous operations must have try/catch blocks

## Custom Error Classes

Use custom error classes for domain-specific errors:

```typescript
class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public data?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
    Object.setPrototypeOf(this, ApiError.prototype);
  }
}
```

## Function Error Handling

### Synchronous Functions

```typescript
// ✅ Good Practice
function divideNumbers(a: number, b: number): number {
  if (b === 0) {
    throw new Error('Division by zero is not allowed');
  }
  return a / b;
}

// ❌ Bad Practice
function divideNumbers(a: number, b: number): number {
  return a / b; // Will return Infinity or throw unexpected error
}
```

### Asynchronous Functions

```typescript
// ✅ Good Practice
async function fetchUserData(userId: string): Promise<UserData> {
  try {
    const response = await apiClient.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    if (error instanceof ApiError && error.statusCode === 404) {
      throw new UserNotFoundError(`User with ID ${userId} not found`);
    }
    throw new DataFetchError('Failed to fetch user data', { cause: error });
  }
}

// ❌ Bad Practice
async function fetchUserData(userId: string): Promise<UserData> {
  const response = await apiClient.get(`/users/${userId}`);
  return response.data;
}
```

## Examples

### Good
```typescript
try {
  await processUserPayment(userId, amount);
} catch (error) {
  logger.error('Payment processing failed', {
    userId,
    amount,
    errorMessage: error instanceof Error ? error.message : String(error),
  });
  throw new PaymentProcessingError('Failed to process payment', { cause: error });
}
```

### Bad
```typescript
// No error handling
processUserPayment(userId, amount);

// or inadequate error handling
try {
  await processUserPayment(userId, amount);
} catch (error) {
  console.error('Error:', error);
}
``` 