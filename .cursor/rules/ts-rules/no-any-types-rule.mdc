---
description: Enforce strict typing by prohibiting 'any' type in TypeScript files
globs: "**/*.{ts,tsx}"
alwaysApply: true
---
# No 'any' Type Rule

## Core Rule
**NEVER use the `any` type in TypeScript files under any circumstances.**

All variables, parameters, return types, and generic types must be explicitly typed with specific types rather than using `any`.

## Alternatives to 'any'

1. **Use proper type definitions:**
   - Define interfaces and types for structured data
   - Use built-in TypeScript utility types
   - Use union types for multiple possible types

2. **When type is truly unknown:**
   - Use `unknown` instead of `any`
   - Use `Record<string, unknown>` for generic objects
   - Use proper type guards and type assertions

3. **For API responses:**
   - Define interfaces that match the expected response structure
   - Use zod or similar libraries for runtime validation

## Examples

### ❌ Bad Examples (Never Do This)

```typescript
// Using 'any' for function parameters
function processData(data: any) {
  return data.property;
}

// Using 'any' for variables
const userInput: any = getUserInput();

// Using 'any' in generics
const items: Array<any> = getItems();

// Implicit 'any' from lack of types
function calculate(a, b) {
  return a + b;
}
```

### ✅ Good Examples

```typescript
// Define an interface
interface UserData {
  id: number;
  name: string;
  email: string;
}

// Use the interface
function processUser(user: UserData) {
  return user.name;
}

// For objects with unknown structure
function processData(data: Record<string, unknown>) {
  if (typeof data.property === 'string') {
    return data.property;
  }
  return '';
}

// Using unknown with type guards
function processValue(value: unknown) {
  if (typeof value === 'string') {
    return value.toUpperCase();
  }
  return String(value);
}

// Union types for multiple possibilities
function formatValue(value: string | number | boolean) {
  return String(value);
}
```

## Implementation Requirements

1. Set up ESLint with the `@typescript-eslint/no-explicit-any` rule set to 'error'
2. Enable strict mode in tsconfig.json (`"strict": true`)
3. Add the `noImplicitAny` compiler option in tsconfig.json
4. Code reviews should specifically check for and reject any instances of `any`

## Remember
If you're tempted to use `any`, that's a sign you need to better understand the data structure or create a more appropriate type definition. 