---
description: Enforce TypeScript-only files and prevent any JavaScript files creation in the project
globs: 
alwaysApply: false
---
# TypeScript-Only Project Standard

## Core Rule
**NEVER create JavaScript (.js) files under any circumstances.**

All code must be written in TypeScript (.ts/.tsx) or JSON (.json) format only.

## Examples of Violations

### Incorrect Configuration Files
```
❌ Creating jest.config.js
❌ Creating babel.config.js
❌ Creating webpack.config.js
```

### Correct Alternatives
```
✅ Creating jest.config.ts
✅ Creating babel.config.ts
✅ Creating webpack.config.ts
✅ Creating tsconfig.json
```

## Specific Violation Example
"I asked you to set up Jest for our project and you created a JestConfig.js file, yet this is a TypeScript only project. Never again create any JS files. Always use TypeScript or JSON if necessary."

## File Extension Rules
- For React components: `.tsx`
- For TypeScript modules: `.ts`
- For configuration: `.ts` or `.json`
- For data: `.json` 