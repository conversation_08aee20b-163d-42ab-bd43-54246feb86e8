---
description: 
globs: *.ts, *.tsx
alwaysApply: false
---
# Naming Conventions

## Core Principles
- Names should be descriptive and reveal intent
- Use consistent casing conventions for each type
- Prefer full words over abbreviations
- Maintain a standard vocabulary

## TypeScript/JavaScript Conventions

### Variables and Functions
- Variables: camelCase (`userName`, `totalCount`)
- Functions: camelCase (`getUserData()`, `calculateTotal()`)
- Constants: UPPER_SNAKE_CASE (`MAX_RETRY_COUNT`, `API_BASE_URL`)
- Private variables: _camelCase (`_privateValue`, `_internalState`)

### Classes and Types
- Classes: PascalCase (`UserRepository`, `PaymentProcessor`)
- Interfaces: PascalCase (`UserProfile`, `ApiResponse`)
- Types: PascalCase (`UserState`, `RequestOptions`)
- Enums: PascalCase (`UserRole`, `PaymentStatus`)

### Boolean Variables
Use `is`, `has`, `can` or similar prefix:

```typescript
// ✅ Good
const isActive = true;
const hasPermission = checkPermission();

// ❌ Bad
const active = true;
const permission = checkPermission();
```

### Event Handlers
Use `handle` prefix for event handlers:

```typescript
// ✅ Good
const handleSubmit = (event) => { /* ... */ };
const handleInputChange = (event) => { /* ... */ };

// ❌ Bad
const submit = (event) => { /* ... */ };
const inputChange = (event) => { /* ... */ };
```

## Examples

### Good Component
```typescript
interface ButtonProps {
  label: string;
  onClick: () => void;
  isDisabled?: boolean;
}

function Button({ label, onClick, isDisabled }: ButtonProps) {
  const handleClick = () => {
    if (!isDisabled) {
      onClick();
    }
  };
  
  return <button onClick={handleClick}>{label}</button>;
}
```

### Bad Component
```typescript
interface BtnParams {
  text: string;
  click: () => void;
  disabled?: boolean;
}

function btn({ text, click, disabled }: BtnParams) {
  const submitFn = () => {
    if (!disabled) {
      click();
    }
  };
  
  return <button onClick={submitFn}>{text}</button>;
}
``` 