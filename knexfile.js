require('dotenv').config({ path: '.env.local' });

// Knex configuration for CLI and migrations
const config = {
  client: 'mysql2',
  connection: {
    host: process.env.MYSQL_HOST || process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.MYSQL_PORT || process.env.DB_PORT || '3306', 10),
    user: process.env.MYSQL_USER || process.env.DB_USER || 'root',
    password: process.env.MYSQL_PASSWORD || process.env.DB_PWD || '',
    database: process.env.MYSQL_DATABASE || process.env.DB_NAME || 'frontdesk_ai',
    timezone: 'Z',
    dateStrings: false,
    supportBigNumbers: true,
    bigNumberStrings: false,
    multipleStatements: false,
  },
  pool: {
    min: 2,
    max: 20,
    acquireTimeoutMillis: 60000,
    idleTimeoutMillis: 600000,
  },
  migrations: {
    directory: './migrations',
    tableName: 'knex_migrations',
  },
};

// Enable SSL for production/GCP MySQL
if (process.env.NODE_ENV === 'production' || process.env.MYSQL_SSL === 'true') {
  config.connection.ssl = {
    rejectUnauthorized: false,
  };
}

module.exports = config;
