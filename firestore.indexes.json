{"indexes": [{"collectionGroup": "agent-location-mappings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "agent-location-mappings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "availableCalendarSlots", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "phoneNumber", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "isOutboundCall", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "priorityScore", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "priorityScore", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "priorityScore", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "priorityScore", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "phoneNumber", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "priorityScore", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "calls", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}, {"fieldPath": "isOutboundCall", "order": "DESCENDING"}]}, {"collectionGroup": "callSessions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "callerPhone", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "locations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "on-call-notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "on-call-notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "on-call-notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "scheduleId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "on-call-schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "on-call-schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doctorId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "on-call-schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "clinicId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}]}, {"collectionGroup": "on-call-schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "on-call-schedules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "ASCENDING"}, {"fieldPath": "startTime", "order": "ASCENDING"}, {"fieldPath": "endTime", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}]}, {"collectionGroup": "patientReferences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "provider", "order": "ASCENDING"}, {"fieldPath": "providerId", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "preferences.isAppointmentNotificationsEnabled", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "locationId", "order": "ASCENDING"}, {"fieldPath": "preferences.isAppointmentNotificationsEnabled", "order": "DESCENDING"}]}], "fieldOverrides": []}