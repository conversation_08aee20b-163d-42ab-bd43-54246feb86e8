# Story: Nextech API Basic Services Implementation

<version>1.0.0</version>
<status>Completed</status>
<epic>Nextech API Provider Implementation</epic>
<story-points>5</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the implementation of the basic Nextech API services for clinics and locations. It includes creating a robust HTTP client with error handling and retries, implementing the clinic and location services that conform to our provider interfaces, and mapping Nextech API responses to our common data models.

## Acceptance Criteria

- [x] Base HTTP client implemented with proper error handling and retry logic
- [x] Clinic service implemented following the IClinicService interface
- [x] Location service implemented following the ILocationService interface
- [x] Data mapping between Nextech API responses and common data models
- [x] Rate limiting and pagination handling implemented
- [x] Comprehensive error handling for service-specific errors
- [x] Unit tests written and passing for all components
- [x] Documentation updated with service implementation details

## Technical Details

### Base HTTP Client

The HTTP client will:
- [x] Build on the authentication work from Story-2
- [x] Handle common HTTP errors (4xx, 5xx)
- [x] Implement retry logic with exponential backoff
- [x] Support request timeouts and cancellation
- [x] Properly handle and parse Nextech API responses
- [x] Support pagination for list endpoints
- [x] Follow rate limiting guidelines (20 requests per second)

```typescript
class NextechHttpClient {
  private authClient: NextechAuthClient;
  private baseUrl: string;
  private practiceId: string;
  
  constructor(config: NextechConfig, authClient: NextechAuthClient) {
    this.authClient = authClient;
    this.baseUrl = config.baseUrl;
    this.practiceId = config.practiceId;
  }
  
  async get<T>(path: string, queryParams?: Record<string, string>): Promise<T> {
    // Implementation with error handling, retries, and auth token management
  }
  
  async post<T>(path: string, data: unknown): Promise<T> {
    // Implementation with error handling, retries, and auth token management
  }
  
  // Other methods: put, patch, delete
}
```

### Clinic Service

The clinic service will:
- [x] Implement all methods in the IClinicService interface
- [x] Map Nextech clinic data to our common Clinic model
- [x] Handle Nextech-specific filtering and pagination
- [x] Implement proper error handling for clinic-specific errors

```typescript
class NextechClinicService implements IClinicService {
  private client: NextechHttpClient;
  
  constructor(client: NextechHttpClient) {
    this.client = client;
  }
  
  async getClinics(filters?: Record<string, unknown>): Promise<Clinic[]> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
  
  async getClinicById(id: string): Promise<Clinic | null> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
  
  async getClinicByPhone(phone: string): Promise<Clinic | null> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
}
```

### Location Service

The location service will:
- [x] Implement all methods in the ILocationService interface
- [x] Map Nextech location data to our common Location model
- [x] Handle Nextech-specific filtering and pagination
- [x] Implement proper error handling for location-specific errors

```typescript
class NextechLocationService implements ILocationService {
  private client: NextechHttpClient;
  
  constructor(client: NextechHttpClient) {
    this.client = client;
  }
  
  async getLocations(filters?: Record<string, unknown>): Promise<Location[]> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
  
  async getLocationById(id: string): Promise<Location | null> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
  
  async getLocationByPhone(phone: string): Promise<Location | null> {
    // Implementation with Nextech API calls, data mapping, and error handling
  }
}
```

### Data Mapping

- [x] Create mapper functions to convert Nextech API responses to common models
- [x] Handle edge cases and missing data gracefully
- [x] Ensure consistent IDs and provider info across all entities

## Tasks

- [x] Enhance HTTP client with retry logic and error handling
- [x] Create Nextech clinic API client methods
- [x] Implement NextechClinicService
- [x] Create Nextech location API client methods
- [x] Implement NextechLocationService
- [x] Create data mappers for clinic and location entities
- [x] Write unit tests for all components
- [x] Update documentation with service implementation details
- [x] Integrate services with the Nextech provider

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication

## Risks and Mitigations

- **Risk**: Nextech API may have inconsistent data formats or missing fields
  **Mitigation**: Implement robust data validation and provide sensible defaults

- **Risk**: Rate limiting may impact performance
  **Mitigation**: Implement caching and rate limit awareness to optimize API usage

- **Risk**: Pagination handling may be complex for large datasets
  **Mitigation**: Create a reusable pagination utility that works across services

- **Risk**: Service endpoints may change in future Nextech API versions
  **Mitigation**: Isolate API-specific code to minimize impact of future changes

## Resources

- [Nextech API Locations Documentation](https://nextechsystems.github.io/practiceplusapidocspub/#tag/Locations)
- [Nextech API Practices Documentation](https://nextechsystems.github.io/practiceplusapidocspub/#tag/Practices)
- [Architecture Document](.ai/arch.md)

## Notes

- This story focuses on implementing the basic Nextech services (Clinic and Location)
- These services will serve as a foundation for more complex services (Patient, User, Appointment)
- The HTTP client should be designed to be reusable across all Nextech services

## Chat Log

*Updated 2024-03-30*

- Created initial story draft for implementing Nextech API Basic Services
- Implemented mappers for Nextech API responses to common models
- Implemented NextechClinicService following the IClinicService interface
- Implemented NextechLocationService following the ILocationService interface
- Created comprehensive unit tests for all components
- Verified all tests are passing
- Integrated services with the Nextech provider 