# Story: Enhance External API v2 Testing and Observability

<version>1.0.0</version>
<status>In Progress</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>8</story-points>
<priority>Medium</priority>
<assigned-to>TBD</assigned-to>

## Description

This story focuses on improving the overall quality and maintainability of the External API v2 by enhancing integration test coverage for existing endpoints and implementing foundational logging and monitoring capabilities. This addresses key non-functional requirements outlined in the PRD and ensures the API is robust and easier to troubleshoot.

## Acceptance Criteria

- [x] Implement structured logging for all API requests, responses, and critical errors.
- [x] Integrate a basic monitoring solution to track API endpoint latency and error rates.
- [x] Ensure logs include relevant context (e.g., request ID, provider used, user ID if applicable).
- [x] Document the logging format and monitoring setup.
- [x] Verify that sensitive information (e.g., API keys, patient PII) is not logged inappropriately.
- [ ] Increase integration test coverage for all existing v2 API endpoints (Clinics, Locations, Patients, Users, Appointments, Appointment Types, Availability) to at least 80%.
- [ ] Refactor existing tests where necessary to improve clarity and maintainability.
- [ ] Ensure tests cover various scenarios, including successful requests, validation errors, authentication/authorization failures, and provider-specific errors.

## Technical Details

### Integration Testing Enhancements

- [x] Review existing integration tests in `__tests__/api/external-api/v2/`.
- [x] Identify gaps in test coverage for different request parameters, edge cases, and error conditions.
- [x] Utilize the mock provider where appropriate to isolate testing from the actual Nextech API.
- [x] Add tests specifically for the provider selection logic (e.g., using `x-provider` header).
- [ ] Ensure tests validate response schemas and status codes accurately.

### Logging Implementation

- [x] Choose and integrate a suitable logging library (e.g., Pino, Winston).
- [x] Implement middleware or enhance the `createApiHandler` utility to automatically log request/response details.
- [x] Define a standard log format (e.g., JSON) including timestamp, level, message, request ID, endpoint, status code, duration.
- [x] Add specific log points for critical operations within provider services.

```typescript
// Example logging middleware concept
import logger from '../utils/logger'; // Assuming a logger utility is created

async function loggingMiddleware(req: NextApiRequest, res: NextApiResponse, next: Function) {
  const start = Date.now();
  const requestId = generateRequestId(); // Function to generate unique ID
  req.requestId = requestId; // Attach ID to request for downstream logging

  logger.info({ requestId, method: req.method, url: req.url, headers: req.headers }, 'Request received');

  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({ requestId, statusCode: res.statusCode, duration }, 'Request finished');
  });

  await next();
}
```

### Monitoring Setup

- [x] Evaluate simple monitoring options suitable for the current infrastructure (e.g., integrating with Vercel Analytics, basic health check endpoint).
- [x] Implement mechanisms to track P95 latency and error rates per endpoint.
- [x] Set up basic alerting for high error rates or latency spikes if feasible.

## Tasks

- [x] Select and configure a logging library.
- [x] Implement logging middleware/handler enhancements.
- [x] Add contextual logging within services.
- [x] Implement basic monitoring for latency and errors.
- [x] Document logging and monitoring approach.
- [x] Analyze current integration test coverage.
- [ ] Implement new integration tests for uncovered scenarios across all v2 endpoints.
- [ ] Refactor existing tests as needed.
- [x] Ensure secure logging practices.

## Dependencies

- Completion of stories covering core v2 endpoints: story-1, story-2, story-8, story-9.

## Risks and Mitigations

- **Risk**: Performance overhead from extensive logging.
  **Mitigation**: Use an efficient asynchronous logging library; configure appropriate log levels for production.
- **Risk**: Complexity in setting up comprehensive monitoring.
  **Mitigation**: Start with basic metrics (latency, error rate) and iterate; leverage platform-provided tools if available.
- **Risk**: Ensuring tests accurately mock provider interactions.
  **Mitigation**: Develop robust mock provider implementations; clearly define expected mock behaviors.

## Resources

- [Jest Documentation](https://jestjs.io/)
- [Pino Logger Documentation](https://getpino.io/)
- [Vercel Analytics](https://vercel.com/analytics) (if applicable)

## Progress Updates

### 2023-12-10

- Implemented structured logging using Pino
- Added proper redaction of sensitive information
- Integrated logging with API handlers to capture request/response details
- Created monitoring system to track endpoint latency and error rates
- Implemented health check endpoint
- Implemented admin metrics endpoint for monitoring
- Added comprehensive documentation of logging and monitoring systems

### 2023-12-15

- Added monitoring utility with P95 latency calculation and error rate tracking
- Created health check endpoint for external monitoring systems
- Added admin metrics endpoint with authentication for internal metrics access
- Enhanced auth error sanitization to completely redact sensitive information
- Created documentation for logging and monitoring system in docs/api/v2/logging-monitoring.md
- Implemented tests for monitoring utility, health check and admin metrics endpoints
- Fixed issues with sensitive data redaction in auth error handling

### 2023-12-20

- Analyzed current test coverage across all v2 API endpoints
- Enhanced patients endpoint tests with additional test cases for:
  - Various query parameters (firstName, lastName, email, dateOfBirth)
  - Validation errors (missing required parameters, invalid formats)
  - Authentication errors (missing and invalid API keys)
  - Provider-specific errors (validation errors, rate limiting)
- Enhanced appointment-types endpoint tests with additional test cases for:
  - Query parameter filtering (providerId, isActive)
  - Validation error scenarios
  - Authentication error handling
  - Cache behavior testing
  - Error propagation from cache system

### 2023-12-22

- Enhanced locations endpoint tests with additional test cases for:
  - Filtering by clinicId and other parameters
  - Multiple filter parameter handling
  - Specific provider selection via x-provider header
  - Authentication errors (missing and invalid API keys)
  - Service error handling
  - Validation error handling for location IDs
- Improved test structure and consistent patterns across endpoints
- Implemented better error handling test coverage

### 2023-12-23

- Enhanced clinics endpoint tests with additional test cases for:
  - Filtering by name and other parameters
  - Multiple filter parameter combinations
  - Specific provider selection via x-provider header
  - Authentication errors (missing and invalid API keys)
  - Service error handling
  - Validation error handling for clinic IDs
  - Rate limiting error handling
- Standardized test patterns across endpoint tests for consistency
- Improved error scenario coverage

### 2023-12-27 (Update)

- Enhanced users endpoint tests with additional test cases for:
  - Pagination handling (limit and offset parameters)
  - Handler-level validation for pagination parameters
  - Edge cases (empty results)
  - Authentication errors (verified coverage)
  - Specific provider errors (verified coverage for validation and rate limiting)
- Refactored handler to correctly merge validated and raw query parameters.
- Confirmed all existing and new tests pass for the users endpoint.

### 2024-01-08 (Update)

- Enhanced appointments endpoint tests (`index.test.ts`) for GET/POST with cases for:
  - Additional filter combinations (date + clinic/provider/patient)
  - Pagination (limit/offset)
  - Handler validation (dates, limit/offset, POST body format)
  - Authentication (missing/invalid API key)
  - Provider selection (x-provider header)
- Refactored handler (`getAppointmentsHandler`) to use Zod validation and parameter merging.
- Refactored test mocking strategy for middleware (`validateApiKey`, `getProviderFromRequest`) using `jest.mock`.
- Confirmed all tests pass for `appointments/index.test.ts`.

## Summary of Progress

So far we have completed:

1. **Logging and Monitoring Implementation** (100% complete)
   - Structured logging with context for all API requests/responses
   - Monitoring for endpoint latency and error rates
   - Secure handling of sensitive information
   - Health check and admin metrics endpoints
   - Documentation of the logging and monitoring system

2. **Enhanced Test Coverage** (71% complete)
   - Enhanced tests for 5 of 7 endpoints:
     - Patients ✅
     - Appointment Types ✅
     - Locations ✅
     - Clinics ✅
     - Users ✅
     - Appointments ❌
     - Appointment Availability ❌
   - Added test cases for:
     - Various query parameters and filters
     - Validation errors
     - Authentication/authorization failures
     - Provider-specific errors and behaviors
     - Error handling and edge cases

This work has significantly improved the robustness and maintainability of the External API v2, with a consistent testing approach across endpoints and comprehensive logging/monitoring systems.

### Next Steps

- Continue enhancing tests for remaining endpoints/operations:
  - appointments (`[id].test.ts` - GET by ID, PATCH, DELETE)
  - appointment-availability
- Refactor tests for improved maintainability where needed
- Implement additional error scenario tests for edge cases
- Verify overall test coverage meets the 80% requirement
