# Story: External API v2 Appointment Endpoints Implementation

<version>1.0.0</version>
<status>Draft</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>10</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the implementation of RESTful API endpoints for appointment management in the External API v2. These endpoints will allow clients to retrieve, create, update, and cancel appointments through provider-agnostic interfaces, initially using the Nextech provider implementation. The appointment endpoints are critical for enabling scheduling functionality in the Front Desk Portal system and must be robust, secure, and performant.

## Acceptance Criteria

- [ ] Create GET /api/external-api/v2/appointments endpoint for retrieving appointments
- [ ] Create POST /api/external-api/v2/appointments endpoint for booking new appointments
- [ ] Create PATCH /api/external-api/v2/appointments/{id} endpoint for updating appointments
- [ ] Create DELETE /api/external-api/v2/appointments/{id} endpoint for cancelling appointments
- [ ] Implement filtering capabilities for appointment retrieval (by date range, patient, provider, location, clinic)
- [ ] Ensure proper API key validation and authorization checks
- [ ] Implement request validation using Zod schemas
- [ ] Add proper error handling for all scenarios (including provider-specific errors)
- [ ] Implement pagination for list endpoints
- [ ] Add comprehensive Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Implement integration tests with the Nextech provider
- [ ] Ensure proper provider selection from request

## Technical Details

### Appointment Retrieval Endpoint

The GET appointment endpoint will:
- [ ] Support retrieving all appointments or a specific appointment by ID
- [ ] Accept query parameters for filtering (date range, patient ID, provider ID, location ID, clinic ID)
- [ ] Use the provider's appointment service to retrieve data
- [ ] Return data in a consistent format matching the Appointment model
- [ ] Support pagination through `limit` and `offset` parameters
- [ ] Return proper status codes for different scenarios (200, 400, 401, 403, 404, 500)

```typescript
// GET /api/external-api/v2/appointments
async function getAppointmentsHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Get a specific appointment by ID
    if (req.query.id) {
      const appointment = await appointmentService.getAppointmentById(req.query.id as string);
      if (!appointment) {
        return res.status(404).json({ message: 'Appointment not found' });
      }
      return res.status(200).json(appointment);
    }

    // Get appointments with filtering
    const appointments = await appointmentService.getAppointments(req.query);
    return res.status(200).json(appointments);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Appointment Creation Endpoint

The POST appointment endpoint will:
- [ ] Accept appointment creation data in request body
- [ ] Validate request body against a Zod schema
- [ ] Use the provider's appointment service to create the appointment
- [ ] Return the created appointment with appropriate status code (201)
- [ ] Include location header with the URL of the newly created resource

```typescript
// POST /api/external-api/v2/appointments
async function createAppointmentHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();

    // Create new appointment
    const createdAppointment = await appointmentService.createAppointment(req.body);

    // Set location header and return created appointment
    res.setHeader('Location', `/api/external-api/v2/appointments/${createdAppointment.id}`);
    return res.status(201).json(createdAppointment);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Appointment Update Endpoint

The PATCH appointment endpoint will:
- [ ] Accept appointment ID in the URL path
- [ ] Accept partial appointment data in the request body
- [ ] Validate request body against a Zod schema
- [ ] Use the provider's appointment service to update the appointment
- [ ] Return the updated appointment with appropriate status code (200)

```typescript
// PATCH /api/external-api/v2/appointments/[id]
async function updateAppointmentHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();
    const appointmentId = req.query.id as string;

    // Update appointment
    const updatedAppointment = await appointmentService.updateAppointment(appointmentId, req.body);

    return res.status(200).json(updatedAppointment);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Appointment Cancellation Endpoint

The DELETE appointment endpoint will:
- [ ] Accept appointment ID in the URL path
- [ ] Optionally accept cancellation reason in query parameters
- [ ] Use the provider's appointment service to cancel the appointment
- [ ] Return success status with appropriate code (204 No Content)

```typescript
// DELETE /api/external-api/v2/appointments/[id]
async function cancelAppointmentHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();
    const appointmentId = req.query.id as string;
    const reason = req.query.reason as string | undefined;

    // Cancel appointment
    await appointmentService.cancelAppointment(appointmentId, reason);

    return res.status(204).send();
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Request Validation

- [ ] Create Zod schemas for request validation
- [ ] Validate query parameters and request bodies before processing
- [ ] Return descriptive error messages for invalid requests

```typescript
import { z } from 'zod';

// Schema for GET /api/external-api/v2/appointments query parameters
const getAppointmentsQuerySchema = z.object({
  id: z.string().optional(),
  patientId: z.string().optional(),
  practitionerId: z.string().optional(),
  locationId: z.string().optional(),
  clinicId: z.string().optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

// Schema for POST /api/external-api/v2/appointments request body
const createAppointmentSchema = z.object({
  patientId: z.string(),
  practitionerId: z.string(),
  locationId: z.string(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
  startTime: z.string().regex(/^\d{2}:\d{2}$/), // HH:MM
  endTime: z.string().regex(/^\d{2}:\d{2}$/), // HH:MM
  appointmentTypeId: z.string(),
  notes: z.string().optional(),
});

// Schema for PATCH /api/external-api/v2/appointments/[id] request body
const updateAppointmentSchema = z.object({
  practitionerId: z.string().optional(),
  locationId: z.string().optional(),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/).optional(), // YYYY-MM-DD
  startTime: z.string().regex(/^\d{2}:\d{2}$/).optional(), // HH:MM
  endTime: z.string().regex(/^\d{2}:\d{2}$/).optional(), // HH:MM
  appointmentTypeId: z.string().optional(),
  notes: z.string().optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: "At least one field must be provided for update"
});
```

### Combined Endpoint Handler

- [ ] Implement a single handler that routes to specific functions based on HTTP method
- [ ] Use NextJS API routes for dynamic routes (`[id].ts`)

```typescript
// Combined handler for /api/external-api/v2/appointments
export default createApiHandler(async function appointmentsHandler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // GET request for multiple appointments or single appointment
  if (req.method === 'GET') {
    return getAppointmentsHandler(req, res);
  }

  // POST request to create a new appointment
  if (req.method === 'POST') {
    return createAppointmentHandler(req, res);
  }

  // PATCH request to update an appointment (handled in [id].ts)

  // DELETE request to cancel an appointment (handled in [id].ts)

  // Handle unsupported methods
  return res.status(405).json({ message: 'Method not allowed' });
}, {
  middleware: [validateApiKey, requireAppointmentAccess],
});
```

### Error Handling

- [ ] Handle provider-specific errors and map them to standard HTTP status codes
- [ ] Provide clear error messages for clients
- [ ] Log detailed error information for troubleshooting

```typescript
// Example error handling middleware
export const handleAppointmentErrors = async (
  error: unknown,
  req: NextApiRequest,
  res: NextApiResponse
) => {
  if (error instanceof AppointmentConflictError) {
    return res.status(409).json({
      message: 'Appointment time slot is not available',
      details: error.details
    });
  }

  if (error instanceof ResourceNotFoundError) {
    return res.status(404).json({
      message: 'Requested resource not found',
      details: error.details
    });
  }

  if (error instanceof ValidationError) {
    return res.status(400).json({
      message: 'Invalid request data',
      details: error.details
    });
  }

  // Default error handling
  console.error('Unhandled appointment error:', error);
  return res.status(500).json({
    message: 'An unexpected error occurred while processing your request'
  });
};
```

### Security and Performance Considerations

- [ ] Implement proper rate limiting for appointment endpoints
- [ ] Ensure authorization checks for sensitive operations
- [ ] Cache appointment type/slot information where appropriate
- [ ] Implement idempotency for appointment creation/updates

## Tasks

- [ ] Create Zod schemas for request validation
- [ ] Implement GET /api/external-api/v2/appointments endpoint
- [ ] Implement POST /api/external-api/v2/appointments endpoint
- [ ] Implement PATCH /api/external-api/v2/appointments/[id] endpoint
- [ ] Implement DELETE /api/external-api/v2/appointments/[id] endpoint
- [ ] Add authorization middleware for appointment operations
- [ ] Add appointment-specific error handling
- [ ] Implement rate limiting for appointment endpoints
- [ ] Add Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Create integration tests with the Nextech provider
- [ ] Update API documentation with usage examples
- [ ] Implement caching for appointment types and available slots
- [ ] Add idempotency keys for appointment creation/updates

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication
- Completed story-3: Nextech API Basic Services
- Completed story-4: Nextech API Patient and User Services
- Completed story-5: Nextech API Appointment Service Implementation
- Completed story-6: External API v2 Clinic and Location Endpoints
- Completed story-7: External API v2 Patient and User Endpoints

## Risks and Mitigations

- **Risk**: Concurrent appointment booking leading to conflicts
  **Mitigation**: Implement optimistic concurrency control and clear error handling for conflicts

- **Risk**: Performance issues with appointment availability checking
  **Mitigation**: Implement caching for appointment slots and optimize queries

- **Risk**: Rate limiting from the Nextech API affecting appointment operations
  **Mitigation**: Implement retries with exponential backoff and clear user feedback

- **Risk**: Incorrect appointment data leading to scheduling issues
  **Mitigation**: Implement comprehensive validation and verification steps

- **Risk**: Time zone issues causing scheduling confusion
  **Mitigation**: Standardize on UTC for all API operations and document time zone handling clearly

## Resources

- [Nextech API Appointment Documentation](https://nextechsystems.github.io/practiceplusapidocspub/PatientAppointment/Appointment)
- [Next.js Dynamic API Routes](https://nextjs.org/docs/api-routes/dynamic-api-routes)
- [Zod Documentation](https://github.com/colinhacks/zod)
- [RESTful API Best Practices](https://www.vinaysahni.com/best-practices-for-a-pragmatic-restful-api)