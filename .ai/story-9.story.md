# Story: External API v2 Appointment Types and Availability Endpoints Implementation

<version>1.0.0</version>
<status>Draft</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>8</story-points>
<priority>Medium</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the implementation of RESTful API endpoints for appointment types and availability checking in the External API v2. These endpoints will allow clients to retrieve available appointment types, check slot availability, and find open slots for scheduling. Building on the core appointment management endpoints from story-8, these endpoints will provide the additional functionality needed for a complete scheduling experience while maintaining provider-agnostic interfaces.

## Acceptance Criteria

- [ ] Create GET /api/external-api/v2/appointment-types endpoint for retrieving appointment types
- [ ] Create GET /api/external-api/v2/appointment-availability endpoint for checking slot availability
- [ ] Implement filtering capabilities for both endpoints (by provider, location, date range)
- [ ] Ensure proper API key validation and authorization checks
- [ ] Implement request validation using Zod schemas
- [ ] Add proper error handling for all scenarios
- [ ] Implement caching for appointment type data
- [ ] Add comprehensive Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Implement integration tests with the Nextech provider
- [ ] Ensure proper provider selection from request

## Technical Details

### Appointment Types Endpoint

The appointment types endpoint will:
- [ ] Support retrieving all appointment types or filtering by ID, location, or provider
- [ ] Map provider-specific appointment type data to a common model
- [ ] Cache appointment type data to improve performance
- [ ] Return data in a consistent format matching the AppointmentType model
- [ ] Return proper status codes for different scenarios (200, 400, 401, 403, 404, 500)

```typescript
// GET /api/external-api/v2/appointment-types
async function getAppointmentTypesHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();
    
    // Validate query parameters using Zod
    const validatedQuery = appointmentTypesQuerySchema.parse(req.query);
    
    // Get appointment types with filtering
    const appointmentTypes = await appointmentService.getAppointmentTypes(validatedQuery);
    
    return res.status(200).json(appointmentTypes);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Appointment Availability Endpoint

The appointment availability endpoint will:
- [ ] Accept query parameters for date range, provider, location, and appointment type
- [ ] Use the provider's appointment service to check availability
- [ ] Return available slots in a consistent time slot format
- [ ] Support filtering by various criteria
- [ ] Return proper status codes for different scenarios (200, 400, 401, 403, 500)

```typescript
// GET /api/external-api/v2/appointment-availability
async function getAppointmentAvailabilityHandler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const appointmentService = provider.getAppointmentService();
    
    // Validate query parameters using Zod
    const validatedQuery = appointmentAvailabilityQuerySchema.parse(req.query);
    
    // Get appointment availability with filtering
    const availableSlots = await appointmentService.getAvailableSlots(validatedQuery);
    
    return res.status(200).json(availableSlots);
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}
```

### Request Validation

- [ ] Create Zod schemas for request validation
- [ ] Validate query parameters before processing
- [ ] Return descriptive error messages for invalid requests

```typescript
import { z } from 'zod';

// Schema for GET /api/external-api/v2/appointment-types query parameters
const appointmentTypesQuerySchema = z.object({
  id: z.string().optional(),
  providerId: z.string().optional(),
  locationId: z.string().optional(),
  clinicId: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

// Schema for GET /api/external-api/v2/appointment-availability query parameters
const appointmentAvailabilityQuerySchema = z.object({
  appointmentTypeId: z.string(),
  providerId: z.string().optional(),
  locationId: z.string().optional(),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/), // YYYY-MM-DD
  provider: z.string().optional(),
});
```

### Caching Implementation

- [ ] Implement a caching layer for appointment types
- [ ] Set appropriate cache expiration times
- [ ] Implement cache invalidation when appointment types change
- [ ] Include cache headers in API responses

```typescript
// Example caching implementation for appointment types
const CACHE_TTL = 3600; // 1 hour in seconds
const appointmentTypesCache = new Map<string, {data: AppointmentType[], timestamp: number}>();

async function getCachedAppointmentTypes(
  appointmentService: IAppointmentService,
  query: Record<string, unknown>
): Promise<AppointmentType[]> {
  const cacheKey = JSON.stringify(query);
  const now = Date.now();
  const cachedData = appointmentTypesCache.get(cacheKey);
  
  // Return cached data if it exists and is not expired
  if (cachedData && (now - cachedData.timestamp) < CACHE_TTL * 1000) {
    return cachedData.data;
  }
  
  // Fetch fresh data and update cache
  const appointmentTypes = await appointmentService.getAppointmentTypes(query);
  appointmentTypesCache.set(cacheKey, {
    data: appointmentTypes,
    timestamp: now
  });
  
  return appointmentTypes;
}
```

### Model Definitions

- [ ] Define AppointmentType model
- [ ] Define AvailableSlot model

```typescript
interface AppointmentType {
  id: string;
  name: string;
  description: string;
  duration: number; // in minutes
  color?: string;
  isActive: boolean;
  providerInfo: ProviderInfo;
}

interface AvailableSlot {
  startDateTime: string; // ISO 8601 format
  endDateTime: string; // ISO 8601 format
  providerId: string;
  locationId: string;
  appointmentTypeId: string;
}
```

### Swagger Documentation

- [ ] Add comprehensive Swagger/OpenAPI annotations for both endpoints
- [ ] Document all request parameters, response formats, and error scenarios
- [ ] Include examples for common use cases

## Tasks

- [ ] Define AppointmentType and AvailableSlot models
- [ ] Enhance IAppointmentService interface to include appointment type and availability methods
- [ ] Create Zod schemas for request validation
- [ ] Implement GET /api/external-api/v2/appointment-types endpoint
- [ ] Implement GET /api/external-api/v2/appointment-availability endpoint
- [ ] Implement caching layer for appointment types
- [ ] Add Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Create integration tests with the Nextech provider
- [ ] Update API documentation with usage examples

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication
- Completed story-3: Nextech API Basic Services
- Completed story-4: Nextech API Patient and User Services
- Completed story-5: Nextech API Appointment Service Implementation
- Completed story-8: External API v2 Appointment Endpoints Implementation

## Risks and Mitigations

- **Risk**: Provider APIs may have limited or different appointment type/availability functionality
  **Mitigation**: Create adapter functions to normalize differences between providers

- **Risk**: Availability checking may be resource-intensive and slow
  **Mitigation**: Implement efficient caching and optimize query parameters

- **Risk**: Time zone issues causing confusion in availability slots
  **Mitigation**: Standardize on UTC for all API operations and clearly document time zone handling

- **Risk**: Cache invalidation might be complex across distributed deployments
  **Mitigation**: Implement a versioning mechanism for cached data and appropriate TTL settings

- **Risk**: Performance impact of checking many slots across wide date ranges
  **Mitigation**: Implement reasonable limits on date ranges and pagination for results

## Resources

- [Nextech API Appointment Types Documentation](https://nextechsystems.github.io/practiceplusapidocspub/PatientAppointment/AppointmentType)
- [Nextech API Appointment Slot Documentation](https://nextechsystems.github.io/practiceplusapidocspub/PatientAppointment/Slot)
- [Next.js API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [Zod Documentation](https://github.com/colinhacks/zod)
- [Cache Control HTTP Headers](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control) 