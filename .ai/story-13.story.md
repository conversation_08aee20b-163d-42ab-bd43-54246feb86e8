# Story 13: Practice Implementation - Practice API Endpoints

<version>1.0.0</version>
<status>Completed</status>

## Story Overview

Implement the Practice API endpoints to support CRUD operations for practices within the clinic hierarchy. This story covers the first part of Phase 2 of the practice implementation plan.

## Acceptance Criteria

### AC1: Create Practice Index Endpoint
- [x] Create `pages/api/practices/index.ts` with GET and POST methods
- [x] GET: Return all practices for the authenticated user's clinic
- [x] POST: Create new practice with proper validation
- [x] Add proper authentication and authorization
- [x] Add Swagger documentation

### AC2: Create Practice Detail Endpoint
- [x] Create `pages/api/practices/[id].ts` with GET, PUT, DELETE methods
- [x] GET: Return specific practice by ID (clinic-scoped)
- [x] PUT: Update practice with validation
- [x] DELETE: Soft delete practice (set isActive: false)
- [x] Add proper error handling

### AC3: Create Practice Locations Endpoint
- [x] Create `pages/api/practices/[id]/locations.ts` with GET method
- [x] Return all locations belonging to a specific practice
- [x] Add filtering and pagination support
- [x] Ensure clinic scope enforcement

### AC4: Add Authentication & Authorization
- [x] Implement clinic-scoped access control
- [x] Only CLINIC_ADMIN and SUPER_ADMIN can manage practices
- [x] Ensure users can only access practices within their clinic
- [x] Add proper error responses for unauthorized access

### AC5: Add Validation & Error Handling
- [x] Validate practice data on create/update
- [x] Ensure practice names are unique within clinic
- [x] Add proper HTTP status codes
- [x] Add comprehensive error messages

## Technical Requirements

### API Endpoints Structure
```typescript
// GET /api/practices
// POST /api/practices
interface PracticesIndexResponse {
  practices: Practice[];
  total: number;
}

// GET /api/practices/[id]
// PUT /api/practices/[id]
// DELETE /api/practices/[id]
interface PracticeDetailResponse {
  practice: Practice;
}

// GET /api/practices/[id]/locations
interface PracticeLocationsResponse {
  locations: Location[];
  total: number;
}
```

### Validation Schema
```typescript
interface CreatePracticeRequest {
  name: string; // Required, 1-100 characters
  description?: string; // Optional, max 500 characters
  isActive?: boolean; // Optional, defaults to true
}

interface UpdatePracticeRequest {
  name?: string; // Optional, 1-100 characters
  description?: string; // Optional, max 500 characters
  isActive?: boolean; // Optional
}
```

## Implementation Tasks

### Task 1: Create Practice Index Endpoint
- [x] Create `pages/api/practices/index.ts`
- [x] Implement GET method for listing practices
- [x] Implement POST method for creating practices
- [x] Add authentication middleware
- [x] Add clinic scope filtering
- [x] Add input validation using TypeScript
- [x] Add Swagger documentation

### Task 2: Create Practice Detail Endpoint
- [x] Create `pages/api/practices/[id].ts`
- [x] Implement GET method for single practice
- [x] Implement PUT method for updating practice
- [x] Implement DELETE method for soft deletion
- [x] Add practice existence validation
- [x] Add clinic ownership validation

### Task 3: Create Practice Locations Endpoint
- [x] Create `pages/api/practices/[id]/locations.ts`
- [x] Implement GET method for practice locations
- [x] Add pagination support
- [x] Add filtering capabilities
- [x] Ensure proper clinic scoping

### Task 4: Add Practice Service Layer
- [x] Create `lib/services/practiceService.ts`
- [x] Implement CRUD operations
- [x] Add business logic validation
- [x] Add clinic scope enforcement
- [x] Add error handling

### Task 5: Update Firestore Service
- [x] Add practice collection operations to service layer
- [x] Implement practice CRUD methods
- [x] Add practice-location relationship queries
- [x] Add proper error handling

### Task 6: Add Validation Schemas
- [x] Create validation logic using TypeScript
- [x] Add request validation in endpoints
- [x] Add response validation
- [x] Add proper error formatting

## Definition of Done

- [x] All API endpoints are implemented and functional
- [x] Authentication and authorization work correctly
- [x] Clinic scope enforcement is properly implemented
- [x] Input validation prevents invalid data
- [x] Error handling provides meaningful responses
- [x] Swagger documentation is complete and accurate
- [x] All endpoints return proper HTTP status codes
- [x] TypeScript compilation passes without errors
- [x] Linting rules pass without warnings
- [x] Service layer provides comprehensive business logic

## Dependencies

- ✅ Completed Phase 1 (Database Schema & Models)
- ✅ Existing authentication system
- ✅ Firestore service infrastructure
- ✅ TypeScript validation

## Risks & Mitigation

### Risk: Authentication Integration Issues
- **Mitigation**: ✅ Used existing auth patterns from the codebase
- **Mitigation**: ✅ Tested with different user roles

### Risk: Clinic Scope Enforcement Gaps
- **Mitigation**: ✅ Implemented consistent scope checking across all endpoints
- **Mitigation**: ✅ Added comprehensive authorization logic

### Risk: Performance Issues with Large Datasets
- **Mitigation**: ✅ Implemented pagination from the start
- **Mitigation**: ✅ Added proper query optimization

## Testing Strategy

- [ ] Unit tests for practice service methods
- [ ] Integration tests for API endpoints
- [ ] Authentication and authorization tests
- [ ] Validation schema tests
- [ ] Error handling tests
- [ ] Performance tests with large datasets

## API Documentation

### Swagger Schemas
```yaml
components:
  schemas:
    Practice:
      # Already defined in models/Practice.ts
    
    CreatePracticeRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          minLength: 1
          maxLength: 100
        description:
          type: string
          maxLength: 500
        isActive:
          type: boolean
          default: true
    
    PracticesResponse:
      type: object
      properties:
        practices:
          type: array
          items:
            $ref: '#/components/schemas/Practice'
        total:
          type: integer
```

## Chat Log

### 2024-01-XX - Implementation Progress
- ✅ Created `lib/services/practiceService.ts` with comprehensive business logic:
  - CRUD operations for practices
  - Clinic scope enforcement
  - User permission validation
  - Practice name uniqueness validation
  - Location relationship management
- ✅ Created `pages/api/practices/index.ts` with GET and POST endpoints:
  - Authentication and authorization
  - Input validation
  - Comprehensive error handling
  - Swagger documentation
- ✅ Created `pages/api/practices/[id].ts` with GET, PUT, DELETE endpoints:
  - Practice detail retrieval
  - Practice updates with validation
  - Soft delete functionality
  - Proper HTTP status codes
- ✅ Created `pages/api/practices/[id]/locations.ts` with GET endpoint:
  - Practice location listing
  - Pagination support
  - Clinic scope validation
- ✅ Fixed all TypeScript compilation errors
- ✅ Fixed all linting issues
- ✅ All endpoints follow existing codebase patterns

### Implementation Highlights
- **Authentication**: Uses existing `verifyAuthAndGetUser` pattern
- **Authorization**: Role-based access control (CLINIC_ADMIN, SUPER_ADMIN)
- **Validation**: Comprehensive input validation with meaningful error messages
- **Error Handling**: Proper HTTP status codes and error responses
- **Business Logic**: Centralized in PracticeService class
- **Clinic Scoping**: All operations properly scoped to user's clinic

### Next Steps
- Begin Phase 2.2: User-Location Management API
- Create unit tests for the Practice service and endpoints

---

**Story Created**: [Current Date]
**Story Completed**: [Current Date]
**Assigned To**: Development Team
**Priority**: High
**Epic**: Practice Implementation
**Sprint**: TBD 