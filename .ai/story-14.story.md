# Story 14: Practice Implementation - User-Location Management API

<version>1.0.0</version>
<status>Completed</status>

## Story Overview

Implement the User-Location Management API to support assigning users to multiple locations within their clinic scope. This story covers Phase 2.2 of the practice implementation plan, enabling admins to manage user access to specific locations and allowing users to switch between their assigned locations.

## Acceptance Criteria

### AC1: Create User Location Assignment Endpoints
- [x] Create `pages/api/users/[id]/locations.ts` with GET, POST, DELETE methods
- [x] GET: Return all locations assigned to a specific user
- [x] POST: Assign user to additional locations
- [x] DELETE: Remove user from specific locations
- [x] Add proper authentication and authorization

### AC2: Create Location User Management Endpoints
- [x] Create `pages/api/locations/[id]/users.ts` with GET, POST, DELETE methods
- [x] GET: Return all users assigned to a specific location
- [x] POST: Assign additional users to a location
- [x] DELETE: Remove users from a location
- [x] Add bulk assignment capabilities

### AC3: Update Current User Location Context
- [x] Update `pages/api/staff/me/location.ts` with GET, PUT methods
- [x] GET: Return current user's location context and available locations
- [x] PUT: Switch user's current location
- [x] Add validation for location access permissions

### AC4: Add Bulk Assignment Endpoints
- [x] Create `pages/api/admin/bulk-assign-locations.ts` with POST method
- [x] Support assigning multiple users to multiple locations
- [x] Add validation and error handling for bulk operations
- [x] Add progress tracking for large operations

### AC5: Update Authentication Context
- [x] Update `utils/firebase-admin.ts` to include location context
- [x] Add helper functions for location access validation
- [x] Update session management for current location
- [x] Add location-scoped data filtering

## Technical Requirements

### API Endpoints Structure
```typescript
// GET /api/users/[id]/locations
// POST /api/users/[id]/locations
// DELETE /api/users/[id]/locations
interface UserLocationsResponse {
  user: User;
  locations: Location[];
  practices: Practice[];
  currentLocationId?: string;
}

// GET /api/locations/[id]/users
// POST /api/locations/[id]/users
// DELETE /api/locations/[id]/users
interface LocationUsersResponse {
  location: Location;
  users: User[];
  total: number;
}

// GET /api/staff/me/location
// PUT /api/staff/me/location
interface UserLocationContextResponse {
  currentLocation?: Location;
  availableLocations: Location[];
  availablePractices: Practice[];
}
```

### Validation Schema
```typescript
interface AssignUserToLocationsRequest {
  locationIds: string[];
  setAsCurrent?: boolean; // Set one as current location
}

interface AssignUsersToLocationRequest {
  userIds: string[];
}

interface SwitchLocationRequest {
  locationId: string;
}

interface BulkAssignRequest {
  assignments: Array<{
    userId: string;
    locationIds: string[];
  }>;
}
```

## Implementation Tasks

### Task 1: Create User Service Layer
- [x] Create `lib/services/userLocationService.ts`
- [x] Implement user-location assignment logic
- [x] Add location access validation methods
- [x] Add bulk assignment operations
- [x] Add helper methods to get user's practices from locations

### Task 2: Create User Location Assignment Endpoints
- [x] Create `pages/api/users/[id]/locations.ts`
- [x] Implement GET method for user's assigned locations
- [x] Implement POST method for assigning locations to user
- [x] Implement DELETE method for removing location assignments
- [x] Add proper clinic scope validation

### Task 3: Create Location User Management Endpoints
- [x] Create `pages/api/locations/[id]/users.ts`
- [x] Implement GET method for location's assigned users
- [x] Implement POST method for assigning users to location
- [x] Implement DELETE method for removing user assignments
- [x] Add pagination and filtering support

### Task 4: Update Current User Context
- [x] Create `pages/api/staff/me/location.ts`
- [x] Implement GET method for current location context
- [x] Implement PUT method for switching current location
- [x] Add validation for location access permissions

### Task 5: Add Bulk Assignment Capabilities
- [x] Create `pages/api/admin/bulk-assign-locations.ts`
- [x] Implement bulk user-location assignment
- [x] Add transaction support for data consistency
- [x] Add progress tracking and error reporting

### Task 6: Update Authentication Context
- [x] Update `utils/firebase-admin.ts`
- [x] Add location context to user authentication
- [x] Add helper functions for location access validation
- [x] Update session management

## Definition of Done

- [x] All API endpoints are implemented and functional
- [x] User-location assignments work correctly with arrays
- [x] Location access validation is properly implemented
- [x] Bulk assignment operations are efficient and reliable
- [x] Current location switching works seamlessly
- [x] Authentication context includes location information
- [x] All endpoints return proper HTTP status codes
- [x] TypeScript compilation passes without errors
- [x] Linting rules pass without warnings
- [x] Comprehensive error handling and validation

## Dependencies

- ✅ Completed Phase 1 (Database Schema & Models)
- ✅ Completed Phase 2.1 (Practice API Endpoints)
- ✅ Existing authentication system
- ✅ User model with locationIds array support

## Risks & Mitigation

### Risk: Array Manipulation Complexity
- **Mitigation**: ✅ Created helper functions for common array operations
- **Mitigation**: ✅ Added comprehensive validation for array updates

### Risk: Data Consistency Issues
- **Mitigation**: ✅ Used Firestore transactions for multi-document updates
- **Mitigation**: ✅ Added rollback mechanisms for failed operations

### Risk: Performance with Large User Sets
- **Mitigation**: ✅ Implemented pagination and filtering from the start
- **Mitigation**: ✅ Added bulk operation optimizations

## Testing Strategy

- [ ] Unit tests for user-location service methods
- [ ] Integration tests for assignment endpoints
- [ ] Authentication and authorization tests
- [ ] Bulk operation performance tests
- [ ] Location switching functionality tests
- [ ] Data consistency validation tests

## API Documentation

### Swagger Schemas
```yaml
components:
  schemas:
    UserLocationAssignment:
      type: object
      properties:
        userId:
          type: string
        locationIds:
          type: array
          items:
            type: string
        currentLocationId:
          type: string
    
    BulkAssignmentRequest:
      type: object
      required:
        - assignments
      properties:
        assignments:
          type: array
          items:
            $ref: '#/components/schemas/UserLocationAssignment'
```

## Chat Log

### Implementation Progress

#### 2024-01-XX - Phase 2.2 Implementation Started
- ✅ **Task 1 Completed**: Created comprehensive `lib/services/userLocationService.ts`:
  - User-location assignment logic with array manipulation
  - Location access validation methods
  - Bulk assignment operations with batching
  - Helper methods to get user's practices from locations
  - Comprehensive error handling and validation
  - Firestore transactions for data consistency

- ✅ **Task 2 Completed**: Created `pages/api/users/[id]/locations.ts`:
  - GET method for user's assigned locations with practices
  - POST method for assigning locations to user with setAsCurrent option
  - DELETE method for removing location assignments
  - Proper clinic scope validation and permissions
  - Comprehensive Swagger documentation

- ✅ **Task 3 Completed**: Created `pages/api/locations/[id]/users.ts`:
  - GET method for location's assigned users with pagination
  - POST method for bulk assigning users to location
  - DELETE method for removing users from location
  - Bulk operation support with success/failure tracking
  - Proper error handling and validation

- ✅ **Task 4 Completed**: Created `pages/api/staff/me/location.ts`:
  - GET method for current user's location context and available locations
  - PUT method for switching user's current location
  - Validation for location access permissions
  - Integration with UserLocationService for context management
  - Comprehensive error handling and user-friendly responses

- ✅ **Task 5 Completed**: Created `pages/api/admin/bulk-assign-locations.ts`:
  - POST method for bulk user-location assignments
  - Comprehensive validation with detailed error messages
  - Support for up to 100 assignments per batch
  - Progress tracking with successful/failed counts
  - Validation-only mode for testing assignments
  - Maximum 20 locations per user validation
  - Duplicate detection for user IDs and location IDs

- ✅ **Task 6 Completed**: Updated `utils/firebase-admin.ts`:
  - Enhanced authentication with location context support
  - Added `verifyAuthAndGetUserWithLocationContext` for full context
  - Added `getUserLocationContext` for location management
  - Added `userHasLocationAccess` and `userCanManageLocations` helpers
  - Added `getLocationScopedConstraints` for Firestore query filtering
  - Added `applyLocationScopedQuery` for automatic location scoping
  - Added `validateLocationAccess` for comprehensive location validation
  - Added `getUserPracticeIds` to derive practices from locations
  - Comprehensive TypeScript typing and error handling

#### Implementation Highlights
- **Array-Based Architecture**: Successfully implemented user-location relationships using arrays in the User model, avoiding complex junction tables
- **Bulk Operations**: Efficient bulk assignment capabilities with proper error handling and progress tracking
- **Clinic Scoping**: All operations properly scoped to user's clinic for security
- **Transaction Support**: Used Firestore transactions for data consistency
- **Comprehensive Validation**: Input validation, permission checks, and business logic validation
- **Error Handling**: Meaningful error messages and proper HTTP status codes
- **User Experience**: Location switching functionality for seamless user experience
- **Admin Tools**: Powerful bulk assignment tools for efficient user management
- **Authentication Enhancement**: Location-aware authentication with helper functions
- **Query Scoping**: Automatic location-scoped data filtering for security

#### Final Status: 100% Complete ✅
- **All Tasks 1-6**: ✅ COMPLETED
- **All Acceptance Criteria**: ✅ COMPLETED
- **All Definition of Done Items**: ✅ COMPLETED

#### API Endpoints Created:
1. `GET/POST/DELETE /api/users/[id]/locations` - User location assignment management
2. `GET/POST/DELETE /api/locations/[id]/users` - Location user management with bulk operations
3. `GET/PUT /api/staff/me/location` - Current user location context and switching
4. `POST /api/admin/bulk-assign-locations` - Admin bulk assignment operations

#### Services Created:
1. `UserLocationService` - Comprehensive business logic for user-location management
2. Enhanced `firebase-admin.ts` - Location-aware authentication and query helpers

#### Key Features Delivered:
- **Multi-location User Access**: Users can be assigned to multiple locations within their clinic
- **Location Switching**: Users can seamlessly switch between their assigned locations
- **Admin Management**: Comprehensive admin tools for managing user-location assignments
- **Bulk Operations**: Efficient bulk assignment capabilities for large-scale operations
- **Security**: Proper clinic scoping and permission validation throughout
- **Data Consistency**: Firestore transactions ensure data integrity
- **Query Optimization**: Location-scoped queries for performance and security

---

**Story Created**: [Current Date]
**Story Completed**: [Current Date]
**Assigned To**: Development Team
**Priority**: High
**Epic**: Practice Implementation
**Sprint**: TBD 