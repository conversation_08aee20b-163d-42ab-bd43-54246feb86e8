# Office Hours & On-Call Doctors Implementation Plan

## Overview
This implementation plan covers two major features:
1. **Office Hours Integration**: Agent needs to know location office hours and return schedule info in webhook responses
2. **On-Call Doctors System**: Database table for on-call doctor schedules with SMS notifications for after-hours calls

## Architecture Overview

```mermaid
graph TB
    A[Call Session Created] --> B{Determine Location}
    B --> C[Get Location Office Hours]
    C --> D{Current Time vs Office Hours}
    D -->|During Hours| E[Return Office Hours Info]
    D -->|After Hours| F[Check On-Call Schedule]
    F --> G[Send SMS to On-Call Doctor]
    G --> H[Return After-Hours Info]
    E --> I[Webhook Response]
    H --> I
    
    J[Admin Interface] --> K[Manage Office Hours]
    J --> L[Manage On-Call Schedule]
    K --> M[Location Settings]
    L --> N[On-Call Doctors Table]
```

## Phase 1: Office Hours Integration

### 1.1 Agent-to-Location Mapping Service

**New File**: `lib/services/agent-location-mapping.ts`
```typescript
export interface AgentLocationMapping {
  agentId: string;
  locationId: string;
  clinicId: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export class AgentLocationMappingService {
  static async getLocationByAgentId(agentId: string): Promise<Location | null>
  static async createMapping(agentId: string, locationId: string): Promise<AgentLocationMapping>
  static async updateMapping(agentId: string, locationId: string): Promise<AgentLocationMapping>
}
```

### 1.2 Office Hours Utility Service

**New File**: `lib/services/office-hours.ts`
```typescript
export interface OfficeHoursStatus {
  isOpen: boolean;
  currentStatus: 'open' | 'closed' | 'unknown';
  nextOpenTime?: string;
  nextCloseTime?: string;
  todayHours?: { start: string; end: string } | null;
  timezone: string;
}

export class OfficeHoursService {
  /**
   * Check if location is currently open based on office hours
   * @param officeHours Location office hours in format: { "1": { "start": "09:00", "end": "16:00" }, "6": null, "7": null }
   * @param timezone Location timezone (e.g., "America/Chicago")
   * @param currentTime Optional current time for testing
   */
  static checkOfficeHours(
    officeHours: Record<string, { start: string; end: string } | null>,
    timezone: string,
    currentTime?: Date
  ): OfficeHoursStatus {
    // Implementation handles:
    // - Keys "1"-"7" representing Monday-Sunday
    // - null values for closed days (weekends)
    // - 24-hour time format "HH:MM"
    // - Timezone conversion using timezone string
  }
  
  static getNextBusinessDay(
    officeHours: Record<string, { start: string; end: string } | null>,
    timezone: string
  ): { date: string; hours: { start: string; end: string } } | null
}
```

### 1.3 Enhanced Call Session Handler

**Modified File**: `pages/api/external-api/v2/calls/add-or-update-call-session.ts`

```typescript
// Add imports
import { AgentLocationMappingService } from '@/lib/services/agent-location-mapping';
import { OfficeHoursService } from '@/lib/services/office-hours';
import { LocationService } from '@/lib/services/locationService';

// In addOrUpdateCallSessionHandler function, after extracting agentId:
const location = await AgentLocationMappingService.getLocationByAgentId(agentId);
let officeHoursStatus: OfficeHoursStatus | null = null;

if (location?.officeHours) {
  officeHoursStatus = OfficeHoursService.checkOfficeHours(
    location.officeHours, // Format: { "1": { "start": "09:00", "end": "16:00" }, "6": null, "7": null }
    location.timeZone    // Format: "America/Chicago"
  );
}

// Update webhook response to include office hours info:
const webhookResponse = {
  sessionInfo: {
    parameters: {
      sessionId: sessionId,
      isRedirected: isRedirected,
      locationId: location?.id,
      isOfficeHours: officeHoursStatus?.isOpen ?? false,
      officeStatus: officeHoursStatus?.currentStatus ?? 'unknown',
      nextOpenTime: officeHoursStatus?.nextOpenTime,
      todayHours: officeHoursStatus?.todayHours,
    },
  },
  payload: {
    sessionId: sessionId,
    location: location ? {
      id: location.id,
      name: location.name,
      timezone: location.timeZone,
      officeHours: location.officeHours,
    } : null,
    schedule: officeHoursStatus,
  },
};
```

## Phase 2: Location Office Hours Management

### 2.1 Enhanced Office Hours API

**Modified File**: `pages/api/locations/[id].ts`

Add validation for office hours format:
```typescript
interface OfficeHoursValidation {
  [key: string]: { start: string; end: string } | null;
}

function validateOfficeHours(officeHours: OfficeHoursValidation): boolean {
  const validDays = ['1', '2', '3', '4', '5', '6', '7']; // Monday-Sunday
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  
  for (const [day, hours] of Object.entries(officeHours)) {
    if (!validDays.includes(day)) return false;
    if (hours && (!timeRegex.test(hours.start) || !timeRegex.test(hours.end))) {
      return false;
    }
  }
  return true;
}
```

### 2.2 Office Hours Management UI Component

**New File**: `components/OfficeHoursEditor.tsx`
```typescript
interface OfficeHoursEditorProps {
  officeHours?: Record<string, { start: string; end: string } | null>;
  onChange: (hours: Record<string, { start: string; end: string } | null>) => void;
  timezone: string;
}

export const OfficeHoursEditor: React.FC<OfficeHoursEditorProps> = ({
  officeHours,
  onChange,
  timezone
}) => {
  // Implementation with day-by-day time pickers
  // Visual display of current schedule
  // Validation and error handling
}
```

## Phase 3: On-Call Doctors System

### 3.1 Database Model

**New File**: `models/OnCallSchedule.ts`
```typescript
export interface OnCallSchedule {
  id: string;
  doctorId: string; // References User.id where role includes appointment-taking capability
  doctorName: string; // Denormalized for quick access
  doctorPhone: string; // For SMS notifications
  locationId: string; // References Location.id
  clinicId: number; // References clinic
  date: string; // ISO date format YYYY-MM-DD
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  isActive: boolean;
  timezone: string; // Location timezone
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string; // User ID who created the schedule
}

export interface OnCallNotification {
  id: string;
  scheduleId: string; // References OnCallSchedule.id
  callSessionId: string;
  doctorId: string;
  notificationTime: Date;
  smsMessageId?: string; // Twilio message SID
  status: 'sent' | 'failed' | 'delivered';
  callType?: string;
  callerPhone?: string;
  createdAt: Date;
}
```

### 3.2 On-Call Schedule Service

**New File**: `lib/services/on-call-schedule.ts`
```typescript
export class OnCallScheduleService {
  // CRUD operations
  static async createSchedule(scheduleData: Partial<OnCallSchedule>): Promise<OnCallSchedule>
  static async updateSchedule(id: string, updates: Partial<OnCallSchedule>): Promise<OnCallSchedule>
  static async deleteSchedule(id: string): Promise<void>
  static async getSchedulesByLocation(locationId: string, startDate?: Date, endDate?: Date): Promise<OnCallSchedule[]>
  
  // On-call specific queries
  static async getCurrentOnCallDoctor(locationId: string, currentTime?: Date): Promise<OnCallSchedule | null>
  static async getOnCallDoctorsForTimeRange(
    locationId: string, 
    startTime: Date, 
    endTime: Date
  ): Promise<OnCallSchedule[]>
  
  // Notification tracking
  static async logNotification(notificationData: Partial<OnCallNotification>): Promise<OnCallNotification>
  static async getNotificationHistory(scheduleId: string): Promise<OnCallNotification[]>
}
```

### 3.3 On-Call Schedule API Endpoints

**New File**: `pages/api/on-call-schedules/index.ts`
```typescript
/**
 * @swagger
 * /api/on-call-schedules:
 *   get:
 *     summary: Get on-call schedules for a location
 *     parameters:
 *       - name: locationId
 *         required: true
 *       - name: startDate
 *       - name: endDate
 *   post:
 *     summary: Create new on-call schedule
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OnCallSchedule'
 */
```

**New File**: `pages/api/on-call-schedules/[id].ts`
```typescript
// PUT, DELETE operations for individual schedules
```

**New File**: `pages/api/on-call-schedules/current.ts`
```typescript
/**
 * Get currently on-call doctor for a location
 */
```

### 3.4 Eligible Doctors API

**New File**: `pages/api/locations/[id]/eligible-doctors.ts`
```typescript
/**
 * Get users who can be scheduled for on-call duty
 * Filters by: canTakeAppointments = true, locationIds includes specified location
 */
```

## Phase 4: SMS Notification System

### 4.1 Enhanced Call Session Handler for Notifications

**Modified File**: `pages/api/external-api/v2/calls/add-or-update-call-session.ts`

```typescript
// After determining office hours status, if after hours:
if (officeHoursStatus && !officeHoursStatus.isOpen && location) {
  // Check for on-call doctor
  const onCallDoctor = await OnCallScheduleService.getCurrentOnCallDoctor(location.id);
  
  if (onCallDoctor && !isOutboundCall) {
    // Send SMS notification
    await OnCallNotificationService.notifyOnCallDoctor({
      schedule: onCallDoctor,
      callInfo: {
        sessionId,
        callerPhone,
        callType: callType || CallType.OTHER,
        triggerEvent,
      },
    });
  }
  
  // Update webhook response
  webhookResponse.sessionInfo.parameters.onCallDoctor = onCallDoctor ? {
    doctorName: onCallDoctor.doctorName,
    isNotified: true,
  } : null;
}
```

### 4.2 On-Call Notification Service

**New File**: `lib/services/on-call-notification.ts`
```typescript
export interface OnCallNotificationData {
  schedule: OnCallSchedule;
  callInfo: {
    sessionId: string;
    callerPhone?: string;
    callType: CallType;
    triggerEvent?: string;
  };
}

export class OnCallNotificationService {
  static async notifyOnCallDoctor(data: OnCallNotificationData): Promise<string> {
    const { schedule, callInfo } = data;
    
    const message = this.buildNotificationMessage(schedule, callInfo);
    
    try {
      const messageSid = await smsService.sendSms(schedule.doctorPhone, message);
      
      // Log the notification
      await OnCallScheduleService.logNotification({
        scheduleId: schedule.id,
        callSessionId: callInfo.sessionId,
        doctorId: schedule.doctorId,
        notificationTime: new Date(),
        smsMessageId: messageSid,
        status: 'sent',
        callType: callInfo.callType.toString(),
        callerPhone: callInfo.callerPhone,
      });
      
      return messageSid;
    } catch (error) {
      // Log failed notification
      await OnCallScheduleService.logNotification({
        scheduleId: schedule.id,
        callSessionId: callInfo.sessionId,
        doctorId: schedule.doctorId,
        notificationTime: new Date(),
        status: 'failed',
        callType: callInfo.callType.toString(),
        callerPhone: callInfo.callerPhone,
      });
      
      throw error;
    }
  }
  
  private static buildNotificationMessage(
    schedule: OnCallSchedule, 
    callInfo: OnCallNotificationData['callInfo']
  ): string {
    const callerInfo = callInfo.callerPhone ? `from ${callInfo.callerPhone}` : '';
    const timeStr = new Date().toLocaleTimeString();
    
    return `🏥 After-Hours Call Alert\n\nDr. ${schedule.doctorName}, you have an incoming call ${callerInfo} at ${timeStr}.\n\nLocation: [Location Name]\nCall Type: ${callInfo.callType}\n\nPlease check the staff portal for details.`;
  }
}
```

## Phase 5: Admin Interface Development

### 5.1 Office Hours Management Page

**New File**: `pages/dashboard/admin/locations/[id]/office-hours.tsx`
```typescript
export default function LocationOfficeHoursPage() {
  // Location-specific office hours management
  // Visual schedule display
  // Bulk edit capabilities
  // Timezone considerations
}
```

### 5.2 On-Call Schedule Management Page

**New File**: `pages/dashboard/admin/on-call-schedules.tsx`
```typescript
export default function OnCallSchedulesPage() {
  // Calendar view of on-call schedules
  // Doctor assignment interface
  // Conflict detection
  // Schedule templates
  // Notification history
}
```

### 5.3 Enhanced Admin Tools Navigation

**Modified File**: `pages/dashboard/admin-tools.tsx`

Add new menu items:
```typescript
const adminToolsItems = [
  // ... existing items
  {
    title: 'Office Hours',
    description: 'Manage location office hours',
    href: '/dashboard/admin/office-hours',
    icon: Clock,
  },
  {
    title: 'On-Call Schedules',
    description: 'Manage on-call doctor schedules',
    href: '/dashboard/admin/on-call-schedules',
    icon: UserPlus,
  },
];
```

## Phase 6: Database Migrations & Setup

### 6.1 Firestore Collections

**New Collections**:
1. `agent-location-mappings` - Maps DialogFlow agents to locations
2. `on-call-schedules` - On-call doctor schedules
3. `on-call-notifications` - SMS notification log

**Collection Structures**:
```typescript
// agent-location-mappings/{agentId}
{
  agentId: string,
  locationId: string,
  clinicId: number,
  isActive: boolean,
  createdAt: Timestamp,
  updatedAt: Timestamp
}

// on-call-schedules/{scheduleId}
{
  doctorId: string,
  doctorName: string,
  doctorPhone: string,
  locationId: string,
  clinicId: number,
  date: string, // YYYY-MM-DD
  startTime: string, // HH:MM
  endTime: string, // HH:MM
  isActive: boolean,
  timezone: string,
  notes?: string,
  createdAt: Timestamp,
  updatedAt: Timestamp,
  createdBy: string
}

// on-call-notifications/{notificationId}
{
  scheduleId: string,
  callSessionId: string,
  doctorId: string,
  notificationTime: Timestamp,
  smsMessageId?: string,
  status: string,
  callType?: string,
  callerPhone?: string,
  createdAt: Timestamp
}
```

### 6.2 Firestore Security Rules

**Modified File**: `firestore.rules`
```javascript
// On-call schedules - clinic admins can manage
match /on-call-schedules/{scheduleId} {
  allow read, write: if isAuthenticated() && 
    (isClinicAdmin() || isSuperAdmin()) &&
    belongsToSameClinic(scheduleId, 'on-call-schedules');
}

// Agent location mappings - super admin only
match /agent-location-mappings/{agentId} {
  allow read, write: if isAuthenticated() && isSuperAdmin();
}

// On-call notifications - read only for clinic admins
match /on-call-notifications/{notificationId} {
  allow read: if isAuthenticated() && 
    (isClinicAdmin() || isSuperAdmin()) &&
    belongsToSameClinic(notificationId, 'on-call-notifications');
}
```

### 6.3 Migration Scripts

**New File**: `scripts/setup-agent-location-mappings.js`
```javascript
// Script to create initial agent-to-location mappings
// Based on existing hardcoded values like URMA_LOMBARD_LOCATION_ID
```

**New File**: `scripts/setup-on-call-collections.js`
```javascript
// Script to create Firestore indexes for on-call schedules
// Set up initial data structure
```

## Phase 7: Testing Strategy

### 7.1 Unit Tests

**New Files**:
- `__tests__/lib/services/office-hours.test.ts`
- `__tests__/lib/services/on-call-schedule.test.ts`
- `__tests__/lib/services/on-call-notification.test.ts`

### 7.2 Integration Tests

**New Files**:
- `__tests__/api/on-call-schedules/index.test.ts`
- `__tests__/api/external-api/v2/calls/add-or-update-call-session-office-hours.test.ts`

### 7.3 End-to-End Tests

Test scenarios:
1. Office hours detection during business hours
2. Office hours detection after hours
3. On-call doctor SMS notification flow
4. Admin interface for schedule management

## Implementation Timeline

### Week 1: Foundation
- [ ] Create office hours utility service
- [ ] Implement agent-location mapping service
- [ ] Update call session handler with office hours logic

### Week 2: Database & API
- [ ] Design and implement on-call schedule models
- [ ] Create on-call schedule API endpoints
- [ ] Set up Firestore collections and security rules

### Week 3: Notifications
- [ ] Implement on-call notification service
- [ ] Integrate SMS notifications in call session flow
- [ ] Create notification logging system

### Week 4: Admin Interface
- [ ] Build office hours management UI
- [ ] Create on-call schedule management interface
- [ ] Update admin navigation and permissions

### Week 5: Testing & Polish
- [ ] Comprehensive testing suite
- [ ] Documentation updates
- [ ] Performance optimization
- [ ] Bug fixes and refinements

## Configuration Requirements

### Environment Variables
```bash
# Existing Twilio config (already set up)
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=

# New configuration for notifications
ON_CALL_SMS_ENABLED=true
ON_CALL_NOTIFICATION_DELAY_MINUTES=0 # Immediate notification
```

### App Config Updates

**Modified File**: `app-config.ts`
```typescript
// On-call notification settings
export const ON_CALL_SMS_ENABLED = process.env.ON_CALL_SMS_ENABLED === 'true';
export const ON_CALL_NOTIFICATION_DELAY_MINUTES = parseInt(process.env.ON_CALL_NOTIFICATION_DELAY_MINUTES || '0', 10);

// Default agent-location mappings (migrate from hardcoded values)
export const DEFAULT_AGENT_LOCATION_MAPPINGS = {
  'your-agent-id-here': URMA_LOMBARD_LOCATION_ID,
  // Add more mappings as needed
};
```

## Security Considerations

1. **Phone Number Validation**: Ensure all doctor phone numbers are validated before SMS sending
2. **Rate Limiting**: Implement rate limiting for on-call SMS notifications to prevent spam
3. **Audit Logging**: Log all admin actions for schedule changes
4. **Permission Checks**: Strict validation that only clinic admins can manage their own clinic's schedules
5. **Data Encryption**: Ensure sensitive data (phone numbers) are properly handled

## Monitoring & Observability

1. **SMS Delivery Tracking**: Monitor Twilio delivery status
2. **Office Hours Detection Metrics**: Track accuracy of office hours detection
3. **On-Call Response Times**: Measure how quickly doctors respond to notifications
4. **Error Monitoring**: Alert on failed SMS sends or system errors

## Future Enhancements

1. **Multi-Doctor On-Call**: Support for backup on-call doctors
2. **Schedule Templates**: Recurring schedule templates for easy setup
3. **Mobile App Notifications**: Push notifications in addition to SMS
4. **Integration with Calendar Systems**: Sync with external calendar systems
5. **Analytics Dashboard**: Usage statistics and response analytics

This comprehensive plan provides a robust foundation for both office hours management and on-call doctor scheduling with SMS notifications, while maintaining the existing architecture patterns and ensuring proper security and scalability 🏴‍☠️ 