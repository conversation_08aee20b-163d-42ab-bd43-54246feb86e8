# Architecture Document: External API v2 and Nextech Integration

<version>1.0.0</version>
<status>Draft</status>

## 1. Introduction

This document outlines the technical architecture for the External API v2 implementation that integrates with Nextech Practice+ APIs and is designed to support future provider integrations. The architecture follows a modular, adapter-based approach to enable easy extension with additional providers.

## 2. Architecture Overview

### 2.1. Key Components

The architecture consists of the following key components:

1. **External API Controllers**: Next.js API route handlers that expose RESTful endpoints.
2. **Provider Registry**: A central registry that manages available provider implementations.
3. **Provider Interfaces**: Common interfaces that all providers must implement.
4. **Provider Implementations**: Concrete implementations for each provider (starting with Nextech).
5. **Models**: Common data models that provide a consistent interface regardless of provider.
6. **Middleware**: Cross-cutting concerns like authentication, validation, and error handling.

### 2.2. High-Level Architecture

```mermaid
graph TD
    Client[API Client] --> ExternalAPI[External API v2]
    ExternalAPI --> ProviderRegistry
    ProviderRegistry --> Provider1[Nextech Provider]
    ProviderRegistry --> Provider2[Future Provider]
    Provider1 --> Nextech[Nextech API]
    Provider2 --> OtherAPI[Other External API]
```

## 3. Detailed Component Design

### 3.1. Provider Interfaces

All providers will implement a set of common interfaces to ensure consistent behavior:

```typescript
// Base provider interface
export interface IProvider {
  name: string;
  getClinicService(): IClinicService;
  getLocationService(): ILocationService;
  getPatientService(): IPatientService;
  getUserService(): IUserService;
  getAppointmentService(): IAppointmentService;
}

// Service interfaces
export interface IClinicService {
  getClinics(filters?: Record<string, unknown>): Promise<Clinic[]>;
  getClinicById(id: string): Promise<Clinic | null>;
  getClinicByPhone(phone: string): Promise<Clinic | null>;
}

export interface ILocationService {
  getLocations(filters?: Record<string, unknown>): Promise<Location[]>;
  getLocationById(id: string): Promise<Location | null>;
  getLocationByPhone(phone: string): Promise<Location | null>;
}

export interface IPatientService {
  getPatients(filters?: Record<string, unknown>): Promise<Patient[]>;
  getPatientById(id: string): Promise<Patient | null>;
  getPatientByPhone(phone: string): Promise<Patient | null>;
  getPatientByEmail(email: string): Promise<Patient | null>;
  getPatientByFullNameAndDob(fullName: string, dob: string): Promise<Patient | null>;
}

export interface IUserService {
  getUsers(filters?: Record<string, unknown>): Promise<User[]>;
  getUserById(id: string): Promise<User | null>;
  getUserByPhone(phone: string): Promise<User | null>;
  getUserByEmail(email: string): Promise<User | null>;
  getUserByFullName(fullName: string): Promise<User | null>;
}

export interface IAppointmentService {
  getAppointments(filters?: Record<string, unknown>): Promise<Appointment[]>;
  getAppointmentById(id: string): Promise<Appointment | null>;
  createAppointment(data: CreateAppointmentDto): Promise<Appointment>;
  updateAppointment(id: string, data: UpdateAppointmentDto): Promise<Appointment>;
  cancelAppointment(id: string, reason?: string): Promise<boolean>;
  getAppointmentByPatientId(patientId: string): Promise<Appointment | null>;
  getAppointmentByClinicId(clinicId: string): Promise<Appointment | null>;
  getAppointmentByLocationId(locationId: string): Promise<Appointment | null>;
  getAppointmentByDate(date: string): Promise<Appointment[]>;
  getAppointmentByDateRange(startDate: string, endDate: string): Promise<Appointment[]>;
  getAppointmentByDateRangeAndLocationId(startDate: string, endDate: string, locationId: string): Promise<Appointment[]>;
  getAppointmentByDateRangeAndClinicId(startDate: string, endDate: string, clinicId: string): Promise<Appointment[]>;
  getAppointmentByDateRangeAndProviderId(startDate: string, endDate: string, providerId: string): Promise<Appointment[]>;
  getAppointmentByDateRangeAndPatientId(startDate: string, endDate: string, patientId: string): Promise<Appointment[]>;
}
```

### 3.2. Provider Registry

The provider registry manages available providers and selects the appropriate provider based on configuration:

```typescript
export class ProviderRegistry {
  private providers: Map<string, IProvider> = new Map();
  private defaultProvider: string;

  constructor(defaultProvider: string) {
    this.defaultProvider = defaultProvider;
  }

  registerProvider(provider: IProvider): void {
    this.providers.set(provider.name, provider);
  }

  getProvider(name?: string): IProvider {
    const providerName = name || this.defaultProvider;
    const provider = this.providers.get(providerName);
    
    if (!provider) {
      throw new Error(`Provider ${providerName} not found`);
    }
    
    return provider;
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }
}
```

### 3.3. Nextech Provider Implementation

The Nextech provider implementation will handle authentication, data mapping, and API communication:

```typescript
export class NextechProvider implements IProvider {
  name = 'nextech';
  private client: NextechApiClient;
  
  constructor(config: NextechConfig) {
    this.client = new NextechApiClient(config);
  }
  
  getClinicService(): IClinicService {
    return new NextechClinicService(this.client);
  }
  
  getLocationService(): ILocationService {
    return new NextechLocationService(this.client);
  }
  
  getPatientService(): IPatientService {
    return new NextechPatientService(this.client);
  }
  
  getUserService(): IUserService {
    return new NextechUserService(this.client);
  }
  
  getAppointmentService(): IAppointmentService {
    return new NextechAppointmentService(this.client);
  }
}
```

### 3.4. API Controllers

API controllers handle request/response processing and delegate to the appropriate provider:

```typescript
// GET /api/external-api/v2/clinics
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    const providerName = req.headers['x-provider'] as string;
    const provider = providerRegistry.getProvider(providerName);
    const clinicService = provider.getClinicService();
    
    const clinics = await clinicService.getClinics(req.query);
    
    return res.status(200).json(clinics);
  } catch (error) {
    return handleError(error, res);
  }
}
```

## 4. Data Flow

### 4.1. Authentication Flow

1. Client sends request with API key
2. External API validates API key
3. Provider retrieves OAuth token from Nextech (or other provider)
4. Provider caches token for future requests
5. Provider makes authenticated request to provider API
6. Response is transformed and returned to client

```mermaid
sequenceDiagram
    Client->>External API: Request with API Key
    External API->>Auth Middleware: Validate API Key
    Auth Middleware->>Provider: Get Provider
    Provider->>Provider API: Get OAuth Token
    Provider API->>Provider: Return Token
    Provider->>Provider API: Make Authenticated Request
    Provider API->>Provider: Return Data
    Provider->>External API: Transform Data
    External API->>Client: Return Response
```

### 4.2. Request Flow

```mermaid
sequenceDiagram
    Client->>External API: API Request
    External API->>Provider Registry: Get Provider
    Provider Registry->>Provider: Get Service
    Provider->>Provider Service: Call Method
    Provider Service->>Provider API: Make API Call
    Provider API->>Provider Service: Return Response
    Provider Service->>Provider: Transform Data
    Provider->>External API: Return Transformed Data
    External API->>Client: Return Response
```

## 5. Error Handling

All errors will be handled consistently across providers:

1. Provider-specific errors are caught and mapped to standard error types
2. HTTP status codes are standardized
3. Error responses follow a consistent format

```typescript
interface ErrorResponse {
  status: number;
  code: string;
  message: string;
  details?: Record<string, unknown>;
}
```

## 6. Security Considerations

1. API keys for accessing External API
2. OAuth tokens for provider communication
3. Credentials stored in secure environment variables
4. Rate limiting to prevent abuse
5. Input validation to prevent injection attacks

## 7. Deployment and Configuration

### 7.1. Environment Variables

```
# Provider Selection
EXTERNAL_API_DEFAULT_PROVIDER=nextech

# Nextech Configuration
NEXTECH_CLIENT_ID=your-client-id
NEXTECH_CLIENT_SECRET=your-client-secret
NEXTECH_RESOURCE=your-resource
NEXTECH_PRACTICE_ID=your-practice-id
NEXTECH_BASE_URL=https://api.pm.nextech.com/api
```

### 7.2. Deployment Process

1. Environment variables configured in deployment environment
2. Code deployed via existing CI/CD pipeline
3. API versioning handled at the URL level

## 8. Testing Strategy

1. Unit tests for each provider implementation
2. Integration tests for API controllers
3. Mock provider for testing without external dependencies
4. End-to-end tests for critical flows

## 9. Future Considerations

1. Caching layer for improved performance
2. Webhook support for real-time updates
3. GraphQL API for more flexible querying
4. Additional provider implementations

## 10. References

1. [Nextech API Documentation](https://nextechsystems.github.io/practiceplusapidocspub/)
2. [Next.js API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
3. [Adapter Pattern](https://refactoring.guru/design-patterns/adapter)
4. [Factory Pattern](https://refactoring.guru/design-patterns/factory-method)

# 11. Office Hours & On-Call Doctors Integration Architecture

## 11.1 Overview

This section describes the architecture for the Office Hours & On-Call Doctors Integration epic. It extends the core platform with services and APIs for office hours detection, agent-to-location mapping, on-call doctor scheduling, and automated SMS notifications for after-hours calls.

## 11.2 Key Components

- **Agent-Location Mapping Service**: Maps DialogFlow agent IDs to specific locations for office hours context.
- **Office Hours Service**: Provides timezone-aware office hours status, next open/close time, and business day calculations.
- **On-Call Schedule Service**: Manages on-call doctor schedules, conflict detection, and current on-call lookups.
- **SMS Notification System**: Sends SMS alerts to on-call doctors for after-hours calls, with rate limiting and logging.
- **Firestore Collections**: `agent-location-mappings`, `on-call-schedules`, `on-call-notifications`.
- **API Endpoints**: For office hours management, on-call schedule management, and notification tracking.

## 11.3 Data Flow & Sequence Diagrams

### Office Hours & On-Call Notification Flow

```mermaid
sequenceDiagram
    participant User
    participant DialogFlow
    participant API as StaffPortal API
    participant Firestore
    participant SMS as SMS Service
    User->>DialogFlow: Initiate call/session
    DialogFlow->>API: Webhook request (with agentId)
    API->>Firestore: Get agent-location mapping
    API->>Firestore: Get location office hours
    API->>API: Check office hours (timezone-aware)
    alt During Office Hours
        API->>DialogFlow: Respond with office hours info
    else After Hours
        API->>Firestore: Get on-call schedule for location
        API->>SMS: Send SMS to on-call doctor
        SMS->>API: Delivery status
        API->>Firestore: Log notification
        API->>DialogFlow: Respond with after-hours info
    end
```

## 11.4 Data Models & Firestore Structure

### Agent-Location Mapping
```typescript
// Collection: agent-location-mappings/{agentId}
interface AgentLocationMapping {
  agentId: string;
  locationId: string;
  clinicId: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}
```

### On-Call Schedule
```typescript
// Collection: on-call-schedules/{scheduleId}
interface OnCallSchedule {
  id: string;
  doctorId: string;
  doctorName: string;
  doctorPhone: string;
  locationId: string;
  clinicId: number;
  date: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
  timezone: string;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
}
```

### On-Call Notification
```typescript
// Collection: on-call-notifications/{notificationId}
interface OnCallNotification {
  id: string;
  scheduleId: string;
  callSessionId: string;
  doctorId: string;
  clinicId: number;
  notificationTime: Timestamp;
  smsMessageId?: string;
  status: 'sent' | 'failed' | 'delivered';
  callType?: string;
  callerPhone?: string;
  errorMessage?: string;
  createdAt: Timestamp;
}
```

## 11.5 Security & Configuration
- Firestore security rules restrict agent-location mappings to admins, on-call schedules to clinic admins, and notifications to read-only for clinic admins.
- Environment variables control office hours and notification features, Twilio credentials, and timezone settings.
- Rate limiting and cooldowns prevent SMS spam.

## 11.6 Testing Strategy
- Unit tests for all new services (office hours, agent-location, on-call schedule, notification)
- Integration tests for API endpoints and Firestore queries
- End-to-end tests for call session and notification flows
- Edge case and timezone scenario tests 