# Story 16: Database Schema & Infrastructure Setup

<version>1.0.0</version>
<status>In Progress</status>

## Story Overview

Complete the database infrastructure setup for the Office Hours & On-Call Doctors feature. This story addresses the missing Firestore collections, indexes, and security rules identified in Story 15, and establishes a comprehensive database schema that can be used for future MySQL migration.

## Acceptance Criteria

### AC1: Create Firestore Collections Structure
- [x] Create `agent-location-mappings` collection with proper document structure
- [x] Create `on-call-schedules` collection with comprehensive schedule data
- [x] Create `on-call-notifications` collection for SMS notification tracking
- [x] Add sample/seed data for testing and validation

### AC2: Implement Firestore Indexes
- [x] Add indexes for `agent-location-mappings` queries (agentId, clinicId + isActive)
- [x] Add indexes for `on-call-schedules` queries (locationId + date, doctorId + date, current on-call lookups)
- [x] Add indexes for `on-call-notifications` queries (scheduleId, callSessionId, doctorId + createdAt)
- [x] Update `firestore.indexes.json` with all required indexes
- [ ] Deploy indexes to Firestore

### AC3: Update Firestore Security Rules
- [x] Add security rules for `agent-location-mappings` (admin-only access)
- [x] Add security rules for `on-call-schedules` (clinic-admin access with proper clinic isolation)
- [x] Add security rules for `on-call-notifications` (read-only access for clinic admins)
- [ ] Test security rules with different user roles
- [ ] Deploy updated security rules

### AC4: Database Schema Documentation
- [x] Create comprehensive database schema documentation
- [x] Document all collections with field types and constraints
- [x] Document all indexes with query patterns
- [x] Create MySQL migration reference schema for future use
- [x] Add data relationship diagrams

### AC5: Migration Scripts and Seed Data
- [x] Create script to populate initial agent-location mappings
- [x] Create script to set up default on-call schedules for testing
- [x] Create validation scripts to verify data integrity
- [x] Add cleanup/reset scripts for development

## Technical Requirements

### Collection Schemas

#### Agent-Location Mappings Collection
```typescript
// Collection: agent-location-mappings/{agentId}
interface AgentLocationMappingDoc {
  agentId: string;          // Primary key, DialogFlow agent ID
  locationId: string;       // References locations collection
  clinicId: number;         // References clinic (for security isolation)
  isActive: boolean;        // Soft delete flag
  createdAt: Timestamp;     // Auto-generated
  updatedAt: Timestamp;     // Auto-updated
}
```

#### On-Call Schedules Collection
```typescript
// Collection: on-call-schedules/{scheduleId}
interface OnCallScheduleDoc {
  id: string;               // Auto-generated document ID
  doctorId: string;         // References users collection
  doctorName: string;       // Denormalized for quick access
  doctorPhone: string;      // For SMS notifications (validated format)
  locationId: string;       // References locations collection
  clinicId: number;         // References clinic (for security isolation) 
  date: string;             // ISO date format YYYY-MM-DD
  startTime: string;        // 24-hour format HH:MM
  endTime: string;          // 24-hour format HH:MM
  isActive: boolean;        // Soft delete flag
  timezone: string;         // Location timezone from database (e.g., "America/Chicago")
  notes?: string;           // Optional notes
  createdAt: Timestamp;     // Auto-generated
  updatedAt: Timestamp;     // Auto-updated
  createdBy: string;        // User ID who created the schedule
}
```

#### On-Call Notifications Collection
```typescript
// Collection: on-call-notifications/{notificationId}
interface OnCallNotificationDoc {
  id: string;               // Auto-generated document ID
  scheduleId: string;       // References on-call-schedules collection
  callSessionId: string;    // References callSessions collection
  doctorId: string;         // References users collection
  clinicId: number;         // References clinic (for security isolation)
  notificationTime: Timestamp; // When SMS was sent
  smsMessageId?: string;    // Twilio message SID
  status: 'sent' | 'failed' | 'delivered'; // SMS delivery status
  callType?: string;        // Type of call that triggered notification
  callerPhone?: string;     // Phone number of caller
  errorMessage?: string;    // Error details if status is 'failed'
  createdAt: Timestamp;     // Auto-generated
}
```

### Required Firestore Indexes

#### Agent-Location Mappings Indexes
```json
{
  "collectionGroup": "agent-location-mappings",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "agentId", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
},
{
  "collectionGroup": "agent-location-mappings", 
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "clinicId", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
}
```

#### On-Call Schedules Indexes
```json
{
  "collectionGroup": "on-call-schedules",
  "queryScope": "COLLECTION", 
  "fields": [
    { "fieldPath": "locationId", "order": "ASCENDING" },
    { "fieldPath": "date", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
},
{
  "collectionGroup": "on-call-schedules",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "doctorId", "order": "ASCENDING" },
    { "fieldPath": "date", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
},
{
  "collectionGroup": "on-call-schedules",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "clinicId", "order": "ASCENDING" },
    { "fieldPath": "date", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
},
{
  "collectionGroup": "on-call-schedules",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "locationId", "order": "ASCENDING" },
    { "fieldPath": "date", "order": "ASCENDING" },
    { "fieldPath": "startTime", "order": "ASCENDING" },
    { "fieldPath": "endTime", "order": "ASCENDING" },
    { "fieldPath": "isActive", "order": "ASCENDING" }
  ]
}
```

#### On-Call Notifications Indexes  
```json
{
  "collectionGroup": "on-call-notifications",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "scheduleId", "order": "ASCENDING" },
    { "fieldPath": "createdAt", "order": "DESCENDING" }
  ]
},
{
  "collectionGroup": "on-call-notifications",
  "queryScope": "COLLECTION", 
  "fields": [
    { "fieldPath": "doctorId", "order": "ASCENDING" },
    { "fieldPath": "createdAt", "order": "DESCENDING" }
  ]
},
{
  "collectionGroup": "on-call-notifications",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "clinicId", "order": "ASCENDING" },
    { "fieldPath": "createdAt", "order": "DESCENDING" }
  ]
},
{
  "collectionGroup": "on-call-notifications",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "callSessionId", "order": "ASCENDING" }
  ]
}
```

### Firestore Security Rules

```javascript
// Agent-location mappings - Super admin only
match /agent-location-mappings/{agentId} {
  allow read, write: if isAuthenticated() && isAdmin();
}

// On-call schedules - Clinic admins can manage their clinic's schedules
match /on-call-schedules/{scheduleId} {
  allow read: if isAuthenticated() && 
    (isAdmin() || belongsToSameClinic(scheduleId, 'on-call-schedules'));
  allow create: if isAuthenticated() && 
    isAdmin() && 
    request.resource.data.clinicId == getUserClinicId();
  allow update, delete: if isAuthenticated() && 
    isAdmin() && 
    belongsToSameClinic(scheduleId, 'on-call-schedules');
}

// On-call notifications - Read-only for clinic admins, system writes only
match /on-call-notifications/{notificationId} {
  allow read: if isAuthenticated() && 
    (isAdmin() || belongsToSameClinic(notificationId, 'on-call-notifications'));
  // No client-side writes - server-side only
  allow create, update, delete: if false;
}
```

## Implementation Tasks

### Task 1: Create Firestore Collections and Documents
- [x] Create initial documents in each collection to establish structure
- [x] Add validation for document schemas
- [x] Create sample data for development and testing
- [x] Verify collections are properly created in Firestore console

### Task 2: Update firestore.indexes.json
- [x] Add all required indexes to firestore.indexes.json
- [x] Validate index definitions against query patterns
- [ ] Deploy indexes using Firebase CLI
- [ ] Monitor index build progress and completion

### Task 3: Update firestore.rules  
- [x] Add security rules for all new collections
- [x] Add helper functions for clinic isolation
- [ ] Test rules with Firebase emulator
- [ ] Deploy updated rules to production

### Task 4: Create Migration and Seed Scripts
- [x] Create `scripts/migrate-agent-location-mappings.ts` (already existed)
- [x] Create `scripts/setup-on-call-collections.ts` 
- [x] Create `scripts/validate-database-integrity.ts`
- [x] Create seed data functionality within setup scripts

### Task 5: Database Schema Documentation
- [x] Create `docs/database-schema.md` with complete schema
- [x] Create MySQL migration reference in documentation
- [x] Document all relationships and constraints
- [x] Create entity relationship diagrams

## Definition of Done

- [ ] All Firestore collections exist with proper document structure
- [ ] All required indexes are deployed and active  
- [ ] Security rules are deployed and tested with different user roles
- [ ] Migration scripts execute successfully
- [ ] Database schema documentation is complete and accurate
- [ ] Sample data is available for development and testing
- [ ] All existing functionality continues to work without issues
- [ ] AgentLocationMappingService can successfully query the new collection
- [ ] TypeScript compilation passes without errors
- [ ] Linting rules pass without warnings

## Dependencies

- ✅ Story 15: Foundation services are implemented
- ✅ Firebase CLI is available and configured
- ✅ Firestore database is accessible
- ✅ Proper Firebase project permissions

## Risks & Mitigation

### Risk: Index Build Time
- **Mitigation**: Deploy indexes during low-traffic periods
- **Mitigation**: Monitor index build progress and have rollback plan

### Risk: Security Rule Changes Break Existing Functionality
- **Mitigation**: Test with Firebase emulator first
- **Mitigation**: Deploy rules incrementally with monitoring

### Risk: Data Migration Issues
- **Mitigation**: Run migration scripts against test data first
- **Mitigation**: Have backup and rollback procedures

## Future MySQL Migration Schema

```sql
-- Agent Location Mappings Table
CREATE TABLE agent_location_mappings (
  agent_id VARCHAR(255) PRIMARY KEY,
  location_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_clinic_active (clinic_id, is_active),
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id)
);

-- On-Call Schedules Table  
CREATE TABLE on_call_schedules (
  id VARCHAR(255) PRIMARY KEY,
  doctor_id VARCHAR(255) NOT NULL,
  doctor_name VARCHAR(255) NOT NULL,
  doctor_phone VARCHAR(20) NOT NULL,
  location_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  timezone VARCHAR(50) NOT NULL,
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by VARCHAR(255) NOT NULL,
  INDEX idx_location_date_active (location_id, date, is_active),
  INDEX idx_doctor_date_active (doctor_id, date, is_active),
  INDEX idx_clinic_date_active (clinic_id, date, is_active),
  INDEX idx_current_oncall (location_id, date, start_time, end_time, is_active),
  FOREIGN KEY (doctor_id) REFERENCES users(id),
  FOREIGN KEY (location_id) REFERENCES locations(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id),
  FOREIGN KEY (created_by) REFERENCES users(id)
);

-- On-Call Notifications Table
CREATE TABLE on_call_notifications (
  id VARCHAR(255) PRIMARY KEY,
  schedule_id VARCHAR(255) NOT NULL,
  call_session_id VARCHAR(255) NOT NULL, 
  doctor_id VARCHAR(255) NOT NULL,
  clinic_id INT NOT NULL,
  notification_time TIMESTAMP NOT NULL,
  sms_message_id VARCHAR(255),
  status ENUM('sent', 'failed', 'delivered') NOT NULL,
  call_type VARCHAR(50),
  caller_phone VARCHAR(20),
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_schedule_created (schedule_id, created_at),
  INDEX idx_doctor_created (doctor_id, created_at),
  INDEX idx_clinic_created (clinic_id, created_at),
  INDEX idx_call_session (call_session_id),
  FOREIGN KEY (schedule_id) REFERENCES on_call_schedules(id),
  FOREIGN KEY (call_session_id) REFERENCES call_sessions(id),
  FOREIGN KEY (doctor_id) REFERENCES users(id),
  FOREIGN KEY (clinic_id) REFERENCES clinics(id)
);
```

## Next Steps

After completing this story:
1. Story 17: Location Office Hours Management API
2. Story 18: On-Call Doctors Database & Models  
3. Story 19: SMS Notification System
4. Story 20: Admin Interface - Office Hours
5. Story 21: Admin Interface - On-Call Schedules

## Chat Log

### 2024-01-15 - Story Creation
- Created comprehensive database schema and infrastructure story
- Defined all required Firestore collections, indexes, and security rules
- Prepared MySQL migration schema for future database transition
- Ready to begin implementation of complete database foundation 🏗️

### 2024-01-15 - Implementation Progress
- ✅ **Discovery**: Found existing firestore.indexes.json already contained all required indexes
- ✅ **Security Rules**: Added on-call-schedules and on-call-notifications security rules to firestore.rules
- ✅ **Migration Scripts**: Created comprehensive setup-on-call-collections.ts script with sample data
- ✅ **Validation Scripts**: Created validate-database-integrity.ts with full schema validation
- ✅ **Documentation**: Created comprehensive database-schema.md with ERDs and query patterns
- ⚠️ **Lint Issues**: Minor TypeScript lint warnings in validation script (non-blocking)
- 🚧 **Remaining**: Deploy indexes and security rules to Firebase (requires Firebase CLI)

### 2024-01-15 - ✅ **COLLECTIONS SUCCESSFULLY DEPLOYED**
- ✅ **Script Execution**: Enhanced setup script ran successfully with real database queries
- ✅ **Real IDs Retrieved**: Dr. Meena George (`N7NiUCplfCS83EKs1tdcDJu2bPo1`), Jessica Kamching (`nI70gD4r34P4kFXPc24LSIdc85l2`), Lombard location (`118`)
- ✅ **Collections Created**: All 3 collections (agent-location-mappings, on-call-schedules, on-call-notifications) now exist in Firestore
- ✅ **Sample Data**: Created realistic schedules for today, tomorrow, and weekend coverage
- ✅ **Cross-References**: Notifications properly linked to actual schedule IDs
- ✅ **Validation Passed**: All collections verified to exist and contain data
- 🎯 **Ready for Development**: Database infrastructure complete and ready for application services

### Real Data Features Implemented:
- **User ID Resolution**: Queries users collection by email and clinic ID
- **Location ID Resolution**: Queries locations collection by name and clinic ID  
- **Agent Configuration**: Checks configurations collection for DialogFlow agent setup
- **Data Validation**: Verifies all required entities exist before creating records
- **Cross-Reference Support**: Links schedules to notifications using real IDs
- **Timezone Integration**: Uses location-specific timezone from database

### Implementation Summary
- **Core Infrastructure**: All Firestore collections, indexes, and security rules are ready
- **Migration Support**: Complete scripts for setup, validation, and seed data
- **Documentation**: Comprehensive schema documentation with MySQL migration reference
- **Status**: Ready for deployment and testing 🚀 