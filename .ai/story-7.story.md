# Story: External API v2 Patient and User Endpoints Implementation

<version>1.0.0</version>
<status>Draft</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>8</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the implementation of the RESTful API endpoints for patients and users in the External API v2. These endpoints will provide secure access to patient and user data through provider-agnostic interfaces, initially using the Nextech provider implementation. Due to the sensitive nature of patient data, special attention will be given to security, privacy, and compliance requirements.

## Acceptance Criteria

- [ ] Create GET /api/external-api/v2/patients endpoint
- [ ] Create GET /api/external-api/v2/users endpoint
- [ ] Implement filtering capabilities for both endpoints
- [ ] Ensure proper API key validation and authorization checks
- [ ] Implement request validation using Zod schemas
- [ ] Add proper error handling for all scenarios
- [ ] Implement pagination for list endpoints
- [ ] Add comprehensive Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Implement proper data sanitization for sensitive patient information
- [ ] Ensure proper provider selection from request

## Technical Details

### Patient Endpoint

The patient endpoint will:
- [ ] Support GET requests to retrieve all patients or a specific patient by ID
- [ ] Accept query parameters for filtering (`id`, `fullName`, `dateOfBirth`, `email`, `phoneNumber`)
- [ ] Use the provider's patient service to retrieve data
- [ ] Return data in a consistent format matching the Patient model
- [ ] Support pagination through `limit` and `offset` parameters
- [ ] Return proper status codes for different scenarios (200, 400, 401, 403, 404, 500)
- [ ] Implement data minimization to limit exposure of sensitive information
- [ ] Include proper logging with PHI redaction

```typescript
// GET /api/external-api/v2/patients
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const patientService = provider.getPatientService();
    
    // Handle GET requests
    if (req.method === 'GET') {
      // Get a specific patient by ID
      if (req.query.id) {
        const patient = await patientService.getPatientById(req.query.id as string);
        if (!patient) {
          return res.status(404).json({ message: 'Patient not found' });
        }
        return res.status(200).json(patient);
      }
      
      // Get all patients with optional filtering
      const patients = await patientService.getPatients(req.query);
      return res.status(200).json(patients);
    }
    
    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation and authorization middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requirePatientAccess],
});
```

### User Endpoint

The user endpoint will:
- [ ] Support GET requests to retrieve all users or a specific user by ID
- [ ] Accept query parameters for filtering (`id`, `fullName`, `email`, `role`)
- [ ] Use the provider's user service to retrieve data
- [ ] Return data in a consistent format matching the User model
- [ ] Support pagination through `limit` and `offset` parameters
- [ ] Return proper status codes for different scenarios (200, 400, 401, 403, 404, 500)

```typescript
// GET /api/external-api/v2/users
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const userService = provider.getUserService();
    
    // Handle GET requests
    if (req.method === 'GET') {
      // Get a specific user by ID
      if (req.query.id) {
        const user = await userService.getUserById(req.query.id as string);
        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }
        return res.status(200).json(user);
      }
      
      // Get all users with optional filtering
      const users = await userService.getUsers(req.query);
      return res.status(200).json(users);
    }
    
    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey, requireUserAccess],
});
```

### Request Validation

- [ ] Create Zod schemas for request validation
- [ ] Validate query parameters before processing
- [ ] Return descriptive error messages for invalid requests

```typescript
import { z } from 'zod';

// Schema for GET /api/external-api/v2/patients query parameters
const getPatientQuerySchema = z.object({
  id: z.string().optional(),
  fullName: z.string().optional(),
  dateOfBirth: z.string().optional(), // Format: YYYY-MM-DD
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

// Schema for GET /api/external-api/v2/users query parameters
const getUserQuerySchema = z.object({
  id: z.string().optional(),
  fullName: z.string().optional(),
  email: z.string().email().optional(),
  role: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});
```

### Authorization Middleware

- [ ] Implement middleware to validate access to patient and user data
- [ ] Check API key permissions for different types of data
- [ ] Return appropriate error messages for unauthorized access

```typescript
// Example middleware for patient data access
export const requirePatientAccess = async (
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>
) => {
  const apiKey = req.headers['x-api-key'] as string;
  
  try {
    const hasAccess = await checkPatientDataAccess(apiKey);
    
    if (!hasAccess) {
      return res.status(403).json({
        message: 'Access denied: Insufficient permissions for patient data'
      });
    }
    
    return next();
  } catch (error) {
    return res.status(500).json({
      message: 'Error verifying access permissions'
    });
  }
};

// Similar middleware for user data access
```

### Security and Privacy Considerations

- [ ] Implement PHI redaction in logs and error messages
- [ ] Apply data minimization principles in API responses
- [ ] Use proper authentication and authorization checks
- [ ] Follow HIPAA compliance guidelines for patient data
- [ ] Implement proper rate limiting to prevent abuse

### Swagger Documentation

- [ ] Add comprehensive Swagger/OpenAPI annotations for both endpoints
- [ ] Document all request parameters, response formats, and error scenarios
- [ ] Include examples for common use cases
- [ ] Document security requirements

## Tasks

- [ ] Create Zod schemas for request validation
- [ ] Implement authorization middleware for patient and user data
- [ ] Implement GET /api/external-api/v2/patients endpoint
- [ ] Implement GET /api/external-api/v2/users endpoint
- [ ] Add PHI redaction and data minimization
- [ ] Add Swagger/OpenAPI documentation
- [ ] Write unit tests for both endpoints
- [ ] Create integration tests with the Nextech provider
- [ ] Update API documentation with usage examples
- [ ] Verify security and privacy compliance

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication
- Completed story-3: Nextech API Basic Services
- Completed story-4: Nextech API Patient and User Services
- Completed story-5: Nextech API Appointment Service Implementation
- Completed story-6: External API v2 Clinic and Location Endpoints

## Risks and Mitigations

- **Risk**: Exposing sensitive patient information in responses or logs
  **Mitigation**: Implement comprehensive PHI redaction and follow data minimization principles

- **Risk**: Unauthorized access to patient or user data
  **Mitigation**: Implement robust authentication and authorization checks

- **Risk**: Performance issues with large datasets
  **Mitigation**: Implement pagination and appropriate limits

- **Risk**: Non-compliance with healthcare data regulations
  **Mitigation**: Follow HIPAA compliance guidelines and conduct security reviews

- **Risk**: Rate limiting from the Nextech API could affect reliability
  **Mitigation**: Ensure proper handling of rate limit errors and implement caching where appropriate

## Resources

- [NextJS API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [Zod Documentation](https://github.com/colinhacks/zod)
- [Swagger/OpenAPI Specification](https://swagger.io/specification/)
- [HIPAA Security Rule Guidance](https://www.hhs.gov/hipaa/for-professionals/security/index.html)
- [Architecture Document](.ai/arch.md)

## Notes

- This story implements the second set of endpoints for the External API v2, focusing on patient and user data
- Special attention is given to security and privacy due to the sensitive nature of patient data
- The implementation should follow the patterns established in Story 6 (Clinic and Location Endpoints)

## Chat Log

*Created 2024-03-30*

- Created initial story draft for implementing External API v2 Patient and User Endpoints 