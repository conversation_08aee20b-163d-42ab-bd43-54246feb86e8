# 1. Title: PRD for External API v2 and Nextech Integration

<version>1.0.0</version>

## Status: In Review

## Intro

This PRD outlines the integration of the Nextech Practice+ APIs with our Front Desk Portal system. The goal is to create a modular and extensible External API v2 that will initially use Nextech as a provider for clinic data, locations, patients, users, and appointment booking functionality, while being designed to easily incorporate additional providers in the future.

## Goals

- Create a modular External API v2 architecture that supports multiple providers
- Implement Nextech Practice+ API as the first provider
- Enable secure access to patient data, clinics, locations, users and appointment booking
- Design a provider-agnostic interface that abstracts away provider-specific details
- Support future expansion with minimal changes to the core architecture
- Maintain backward compatibility with v1 API where possible

## Features and Requirements

- Functional requirements
  - Access to clinic information via Nextech API
  - Access to location information via Nextech API
  - Access to patient data via Nextech API
  - Access to user/practitioner data via Nextech API
  - Appointment booking functionality via Nextech API
  - Consistent error handling across providers
  - Proper authentication and authorization
  
- Non-functional requirements
  - High performance (response time < 500ms for 95% of requests)
  - High availability (99.9% uptime)
  - Scalability to handle increasing numbers of requests
  - Security compliance with healthcare data standards
  - Comprehensive logging and monitoring
  
- Integration requirements
  - OAuth 2.0 authentication with Nextech
  - Rate limiting management (20 requests per second per endpoint)
  - Pagination handling
  - Response format normalization

## Epic List

### Epic-1: External API v2 Core Architecture

### Epic-2: Nextech API Provider Implementation

### Epic-3: External API v2 Endpoints Implementation

### Epic-N: Future Provider Integrations (Beyond Scope of current PRD)

## Epic 1: Story List

- Story 1: External API v2 Core Architecture Design
  Status: ''
  Requirements:
  - Design provider-agnostic interfaces and contracts
  - Create adapter pattern for provider implementations
  - Design error handling and logging strategy
  - Create authentication and authorization strategy

- Story 2: External API v2 Base Implementation
  Status: ''
  Requirements:
  - Create provider registry
  - Implement provider factory
  - Create base controller classes
  - Implement middleware for request validation and authentication

## Epic 2: Story List

- Story 1: Nextech API Provider Authentication
  Status: ''
  Requirements:
  - Implement OAuth 2.0 authentication
  - Create token management and refresh strategy
  - Implement secure credential storage

- Story 2: Nextech API Basic Services
  Status: ''
  Requirements:
  - Implement clinic service
  - Implement location service
  - Implement base HTTP client with error handling and retries

- Story 3: Nextech API Patient and User Services
  Status: ''
  Requirements:
  - Implement patient service
  - Implement user/practitioner service
  - Create data mapping to common models

- Story 4: Nextech API Appointment Service
  Status: ''
  Requirements:
  - Implement appointment booking
  - Implement appointment search and retrieval
  - Implement appointment modification and cancellation

## Epic 3: Story List

- Story 1: Clinic and Location Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/clinics endpoint
  - Create GET /api/external-api/v2/locations endpoint
  - Implement filtering and search capabilities

- Story 2: Patient and User Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/patients endpoint
  - Create GET /api/external-api/v2/users endpoint
  - Implement proper authentication and authorization checks

- Story 3: Appointment Endpoints
  Status: ''
  Requirements:
  - Create GET /api/external-api/v2/appointments endpoint
  - Create POST /api/external-api/v2/appointments endpoint for booking
  - Create PATCH and DELETE methods for appointment management

## Epic 4: Office Hours & On-Call Doctors Integration

- **Story 15: Office Hours Integration - Foundation Services**
  - Status: In Progress
  - Implements foundational services for office hours detection and agent-to-location mapping. Includes Firestore integration, office hours utility service, and enhanced call session handler for office hours context.

- **Story 16: Database Schema & Infrastructure Setup**
  - Status: Not Started
  - Sets up Firestore collections, indexes, and security rules for agent-location mappings, on-call schedules, and notifications. Includes migration scripts, schema documentation, and seed data for testing.

- **Story 17: Location Office Hours Management API**
  - Status: Not Started
  - Enhances the location API with office hours CRUD operations, validation, and timezone handling. Adds dedicated endpoints for office hours management and status, and improves office hours utility services.

- **Story 18: On-Call Doctors Database & Models**
  - Status: Not Started
  - Implements models and service layer for on-call doctor schedules and notifications. Adds API endpoints for schedule management, eligible doctors, and conflict detection.

- **Story 19: SMS Notification System**
  - Status: Not Started
  - Implements SMS notification system for alerting on-call doctors after hours. Integrates with call session handler, adds notification logging, retry logic, and admin management endpoints.

## Technology Stack

| Technology | Description |
| ------------ | ------------------------------------------------------------- |
| Next.js | Server-side rendering and API framework |
| TypeScript | Type-safe language for backend development |
| Zod | Schema validation for API requests and responses |
| Jest | Testing framework |
| Swagger/OpenAPI | API documentation |
| Axios | HTTP client for provider API communication |

## Reference

### Provider Adapter Pattern

```mermaid
graph TD
    Client[API Client] --> ExternalAPIV2[External API v2 Controllers]
    ExternalAPIV2 --> ProviderRegistry[Provider Registry]
    ProviderRegistry --> ProviderFactory[Provider Factory]
    ProviderFactory --> NextechProvider[Nextech Provider]
    ProviderFactory --> FutureProvider1[Future Provider 1]
    ProviderFactory --> FutureProvider2[Future Provider 2]
    NextechProvider --> NextechAPIClient[Nextech API Client]
    FutureProvider1 --> Provider1APIClient[Provider 1 API Client]
    FutureProvider2 --> Provider2APIClient[Provider 2 API Client]
```

## Data Models, API Specs, Schemas, etc...

### Common Models

```typescript
// Clinic Model
interface Clinic {
  id: string;
  name: string;
  address: Address;
  phoneNumber: string;
  emailAddress?: string;
  website?: string;
  providerInfo: ProviderInfo;
}

// Location Model
interface Location {
  id: string;
  name: string;
  address: Address;
  clinicId: string;
  providerInfo: ProviderInfo;
}

// Provider Info Model
interface ProviderInfo {
  provider: string; // e.g., "nextech", "otherProvider"
  externalId: string; // ID in the provider's system
}
```

## Project Structure

```text
pages/api/external-api/
├── v1/                  # Existing v1 API implementation
├── v2/                  # New v2 API implementation
│   ├── index.ts         # Main API router
│   ├── controllers/     # API endpoint controllers
│   │   ├── clinics.ts
│   │   ├── locations.ts
│   │   ├── patients.ts
│   │   ├── users.ts
│   │   └── appointments.ts
│   ├── providers/       # Provider implementations
│   │   ├── index.ts     # Provider registry and factory
│   │   ├── types.ts     # Common provider interfaces
│   │   ├── nextech/     # Nextech provider implementation
│   │   │   ├── index.ts
│   │   │   ├── client.ts
│   │   │   ├── auth.ts
│   │   │   ├── services/
│   │   │   │   ├── clinic.ts
│   │   │   │   ├── location.ts
│   │   │   │   ├── patient.ts
│   │   │   │   ├── user.ts
│   │   │   │   └── appointment.ts
│   │   │   └── models.ts
│   │   └── [future-provider]/
│   ├── middleware/      # Middleware for authentication, validation, etc.
│   ├── utils/           # Utility functions
│   └── models/          # Common data models
├── index.ts             # Root API handler (redirects to latest version)
└── README.md            # API documentation
```

## Change Log

| Change               | Story ID | Description                            |
| -------------------- | -------- | -------------------------------------- |
| Initial draft        | N/A      | Initial draft PRD                      | 