# Story: External API v2 Clinic and Location Endpoints Implementation

<version>1.0.0</version>
<status>Draft</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>5</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the implementation of the RESTful API endpoints for clinics and locations in the External API v2. These endpoints will provide access to clinic and location data through provider-agnostic interfaces, initially using the Nextech provider implementation.

## Acceptance Criteria

- [ ] Create GET /api/external-api/v2/clinics endpoint
- [ ] Create GET /api/external-api/v2/locations endpoint
- [ ] Implement filtering capabilities for both endpoints
- [ ] Ensure proper API key validation
- [ ] Implement request validation using Zod schemas
- [ ] Add proper error handling for all scenarios
- [ ] Implement pagination for list endpoints
- [ ] Add comprehensive Swagger/OpenAPI documentation
- [ ] Write unit tests for all endpoints
- [ ] Ensure proper provider selection from request

## Technical Details

### Clinic Endpoint

The clinic endpoint will:
- [ ] Support GET requests to retrieve all clinics or a specific clinic by ID
- [ ] Accept query parameters for filtering (`id`, `name`, `phoneNumber`)
- [ ] Use the provider's clinic service to retrieve data
- [ ] Return data in a consistent format matching the Clinic model
- [ ] Support pagination through `limit` and `offset` parameters
- [ ] Return proper status codes for different scenarios (200, 400, 401, 404, 500)

```typescript
// GET /api/external-api/v2/clinics
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const clinicService = provider.getClinicService();
    
    // Handle GET requests
    if (req.method === 'GET') {
      // Get a specific clinic by ID
      if (req.query.id) {
        const clinic = await clinicService.getClinicById(req.query.id as string);
        if (!clinic) {
          return res.status(404).json({ message: 'Clinic not found' });
        }
        return res.status(200).json(clinic);
      }
      
      // Get all clinics with optional filtering
      const clinics = await clinicService.getClinics(req.query);
      return res.status(200).json(clinics);
    }
    
    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
```

### Location Endpoint

The location endpoint will:
- [ ] Support GET requests to retrieve all locations or a specific location by ID
- [ ] Accept query parameters for filtering (`id`, `name`, `clinicId`, `phoneNumber`)
- [ ] Use the provider's location service to retrieve data
- [ ] Return data in a consistent format matching the Location model
- [ ] Support pagination through `limit` and `offset` parameters
- [ ] Return proper status codes for different scenarios (200, 400, 401, 404, 500)

```typescript
// GET /api/external-api/v2/locations
async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const provider = getProviderFromRequest(req);
    const locationService = provider.getLocationService();
    
    // Handle GET requests
    if (req.method === 'GET') {
      // Get a specific location by ID
      if (req.query.id) {
        const location = await locationService.getLocationById(req.query.id as string);
        if (!location) {
          return res.status(404).json({ message: 'Location not found' });
        }
        return res.status(200).json(location);
      }
      
      // Get all locations with optional filtering
      const locations = await locationService.getLocations(req.query);
      return res.status(200).json(locations);
    }
    
    // Handle unsupported methods
    return res.status(405).json({ message: 'Method not allowed' });
  } catch (error) {
    // Error handling is done by the createApiHandler wrapper
    throw error;
  }
}

// Export with API key validation middleware
export default createApiHandler(handler, {
  middleware: [validateApiKey],
});
```

### Request Validation

- [ ] Create Zod schemas for request validation
- [ ] Validate query parameters before processing
- [ ] Return descriptive error messages for invalid requests

```typescript
import { z } from 'zod';

// Schema for GET /api/external-api/v2/clinics query parameters
const getClinicQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});

// Schema for GET /api/external-api/v2/locations query parameters
const getLocationQuerySchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  clinicId: z.string().optional(),
  phoneNumber: z.string().optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  offset: z.string().regex(/^\d+$/).transform(Number).optional(),
  provider: z.string().optional(),
});
```

### Swagger Documentation

- [ ] Add comprehensive Swagger/OpenAPI annotations for both endpoints
- [ ] Document all request parameters, response formats, and error scenarios
- [ ] Include examples for common use cases

## Tasks

- [ ] Create Zod schemas for request validation
- [ ] Implement GET /api/external-api/v2/clinics endpoint
- [ ] Implement GET /api/external-api/v2/locations endpoint
- [ ] Add Swagger/OpenAPI documentation
- [ ] Write unit tests for both endpoints
- [ ] Create integration tests with the Nextech provider
- [ ] Update API documentation with usage examples

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication
- Completed story-3: Nextech API Basic Services
- Completed story-4: Nextech API Patient and User Services
- Completed story-5: Nextech API Appointment Service Implementation

## Risks and Mitigations

- **Risk**: Request validation may be too strict for client needs
  **Mitigation**: Start with basic validation and refine based on feedback

- **Risk**: Performance issues with large datasets
  **Mitigation**: Implement pagination and appropriate limits

- **Risk**: Rate limiting from the Nextech API could affect reliability
  **Mitigation**: Ensure proper handling of rate limit errors and implement caching where appropriate

- **Risk**: Documentation may become out of sync with implementation
  **Mitigation**: Use OpenAPI annotations directly in the code to keep documentation up to date

## Resources

- [NextJS API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [Zod Documentation](https://github.com/colinhacks/zod)
- [Swagger/OpenAPI Specification](https://swagger.io/specification/)
- [Architecture Document](.ai/arch.md)

## Notes

- This story implements the first set of endpoints for the External API v2, focusing on clinic and location data
- The implementation should follow the existing patterns and use the provider registry to support multiple providers
- Future stories will implement the Patient, User, and Appointment endpoints

## Chat Log

*Created 2024-03-30*

- Created initial story draft for implementing External API v2 Clinic and Location Endpoints 