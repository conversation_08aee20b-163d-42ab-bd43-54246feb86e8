# Story: External API v2 Core Architecture Design and Implementation

<version>1.0.0</version>
<status>Completed</status>
<epic>External API v2 Core Architecture</epic>
<story-points>8</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story covers the design and implementation of the core architecture for the External API v2, which will initially support Nextech Practice+ API as a provider and be designed for extensibility to support future providers.

## Acceptance Criteria

- [x] Provider interfaces defined with TypeScript for all required services (clinic, location, patient, user, appointment)
- [x] Provider registry implemented to manage multiple provider implementations
- [x] Provider factory implemented to create provider instances based on configuration
- [x] Common data models defined that are provider-agnostic
- [x] Error handling and logging strategy implemented
- [x] Authentication and authorization middleware created
- [x] Basic project structure created following the architecture document
- [x] Unit tests written for core components
- [x] Documentation updated to reflect the new architecture

## Technical Details

### Provider Interfaces

We'll need to create interfaces for the following:
- [x] `IProvider` - Base provider interface
- [x] `IClinicService` - Clinic data access
- [x] `ILocationService` - Location data access
- [x] `IPatientService` - Patient data access
- [x] `IUserService` - User/Practitioner data access
- [x] `IAppointmentService` - Appointment booking and management

### Provider Registry

The provider registry will:
- [x] Allow registration of multiple providers
- [x] Store providers by name
- [x] Return the appropriate provider based on request or configuration
- [x] Have a default provider configuration

### Error Handling

- [x] Create standardized error types
- [x] Implement consistent error response format
- [x] Map provider-specific errors to standard errors

### Authentication

- [x] Implement API key validation for external clients
- [ ] Create token management for provider APIs (OAuth)
- [ ] Implement secure credential storage

## Tasks

- [x] Create project structure for External API v2
- [x] Define common data models (clinic, location, patient, user, appointment)
- [x] Create provider interfaces
- [x] Implement provider registry
- [x] Create error handling utilities
- [x] Implement authentication middleware
- [x] Create base NextJS API handlers with provider selection
- [x] Create tests for core components
- [x] Update documentation

## Dependencies

- None

## Risks and Mitigations

- **Risk**: Provider interfaces may not cover all necessary functionality
  **Mitigation**: Review Nextech API documentation thoroughly and allow for future interface extensions

- **Risk**: Authentication mechanisms may vary between providers
  **Mitigation**: Create an abstraction layer for authentication that can be extended per provider

## Resources

- [Nextech API Documentation](https://nextechsystems.github.io/practiceplusapidocspub/)
- [Architecture Document](.ai/arch.md)

## Notes

- This story focuses on the core architecture design and implementation, not the specific Nextech provider implementation
- The goal is to create a flexible, extensible architecture that can support multiple providers with minimal changes

## Chat Log

*Updated 2024-03-29*

- Created core architecture components:
  - Provider interfaces for all required services
  - Common data models for clinic, location, patient, user, and appointment data
  - Provider registry and factory for managing providers
  - Error handling utilities with standardized error types
  - Authentication middleware for API key validation
  - Base NextJS API handlers with error handling and middleware support
  - Tests for core components

*Updated 2024-03-29*

- Restructured the project to follow Next.js best practices:
  - Moved implementation from `/pages/api/external-api/v2/` to `/lib/external-api/v2/`
  - Kept only API route handlers in the `pages/` directory
  - Fixed build issues related to ES module exports
  - Updated documentation to reflect the new structure 