# Story: Finalize Nextech Integration and Implement Pagination

<version>1.0.0</version>
<status>Draft</status>
<epic>External API v2 Endpoints Implementation</epic>
<story-points>8</story-points>
<priority>High</priority>
<assigned-to>TBD</assigned-to>

## Description

This story focuses on finalizing the Nextech API integration by ensuring all endpoints are fully implemented, well-tested, and handle errors appropriately. Additionally, it covers implementing consistent pagination across all list endpoints to optimize data retrieval. This work is critical to ensure the External API v2 provides a reliable and efficient interface for clients interacting with Nextech data.

## Acceptance Criteria

- [ ] Complete and verify implementation of all required Nextech API endpoints
- [ ] Implement standardized pagination for all list endpoints following Nextech's model (default 10 items, max 50)
- [ ] Enhance error handling for Nextech-specific errors and edge cases
- [ ] Improve token refresh and authentication logic for reliable long-term operation
- [ ] Ensure compliance with Nextech's rate limiting (20 requests per second per endpoint)
- [ ] Add proper validation for Nextech-specific data structures and extensions
- [ ] Implement comprehensive logging for API communication issues
- [ ] Create integration tests for all endpoints, including error scenarios
- [ ] Document all supported endpoints, parameters, and response formats
- [ ] Optimize performance for high-traffic endpoints through appropriate caching

## Technical Details

### Nextech API Endpoint Audit and Completion

A thorough audit of all Nextech endpoints will be conducted to ensure full coverage:

```typescript
// Required Nextech endpoints to verify/implement
const requiredEndpoints = [
  'Patient',
  'Appointment',
  'Slot',
  'AppointmentType',
  'AppointmentPurpose',
  'Location',
  'Practitioner',
  'PatientType'
];

// Example mapping to our API endpoints
const endpointMapping = {
  'Patient': '/api/external-api/v2/patients',
  'Appointment': '/api/external-api/v2/appointments',
  'Slot': '/api/external-api/v2/appointment-availability',
  'AppointmentType': '/api/external-api/v2/appointment-types',
  'Location': '/api/external-api/v2/locations',
  'Practitioner': '/api/external-api/v2/users',
  // Add remaining endpoints
};
```

Each endpoint will be verified for:
- Correct authentication handling
- Proper parameter passing
- Complete data mapping
- Error handling
- Rate limit compliance

### Standardized Pagination Implementation

The pagination implementation will follow Nextech's model while providing a consistent interface:

```typescript
// Enhanced interface with pagination support
export interface IBaseService<T> {
  getAll(filters?: Record<string, unknown>, pagination?: PaginationParams): Promise<PaginatedResult<T>>;
  // Other methods...
}

// Pagination parameters based on Nextech's model
interface PaginationParams {
  limit: number; // Default 10, max 50
  offset: number; // For handling pages beyond the first
}

// Standardized result format
interface PaginatedResult<T> {
  items: T[];
  pagination: {
    totalCount: number;
    limit: number;
    offset: number;
    hasMore: boolean;
    links: {
      first?: string;
      prev?: string;
      next?: string;
      last?: string;
    }
  }
}
```

### Pagination Handler

A pagination middleware will be implemented to standardize pagination handling:

```typescript
// Middleware to handle pagination parameters
export function withPagination(handler: NextApiHandler): NextApiHandler {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // Parse and validate pagination parameters
    const limit = req.query.limit ? 
      Math.min(parseInt(req.query.limit as string, 10), 50) : 10;
    const offset = req.query.offset ? 
      parseInt(req.query.offset as string, 10) : 0;
    
    // Attach validated pagination to request
    req.pagination = { limit, offset };
    
    // Call the original handler
    return handler(req, res);
  };
}

// Example usage in API route
export default createApiHandler(
  withPagination(async function patientsHandler(req: NextApiRequest, res: NextApiResponse) {
    const provider = getProviderFromRequest(req);
    const patientService = provider.getPatientService();
    
    const paginatedResult = await patientService.getPatients(
      req.query, 
      req.pagination
    );
    
    // Add pagination links to response headers
    if (paginatedResult.pagination.links.next) {
      res.setHeader('Link', `<${paginatedResult.pagination.links.next}>; rel="next"`);
    }
    
    return res.status(200).json(paginatedResult);
  }),
  {
    middleware: [validateApiKey]
  }
);
```

### Enhanced Error Handling

Improved error handling for Nextech-specific errors:

```typescript
// Nextech-specific error handler
function handleNextechError(error: unknown): ApiError {
  if (axios.isAxiosError(error)) {
    const status = error.response?.status || 500;
    const data = error.response?.data;
    
    // Map Nextech error codes to appropriate HTTP status and messages
    switch (status) {
      case 401:
        return new ApiError(401, 'AUTH_ERROR', 'Authentication with Nextech failed');
      case 403:
        return new ApiError(403, 'FORBIDDEN', 'Not authorized to access this resource');
      case 404:
        return new ApiError(404, 'NOT_FOUND', 'Resource not found in Nextech');
      case 429:
        return new ApiError(429, 'RATE_LIMITED', 'Nextech rate limit exceeded, try again later');
      default:
        // Log detailed error for debugging
        logger.error({
          message: 'Nextech API error',
          status,
          data,
          stack: error.stack,
        });
        return new ApiError(500, 'PROVIDER_ERROR', 'Error communicating with Nextech');
    }
  }
  
  // Handle other error types
  return new ApiError(500, 'UNKNOWN_ERROR', 'An unexpected error occurred');
}
```

### Authentication Enhancements

Improved token management for Nextech:

```typescript
class NextechAuthManager {
  private token: string | null = null;
  private expiresAt: Date | null = null;
  private refreshPromise: Promise<string> | null = null;
  
  async getToken(): Promise<string> {
    // Return existing token if valid
    if (this.token && this.expiresAt && this.expiresAt > new Date(Date.now() + 5 * 60 * 1000)) {
      return this.token;
    }
    
    // If already refreshing, wait for that to complete
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    
    // Get new token
    this.refreshPromise = this.fetchNewToken();
    try {
      const token = await this.refreshPromise;
      return token;
    } finally {
      this.refreshPromise = null;
    }
  }
  
  private async fetchNewToken(): Promise<string> {
    // Implement OAuth token request to Nextech
    const response = await axios.post(
      'https://login.microsoftonline.com/nextech-api.com/oauth2/token',
      new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: process.env.NEXTECH_CLIENT_ID!,
        client_secret: process.env.NEXTECH_CLIENT_SECRET!,
        resource: process.env.NEXTECH_RESOURCE!,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );
    
    // Store token and expiration
    this.token = response.data.access_token;
    this.expiresAt = new Date(Date.now() + response.data.expires_in * 1000);
    
    return this.token;
  }
}
```

### Rate Limiting

Implement rate limiting compliance with Nextech's requirements:

```typescript
class NextechRateLimiter {
  private requestCounts: Map<string, number[]> = new Map();
  private readonly maxRequests = 20; // 20 requests per second per endpoint
  private readonly timeWindow = 1000; // 1 second in milliseconds
  
  async acquireToken(endpoint: string): Promise<void> {
    // Get the current time
    const now = Date.now();
    
    // Get or initialize the request timestamps for this endpoint
    let requests = this.requestCounts.get(endpoint) || [];
    
    // Filter out requests older than the time window
    requests = requests.filter(timestamp => now - timestamp < this.timeWindow);
    
    // Check if we've hit the rate limit
    if (requests.length >= this.maxRequests) {
      // Calculate time to wait before next request
      const oldestRequest = requests[0];
      const timeToWait = this.timeWindow - (now - oldestRequest);
      
      // Wait for the required time
      await new Promise(resolve => setTimeout(resolve, timeToWait));
      
      // Try again after waiting
      return this.acquireToken(endpoint);
    }
    
    // Add this request to the list
    requests.push(now);
    this.requestCounts.set(endpoint, requests);
  }
}
```

## Tasks

subtask 1:
- [x] Conduct audit of all required Nextech endpoints vs. current implementation
- [x] Complete implementation of any missing endpoints

subtask 2:
- [x] Enhance NextechApiClient with improved error handling
- [x] Implement standardized pagination interfaces

subtask 3:
- [x] Update all list endpoints to support pagination
- [x] Add pagination link headers to responses

subtask 4:
- [x] Implement enhanced token management with proper refresh logic
- [x] Add rate limiting to comply with Nextech's restrictions

subtask 5:
- [x] Implement appropriate caching for frequently accessed, rarely changing data

subtask 6:
  - [x] Add detailed logging for API communication issues

subtask 7:
- [x] Create comprehensive integration tests for all endpoints

subtask 8:
- [x] Update documentation with pagination details and endpoint specifics

## Dependencies

- Completed story-1: External API v2 Core Architecture
- Completed story-2: Nextech API Provider Authentication
- Completed story-8: External API v2 Appointment Endpoints Implementation
- Completed story-9: External API v2 Appointment Types and Availability Endpoints
- Completed story-10: Enhance External API v2 Testing and Observability

## Risks and Mitigations

- **Risk**: Nextech API changes could break our integration
  **Mitigation**: Implement robust error handling and monitoring to detect issues quickly

- **Risk**: Rate limiting could affect performance during high traffic periods
  **Mitigation**: Implement client-side request batching and appropriate caching strategies

- **Risk**: Token expiration could cause service interruptions
  **Mitigation**: Implement preemptive token refresh and multiple retry attempts

- **Risk**: Pagination implementation could be inconsistent across endpoints
  **Mitigation**: Create a shared pagination utility and standardized interfaces

- **Risk**: Large datasets might be slow to process even with pagination
  **Mitigation**: Implement query optimizations and consider background processing for large exports

## Resources

- [Nextech API Documentation](https://nextechsystems.github.io/practiceplusapidocspub/)
- [FHIR STU3 Documentation](https://www.hl7.org/fhir/STU3/)
- [OAuth 2.0 Best Practices](https://oauth.net/2/) 