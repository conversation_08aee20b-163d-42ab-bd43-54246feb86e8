# Firebase Admin SDK configuration
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Firebase private key here\n-----<PERSON><PERSON> PRIVATE KEY-----"

# Next.js public environment variables for Firebase client SDK
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# External API access
EXTERNAL_SERVICE_API_KEY="api_key_for_external_services"

# Nextech API Credentials
NEXTECH_GROUP_ID="your_group_id"
NEXTECH_CLIENT_ID=your_client_id
NEXTECH_CLIENT_SECRET=your_client_secret
NEXTECH_RESOURCE=https://api.pm.nextech.com/
NEXTECH_BASE_URL=https://api.pm.nextech.com/api
NEXTECH_PRACTICE_ID=your_practice_id

# Twilio SMS Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# GCP Conversational Agent
GCP_PROJECT_ID=""
GCP_LOCATION_ID="global"
GCP_AGENT_ID=""
GCP_SERVICE_ACCOUNT_KEY=""
GCP_AUDIO_BUCKET_NAME=""

# MySQL Database Connection
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-username  
MYSQL_PASSWORD=your-mysql-password
MYSQL_DATABASE=frontdesk_ai
INSTANCE_CONNECTION_NAME=
MYSQL_SSL=false