const fs = require('fs');

// Read both index files
const localIndexes = JSON.parse(fs.readFileSync('firestore.indexes.json', 'utf8'));
const gcpIndexes = JSON.parse(fs.readFileSync('gcp-indexes.json', 'utf8'));

// Function to create a unique key for an index
function getIndexKey(index) {
  const fields = index.fields.map(f => `${f.fieldPath}:${f.order}`).join(',');
  return `${index.collectionGroup}|${fields}`;
}

// Create a map to track all unique indexes
const allIndexes = new Map();

// Add all GCP indexes first (existing ones)
gcpIndexes.indexes.forEach(index => {
  const key = getIndexKey(index);
  allIndexes.set(key, { ...index, source: 'gcp' });
});

// Add all local indexes (will include new ones and overwrite duplicates)
localIndexes.indexes.forEach(index => {
  const key = getIndexKey(index);
  const existing = allIndexes.get(key);
  allIndexes.set(key, {
    ...index,
    source: existing ? 'both' : 'local',
  });
});

// Convert back to array and sort for better organization
const mergedIndexes = Array.from(allIndexes.values())
  .map(({ source, ...index }) => index) // Remove the source field
  .sort((a, b) => {
    // Sort by collection group first, then by number of fields
    if (a.collectionGroup !== b.collectionGroup) {
      return a.collectionGroup.localeCompare(b.collectionGroup);
    }
    return a.fields.length - b.fields.length;
  });

// Create the complete index file
const completeIndexFile = {
  indexes: mergedIndexes,
  fieldOverrides: localIndexes.fieldOverrides || [],
};

// Write the merged file
fs.writeFileSync('firestore.indexes.json', JSON.stringify(completeIndexFile, null, 2));

console.log('✅ MERGED INDEX FILE CREATED');
console.log('============================');
console.log(`📊 Total indexes: ${mergedIndexes.length}`);
console.log(`📁 Updated: firestore.indexes.json`);
console.log('');
console.log('🚀 READY TO DEPLOY:');
console.log('   firebase deploy --only firestore:indexes');
console.log('');
console.log('📋 INDEX BREAKDOWN BY COLLECTION:');
const collectionCounts = {};
mergedIndexes.forEach(index => {
  collectionCounts[index.collectionGroup] = (collectionCounts[index.collectionGroup] || 0) + 1;
});
Object.entries(collectionCounts)
  .sort(([a], [b]) => a.localeCompare(b))
  .forEach(([collection, count]) => {
    console.log(`   ${collection}: ${count} indexes`);
  });
