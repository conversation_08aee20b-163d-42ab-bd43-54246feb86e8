-- FrontDesk AI - MySQL Database Schema
-- Migration from Firestore to MySQL
-- Created: 2025-01-08

SET FOREIGN_KEY_CHECKS = 0;
DROP DATABASE IF EXISTS frontdesk_ai;
CREATE DATABASE frontdesk_ai CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE frontdesk_ai;

-- =============================================
-- CORE ORGANIZATION TABLES
-- =============================================

CREATE TABLE practices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_practices_uuid (uuid),
    INDEX idx_practices_name (name)
) ENGINE=InnoDB;

CREATE TABLE clinics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    practice_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (practice_id) REFERENCES practices(id) ON DELETE CASCADE,
    INDEX idx_clinics_uuid (uuid),
    INDEX idx_clinics_practice_id (practice_id),
    INDEX idx_clinics_name (name)
) ENGINE=InnoDB;

CREATE TABLE locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    clinic_id INT NOT NULL,
    practice_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    time_zone VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    FOREIGN KEY (practice_id) REFERENCES practices(id) ON DELETE CASCADE,
    INDEX idx_locations_uuid (uuid),
    INDEX idx_locations_clinic_id (clinic_id),
    INDEX idx_locations_practice_id (practice_id),
    INDEX idx_locations_name (name),
    INDEX idx_locations_is_active (is_active)
) ENGINE=InnoDB;

CREATE TABLE location_office_hours (
    id INT AUTO_INCREMENT PRIMARY KEY,
    location_id INT NOT NULL,
    day_of_week ENUM('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    INDEX idx_office_hours_location_day (location_id, day_of_week),
    UNIQUE KEY uk_location_day (location_id, day_of_week)
) ENGINE=InnoDB;

-- =============================================
-- USER MANAGEMENT TABLES
-- =============================================

CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    clinic_id INT NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('admin', 'doctor', 'nurse', 'receptionist', 'manager') DEFAULT 'receptionist',
    specialty VARCHAR(255),
    profile_picture VARCHAR(500),
    can_take_appointments BOOLEAN DEFAULT FALSE,
    current_location_id INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    FOREIGN KEY (current_location_id) REFERENCES locations(id) ON DELETE SET NULL,
    INDEX idx_users_uuid (uuid),
    INDEX idx_users_clinic_id (clinic_id),
    INDEX idx_users_email (email),
    INDEX idx_users_phone (phone),
    INDEX idx_users_role (role),
    INDEX idx_users_specialty (specialty),
    INDEX idx_users_can_take_appointments (can_take_appointments),
    INDEX idx_users_current_location_id (current_location_id),
    INDEX idx_users_is_active (is_active)
) ENGINE=InnoDB;

CREATE TABLE user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    appointment_notifications_enabled BOOLEAN DEFAULT TRUE,
    incoming_call_notifications_enabled BOOLEAN DEFAULT TRUE,
    voicemail_notifications_enabled BOOLEAN DEFAULT TRUE,
    additional_preferences JSON,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_preferences_user_id (user_id)
) ENGINE=InnoDB;

CREATE TABLE user_locations (
    user_id INT NOT NULL,
    location_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, location_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    INDEX idx_user_locations_location_id (location_id)
) ENGINE=InnoDB;

-- =============================================
-- CLIENT/PATIENT TABLES
-- =============================================

CREATE TABLE clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    clinic_id INT NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    email VARCHAR(255),
    birthday DATE,
    insurance_company VARCHAR(255),
    insurance_group_number VARCHAR(100),
    subscriber_name VARCHAR(255),
    medical_history TEXT,
    recent_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    INDEX idx_clients_uuid (uuid),
    INDEX idx_clients_clinic_id (clinic_id),
    INDEX idx_clients_full_name (full_name),
    INDEX idx_clients_phone_number (phone_number),
    INDEX idx_clients_email (email),
    INDEX idx_clients_birthday (birthday),
    INDEX idx_clients_name_birthday (full_name, birthday)
) ENGINE=InnoDB;

-- =============================================
-- CALL MANAGEMENT TABLES
-- =============================================

CREATE TABLE calls (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    client_id INT,
    user_id INT,
    clinic_id INT NOT NULL,
    location_id INT,
    session_id VARCHAR(255),
    agent_id VARCHAR(255),
    call_date DATETIME NOT NULL,
    phone_number VARCHAR(20),
    reason TEXT,
    summary TEXT,
    voicemail_summary TEXT,
    transcription LONGTEXT,
    transcription_with_audio LONGTEXT,
    recording_url VARCHAR(500),
    voicemail_url VARCHAR(500),
    notes TEXT,
    priority_score INT DEFAULT 0,
    is_urgent BOOLEAN DEFAULT FALSE,
    tags JSON,
    has_voicemail BOOLEAN DEFAULT FALSE,
    is_outbound_call BOOLEAN DEFAULT FALSE,
    duration VARCHAR(20),
    call_type ENUM('appointment', 'consultation', 'followup', 'emergency', 'other') DEFAULT 'other',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE SET NULL,
    
    INDEX idx_calls_uuid (uuid),
    INDEX idx_calls_client_id (client_id),
    INDEX idx_calls_user_id (user_id),
    INDEX idx_calls_clinic_id (clinic_id),
    INDEX idx_calls_location_id (location_id),
    INDEX idx_calls_session_id (session_id),
    INDEX idx_calls_agent_id (agent_id),
    INDEX idx_calls_call_date (call_date),
    INDEX idx_calls_phone_number (phone_number),
    INDEX idx_calls_priority_score (priority_score),
    INDEX idx_calls_is_urgent (is_urgent),
    INDEX idx_calls_has_voicemail (has_voicemail),
    INDEX idx_calls_is_outbound (is_outbound_call),
    INDEX idx_calls_call_type (call_type),
    INDEX idx_calls_clinic_date (clinic_id, call_date),
    INDEX idx_calls_location_date (location_id, call_date),
    INDEX idx_calls_client_date (client_id, call_date),
    
    -- Full-text search indexes
    FULLTEXT idx_calls_transcription (transcription),
    FULLTEXT idx_calls_summary (summary),
    FULLTEXT idx_calls_notes (notes)
) ENGINE=InnoDB;

-- Partition calls table by date (monthly partitions)
-- This will be added after initial data load
-- ALTER TABLE calls PARTITION BY RANGE (YEAR(call_date) * 100 + MONTH(call_date)) (
--     PARTITION p202401 VALUES LESS THAN (202402),
--     PARTITION p202402 VALUES LESS THAN (202403),
--     ...
-- );

CREATE TABLE call_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    agent_id VARCHAR(255),
    has_voicemail BOOLEAN DEFAULT FALSE,
    is_redirected BOOLEAN DEFAULT FALSE,
    call_type INT,
    caller_phone VARCHAR(20),
    client_id INT,
    appointment_id INT,
    call_id INT,
    trigger_event VARCHAR(255),
    status VARCHAR(100),
    telephony_call_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    FOREIGN KEY (call_id) REFERENCES calls(id) ON DELETE SET NULL,
    
    INDEX idx_call_sessions_uuid (uuid),
    INDEX idx_call_sessions_session_id (session_id),
    INDEX idx_call_sessions_agent_id (agent_id),
    INDEX idx_call_sessions_caller_phone (caller_phone),
    INDEX idx_call_sessions_client_id (client_id),
    INDEX idx_call_sessions_appointment_id (appointment_id),
    INDEX idx_call_sessions_call_id (call_id),
    INDEX idx_call_sessions_trigger_event (trigger_event),
    INDEX idx_call_sessions_status (status),
    INDEX idx_call_sessions_created_at (created_at)
) ENGINE=InnoDB;

-- =============================================
-- APPOINTMENT TABLES
-- =============================================

CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    client_id INT NOT NULL,
    client_name VARCHAR(255),
    call_id INT,
    slot_id VARCHAR(255),
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    status ENUM('active', 'cancelled', 'completed', 'no_show') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (call_id) REFERENCES calls(id) ON DELETE SET NULL,
    
    INDEX idx_appointments_uuid (uuid),
    INDEX idx_appointments_user_id (user_id),
    INDEX idx_appointments_client_id (client_id),
    INDEX idx_appointments_call_id (call_id),
    INDEX idx_appointments_appointment_date (appointment_date),
    INDEX idx_appointments_status (status),
    INDEX idx_appointments_user_date (user_id, appointment_date),
    INDEX idx_appointments_client_status (client_id, status),
    INDEX idx_appointments_date_status (appointment_date, status)
) ENGINE=InnoDB;

-- =============================================
-- CALENDAR TABLES
-- =============================================

CREATE TABLE calendar_slots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    user_id INT NOT NULL,
    location_id INT NOT NULL,
    slot_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    
    INDEX idx_calendar_slots_uuid (uuid),
    INDEX idx_calendar_slots_user_id (user_id),
    INDEX idx_calendar_slots_location_id (location_id),
    INDEX idx_calendar_slots_slot_date (slot_date),
    INDEX idx_calendar_slots_user_date (user_id, slot_date),
    UNIQUE KEY uk_calendar_slots_user_date (user_id, slot_date)
) ENGINE=InnoDB;

CREATE TABLE time_slots (
    id INT AUTO_INCREMENT PRIMARY KEY,
    calendar_slot_id INT NOT NULL,
    slot_id VARCHAR(255) NOT NULL,
    slot_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (calendar_slot_id) REFERENCES calendar_slots(id) ON DELETE CASCADE,
    
    INDEX idx_time_slots_calendar_slot_id (calendar_slot_id),
    INDEX idx_time_slots_slot_id (slot_id),
    INDEX idx_time_slots_slot_time (slot_time),
    INDEX idx_time_slots_is_available (is_available),
    UNIQUE KEY uk_time_slots_calendar_slot_time (calendar_slot_id, slot_time)
) ENGINE=InnoDB;

-- =============================================
-- EMAIL SYSTEM TABLES
-- =============================================

CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    subject VARCHAR(500) NOT NULL,
    html_content LONGTEXT,
    text_content LONGTEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_email_templates_name (name),
    INDEX idx_email_templates_is_active (is_active)
) ENGINE=InnoDB;

CREATE TABLE email_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    recipients JSON NOT NULL,
    from_address VARCHAR(255) NOT NULL,
    template_name VARCHAR(255),
    template_data JSON,
    status ENUM('pending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    
    FOREIGN KEY (template_name) REFERENCES email_templates(name) ON DELETE SET NULL,
    
    INDEX idx_email_queue_uuid (uuid),
    INDEX idx_email_queue_status (status),
    INDEX idx_email_queue_template_name (template_name),
    INDEX idx_email_queue_created_at (created_at),
    INDEX idx_email_queue_processed_at (processed_at)
) ENGINE=InnoDB;

-- =============================================
-- AUTHENTICATION TABLES
-- =============================================

CREATE TABLE otp_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    code VARCHAR(10) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_otp_codes_email (email),
    INDEX idx_otp_codes_code (code),
    INDEX idx_otp_codes_expires_at (expires_at),
    INDEX idx_otp_codes_is_used (is_used),
    INDEX idx_otp_codes_email_created (email, created_at)
) ENGINE=InnoDB;

-- =============================================
-- MISSING CORE FEATURE TABLES
-- =============================================

CREATE TABLE agent_location_mappings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id VARCHAR(255) NOT NULL UNIQUE,
    location_id INT NOT NULL,
    clinic_id INT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    
    INDEX idx_agent_location_mappings_agent_id (agent_id),
    INDEX idx_agent_location_mappings_location_id (location_id),
    INDEX idx_agent_location_mappings_clinic_id (clinic_id),
    INDEX idx_agent_location_mappings_is_active (is_active),
    INDEX idx_agent_location_mappings_agent_active (agent_id, is_active),
    INDEX idx_agent_location_mappings_clinic_active (clinic_id, is_active)
) ENGINE=InnoDB;

CREATE TABLE on_call_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    doctor_id INT NOT NULL,
    doctor_name VARCHAR(255) NOT NULL,
    doctor_phone VARCHAR(20) NOT NULL,
    location_id INT NOT NULL,
    clinic_id INT NOT NULL,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    timezone VARCHAR(50) NOT NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    
    FOREIGN KEY (doctor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (location_id) REFERENCES locations(id) ON DELETE CASCADE,
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_on_call_schedules_uuid (uuid),
    INDEX idx_on_call_schedules_doctor_id (doctor_id),
    INDEX idx_on_call_schedules_location_id (location_id),
    INDEX idx_on_call_schedules_clinic_id (clinic_id),
    INDEX idx_on_call_schedules_date (date),
    INDEX idx_on_call_schedules_is_active (is_active),
    INDEX idx_on_call_schedules_doctor_date (doctor_id, date),
    INDEX idx_on_call_schedules_location_date (location_id, date),
    INDEX idx_on_call_schedules_clinic_date (clinic_id, date),
    INDEX idx_on_call_schedules_active_date (is_active, date)
) ENGINE=InnoDB;

CREATE TABLE on_call_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    schedule_id INT NOT NULL,
    call_session_id INT NOT NULL,
    doctor_id INT NOT NULL,
    clinic_id INT NOT NULL,
    notification_time TIMESTAMP NOT NULL,
    sms_message_id VARCHAR(255),
    status ENUM('sent', 'failed', 'delivered') DEFAULT 'sent',
    call_type VARCHAR(100),
    caller_phone VARCHAR(20),
    error_message TEXT,
    retry_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (schedule_id) REFERENCES on_call_schedules(id) ON DELETE CASCADE,
    FOREIGN KEY (call_session_id) REFERENCES call_sessions(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    
    INDEX idx_on_call_notifications_uuid (uuid),
    INDEX idx_on_call_notifications_schedule_id (schedule_id),
    INDEX idx_on_call_notifications_call_session_id (call_session_id),
    INDEX idx_on_call_notifications_doctor_id (doctor_id),
    INDEX idx_on_call_notifications_clinic_id (clinic_id),
    INDEX idx_on_call_notifications_status (status),
    INDEX idx_on_call_notifications_notification_time (notification_time),
    INDEX idx_on_call_notifications_created_at (created_at)
) ENGINE=InnoDB;

CREATE TABLE staff_invite_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    code VARCHAR(255) NOT NULL UNIQUE,
    clinic_id INT NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_at TIMESTAMP NULL,
    used_by INT NULL,
    
    FOREIGN KEY (clinic_id) REFERENCES clinics(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_staff_invite_codes_uuid (uuid),
    INDEX idx_staff_invite_codes_code (code),
    INDEX idx_staff_invite_codes_clinic_id (clinic_id),
    INDEX idx_staff_invite_codes_used (used),
    INDEX idx_staff_invite_codes_expires_at (expires_at),
    INDEX idx_staff_invite_codes_created_by (created_by)
) ENGINE=InnoDB;

CREATE TABLE appointment_references (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    provider VARCHAR(100) NOT NULL,
    external_id VARCHAR(255) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    patient_id VARCHAR(255) NOT NULL,
    patient_name VARCHAR(255),
    practitioner_id VARCHAR(255) NOT NULL,
    practitioner_name VARCHAR(255),
    location_id VARCHAR(255) NOT NULL,
    location_name VARCHAR(255),
    start_time VARCHAR(50) NOT NULL,
    end_time VARCHAR(50) NOT NULL,
    type VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    reason TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_appointment_references_uuid (uuid),
    INDEX idx_appointment_references_provider (provider),
    INDEX idx_appointment_references_external_id (external_id),
    INDEX idx_appointment_references_patient_id (patient_id),
    INDEX idx_appointment_references_practitioner_id (practitioner_id),
    INDEX idx_appointment_references_status (status),
    INDEX idx_appointment_references_start_time (start_time),
    UNIQUE KEY uk_appointment_references_provider_external (provider, external_id)
) ENGINE=InnoDB;

CREATE TABLE patient_references (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    provider VARCHAR(100) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_patient_references_uuid (uuid),
    INDEX idx_patient_references_provider (provider),
    INDEX idx_patient_references_provider_id (provider_id),
    INDEX idx_patient_references_phone_number (phone_number),
    UNIQUE KEY uk_patient_references_provider_id (provider, provider_id)
) ENGINE=InnoDB;

-- =============================================
-- UTILITY TABLES
-- =============================================

CREATE TABLE migration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    firestore_id VARCHAR(255) NOT NULL,
    mysql_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_migration_log_table_name (table_name),
    INDEX idx_migration_log_firestore_id (firestore_id),
    INDEX idx_migration_log_mysql_id (mysql_id),
    UNIQUE KEY uk_migration_table_firestore (table_name, firestore_id)
) ENGINE=InnoDB;

-- =============================================
-- STORED PROCEDURES FOR COMMON OPERATIONS
-- =============================================

DELIMITER $$

CREATE PROCEDURE GetCallsWithPagination(
    IN p_clinic_id INT,
    IN p_location_id INT,
    IN p_start_date DATE,
    IN p_end_date DATE,
    IN p_limit INT,
    IN p_offset INT
)
BEGIN
    SELECT 
        c.*,
        cl.full_name as client_name,
        u.name as user_name,
        l.name as location_name
    FROM calls c
    LEFT JOIN clients cl ON c.client_id = cl.id
    LEFT JOIN users u ON c.user_id = u.id
    LEFT JOIN locations l ON c.location_id = l.id
    WHERE 
        (p_clinic_id IS NULL OR c.clinic_id = p_clinic_id)
        AND (p_location_id IS NULL OR c.location_id = p_location_id)
        AND (p_start_date IS NULL OR DATE(c.call_date) >= p_start_date)
        AND (p_end_date IS NULL OR DATE(c.call_date) <= p_end_date)
    ORDER BY c.call_date DESC
    LIMIT p_limit OFFSET p_offset;
END$$

CREATE PROCEDURE GetActiveAppointmentsByClient(
    IN p_client_id INT,
    IN p_limit INT DEFAULT 5
)
BEGIN
    SELECT 
        a.*,
        u.name as provider_name,
        c.full_name as client_name
    FROM appointments a
    JOIN users u ON a.user_id = u.id
    JOIN clients c ON a.client_id = c.id
    WHERE 
        a.client_id = p_client_id
        AND a.status = 'active'
        AND a.appointment_date >= CURDATE()
    ORDER BY a.appointment_date ASC, a.appointment_time ASC
    LIMIT p_limit;
END$$

DELIMITER ;

-- =============================================
-- VIEWS FOR COMMON QUERIES
-- =============================================

CREATE VIEW v_calls_with_details AS
SELECT 
    c.*,
    cl.full_name as client_name,
    cl.phone_number as client_phone,
    u.name as handled_by_user,
    l.name as location_name,
    l.time_zone as location_timezone
FROM calls c
LEFT JOIN clients cl ON c.client_id = cl.id
LEFT JOIN users u ON c.user_id = u.id
LEFT JOIN locations l ON c.location_id = l.id;

CREATE VIEW v_appointments_with_details AS
SELECT 
    a.*,
    c.full_name as client_name,
    c.phone_number as client_phone,
    u.name as provider_name,
    u.email as provider_email
FROM appointments a
JOIN clients c ON a.client_id = c.id
JOIN users u ON a.user_id = u.id;

-- =============================================
-- TRIGGERS FOR AUDIT TRAIL
-- =============================================

-- Update appointment foreign key reference after call sessions
UPDATE call_sessions cs 
JOIN appointments a ON cs.appointment_id = a.uuid 
SET cs.appointment_id = a.id;

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;