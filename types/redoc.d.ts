/**
 * Type declarations for Redoc library loaded from CDN
 */
interface RedocOptions {
  hideDownloadButton?: boolean;
  scrollYOffset?: number;
  theme?: {
    colors?: {
      primary?: {
        main?: string;
      };
    };
    sidebar?: {
      width?: string;
    };
  };
}

interface Redoc {
  init(specUrl: string, options?: RedocOptions, element?: HTMLElement | null): void;
}

interface Window {
  Redoc: Redoc;
}
