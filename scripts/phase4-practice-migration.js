#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Phase 4.1: Practice Creation Migration Script
 *
 * This script creates default practices for existing clinics and assigns
 * all existing locations to these default practices.
 *
 * Tasks:
 * 1. Create default practices for existing clinics
 * 2. Assign all locations to default practices
 * 3. Update user records with location access
 */

const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const clinicsCollection = db.collection('clinics');
const practicesCollection = db.collection('practices');
const locationsCollection = db.collection('locations');
const usersCollection = db.collection('users');

// Practice definitions for clinic 12 (University Retina)
const CLINIC_12_PRACTICES = [
  {
    name: 'Lombard',
    description: 'Lombard practice with multiple locations',
    isActive: true,
  },
];

// Location definitions for clinic 12 (University Retina)
const CLINIC_12_LOCATIONS = [
  {
    name: 'Lombard',
    address: 'Lombard, IL',
    phone: '',
    isActive: true, // Only Lombard is active
    practiceName: 'Lombard',
  },
  {
    name: 'Oak-Forrest',
    address: 'Oak Forest, IL',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
  {
    name: 'Jefferson Park',
    address: 'Jefferson Park, IL',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
  {
    name: 'Lakeview East',
    address: 'Lakeview East, IL',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
  {
    name: 'Lemont',
    address: 'Lemont, IL',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
  {
    name: 'Bedford Park',
    address: 'Bedford Park, IL',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
  {
    name: 'EyeSouth Partners Surgery Center',
    address: 'Surgery Center Location',
    phone: '',
    isActive: false,
    practiceName: 'Lombard',
  },
];

/**
 * Create specific practices for clinic 12 (University Retina)
 */
async function createClinic12Practices(clinicId, clinicName) {
  console.log(`🏥 Creating specific practices for ${clinicName} (Clinic ${clinicId})...`);

  let createdCount = 0;
  let skippedCount = 0;

  for (const practiceData of CLINIC_12_PRACTICES) {
    try {
      // Check if practice already exists
      const existingPracticesSnapshot = await practicesCollection
        .where('clinicId', '==', clinicId)
        .where('name', '==', practiceData.name)
        .get();

      if (existingPracticesSnapshot.empty) {
        // Create the practice
        const practiceId = uuidv4();
        const now = admin.firestore.Timestamp.now();

        const newPractice = {
          id: practiceId,
          clinicId: clinicId,
          name: practiceData.name,
          description: practiceData.description,
          isActive: practiceData.isActive,
          createdAt: now,
          updatedAt: now,
        };

        await practicesCollection.doc(practiceId).set(newPractice);

        const status = practiceData.isActive ? '✅ ACTIVE' : '⏸️  INACTIVE';
        console.log(`   ✅ Created practice: "${practiceData.name}" ${status}`);
        console.log(`      Practice ID: ${practiceId}`);
        createdCount++;
      } else {
        console.log(`   ⏭️  Practice "${practiceData.name}" already exists, skipping...`);
        skippedCount++;
      }
    } catch (error) {
      console.error(`   ❌ Error creating practice "${practiceData.name}":`, error.message);
    }
  }

  console.log(`   📊 Clinic 12 Summary: Created ${createdCount}, Skipped ${skippedCount}`);
  return { createdCount, skippedCount };
}

/**
 * Step 1: Create default practices for existing clinics
 */
async function createDefaultPractices() {
  console.log('=== Phase 4.1: Creating Default Practices ===');

  const clinicsSnapshot = await clinicsCollection.get();
  console.log(`Found ${clinicsSnapshot.size} clinics`);

  let createdCount = 0;
  let skippedCount = 0;

  for (const clinicDoc of clinicsSnapshot.docs) {
    const clinic = clinicDoc.data();
    const clinicId = clinic.id || parseInt(clinicDoc.id);

    // Special handling for clinic 12 (University Retina)
    if (clinicId === 12) {
      const clinic12Results = await createClinic12Practices(clinicId, clinic.name);
      createdCount += clinic12Results.createdCount;
      skippedCount += clinic12Results.skippedCount;
      continue;
    }

    // Check if clinic already has a default practice
    const existingPracticesSnapshot = await practicesCollection
      .where('clinicId', '==', clinicId)
      .where('name', '==', `${clinic.name} - Main Practice`)
      .get();

    if (existingPracticesSnapshot.empty) {
      // Create a default practice for this clinic
      const practiceId = uuidv4();
      const now = admin.firestore.Timestamp.now();

      const practiceData = {
        id: practiceId,
        clinicId: clinicId,
        name: `${clinic.name} - Main Practice`,
        description: `Default practice for ${clinic.name}`,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      };

      await practicesCollection.doc(practiceId).set(practiceData);

      createdCount++;
      console.log(`✅ Created default practice for clinic ${clinicId} (${clinic.name})`);
      console.log(`   Practice ID: ${practiceId}`);
    } else {
      console.log(`⏭️  Clinic ${clinicId} already has a default practice`);
      skippedCount++;
    }
  }

  console.log(`\n📊 Practice Creation Summary:`);
  console.log(`   Created: ${createdCount} practices`);
  console.log(`   Skipped: ${skippedCount} practices (already exist)`);

  return { createdCount, skippedCount };
}

/**
 * Create all locations for clinic 12 under the Lombard practice
 */
async function createAllLocationsForClinic12(lombardPracticeId) {
  console.log(`   🏢 Creating all locations for Lombard practice...`);

  let createdCount = 0;
  let updatedCount = 0;
  let skippedCount = 0;

  for (const locationData of CLINIC_12_LOCATIONS) {
    try {
      // Check if location already exists
      const existingLocationsSnapshot = await locationsCollection
        .where('clinicId', '==', 12)
        .where('name', '==', locationData.name)
        .get();

      if (!existingLocationsSnapshot.empty) {
        // Update existing location to have the correct practiceId
        const existingLocation = existingLocationsSnapshot.docs[0];
        await locationsCollection.doc(existingLocation.id).update({
          practiceId: lombardPracticeId,
          address: locationData.address,
          phone: locationData.phone,
          isActive: locationData.isActive,
          practiceName: locationData.practiceName,
          updatedAt: admin.firestore.Timestamp.now(),
        });

        const status = locationData.isActive ? '✅ ACTIVE' : '⏸️  INACTIVE';
        console.log(`   ✅ Updated existing ${locationData.name} location ${status}`);
        console.log(`      Location ID: ${existingLocation.id}`);
        updatedCount++;
      } else {
        // Create new location
        const locationId = uuidv4();
        const now = admin.firestore.Timestamp.now();

        const newLocationData = {
          id: locationId,
          clinicId: 12,
          practiceId: lombardPracticeId,
          name: locationData.name,
          address: locationData.address,
          phone: locationData.phone,
          isActive: locationData.isActive,
          practiceName: locationData.practiceName,
          officeHours: {},
          createdAt: now,
          updatedAt: now,
        };

        await locationsCollection.doc(locationId).set(newLocationData);

        const status = locationData.isActive ? '✅ ACTIVE' : '⏸️  INACTIVE';
        console.log(`   ✅ Created new ${locationData.name} location ${status}`);
        console.log(`      Location ID: ${locationId}`);
        createdCount++;
      }
    } catch (error) {
      console.error(`   ❌ Error creating/updating ${locationData.name} location:`, error.message);
      skippedCount++;
    }
  }

  console.log(
    `   📊 Locations Summary: Created ${createdCount}, Updated ${updatedCount}, Errors ${skippedCount}`,
  );

  // Return the Lombard location ID for user assignment
  try {
    const lombardLocationSnapshot = await locationsCollection
      .where('clinicId', '==', 12)
      .where('name', '==', 'Lombard')
      .get();

    if (!lombardLocationSnapshot.empty) {
      return {
        lombardLocationId: lombardLocationSnapshot.docs[0].id,
        createdCount,
        updatedCount,
        errorCount: skippedCount,
      };
    } else {
      throw new Error('Lombard location not found after creation');
    }
  } catch (error) {
    console.error('   ❌ Error finding Lombard location:', error.message);
    return {
      lombardLocationId: null,
      createdCount,
      updatedCount,
      errorCount: skippedCount + 1,
    };
  }
}

/**
 * Handle location assignment for clinic 12 (University Retina)
 */
async function handleClinic12Locations() {
  console.log(`🏥 Handling locations for Clinic 12 (University Retina)...`);

  try {
    // Find the Lombard practice
    const lombardPracticeSnapshot = await practicesCollection
      .where('clinicId', '==', 12)
      .where('name', '==', 'Lombard')
      .get();

    if (lombardPracticeSnapshot.empty) {
      throw new Error(
        'Lombard practice not found for clinic 12. Please run practice creation first.',
      );
    }

    const lombardPracticeId = lombardPracticeSnapshot.docs[0].id;
    console.log(`   🎯 Found Lombard practice: ${lombardPracticeId}`);

    // Create/update Lombard location
    const lombardResults = await createAllLocationsForClinic12(lombardPracticeId);

    return {
      lombardLocationId: lombardResults.lombardLocationId,
      updatedCount: lombardResults.updatedCount,
      skippedCount: lombardResults.errorCount,
    };
  } catch (error) {
    console.error(`   ❌ Error handling clinic 12 locations:`, error.message);
    return { lombardLocationId: null, updatedCount: 0, skippedCount: 0 };
  }
}

/**
 * Handle user location access for clinic 12 (University Retina)
 */
async function handleClinic12UserAccess() {
  console.log(`👥 Handling user access for Clinic 12 (University Retina)...`);

  try {
    // Get all locations for clinic 12
    const allLocationsSnapshot = await locationsCollection.where('clinicId', '==', 12).get();

    if (allLocationsSnapshot.empty) {
      throw new Error('No locations found for clinic 12. Please run location assignment first.');
    }

    // Find the Lombard location specifically
    const lombardLocation = allLocationsSnapshot.docs.find(doc => doc.data().name === 'Lombard');

    if (!lombardLocation) {
      throw new Error(
        'Lombard location not found for clinic 12. Please run location assignment first.',
      );
    }

    const lombardLocationId = lombardLocation.id;
    // Only include active locations for user access
    const activeLocationIds = allLocationsSnapshot.docs
      .filter(doc => doc.data().isActive === true)
      .map(doc => doc.id);
    const allLocationIds = allLocationsSnapshot.docs.map(doc => doc.id);

    console.log(`   🎯 Found Lombard location: ${lombardLocationId}`);
    console.log(`   🏢 Found ${allLocationIds.length} total locations for clinic 12`);
    console.log(`   ✅ Found ${activeLocationIds.length} active locations for user access`);

    // Get all users from clinic 12
    const usersSnapshot = await usersCollection.where('clinicId', '==', 12).get();
    console.log(`   Found ${usersSnapshot.size} users in Clinic 12`);

    let updatedCount = 0;
    let skippedCount = 0;

    for (const userDoc of usersSnapshot.docs) {
      const user = userDoc.data();

      try {
        // Skip SUPER_ADMIN users
        if (user.role === 'SUPER_ADMIN') {
          console.log(`   ⏭️  Skipping SUPER_ADMIN user: ${user.name || user.email}`);
          skippedCount++;
          continue;
        }

        // Check if user already has the correct location setup
        const hasAllLocations =
          user.locationIds &&
          activeLocationIds.every(id => user.locationIds.includes(id)) &&
          user.locationIds.length === activeLocationIds.length;

        const hasCorrectCurrentLocation = user.currentLocationId === lombardLocationId;

        if (hasAllLocations && hasCorrectCurrentLocation) {
          console.log(`   ⏭️  User ${user.email} already has correct location access`);
          skippedCount++;
          continue;
        }

        // Update user with active locations but Lombard as current
        const updates = {
          locationIds: activeLocationIds, // Give access to active locations only
          currentLocationId: lombardLocationId, // Set Lombard as current location
          updatedAt: admin.firestore.Timestamp.now(),
        };

        await usersCollection.doc(userDoc.id).update(updates);

        console.log(`   ✅ Updated user: ${user.name || user.email}`);
        console.log(
          `      Access to ${activeLocationIds.length} active locations, current: Lombard`,
        );

        updatedCount++;
      } catch (error) {
        console.error(`   ❌ Error updating user ${user.email}:`, error.message);
      }
    }

    console.log(`   📊 Clinic 12 User Summary: Updated ${updatedCount}, Skipped ${skippedCount}`);
    return { updatedCount, skippedCount, errorCount: 0 };
  } catch (error) {
    console.error(`   ❌ Error handling clinic 12 user access:`, error.message);
    return { updatedCount: 0, skippedCount: 0, errorCount: 1 };
  }
}

/**
 * Step 2: Assign all locations to default practices
 */
async function assignLocationsToDefaultPractices() {
  console.log('\n=== Phase 4.1: Assigning Locations to Default Practices ===');

  const locationsSnapshot = await locationsCollection.get();
  console.log(`Found ${locationsSnapshot.size} locations`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  // Handle clinic 12 specially first
  const clinic12Results = await handleClinic12Locations();
  updatedCount += clinic12Results.updatedCount;
  skippedCount += clinic12Results.skippedCount;
  errorCount += clinic12Results.errorCount;

  for (const locationDoc of locationsSnapshot.docs) {
    const location = locationDoc.data();
    const locationId = locationDoc.id;
    const clinicId = location.clinicId;

    try {
      // Skip clinic 12 locations as they're handled above
      if (clinicId === 12) {
        continue;
      }

      // Skip if location already has a practiceId
      if (location.practiceId && location.practiceId !== 'default-practice') {
        console.log(
          `⏭️  Location ${locationId} already assigned to practice ${location.practiceId}`,
        );
        skippedCount++;
        continue;
      }

      // Find the default practice for this clinic
      const defaultPracticeSnapshot = await practicesCollection
        .where('clinicId', '==', clinicId)
        .where('name', '==', `${location.clinicName || 'Clinic'} - Main Practice`)
        .limit(1)
        .get();

      let practiceId = null;

      if (!defaultPracticeSnapshot.empty) {
        practiceId = defaultPracticeSnapshot.docs[0].id;
      } else {
        // If no default practice found, find any practice for this clinic
        const anyPracticeSnapshot = await practicesCollection
          .where('clinicId', '==', clinicId)
          .limit(1)
          .get();

        if (!anyPracticeSnapshot.empty) {
          practiceId = anyPracticeSnapshot.docs[0].id;
        } else {
          // Create a fallback practice if none exists
          practiceId = uuidv4();
          const now = admin.firestore.Timestamp.now();

          await practicesCollection.doc(practiceId).set({
            id: practiceId,
            clinicId: clinicId,
            name: `Clinic ${clinicId} - Main Practice`,
            description: `Default practice for clinic ${clinicId}`,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          });

          console.log(`🆕 Created fallback practice ${practiceId} for clinic ${clinicId}`);
        }
      }

      if (practiceId) {
        // Update location with practiceId
        await locationDoc.ref.update({
          practiceId: practiceId,
          updatedAt: admin.firestore.Timestamp.now(),
        });

        console.log(
          `✅ Assigned location ${locationId} (${location.name}) to practice ${practiceId}`,
        );
        updatedCount++;
      } else {
        console.error(`❌ Could not find or create practice for location ${locationId}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing location ${locationId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 Location Assignment Summary:`);
  console.log(`   Updated: ${updatedCount} locations`);
  console.log(`   Skipped: ${skippedCount} locations (already assigned)`);
  console.log(`   Errors: ${errorCount} locations`);

  return { updatedCount, skippedCount, errorCount };
}

/**
 * Step 3: Update user records with location access
 */
async function updateUserLocationAccess() {
  console.log('\n=== Phase 4.1: Updating User Location Access ===');

  const usersSnapshot = await usersCollection.get();
  console.log(`Found ${usersSnapshot.size} users`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  // Handle clinic 12 users specially first
  const clinic12Results = await handleClinic12UserAccess();
  updatedCount += clinic12Results.updatedCount;
  skippedCount += clinic12Results.skippedCount;
  errorCount += clinic12Results.errorCount;

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const userId = userDoc.id;

    try {
      // Skip clinic 12 users as they're handled above
      if (user.clinicId === 12) {
        continue;
      }

      // Skip if user already has locationIds
      if (user.locationIds && user.locationIds.length > 0) {
        console.log(`⏭️  User ${userId} (${user.email}) already has location access`);
        skippedCount++;
        continue;
      }

      // Skip super admins (they don't need specific location access)
      if (user.role === 'SUPER_ADMIN') {
        console.log(`⏭️  Skipping SUPER_ADMIN user ${userId} (${user.email})`);
        skippedCount++;
        continue;
      }

      // Get all locations for this user's clinic
      const clinicId = user.clinicId;
      if (!clinicId) {
        console.log(`⚠️  User ${userId} (${user.email}) has no clinicId`);
        skippedCount++;
        continue;
      }

      const locationsSnapshot = await locationsCollection.where('clinicId', '==', clinicId).get();

      if (locationsSnapshot.empty) {
        console.log(`⚠️  No locations found for clinic ${clinicId} (user ${user.email})`);
        skippedCount++;
        continue;
      }

      // Assign user to all locations in their clinic
      const locationIds = locationsSnapshot.docs.map(doc => doc.id);
      const currentLocationId = locationIds[0]; // Set first location as current

      await userDoc.ref.update({
        locationIds: locationIds,
        currentLocationId: currentLocationId,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`✅ Updated user ${userId} (${user.email}) with ${locationIds.length} locations`);
      console.log(`   Current location: ${currentLocationId}`);
      updatedCount++;
    } catch (error) {
      console.error(`❌ Error processing user ${userId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 User Update Summary:`);
  console.log(`   Updated: ${updatedCount} users`);
  console.log(`   Skipped: ${skippedCount} users (already configured or super admin)`);
  console.log(`   Errors: ${errorCount} users`);

  return { updatedCount, skippedCount, errorCount };
}

/**
 * Main migration function
 */
async function runPhase4Migration() {
  console.log('🚀 Starting Phase 4.1: Practice Creation Migration');
  console.log('================================================\n');

  try {
    // Step 1: Create default practices
    const practiceResults = await createDefaultPractices();

    // Step 2: Assign locations to practices
    const locationResults = await assignLocationsToDefaultPractices();

    // Step 3: Update user location access
    const userResults = await updateUserLocationAccess();

    // Final summary
    console.log('\n🎉 Phase 4.1 Migration Complete!');
    console.log('================================');
    console.log(`📊 Final Summary:`);
    console.log(`   Practices Created: ${practiceResults.createdCount}`);
    console.log(`   Locations Updated: ${locationResults.updatedCount}`);
    console.log(`   Users Updated: ${userResults.updatedCount}`);
    console.log(`   Total Errors: ${locationResults.errorCount + userResults.errorCount}`);

    if (locationResults.errorCount + userResults.errorCount === 0) {
      console.log('\n✅ Migration completed successfully with no errors!');
    } else {
      console.log('\n⚠️  Migration completed with some errors. Please review the logs above.');
    }
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runPhase4Migration()
    .then(() => {
      console.log('\n🏁 Migration script finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runPhase4Migration,
  createDefaultPractices,
  assignLocationsToDefaultPractices,
  updateUserLocationAccess,
};
