/**
 * This script seeds the Firestore database with initial data from our JSON files
 * Run with: node scripts/seed-firestore.js
 */

import fs from 'fs';
import path from 'path';
import admin from 'firebase-admin';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Get current file directory with ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const db = admin.firestore();

// Helper to clear a collection
async function clearCollection(collectionName) {
  const collectionRef = db.collection(collectionName);
  const batchSize = 500;

  try {
    const snapshot = await collectionRef.limit(batchSize).get();

    if (snapshot.size === 0) {
      return 0;
    }

    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    if (snapshot.size === batchSize) {
      const numDeleted = await clearCollection(collectionName);
      return numDeleted + snapshot.size;
    } else {
      return snapshot.size;
    }
  } catch (error) {
    console.error(`Error clearing collection ${collectionName}:`, error);
    throw error;
  }
}

async function seedDatabase() {
  try {
    // Read client data
    const clientsData = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../data/patients.json'), 'utf8'),
    );
    console.log(`Found ${clientsData.length} clients in JSON file`);

    // Read call data
    const callsData = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../data/calls.json'), 'utf8'),
    );
    console.log(`Found ${callsData.length} calls in JSON file`);

    // Read staff data
    const staffData = JSON.parse(
      fs.readFileSync(path.join(__dirname, '../data/staff.json'), 'utf8'),
    );
    console.log(`Found ${staffData.length} staff in JSON file`);

    // Clear existing data
    console.log('Clearing existing data...');

    await clearCollection('calls');
    console.log('Cleared calls collection');

    await clearCollection('clients');
    console.log('Cleared clients collection');

    await clearCollection('staff');
    console.log('Cleared staff collection');

    // Process and upload data
    console.log('Uploading new data...');

    // Process and upload staff
    for (const staff of staffData) {
      await db.collection('staff').doc(staff.id).set(staff);
      console.log(`Added staff with ID ${staff.id}`);
    }

    // Process and upload clients
    for (const client of clientsData) {
      // Convert string dates to Firestore Timestamps
      const processedClient = {
        ...client,
        birthday: admin.firestore.Timestamp.fromDate(new Date(client.birthday)),
      };

      // Add to Firestore
      await db.collection('clients').doc(client.id).set(processedClient);
      console.log(`Added client with ID ${client.id}`);
    }

    // Process and upload calls
    for (const call of callsData) {
      // Convert string dates to Firestore Timestamps
      const processedCall = {
        ...call,
        date: admin.firestore.Timestamp.fromDate(new Date(call.date)),
      };

      // Add to Firestore
      await db.collection('calls').doc(call.id).set(processedCall);
      console.log(`Added call with ID ${call.id}`);
    }

    console.log('Database seeded successfully!');
  } catch (error) {
    console.error('Error seeding database:', error);
  }
}

// Run the seed function
seedDatabase()
  .then(() => {
    console.log('Seed process complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Seed process failed:', error);
    process.exit(1);
  });
