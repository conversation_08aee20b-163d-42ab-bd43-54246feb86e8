/**
 * @file Complete setup script for on-call collections
 * @description Creates and populates agent-location-mappings, on-call-schedules, and on-call-notifications collections
 * Run with: node scripts/setup-on-call-collections.js
 */

require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('🚀 Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Firebase Admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function setupAgentLocationMappings() {
  console.log('Setting up agent-location-mappings collection...');

  // Get real location ID for Lombard
  console.log('🔍 Retrieving Lombard location for agent mapping...');
  const lombardQuery = await db
    .collection('locations')
    .where('clinicId', '==', 12)
    .where('name', '==', 'Lombard')
    .limit(1)
    .get();

  if (lombardQuery.empty) {
    throw new Error('Lombard location not found for clinic 12 in agent mapping setup');
  }

  const lombardId = lombardQuery.docs[0].id;
  console.log(`✅ Found Lombard location for mapping: ${lombardId}`);

  // Check if there are any existing agent configurations for clinic 12
  const agentConfigQuery = await db
    .collection('configurations')
    .where('clinicId', '==', 12)
    .where('type', '==', 'agent')
    .limit(1)
    .get();

  let agentId = 'clinic-12-dialogflow-agent'; // Default fallback

  if (!agentConfigQuery.empty) {
    const agentConfig = agentConfigQuery.docs[0].data();
    agentId = agentConfig.agentId || agentConfig.value || agentId;
    console.log(`✅ Found agent configuration: ${agentId}`);
  } else {
    console.log(`⚠️ No agent configuration found for clinic 12, using default: ${agentId}`);
  }

  // University Retina agent-location mapping with real data
  const sampleMapping = {
    agentId: agentId,
    locationId: lombardId,
    clinicId: 12, // University Retina clinic ID
    isActive: true,
  };

  try {
    const docRef = db.collection('agent-location-mappings').doc(agentId);
    const mappingWithTimestamps = {
      ...sampleMapping,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await docRef.set(mappingWithTimestamps);
    console.log(`Created agent-location mapping: ${agentId} -> ${lombardId}`);

    console.log('✅ Successfully set up agent-location-mappings collection');
  } catch (error) {
    console.error('❌ Error setting up agent-location-mappings collection:', error);
    throw error;
  }
}

async function setupOnCallSchedulesCollection() {
  console.log('Setting up on-call-schedules collection...');

  // First, retrieve real data from the database for clinic 12
  console.log('🔍 Retrieving clinic 12 (University Retina) data from database...');

  // Get Dr. Meena George's user ID
  const drMeenaQuery = await db
    .collection('users')
    .where('clinicId', '==', 12)
    .where('email', '==', '<EMAIL>')
    .limit(1)
    .get();

  if (drMeenaQuery.empty) {
    throw new Error('Dr. Meena George not found in users collection for clinic 12');
  }

  const drMeenaDoc = drMeenaQuery.docs[0];
  const drMeenaData = drMeenaDoc.data();
  const drMeenaId = drMeenaDoc.id;
  console.log(`✅ Found Dr. Meena George: ${drMeenaId}`);

  // Get Jessica Kamching's user ID (admin)
  const jessicaQuery = await db
    .collection('users')
    .where('clinicId', '==', 12)
    .where('email', '==', '<EMAIL>')
    .limit(1)
    .get();

  if (jessicaQuery.empty) {
    throw new Error('Jessica Kamching not found in users collection for clinic 12');
  }

  const jessicaDoc = jessicaQuery.docs[0];
  const jessicaId = jessicaDoc.id;
  console.log(`✅ Found Jessica Kamching: ${jessicaId}`);

  // Get Lombard location ID
  const lombardQuery = await db
    .collection('locations')
    .where('clinicId', '==', 12)
    .where('name', '==', 'Lombard')
    .limit(1)
    .get();

  if (lombardQuery.empty) {
    throw new Error('Lombard location not found for clinic 12');
  }

  const lombardDoc = lombardQuery.docs[0];
  const lombardData = lombardDoc.data();
  const lombardId = lombardDoc.id;
  console.log(`✅ Found Lombard location: ${lombardId}`);

  // Get University Retina admin ID
  const adminQuery = await db
    .collection('users')
    .where('clinicId', '==', 12)
    .where('email', '==', '<EMAIL>')
    .limit(1)
    .get();

  if (adminQuery.empty) {
    console.log('⚠️ University Retina admin not found, using Jessica as fallback');
  }

  const adminId = adminQuery.empty ? jessicaId : adminQuery.docs[0].id;
  if (!adminQuery.empty) {
    console.log(`✅ Found University Retina admin: ${adminId}`);
  }

  // Create schedules with real data
  const sampleSchedules = [
    {
      doctorId: drMeenaId,
      doctorName: drMeenaData.name || 'Dr. Meena George',
      doctorPhone: drMeenaData.phone || '+1630555-0001', // Use real phone if available
      locationId: lombardId,
      clinicId: 12, // University Retina clinic ID
      date: new Date().toISOString().split('T')[0], // Today
      startTime: '08:00',
      endTime: '18:00',
      isActive: true,
      timezone: lombardData.timezone || 'America/Chicago',
      notes: 'University Retina Lombard - Day shift',
      createdBy: jessicaId,
    },
    {
      doctorId: drMeenaId,
      doctorName: drMeenaData.name || 'Dr. Meena George',
      doctorPhone: drMeenaData.phone || '+1630555-0001',
      locationId: lombardId,
      clinicId: 12, // University Retina clinic ID
      date: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Tomorrow
      startTime: '18:00',
      endTime: '08:00',
      isActive: true,
      timezone: lombardData.timezone || 'America/Chicago',
      notes: 'University Retina Lombard - After hours on-call',
      createdBy: jessicaId,
    },
    {
      doctorId: adminId, // Use admin as backup doctor
      doctorName: adminQuery.empty
        ? 'Jessica Kamching (Backup)'
        : 'University Retina Admin (Backup)',
      doctorPhone: '+1630555-0002', // Sample backup phone
      locationId: lombardId,
      clinicId: 12, // University Retina clinic ID
      date: new Date(Date.now() + 2 * 86400000).toISOString().split('T')[0], // Day after tomorrow
      startTime: '09:00',
      endTime: '17:00',
      isActive: true,
      timezone: lombardData.timezone || 'America/Chicago',
      notes: 'University Retina Lombard - Weekend coverage',
      createdBy: adminId,
    },
  ];

  try {
    for (const schedule of sampleSchedules) {
      const docRef = db.collection('on-call-schedules').doc();
      const scheduleWithTimestamps = {
        id: docRef.id,
        ...schedule,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      };

      await docRef.set(scheduleWithTimestamps);
      console.log(`Created on-call schedule: ${docRef.id}`);
    }

    console.log('✅ Successfully set up on-call-schedules collection');
  } catch (error) {
    console.error('❌ Error setting up on-call-schedules collection:', error);
    throw error;
  }
}

async function setupOnCallNotificationsCollection() {
  console.log('Setting up on-call-notifications collection...');

  // Get real data for sample notification
  console.log('🔍 Retrieving data for sample notification...');

  // Get Dr. Meena George's user ID
  const drMeenaQuery = await db
    .collection('users')
    .where('clinicId', '==', 12)
    .where('email', '==', '<EMAIL>')
    .limit(1)
    .get();

  if (drMeenaQuery.empty) {
    throw new Error('Dr. Meena George not found for notification setup');
  }

  const drMeenaId = drMeenaQuery.docs[0].id;
  console.log(`✅ Found Dr. Meena George for notification: ${drMeenaId}`);

  // Get a recent schedule ID if any exist
  const recentScheduleQuery = await db
    .collection('on-call-schedules')
    .where('clinicId', '==', 12)
    .where('doctorId', '==', drMeenaId)
    .limit(1)
    .get();

  let scheduleId = 'university-retina-schedule-placeholder';
  if (!recentScheduleQuery.empty) {
    scheduleId = recentScheduleQuery.docs[0].id;
    console.log(`✅ Found existing schedule for notification: ${scheduleId}`);
  } else {
    console.log('⚠️ No existing schedule found, using placeholder');
  }

  // Get a recent call session if any exist
  const recentCallQuery = await db
    .collection('callSessions')
    .where('clinicId', '==', 12)
    .limit(1)
    .get();

  let callSessionId = 'university-retina-call-session-placeholder';
  if (!recentCallQuery.empty) {
    callSessionId = recentCallQuery.docs[0].id;
    console.log(`✅ Found existing call session for notification: ${callSessionId}`);
  } else {
    console.log('⚠️ No existing call session found, using placeholder');
  }

  // Create a sample notification document for University Retina (clinic 12)
  const sampleNotification = {
    scheduleId: scheduleId,
    callSessionId: callSessionId,
    doctorId: drMeenaId,
    clinicId: 12, // University Retina clinic ID
    smsMessageId: 'twilio-msg-university-retina',
    status: 'delivered',
    callType: 'after-hours',
    callerPhone: '+1630555-9999', // Sample caller from Lombard area
  };

  try {
    const docRef = db.collection('on-call-notifications').doc();
    const notificationWithTimestamps = {
      id: docRef.id,
      ...sampleNotification,
      notificationTime: admin.firestore.FieldValue.serverTimestamp(),
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await docRef.set(notificationWithTimestamps);
    console.log(`Created sample notification: ${docRef.id}`);

    console.log('✅ Successfully set up on-call-notifications collection');
  } catch (error) {
    console.error('❌ Error setting up on-call-notifications collection:', error);
    throw error;
  }
}

async function validateCollections() {
  console.log('Validating collection setup...');

  try {
    // Check on-call-schedules collection
    const schedulesSnapshot = await db.collection('on-call-schedules').limit(1).get();
    if (!schedulesSnapshot.empty) {
      console.log('✅ on-call-schedules collection exists and has data');
    } else {
      console.log('⚠️ on-call-schedules collection is empty');
    }

    // Check on-call-notifications collection
    const notificationsSnapshot = await db.collection('on-call-notifications').limit(1).get();
    if (!notificationsSnapshot.empty) {
      console.log('✅ on-call-notifications collection exists and has data');
    } else {
      console.log('⚠️ on-call-notifications collection is empty');
    }

    // Check agent-location-mappings collection
    const mappingsSnapshot = await db.collection('agent-location-mappings').limit(1).get();
    if (!mappingsSnapshot.empty) {
      console.log('✅ agent-location-mappings collection exists and has data');
    } else {
      console.log('⚠️ agent-location-mappings collection is empty');
    }
  } catch (error) {
    console.error('❌ Error validating collections:', error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log('🧹 Cleaning up test data...');

  try {
    // Clean up agent-location-mappings test data for University Retina
    const mappingsSnapshot = await db
      .collection('agent-location-mappings')
      .where('clinicId', '==', 12) // University Retina clinic ID
      .get();

    const deleteMappingPromises = mappingsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(deleteMappingPromises);

    console.log(`Deleted ${deleteMappingPromises.length} University Retina agent mapping records`);

    // Clean up on-call-schedules test data for University Retina
    const schedulesSnapshot = await db
      .collection('on-call-schedules')
      .where('clinicId', '==', 12) // University Retina clinic ID
      .get();

    const deletePromises = schedulesSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(deletePromises);

    console.log(`Deleted ${deletePromises.length} University Retina schedule records`);

    // Clean up on-call-notifications test data for University Retina
    const notificationsSnapshot = await db
      .collection('on-call-notifications')
      .where('clinicId', '==', 12) // University Retina clinic ID
      .get();

    const deleteNotificationPromises = notificationsSnapshot.docs.map(doc => doc.ref.delete());
    await Promise.all(deleteNotificationPromises);

    console.log(`Deleted ${deleteNotificationPromises.length} test notification records`);
    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.error('❌ Error cleaning up test data:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting on-call collections setup...');

    await setupAgentLocationMappings();
    await setupOnCallSchedulesCollection();
    await setupOnCallNotificationsCollection();
    await validateCollections();

    console.log('🎉 On-call collections setup completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  }
}

// Export functions for use in other scripts
module.exports = {
  setupAgentLocationMappings,
  setupOnCallSchedulesCollection,
  setupOnCallNotificationsCollection,
  validateCollections,
  cleanupTestData,
};

// Run main function if this script is executed directly
if (require.main === module) {
  main();
}
