#!/usr/bin/env node

/**
 * @file Test MySQL Database Connection
 * @description Simple script to test MySQL database connectivity
 */

require('dotenv').config({ path: '.env.local' });

// Load MySQL service dynamically
let mysqlService;
try {
  mysqlService = require('./lib/database/mysql-service').mysqlService;
} catch (error) {
  console.error('❌ Error loading MySQL service:', error.message);
  process.exit(1);
}

async function testMySQLConnection() {
  console.log('🔍 Testing MySQL database connection...\n');

  try {
    // Check environment variables
    console.log('📋 Checking environment variables:');
    const requiredEnvVars = ['MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE'];
    const missingVars = [];

    requiredEnvVars.forEach(envVar => {
      const value = process.env[envVar] || process.env[envVar.replace('MYSQL_', 'DB_')];
      if (value) {
        console.log(`✅ ${envVar}: ${envVar.includes('PASSWORD') ? '***' : value}`);
      } else {
        console.log(`❌ ${envVar}: Not set`);
        missingVars.push(envVar);
      }
    });

    if (missingVars.length > 0) {
      console.log(`\n❌ Missing required environment variables: ${missingVars.join(', ')}`);
      console.log('💡 Add these to your .env.local file');
      process.exit(1);
    }

    console.log(`✅ MYSQL_PORT: ${process.env.MYSQL_PORT || process.env.DB_PORT || '3306'}`);
    console.log('');

    // Initialize MySQL service
    console.log('🔄 Initializing MySQL service...');
    await mysqlService.initialize();
    console.log('✅ MySQL service initialized successfully\n');

    // Test basic connection
    console.log('🔄 Testing database connection...');
    const healthResult = await mysqlService.healthCheck();

    if (healthResult.status === 'healthy') {
      console.log('✅ Database connection successful!');
      console.log('📊 Health check details:');
      console.log(`   - Connection Pool: ${healthResult.details.connectionPool ? '✅' : '❌'}`);
      console.log(`   - Knex Instance: ${healthResult.details.knexInstance ? '✅' : '❌'}`);
      console.log(`   - Connectivity: ${healthResult.details.connectivity ? '✅' : '❌'}`);
    } else {
      console.log('❌ Database connection failed');
      console.log('📊 Health check details:');
      console.log(`   - Connection Pool: ${healthResult.details.connectionPool ? '✅' : '❌'}`);
      console.log(`   - Knex Instance: ${healthResult.details.knexInstance ? '✅' : '❌'}`);
      console.log(`   - Connectivity: ${healthResult.details.connectivity ? '✅' : '❌'}`);
    }

    // Test a simple query
    console.log('\n🔄 Testing database query...');
    try {
      const result = await mysqlService.query('SELECT 1 as test, NOW() as current_time');
      console.log('✅ Database query successful!');
      console.log(
        `📊 Query result: test=${result[0].test}, current_time=${result[0].current_time}`,
      );
    } catch (queryError) {
      console.log('❌ Database query failed:', queryError.message);
    }

    // Test database and tables existence
    console.log('\n🔄 Checking database and tables...');
    try {
      const databases = await mysqlService.query('SHOW DATABASES');
      const currentDb = process.env.MYSQL_DATABASE || process.env.DB_NAME || 'frontdesk_ai';
      const dbExists = databases.some(db => db.Database === currentDb);

      if (dbExists) {
        console.log(`✅ Database '${currentDb}' exists`);

        // Check for migration tables
        try {
          const tables = await mysqlService.query('SHOW TABLES');
          console.log(`📊 Found ${tables.length} tables in database`);

          if (tables.length > 0) {
            console.log('📋 Existing tables:');
            tables.forEach(table => {
              const tableName = Object.values(table)[0];
              console.log(`   - ${tableName}`);
            });
          } else {
            console.log('⚠️  No tables found - you may need to run database migrations');
            console.log('💡 Run: npm run db:migrate');
          }
        } catch (tablesError) {
          console.log('❌ Could not list tables:', tablesError.message);
        }
      } else {
        console.log(`❌ Database '${currentDb}' does not exist`);
        console.log('💡 Available databases:');
        databases.forEach(db => console.log(`   - ${db.Database}`));
      }
    } catch (dbError) {
      console.log('❌ Could not check database:', dbError.message);
    }

    console.log('\n🎉 MySQL connection test completed!');
  } catch (error) {
    console.error('\n❌ MySQL connection test failed:', error.message);
    console.error('\n🔧 Troubleshooting tips:');
    console.error('1. Check that MySQL server is running');
    console.error('2. Verify database credentials in .env.local');
    console.error('3. Ensure the database exists');
    console.error('4. Check network connectivity to MySQL host');
    process.exit(1);
  } finally {
    // Clean up connections
    try {
      await mysqlService.close();
      console.log('✅ Database connections closed');
    } catch (closeError) {
      console.error('⚠️  Error closing connections:', closeError.message);
    }
  }
}

// Run the test
if (require.main === module) {
  testMySQLConnection();
}

module.exports = { testMySQLConnection };
