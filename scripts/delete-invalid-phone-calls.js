#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to delete the latest 100 calls with invalid phone numbers
 *
 * Invalid phone numbers are defined as:
 * - Empty or undefined phoneNumber
 * - phoneNumber that's just whitespace when trimmed
 * - phoneNumber that equals "Unknown"
 *
 * Usage: node scripts/delete-invalid-phone-calls.js [--dry-run]
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const callsCollection = db.collection('calls');
const callDetailsCollection = db.collection('callDetails');

/**
 * Check if a phone number is invalid
 */
function isInvalidPhoneNumber(phoneNumber) {
  return (
    !phoneNumber ||
    typeof phoneNumber !== 'string' ||
    phoneNumber.trim() === '' ||
    phoneNumber === 'Unknown'
  );
}

/**
 * Get calls with invalid phone numbers, ordered by date (latest first)
 */
async function getCallsWithInvalidPhoneNumbers(limit = 100) {
  console.log(`🔍 Searching for calls with invalid phone numbers (limit: ${limit})...`);

  try {
    // Get all calls ordered by date (latest first) in batches to filter in memory
    // We need to fetch more than the limit since we're filtering in memory
    const batchSize = Math.min(limit * 10, 1000); // Fetch 10x the limit but cap at 1000
    let allCalls = [];
    let lastDoc = null;
    let totalFetched = 0;

    // Keep fetching until we have enough invalid calls or run out of data
    while (allCalls.length < limit && totalFetched < 5000) {
      // Safety limit of 5000 total calls
      let query = callsCollection.orderBy('date', 'desc').limit(batchSize);

      if (lastDoc) {
        query = query.startAfter(lastDoc);
      }

      const snapshot = await query.get();

      if (snapshot.empty) {
        console.log('📄 No more calls found in database');
        break;
      }

      console.log(
        `📄 Fetched ${snapshot.size} calls (total processed: ${totalFetched + snapshot.size})`,
      );

      // Filter calls with invalid phone numbers
      const invalidCalls = snapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data(),
          date: doc.data().date?.toDate ? doc.data().date.toDate() : new Date(doc.data().date),
        }))
        .filter(call => isInvalidPhoneNumber(call.phoneNumber));

      allCalls.push(...invalidCalls);
      lastDoc = snapshot.docs[snapshot.docs.length - 1];
      totalFetched += snapshot.size;

      console.log(
        `📊 Found ${invalidCalls.length} invalid calls in this batch (total invalid: ${allCalls.length})`,
      );
    }

    // Return only the requested number of calls
    const result = allCalls.slice(0, limit);

    console.log(`✅ Found ${result.length} calls with invalid phone numbers`);
    console.log(`📊 Total calls processed: ${totalFetched}`);

    if (result.length > 0) {
      console.log(
        `📅 Date range: ${result[result.length - 1].date.toISOString()} to ${result[0].date.toISOString()}`,
      );

      // Show some examples
      const examples = result.slice(0, 3).map(call => ({
        id: call.id,
        phoneNumber: call.phoneNumber,
        date: call.date.toISOString(),
        clientId: call.clientId,
      }));
      console.log('📋 Examples of invalid calls:', JSON.stringify(examples, null, 2));
    }

    return result;
  } catch (error) {
    console.error('❌ Error fetching calls with invalid phone numbers:', error);
    throw error;
  }
}

/**
 * Delete calls and their associated call details
 */
async function deleteCalls(calls, isDryRun = false) {
  if (calls.length === 0) {
    console.log('ℹ️  No calls to delete');
    return { deleted: 0, errors: 0 };
  }

  console.log(
    `${isDryRun ? '🧪' : '🗑️'} ${isDryRun ? 'DRY RUN - Would delete' : 'Deleting'} ${calls.length} calls...`,
  );

  let deleted = 0;
  let errors = 0;

  // Process calls in batches of 10 to avoid overwhelming Firestore
  const batchSize = 10;
  for (let i = 0; i < calls.length; i += batchSize) {
    const batch = calls.slice(i, i + batchSize);
    console.log(
      `🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(calls.length / batchSize)} (${batch.length} calls)...`,
    );

    // Process each call in the batch
    for (const call of batch) {
      try {
        if (!isDryRun) {
          // Start a transaction to ensure consistency
          await db.runTransaction(async transaction => {
            // Delete the main call record
            transaction.delete(callsCollection.doc(call.id));

            // Find and delete any associated CallDetail
            const callDetailSnapshot = await callDetailsCollection
              .where('callId', '==', call.id)
              .limit(1)
              .get();

            if (!callDetailSnapshot.empty) {
              const callDetailDoc = callDetailSnapshot.docs[0];
              transaction.delete(callDetailsCollection.doc(callDetailDoc.id));
              console.log(`  🗑️  Deleted CallDetail for call ${call.id}`);
            }
          });
        }

        console.log(
          `  ${isDryRun ? '🧪' : '✅'} ${isDryRun ? 'Would delete' : 'Deleted'} call ${call.id} (phone: "${call.phoneNumber}", date: ${call.date.toISOString()})`,
        );
        deleted++;
      } catch (error) {
        console.error(
          `  ❌ Error ${isDryRun ? 'checking' : 'deleting'} call ${call.id}:`,
          error.message,
        );
        errors++;
      }
    }

    // Small delay between batches to be gentle on Firestore
    if (i + batchSize < calls.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return { deleted, errors };
}

/**
 * Main function to run the deletion script
 */
async function runDeletion() {
  const isDryRun = process.argv.includes('--dry-run');

  console.log('🚀 Starting deletion of calls with invalid phone numbers');
  console.log('================================================');
  console.log(
    `Mode: ${isDryRun ? '🧪 DRY RUN (no actual deletions)' : '🗑️ DELETION (will modify database)'}`,
  );
  console.log('');

  try {
    // Get calls with invalid phone numbers
    const invalidCalls = await getCallsWithInvalidPhoneNumbers(100);

    if (invalidCalls.length === 0) {
      console.log('✅ No calls with invalid phone numbers found!');
      return;
    }

    // Show summary before deletion
    console.log('\n📊 Summary of calls to be deleted:');
    console.log(`   Total calls: ${invalidCalls.length}`);

    // Group by phone number type for analysis
    const phoneNumberTypes = invalidCalls.reduce((acc, call) => {
      let type;
      if (!call.phoneNumber) {
        type = 'undefined/null';
      } else if (call.phoneNumber.trim() === '') {
        type = 'empty string';
      } else if (call.phoneNumber === 'Unknown') {
        type = 'Unknown';
      } else {
        type = 'other invalid';
      }
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    console.log('   Phone number breakdown:');
    Object.entries(phoneNumberTypes).forEach(([type, count]) => {
      console.log(`     - ${type}: ${count} calls`);
    });

    // Date range
    if (invalidCalls.length > 0) {
      const oldestDate = invalidCalls[invalidCalls.length - 1].date;
      const newestDate = invalidCalls[0].date;
      console.log(`   Date range: ${oldestDate.toISOString()} to ${newestDate.toISOString()}`);
    }

    if (!isDryRun) {
      console.log('\n⚠️  WARNING: This will permanently delete the calls from the database!');
      console.log('💡 Run with --dry-run flag first to preview what would be deleted.');
      console.log('\nProceed with deletion? Type "yes" to continue:');

      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout,
      });

      const answer = await new Promise(resolve => {
        rl.question('', resolve);
      });
      rl.close();

      if (answer.toLowerCase() !== 'yes') {
        console.log('❌ Deletion cancelled by user');
        return;
      }
    }

    // Perform the deletion
    const result = await deleteCalls(invalidCalls, isDryRun);

    // Final summary
    console.log('\n🎉 Deletion process completed!');
    console.log('==============================');
    console.log(`📊 Results:`);
    console.log(`   ${isDryRun ? 'Would delete' : 'Deleted'}: ${result.deleted} calls`);
    console.log(`   Errors: ${result.errors} calls`);

    if (result.errors === 0) {
      console.log(
        `\n✅ ${isDryRun ? 'Dry run' : 'Deletion'} completed successfully with no errors!`,
      );
    } else {
      console.log(
        `\n⚠️  ${isDryRun ? 'Dry run' : 'Deletion'} completed with ${result.errors} errors. Please review the logs above.`,
      );
    }

    if (!isDryRun && result.deleted > 0) {
      console.log('\n🎯 Next Steps:');
      console.log('   1. Verify that the application is working correctly');
      console.log('   2. Monitor for any issues with call data');
      console.log('   3. Consider running a data integrity check');
    }
  } catch (error) {
    console.error('💥 Deletion script failed:', error);
    process.exit(1);
  }
}

// Run the script if executed directly
if (require.main === module) {
  runDeletion()
    .then(() => {
      console.log('\n🏁 Script finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runDeletion,
  getCallsWithInvalidPhoneNumbers,
  deleteCalls,
  isInvalidPhoneNumber,
};
