#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Phase 4.3: Cleanup Script
 *
 * This script handles cleanup tasks after the migration is complete.
 *
 * Tasks:
 * 1. Remove deprecated fields/endpoints
 * 2. Update documentation
 * 3. Performance optimization
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const usersCollection = db.collection('users');
const locationsCollection = db.collection('locations');
const practicesCollection = db.collection('practices');

/**
 * Step 1: Remove deprecated fields from documents
 */
async function removeDeprecatedFields() {
  console.log('=== Phase 4.3: Removing Deprecated Fields ===');

  let totalUpdated = 0;
  let totalErrors = 0;

  // Remove deprecated fields from locations
  console.log('\nCleaning up location documents...');
  const locationsSnapshot = await locationsCollection.get();
  let locationUpdated = 0;

  for (const locationDoc of locationsSnapshot.docs) {
    const location = locationDoc.data();
    const updates = {};
    let hasUpdates = false;

    // Remove any deprecated fields (example: old locationId field)
    if (location.hasOwnProperty('locationId')) {
      updates.locationId = admin.firestore.FieldValue.delete();
      hasUpdates = true;
    }

    // Remove default-practice placeholder if it exists
    if (location.practiceId === 'default-practice') {
      // Find the actual practice for this clinic
      const practicesSnapshot = await practicesCollection
        .where('clinicId', '==', location.clinicId)
        .limit(1)
        .get();

      if (!practicesSnapshot.empty) {
        updates.practiceId = practicesSnapshot.docs[0].id;
        hasUpdates = true;
      }
    }

    if (hasUpdates) {
      try {
        updates.updatedAt = admin.firestore.Timestamp.now();
        await locationDoc.ref.update(updates);
        locationUpdated++;
        console.log(`✅ Updated location ${locationDoc.id}`);
      } catch (error) {
        console.error(`❌ Error updating location ${locationDoc.id}:`, error.message);
        totalErrors++;
      }
    }
  }

  // Remove deprecated fields from users
  console.log('\nCleaning up user documents...');
  const usersSnapshot = await usersCollection.get();
  let userUpdated = 0;

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const updates = {};
    let hasUpdates = false;

    // Remove any deprecated fields (example: old locationId field)
    if (user.hasOwnProperty('locationId')) {
      updates.locationId = admin.firestore.FieldValue.delete();
      hasUpdates = true;
    }

    // Ensure practiceIds is properly derived from locationIds
    if (user.locationIds && user.locationIds.length > 0) {
      const locationDocs = await Promise.all(
        user.locationIds.map(locationId => locationsCollection.doc(locationId).get()),
      );

      const practiceIds = Array.from(
        new Set(
          locationDocs
            .filter(doc => doc.exists)
            .map(doc => doc.data().practiceId)
            .filter(practiceId => practiceId && practiceId !== 'default-practice'),
        ),
      );

      if (JSON.stringify(user.practiceIds || []) !== JSON.stringify(practiceIds)) {
        updates.practiceIds = practiceIds;
        hasUpdates = true;
      }
    }

    if (hasUpdates) {
      try {
        updates.updatedAt = admin.firestore.Timestamp.now();
        await userDoc.ref.update(updates);
        userUpdated++;
        console.log(`✅ Updated user ${userDoc.id} (${user.email})`);
      } catch (error) {
        console.error(`❌ Error updating user ${userDoc.id}:`, error.message);
        totalErrors++;
      }
    }
  }

  totalUpdated = locationUpdated + userUpdated;

  console.log(`\n📊 Deprecated Fields Cleanup Summary:`);
  console.log(`   Locations Updated: ${locationUpdated}`);
  console.log(`   Users Updated: ${userUpdated}`);
  console.log(`   Total Updated: ${totalUpdated}`);
  console.log(`   Errors: ${totalErrors}`);

  return { totalUpdated, totalErrors };
}

/**
 * Step 2: Update database indexes for performance optimization
 */
async function optimizePerformance() {
  console.log('\n=== Phase 4.3: Performance Optimization ===');

  // Read current firestore indexes
  const indexesPath = path.join(__dirname, '../firestore.indexes.json');
  let indexes = { indexes: [], fieldOverrides: [] };

  if (fs.existsSync(indexesPath)) {
    indexes = JSON.parse(fs.readFileSync(indexesPath, 'utf8'));
  }

  // Add new indexes for the practice-location model
  const newIndexes = [
    // Index for locations by clinic and practice
    {
      collectionGroup: 'locations',
      queryScope: 'COLLECTION',
      fields: [
        { fieldPath: 'clinicId', order: 'ASCENDING' },
        { fieldPath: 'practiceId', order: 'ASCENDING' },
        { fieldPath: 'name', order: 'ASCENDING' },
      ],
    },
    // Index for practices by clinic
    {
      collectionGroup: 'practices',
      queryScope: 'COLLECTION',
      fields: [
        { fieldPath: 'clinicId', order: 'ASCENDING' },
        { fieldPath: 'isActive', order: 'ASCENDING' },
        { fieldPath: 'name', order: 'ASCENDING' },
      ],
    },
    // Index for users by clinic and location
    {
      collectionGroup: 'users',
      queryScope: 'COLLECTION',
      fields: [
        { fieldPath: 'clinicId', order: 'ASCENDING' },
        { fieldPath: 'locationIds', order: 'ASCENDING' },
      ],
    },
    // Index for calls by clinic and location
    {
      collectionGroup: 'calls',
      queryScope: 'COLLECTION',
      fields: [
        { fieldPath: 'clinicId', order: 'ASCENDING' },
        { fieldPath: 'locationId', order: 'ASCENDING' },
        { fieldPath: 'date', order: 'DESCENDING' },
      ],
    },
  ];

  // Check if indexes already exist
  let addedIndexes = 0;
  for (const newIndex of newIndexes) {
    const exists = indexes.indexes.some(
      existingIndex => JSON.stringify(existingIndex) === JSON.stringify(newIndex),
    );

    if (!exists) {
      indexes.indexes.push(newIndex);
      addedIndexes++;
      console.log(`✅ Added index for ${newIndex.collectionGroup}`);
    } else {
      console.log(`⏭️  Index for ${newIndex.collectionGroup} already exists`);
    }
  }

  // Write updated indexes back to file
  if (addedIndexes > 0) {
    fs.writeFileSync(indexesPath, JSON.stringify(indexes, null, 2));
    console.log(`\n📊 Performance Optimization Summary:`);
    console.log(`   New Indexes Added: ${addedIndexes}`);
    console.log(`   Total Indexes: ${indexes.indexes.length}`);
    console.log(
      `\n⚠️  Note: Deploy the updated indexes using: firebase deploy --only firestore:indexes`,
    );
  } else {
    console.log(`\n📊 Performance Optimization Summary:`);
    console.log(`   No new indexes needed - all optimizations already in place`);
  }

  return { addedIndexes };
}

/**
 * Step 3: Generate migration report
 */
async function generateMigrationReport() {
  console.log('\n=== Phase 4.3: Generating Migration Report ===');

  const report = {
    migrationDate: new Date().toISOString(),
    summary: {
      totalClinics: 0,
      totalPractices: 0,
      totalLocations: 0,
      totalUsers: 0,
      usersWithLocationAccess: 0,
    },
    clinics: [],
    validation: {
      allLocationsHavePractices: true,
      allUsersHaveLocationAccess: true,
      orphanedRecords: [],
    },
  };

  // Get clinic statistics
  const clinicsSnapshot = await db.collection('clinics').get();
  report.summary.totalClinics = clinicsSnapshot.size;

  for (const clinicDoc of clinicsSnapshot.docs) {
    const clinic = clinicDoc.data();
    const clinicId = clinic.id || parseInt(clinicDoc.id);

    // Get practices for this clinic
    const practicesSnapshot = await practicesCollection.where('clinicId', '==', clinicId).get();

    // Get locations for this clinic
    const locationsSnapshot = await locationsCollection.where('clinicId', '==', clinicId).get();

    // Get users for this clinic
    const usersSnapshot = await usersCollection.where('clinicId', '==', clinicId).get();

    const clinicReport = {
      id: clinicId,
      name: clinic.name,
      practices: practicesSnapshot.size,
      locations: locationsSnapshot.size,
      users: usersSnapshot.size,
      usersWithLocationAccess: 0,
    };

    // Count users with location access
    usersSnapshot.docs.forEach(userDoc => {
      const user = userDoc.data();
      if (user.locationIds && user.locationIds.length > 0) {
        clinicReport.usersWithLocationAccess++;
      }
    });

    report.clinics.push(clinicReport);
  }

  // Get overall statistics
  const practicesSnapshot = await practicesCollection.get();
  const locationsSnapshot = await locationsCollection.get();
  const usersSnapshot = await usersCollection.get();

  report.summary.totalPractices = practicesSnapshot.size;
  report.summary.totalLocations = locationsSnapshot.size;
  report.summary.totalUsers = usersSnapshot.size;

  // Count users with location access
  usersSnapshot.docs.forEach(userDoc => {
    const user = userDoc.data();
    if (user.locationIds && user.locationIds.length > 0) {
      report.summary.usersWithLocationAccess++;
    } else if (user.role !== 'SUPER_ADMIN') {
      report.validation.allUsersHaveLocationAccess = false;
      report.validation.orphanedRecords.push({
        type: 'user',
        id: userDoc.id,
        email: user.email,
        issue: 'No location access',
      });
    }
  });

  // Validate locations have practices
  locationsSnapshot.docs.forEach(locationDoc => {
    const location = locationDoc.data();
    if (!location.practiceId || location.practiceId === 'default-practice') {
      report.validation.allLocationsHavePractices = false;
      report.validation.orphanedRecords.push({
        type: 'location',
        id: locationDoc.id,
        name: location.name,
        issue: 'No practice assignment',
      });
    }
  });

  // Save report to file
  const reportPath = path.join(__dirname, '../migration-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  console.log(`✅ Migration report generated: ${reportPath}`);
  console.log(`\n📊 Migration Report Summary:`);
  console.log(`   Total Clinics: ${report.summary.totalClinics}`);
  console.log(`   Total Practices: ${report.summary.totalPractices}`);
  console.log(`   Total Locations: ${report.summary.totalLocations}`);
  console.log(`   Total Users: ${report.summary.totalUsers}`);
  console.log(`   Users with Location Access: ${report.summary.usersWithLocationAccess}`);
  console.log(`   Validation Issues: ${report.validation.orphanedRecords.length}`);

  return report;
}

/**
 * Main cleanup function for Phase 4.3
 */
async function runPhase4Cleanup() {
  console.log('🚀 Starting Phase 4.3: Cleanup');
  console.log('==============================\n');

  try {
    // Step 1: Remove deprecated fields
    const cleanupResults = await removeDeprecatedFields();

    // Step 2: Optimize performance
    const performanceResults = await optimizePerformance();

    // Step 3: Generate migration report
    const report = await generateMigrationReport();

    // Final summary
    console.log('\n🎉 Phase 4.3 Cleanup Complete!');
    console.log('==============================');
    console.log(`📊 Final Summary:`);
    console.log(`   Documents Cleaned: ${cleanupResults.totalUpdated}`);
    console.log(`   New Indexes Added: ${performanceResults.addedIndexes}`);
    console.log(`   Validation Issues: ${report.validation.orphanedRecords.length}`);
    console.log(`   Total Errors: ${cleanupResults.totalErrors}`);

    if (cleanupResults.totalErrors === 0 && report.validation.orphanedRecords.length === 0) {
      console.log('\n✅ Cleanup completed successfully with no errors!');
      console.log('\n🎯 Next Steps:');
      console.log(
        '   1. Deploy updated Firestore indexes: firebase deploy --only firestore:indexes',
      );
      console.log('   2. Review migration report: migration-report.json');
      console.log('   3. Test the application thoroughly');
      console.log('   4. Monitor performance and user feedback');
    } else {
      console.log(
        '\n⚠️  Cleanup completed with some issues. Please review the logs and migration report.',
      );
    }
  } catch (error) {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  runPhase4Cleanup()
    .then(() => {
      console.log('\n🏁 Cleanup script finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Cleanup script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runPhase4Cleanup,
  removeDeprecatedFields,
  optimizePerformance,
  generateMigrationReport,
};
