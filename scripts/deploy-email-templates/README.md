# Email Templates Deployment Tool

A utility script that deploys HTML email templates to Firestore from the `email-templates` folder.

## Overview

This tool reads HTML email templates and their corresponding subject lines from the `email-templates` folder and uploads them to the Firestore `email-templates` collection. It creates new documents for templates that don't exist yet and updates existing ones only if there are changes detected.

## Requirements

- Node.js environment
- Firebase Admin SDK credentials configured in `.env.local`
- Email templates in HTML format located in the `email-templates` folder
- Subject lines defined in `email-templates/subjects.json`

## File Structure

```
email-templates/
├── template-name.html      # HTML email template files
├── another-template.html
└── subjects.json           # Subject lines for each template
```

## Expected Format

### HTML Templates

HTML files should be named with the template identifier (e.g., `reset-password.html`, `new-appointment.html`).

### Subject Lines (subjects.json)

```json
{
  "reset-password": "Reset Your Frontdesk Dashboard Password",
  "new-appointment": "New Appointment Booked with FrontDesk"
}
```

## Firestore Structure

Templates are stored in the `email-templates` collection with the following structure:

```
email-templates/
├── reset-password          # Document ID is the template name
│   ├── html: "<!DOCTYPE html>..."
│   ├── subject: "Reset Your Frontdesk Dashboard Password"
│   ├── createdAt: Timestamp
│   └── updatedAt: Timestamp
│
└── new-appointment         # Document ID is the template name
    ├── html: "<!DOCTYPE html>..."
    ├── subject: "New Appointment Booked with FrontDesk"
    ├── createdAt: Timestamp
    └── updatedAt: Timestamp
```

## Usage

Execute the deployment script by running:

```bash
./run.sh
```

The script will:

1. Read all HTML files from the `email-templates` directory
2. Find corresponding subject lines from `subjects.json`
3. Upload to Firestore with the following behaviors:
   - Create new templates that don't exist
   - Update templates where HTML or subject has changed
   - Skip templates with no changes
4. Provide a summary of the templates processed

## Optimization

The script optimizes Firestore operations by comparing the existing template's HTML and subject with the new versions before updating. If both are identical, no update operation is performed, saving on Firestore write operations.

## Logs

The script outputs detailed logs of the deployment process:

- Number of templates found
- Which templates were created, updated, or skipped (unchanged)
- Any templates skipped due to missing subject lines
- Final summary of operations performed (created, updated, unchanged, total) ✨
