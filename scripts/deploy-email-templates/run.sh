#!/bin/bash

# Display start message
echo "📧 Starting email templates deployment..."

# Navigate to project root directory (from scripts/deploy-email-templates)
cd "$(dirname "$0")/../.." || { echo "Failed to navigate to project root"; exit 1; }

# Run the deployment script
node scripts/deploy-email-templates/index.js

# Check if script executed successfully
if [ $? -eq 0 ]; then
  echo "✅ Email templates deployment completed successfully"
else
  echo "❌ Email templates deployment failed"
  exit 1
fi 