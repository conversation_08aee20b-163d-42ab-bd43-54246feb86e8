/**
 * <PERSON><PERSON><PERSON> to deploy email templates to Firestore
 * Reads templates from email-templates folder and adds them to the emails collection
 * Run with: ./run.sh
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const db = admin.firestore();
const templatesCollection = db.collection('email-templates');

// Path to email templates and subjects file
const templatesDir = path.join(process.cwd(), 'email-templates');
const subjectsPath = path.join(templatesDir, 'subjects.json');

async function deployEmailTemplates() {
  try {
    console.log('Starting email templates deployment...');

    // Check if templates directory exists
    if (!fs.existsSync(templatesDir)) {
      console.error(`Templates directory not found: ${templatesDir}`);
      process.exit(1);
    }

    // Read subjects file
    if (!fs.existsSync(subjectsPath)) {
      console.error(`Subjects file not found: ${subjectsPath}`);
      process.exit(1);
    }

    const subjectsRaw = fs.readFileSync(subjectsPath, 'utf8');
    const subjects = JSON.parse(subjectsRaw);

    console.log(`Found ${Object.keys(subjects).length} subjects in subjects.json`);

    // Get list of HTML files in the templates directory
    const files = fs.readdirSync(templatesDir).filter(file => file.endsWith('.html'));

    console.log(`Found ${files.length} HTML template files`);

    let updatedCount = 0;
    let createdCount = 0;
    let unchangedCount = 0;

    // Process each template file
    for (const file of files) {
      const templateName = path.basename(file, '.html');
      const subject = subjects[templateName];

      if (!subject) {
        console.warn(`No subject found for template: ${templateName}, skipping`);
        continue;
      }

      const templatePath = path.join(templatesDir, file);
      const html = fs.readFileSync(templatePath, 'utf8');

      // Get a reference to the document with templateName as the ID
      const docRef = templatesCollection.doc(templateName);

      // Check if the document exists
      const docSnapshot = await docRef.get();

      if (!docSnapshot.exists) {
        // Create new document with templateName as the ID
        await docRef.set({
          html,
          subject,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        createdCount++;
        console.log(`Created email template: ${templateName}`);
      } else {
        // Get existing document data
        const existingDoc = docSnapshot.data();

        // Check if content has changed
        if (existingDoc.html !== html || existingDoc.subject !== subject) {
          // Update existing document
          await docRef.update({
            html,
            subject,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          updatedCount++;
          console.log(`Updated email template: ${templateName}`);
        } else {
          // Skip update if nothing changed
          unchangedCount++;
          console.log(`Skipped email template (no changes): ${templateName}`);
        }
      }
    }

    console.log('\nDeployment Summary:');
    console.log(`- Created: ${createdCount} email templates`);
    console.log(`- Updated: ${updatedCount} email templates`);
    console.log(`- Unchanged: ${unchangedCount} email templates`);
    console.log(
      `- Total: ${createdCount + updatedCount + unchangedCount} email templates processed`,
    );
  } catch (error) {
    console.error('Error deploying email templates:', error);
    throw error;
  }
}

// Run the deployment function
deployEmailTemplates()
  .then(() => {
    console.log('Email templates deployment complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Deployment process failed:', error);
    process.exit(1);
  });
