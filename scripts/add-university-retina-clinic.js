/**
 * <PERSON><PERSON><PERSON> to add a new clinic with ID 12 and name "University Retina"
 * and create an admin user for this clinic
 *
 * This script uses Firebase Admin SDK to add a new clinic to the Firestore database
 * and create an admin user with the CLINIC_ADMIN role.
 *
 * Usage:
 * 1. Make sure you have the necessary Firebase Admin credentials set up
 * 2. Run the script with: node scripts/add-university-retina-clinic.js
 */

// Import required modules
const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK with environment variables
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

// Check if Firebase credentials are available
if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  console.error(
    'Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
  );
  process.exit(1);
}

// Initialize Firebase Admin
try {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
  console.log('Firebase Admin initialized successfully');
} catch (error) {
  console.error('Firebase Admin initialization error:', error);
  process.exit(1);
}

// Get Firestore instance
const db = admin.firestore();

// Define user roles enum to match the application
const UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  CLINIC_ADMIN: 'CLINIC_ADMIN',
  STAFF: 'STAFF',
};

/**
 * Generate a secure random password
 */
function generateSecurePassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%^&*()_-+=<>?';
  let password = '';

  // Ensure at least one character from each category
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
  password += '**********'[Math.floor(Math.random() * 10)];
  password += '!@#$%^&*()_-+=<>?'[Math.floor(Math.random() * 16)];

  // Fill the rest of the password
  for (let i = password.length; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  // Shuffle the password characters
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
}

/**
 * Add a new clinic with ID 12 and name "University Retina" and create an admin user
 */
async function addUniversityRetinaClinic() {
  try {
    // Create clinic data
    const now = admin.firestore.Timestamp.now();
    const clinicData = {
      id: 12, // Set the ID to 12 as requested
      name: 'University Retina',
      logoUrl: null,
      address: null,
      phone: null,
      createdAt: now,
      updatedAt: now,
    };

    // Set the document with ID "12"
    await db.collection('clinics').doc('12').set(clinicData);

    console.log('Successfully added clinic:');
    console.log({
      id: 12,
      name: 'University Retina',
      createdAt: now.toDate(),
      updatedAt: now.toDate(),
    });

    // Also create a default location for this clinic
    const locationId = crypto.randomUUID();
    await db.collection('locations').doc(locationId).set({
      id: locationId,
      clinicId: 12,
      name: 'University Retina Main Office',
      address: '',
      phone: '',
      createdAt: now,
      updatedAt: now,
    });

    console.log(`Created default location for clinic 12 (University Retina)`);

    console.log('Clinic and default location added successfully!');

    // Now create an admin user for this clinic
    const adminEmail = '<EMAIL>';
    const adminName = 'University Retina Admin';
    const adminPassword = generateSecurePassword();
    const adminId = crypto.randomUUID();

    // Create the user in Firebase Auth
    let adminUser;
    try {
      adminUser = await admin.auth().createUser({
        uid: adminId,
        email: adminEmail,
        password: adminPassword,
        displayName: adminName,
        disabled: false,
      });

      // Set custom claims for role-based access control
      await admin.auth().setCustomUserClaims(adminId, {
        role: UserRole.CLINIC_ADMIN,
        clinicId: 12,
      });

      console.log(`Created Firebase Auth user for admin: ${adminEmail}`);
    } catch (error) {
      console.error('Error creating admin user in Firebase Auth:', error);
      throw error;
    }

    // Create the user document in Firestore
    try {
      await db.collection('users').doc(adminId).set({
        email: adminEmail,
        name: adminName,
        role: UserRole.CLINIC_ADMIN,
        clinicId: 12,
        createdAt: now,
        updatedAt: now,
      });

      console.log(`Created Firestore user document for admin: ${adminEmail}`);
    } catch (error) {
      console.error('Error creating admin user document in Firestore:', error);
      throw error;
    }

    // Save the admin credentials to a file
    const credentials = {
      email: adminEmail,
      name: adminName,
      password: adminPassword,
      role: UserRole.CLINIC_ADMIN,
      clinicId: 12,
    };

    fs.writeFileSync(
      path.join(__dirname, '../temp-users-clinic-12.json'),
      JSON.stringify(credentials, null, 2),
      'utf8',
    );

    console.log(`Admin user credentials saved to temp-users-clinic-12.json`);
    console.log('Admin user created successfully!');
  } catch (error) {
    console.error('Error adding clinic:', error);
    process.exit(1);
  }
}

// Run the function to add the clinic
addUniversityRetinaClinic()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
