/**
 * This script checks which users in Firestore don't have corresponding Firebase Auth accounts
 * Run with: node scripts/check-missing-auth-users.js
 */

const admin = require('firebase-admin');
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function checkMissingAuthUsers() {
  try {
    console.log('Checking for Firestore users without Firebase Auth accounts...');

    // Get all users from Firestore
    const usersSnapshot = await db.collection('users').get();
    console.log(`Found ${usersSnapshot.size} users in Firestore`);

    // Get all Firebase Auth users
    const authUsers = await admin.auth().listUsers();
    console.log(`Found ${authUsers.users.length} users in Firebase Auth`);

    // Create maps for easier lookup
    const firestoreUsers = new Map();
    usersSnapshot.docs.forEach(doc => {
      const userData = doc.data();
      firestoreUsers.set(doc.id, {
        id: doc.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
      });
    });

    const authUsersByUid = new Map();
    const authUsersByEmail = new Map();
    authUsers.users.forEach(user => {
      authUsersByUid.set(user.uid, user);
      authUsersByEmail.set(user.email.toLowerCase(), user);
    });

    // Find users in Firestore but not in Auth (by UID)
    const missingByUid = [];
    firestoreUsers.forEach((user, uid) => {
      if (!authUsersByUid.has(uid)) {
        missingByUid.push(user);
      }
    });

    // Find users in Firestore but not in Auth (by email)
    const missingByEmail = [];
    firestoreUsers.forEach(user => {
      if (!authUsersByEmail.has(user.email.toLowerCase())) {
        missingByEmail.push(user);
      }
    });

    // Print results
    console.log('\n=== USERS MISSING FROM FIREBASE AUTH ===');

    if (missingByUid.length === 0) {
      console.log('No users missing by UID');
    } else {
      console.log(`\n${missingByUid.length} users missing by UID:`);
      missingByUid.forEach(user => {
        console.log(`- ${user.email} (${user.name}, ${user.role}) [ID: ${user.id}]`);
      });
    }

    if (missingByEmail.length === 0) {
      console.log('\nNo users missing by email');
    } else {
      console.log(`\n${missingByEmail.length} users missing by email:`);
      missingByEmail.forEach(user => {
        console.log(`- ${user.email} (${user.name}, ${user.role}) [ID: ${user.id}]`);
      });

      // Create comma-separated list of emails for easy use with reset-user-passwords.js
      const emailList = missingByEmail.map(user => user.email).join(',');
      console.log('\nEmail list for reset-user-passwords.js:');
      console.log(emailList);
    }

    // Provide next steps
    if (missingByUid.length > 0 || missingByEmail.length > 0) {
      console.log('\n=== NEXT STEPS ===');
      console.log('To create Firebase Auth accounts for these users, run:');
      console.log('node scripts/create-auth-users.js');
      console.log('\nOR for a more secure approach with password reset links:');
      console.log('node scripts/reset-user-passwords.js');
    }
  } catch (error) {
    console.error('Error in checkMissingAuthUsers:', error);
    process.exit(1);
  }
}

// Run the function
checkMissingAuthUsers()
  .then(() => {
    console.log('\nCheck completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to check users:', error);
    process.exit(1);
  });
