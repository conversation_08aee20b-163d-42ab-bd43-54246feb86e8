/**
 * Simple script to check calendar slots
 */

const admin = require('firebase-admin');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const db = admin.firestore();
const calendarSlotsCollection = db.collection('availableCalendarSlots');
const usersCollection = db.collection('users');

async function checkCalendarSlots() {
  try {
    // Get all calendar slots
    const slotsSnapshot = await calendarSlotsCollection.get();
    console.log(`Found ${slotsSnapshot.size} calendar slots in Firestore`);

    // Group slots by userId
    const slotsByUser = {};

    for (const slotDoc of slotsSnapshot.docs) {
      const slotData = slotDoc.data();
      const userId = slotData.userId;

      if (!slotsByUser[userId]) {
        slotsByUser[userId] = [];
      }

      slotsByUser[userId].push({
        id: slotDoc.id,
        date: slotData.date,
        timeSlotsCount: slotData.timeSlots?.length || 0,
      });
    }

    console.log('\nCalendar Slots by User:');

    // Get user details for each userId
    for (const userId in slotsByUser) {
      const userDoc = await usersCollection.doc(userId).get();
      const userData = userDoc.exists ? userDoc.data() : { name: 'Unknown User' };
      const userSlots = slotsByUser[userId];

      console.log(`\n${userData.name || userData.email} (${userId}):`);
      console.log(`- Total slots: ${userSlots.length}`);
      console.log(
        `- Date range: ${userSlots[0]?.date} to ${userSlots[userSlots.length - 1]?.date}`,
      );
      console.log(`- Time slots per day: ${userSlots[0]?.timeSlotsCount}`);
    }
  } catch (error) {
    console.error('Error checking calendar slots:', error);
    throw error;
  }
}

// Run the function
checkCalendarSlots()
  .then(() => {
    console.log('\nCheck complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Check failed:', error);
    process.exit(1);
  });
