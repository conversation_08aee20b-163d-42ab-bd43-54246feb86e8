// Test script for external-api voice-records
// @ts-check
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';

// Update this to your API endpoint
const API_URL = 'http://localhost:3000/api/external-api/v1/voice-records';

// Sample API key - replace with your actual key
const API_KEY = 'your-api-key-here';

// Generate a test UUID
const clientId = uuidv4();

// Sample call data
const testData = {
  call: {
    clientId,
    userId: 'user_123',
    clinicId: 1,
    date: new Date().toISOString(),
    summary: 'Test call summary',
    transcription: 'This is a test transcription of a mock call.',
    recordingUrl: 'https://example.com/recording/test.mp3',
    reason: 'Testing API',
    notes: 'Test notes',
    priorityScore: 5,
    urgent: false,
    tags: ['test', 'api'],
  },
  client: {
    id: clientId,
    fullName: 'Test Client',
    birthday: '1990-01-01',
    phoneNumber: '************',
    email: '<EMAIL>',
    insuranceCompany: 'Test Insurance',
    insuranceGroupNumber: 'TI12345',
    subscriberName: 'Test Client',
    clinicId: 1,
  },
};

// Make the API call
async function testApi() {
  try {
    console.log('Sending test data to external voice-records API v1...');

    const response = await fetch(API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
      },
      body: JSON.stringify(testData),
    });

    const data = await response.json();

    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('Test successful!');
    } else {
      console.error('Test failed!');
    }
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testApi();
