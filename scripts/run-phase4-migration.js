#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Master Phase 4 Migration Script
 *
 * This script orchestrates the complete Phase 4 data migration process.
 * It runs all three phases in the correct order:
 *
 * Phase 4.1: Practice Creation
 * Phase 4.2: User Migration
 * Phase 4.3: Cleanup
 *
 * Enhanced Features:
 * - Dry-run mode to preview changes
 * - Automatic backup creation
 * - Rollback capabilities
 * - Enhanced safety checks
 */

const { runPhase4Migration } = require('./phase4-practice-migration');
const { runPhase4UserMigration } = require('./phase4-user-migration');
const { runPhase4Cleanup } = require('./phase4-cleanup');
const { runPhase4Rollback } = require('./phase4-rollback');
const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK for backup operations
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

/**
 * Create a backup before migration
 */
async function createBackup() {
  console.log('🔄 Creating pre-migration backup...');

  const backupData = {
    timestamp: new Date().toISOString(),
    collections: {},
  };

  const db = admin.firestore();

  try {
    // Backup users collection
    const usersSnapshot = await db.collection('users').get();
    backupData.collections.users = usersSnapshot.docs.map(doc => ({
      id: doc.id,
      data: doc.data(),
    }));

    // Backup locations collection
    const locationsSnapshot = await db.collection('locations').get();
    backupData.collections.locations = locationsSnapshot.docs.map(doc => ({
      id: doc.id,
      data: doc.data(),
    }));

    // Backup practices collection (if exists)
    const practicesSnapshot = await db.collection('practices').get();
    backupData.collections.practices = practicesSnapshot.docs.map(doc => ({
      id: doc.id,
      data: doc.data(),
    }));

    // Save backup to file
    const backupPath = path.join(__dirname, `../backup-${Date.now()}.json`);
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));

    console.log(`✅ Backup created: ${backupPath}`);
    console.log(`   Users: ${backupData.collections.users.length}`);
    console.log(`   Locations: ${backupData.collections.locations.length}`);
    console.log(`   Practices: ${backupData.collections.practices.length}`);

    return backupPath;
  } catch (error) {
    console.error('❌ Backup creation failed:', error.message);
    throw error;
  }
}

/**
 * Dry-run mode: Preview changes without applying them
 */
async function runDryRun() {
  console.log('🔍 DRY-RUN MODE: Previewing migration changes');
  console.log('=============================================');
  console.log('This will show what changes would be made without actually applying them.\n');

  const db = admin.firestore();

  try {
    // Analyze current state
    const clinicsSnapshot = await db.collection('clinics').get();
    const usersSnapshot = await db.collection('users').get();
    const locationsSnapshot = await db.collection('locations').get();
    const practicesSnapshot = await db.collection('practices').get();

    console.log('📊 Current Database State:');
    console.log(`   Clinics: ${clinicsSnapshot.size}`);
    console.log(`   Users: ${usersSnapshot.size}`);
    console.log(`   Locations: ${locationsSnapshot.size}`);
    console.log(`   Practices: ${practicesSnapshot.size}`);

    // Analyze what Phase 4.1 would do
    console.log('\n🔍 Phase 4.1 Preview: Practice Creation');
    let practicesWouldCreate = 0;
    let locationsWouldUpdate = 0;
    let usersWouldUpdate = 0;

    for (const clinicDoc of clinicsSnapshot.docs) {
      const clinic = clinicDoc.data();
      const clinicId = clinic.id || parseInt(clinicDoc.id);

      // Check if clinic needs a default practice
      const existingPractices = await db
        .collection('practices')
        .where('clinicId', '==', clinicId)
        .get();

      if (existingPractices.empty) {
        practicesWouldCreate++;
        console.log(`   ➕ Would create practice for clinic: ${clinic.name}`);
      }
    }

    // Check locations that would be updated
    for (const locationDoc of locationsSnapshot.docs) {
      const location = locationDoc.data();
      if (!location.practiceId || location.practiceId === 'default-practice') {
        locationsWouldUpdate++;
      }
    }

    // Check users that would be updated
    for (const userDoc of usersSnapshot.docs) {
      const user = userDoc.data();
      if (user.role !== 'SUPER_ADMIN' && (!user.locationIds || user.locationIds.length === 0)) {
        usersWouldUpdate++;
      }
    }

    console.log(`   📈 Summary:`);
    console.log(`      Practices to create: ${practicesWouldCreate}`);
    console.log(`      Locations to update: ${locationsWouldUpdate}`);
    console.log(`      Users to update: ${usersWouldUpdate}`);

    // Analyze what Phase 4.2 would do
    console.log('\n🔍 Phase 4.2 Preview: User Migration');
    let usersNeedingMigration = 0;
    let usersNeedingCurrentLocation = 0;

    for (const userDoc of usersSnapshot.docs) {
      const user = userDoc.data();
      if (user.role !== 'SUPER_ADMIN') {
        if (!user.locationIds || user.locationIds.length === 0) {
          usersNeedingMigration++;
        }
        if (!user.currentLocationId) {
          usersNeedingCurrentLocation++;
        }
      }
    }

    console.log(`   📈 Summary:`);
    console.log(`      Users needing migration: ${usersNeedingMigration}`);
    console.log(`      Users needing current location: ${usersNeedingCurrentLocation}`);

    // Analyze what Phase 4.3 would do
    console.log('\n🔍 Phase 4.3 Preview: Cleanup');
    let deprecatedFieldsToRemove = 0;
    let indexesToAdd = 4; // We know we'll add 4 new indexes

    // Check for deprecated fields
    for (const userDoc of usersSnapshot.docs) {
      const user = userDoc.data();
      if (user.hasOwnProperty('locationId')) {
        deprecatedFieldsToRemove++;
      }
    }

    for (const locationDoc of locationsSnapshot.docs) {
      const location = locationDoc.data();
      if (location.hasOwnProperty('locationId') || location.practiceId === 'default-practice') {
        deprecatedFieldsToRemove++;
      }
    }

    console.log(`   📈 Summary:`);
    console.log(`      Deprecated fields to remove: ${deprecatedFieldsToRemove}`);
    console.log(`      Database indexes to add: ${indexesToAdd}`);

    console.log('\n✅ Dry-run completed! Review the preview above.');
    console.log('💡 To run the actual migration: node scripts/run-phase4-migration.js --confirm');
  } catch (error) {
    console.error('❌ Dry-run failed:', error.message);
    throw error;
  }
}

/**
 * Main function to run the complete Phase 4 migration
 */
async function runCompletePhase4Migration(options = {}) {
  console.log('🚀 Starting Complete Phase 4 Migration');
  console.log('=====================================');
  console.log('This will run all three phases of the data migration:');
  console.log('  Phase 4.1: Practice Creation');
  console.log('  Phase 4.2: User Migration');
  console.log('  Phase 4.3: Cleanup');
  console.log('=====================================\n');

  // Safety check
  if (!options.skipConfirmation) {
    console.log('⏸️  Migration paused for safety.');
    console.log('💡 Options:');
    console.log('   --dry-run    Preview changes without applying them');
    console.log('   --confirm    Run the actual migration');
    console.log('   --backup     Create backup before migration (recommended)');
    console.log('   --help       Show help information');
    console.log('\n🔒 Example: node scripts/run-phase4-migration.js --backup --confirm');
    return;
  }

  const startTime = Date.now();
  let phase1Success = false;
  let phase2Success = false;
  let phase3Success = false;
  let backupPath = null;

  try {
    // Create backup if requested
    if (options.createBackup) {
      backupPath = await createBackup();
      console.log(`\n💾 Backup created: ${backupPath}`);
      console.log('🔄 Proceeding with migration...\n');
    }

    // Phase 4.1: Practice Creation
    console.log('🔄 Starting Phase 4.1: Practice Creation...\n');
    await runPhase4Migration();
    phase1Success = true;
    console.log('✅ Phase 4.1 completed successfully!\n');

    // Phase 4.2: User Migration
    console.log('🔄 Starting Phase 4.2: User Migration...\n');
    await runPhase4UserMigration();
    phase2Success = true;
    console.log('✅ Phase 4.2 completed successfully!\n');

    // Phase 4.3: Cleanup
    console.log('🔄 Starting Phase 4.3: Cleanup...\n');
    await runPhase4Cleanup();
    phase3Success = true;
    console.log('✅ Phase 4.3 completed successfully!\n');

    // Final success summary
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('🎉 COMPLETE PHASE 4 MIGRATION SUCCESSFUL! 🎉');
    console.log('===========================================');
    console.log(`✅ Phase 4.1: Practice Creation - COMPLETED`);
    console.log(`✅ Phase 4.2: User Migration - COMPLETED`);
    console.log(`✅ Phase 4.3: Cleanup - COMPLETED`);
    console.log(`⏱️  Total Migration Time: ${duration} seconds`);
    if (backupPath) {
      console.log(`💾 Backup Available: ${backupPath}`);
    }
    console.log('===========================================\n');

    console.log('🎯 POST-MIGRATION CHECKLIST:');
    console.log('1. ✅ Deploy updated Firestore indexes:');
    console.log('   firebase deploy --only firestore:indexes');
    console.log('2. ✅ Review the migration report: migration-report.json');
    console.log('3. ✅ Test the application thoroughly:');
    console.log('   - User login and location switching');
    console.log('   - Practice and location management');
    console.log('   - Data filtering by location');
    console.log('   - Admin tools functionality');
    console.log('4. ✅ Monitor application performance');
    console.log('5. ✅ Gather user feedback');
    console.log('\n🔄 ROLLBACK OPTION:');
    console.log('   If issues occur: node scripts/phase4-rollback.js --confirm');
    console.log('\n🏆 The practice implementation is now complete!');
  } catch (error) {
    console.error('\n💥 MIGRATION FAILED!');
    console.error('===================');
    console.error(`❌ Phase 4.1: Practice Creation - ${phase1Success ? 'COMPLETED' : 'FAILED'}`);
    console.error(`❌ Phase 4.2: User Migration - ${phase2Success ? 'COMPLETED' : 'FAILED'}`);
    console.error(`❌ Phase 4.3: Cleanup - ${phase3Success ? 'COMPLETED' : 'FAILED'}`);
    console.error('\nError details:', error.message);
    console.error('\n🔧 RECOVERY OPTIONS:');
    console.error('1. Review the error logs above');
    console.error('2. Fix any data issues identified');
    if (backupPath) {
      console.error(`3. Restore from backup: ${backupPath}`);
    }
    console.error('4. Run rollback script: node scripts/phase4-rollback.js --confirm');
    console.error('5. Re-run failed phase individually:');
    if (!phase1Success) {
      console.error('   node scripts/phase4-practice-migration.js');
    }
    if (!phase2Success) {
      console.error('   node scripts/phase4-user-migration.js');
    }
    if (!phase3Success) {
      console.error('   node scripts/phase4-cleanup.js');
    }
    console.error('6. Or re-run complete migration:');
    console.error('   node scripts/run-phase4-migration.js --backup --confirm');

    process.exit(1);
  }
}

/**
 * Function to run individual phases (for recovery scenarios)
 */
async function runIndividualPhase(phaseNumber) {
  console.log(`🔄 Running individual Phase 4.${phaseNumber}...\n`);

  try {
    switch (phaseNumber) {
      case '1':
        await runPhase4Migration();
        console.log('✅ Phase 4.1 completed successfully!');
        break;
      case '2':
        await runPhase4UserMigration();
        console.log('✅ Phase 4.2 completed successfully!');
        break;
      case '3':
        await runPhase4Cleanup();
        console.log('✅ Phase 4.3 completed successfully!');
        break;
      default:
        throw new Error(`Invalid phase number: ${phaseNumber}. Use 1, 2, or 3.`);
    }
  } catch (error) {
    console.error(`💥 Phase 4.${phaseNumber} failed:`, error.message);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const options = {
  skipConfirmation: args.includes('--confirm'),
  createBackup: args.includes('--backup'),
  dryRun: args.includes('--dry-run'),
};

if (args.length > 0) {
  const command = args[0];

  if (options.dryRun) {
    // Run dry-run mode
    runDryRun()
      .then(() => {
        console.log('\n🏁 Dry-run finished.');
        process.exit(0);
      })
      .catch(error => {
        console.error('💥 Dry-run failed:', error);
        process.exit(1);
      });
  } else if (command === '--phase' && args[1]) {
    // Run individual phase
    runIndividualPhase(args[1])
      .then(() => {
        console.log('\n🏁 Individual phase migration finished.');
        process.exit(0);
      })
      .catch(error => {
        console.error('💥 Individual phase migration failed:', error);
        process.exit(1);
      });
  } else if (command === '--rollback') {
    // Run rollback
    runPhase4Rollback({ skipConfirmation: args.includes('--confirm') })
      .then(() => {
        console.log('\n🏁 Rollback finished.');
        process.exit(0);
      })
      .catch(error => {
        console.error('💥 Rollback failed:', error);
        process.exit(1);
      });
  } else if (command === '--help' || command === '-h') {
    // Show help
    console.log('Phase 4 Migration Script - Enhanced Edition');
    console.log('==========================================');
    console.log('');
    console.log('Usage:');
    console.log('  node scripts/run-phase4-migration.js                    # Show options');
    console.log('  node scripts/run-phase4-migration.js --dry-run          # Preview changes');
    console.log('  node scripts/run-phase4-migration.js --backup --confirm # Run with backup');
    console.log('  node scripts/run-phase4-migration.js --confirm          # Run migration');
    console.log('  node scripts/run-phase4-migration.js --phase 1          # Run only Phase 4.1');
    console.log('  node scripts/run-phase4-migration.js --rollback --confirm # Rollback migration');
    console.log('  node scripts/run-phase4-migration.js --help             # Show this help');
    console.log('');
    console.log('Options:');
    console.log('  --dry-run    Preview what changes would be made without applying them');
    console.log('  --backup     Create a backup before running migration (recommended)');
    console.log('  --confirm    Actually run the migration (required for safety)');
    console.log('  --rollback   Undo the migration changes');
    console.log('');
    console.log('Phases:');
    console.log('  Phase 4.1: Practice Creation - Creates default practices and assigns locations');
    console.log('  Phase 4.2: User Migration - Migrates users to new location model');
    console.log('  Phase 4.3: Cleanup - Removes deprecated fields and optimizes performance');
    console.log('');
    console.log('Safety Features:');
    console.log('  🔍 Dry-run mode to preview changes');
    console.log('  💾 Automatic backup creation');
    console.log('  🔄 Complete rollback capabilities');
    console.log('  ✅ Comprehensive validation');
    console.log('  📊 Detailed reporting');
    process.exit(0);
  } else if (options.skipConfirmation || options.createBackup) {
    // Run complete migration with options
    runCompletePhase4Migration(options)
      .then(() => {
        console.log('\n🏁 Complete migration finished.');
        process.exit(0);
      })
      .catch(error => {
        console.error('💥 Complete migration failed:', error);
        process.exit(1);
      });
  } else {
    console.error('❌ Invalid command. Use --help for usage information.');
    process.exit(1);
  }
} else {
  // Show options
  runCompletePhase4Migration(options)
    .then(() => {
      console.log('\n🏁 Complete migration finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Complete migration failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runCompletePhase4Migration,
  runIndividualPhase,
  createBackup,
  runDryRun,
};
