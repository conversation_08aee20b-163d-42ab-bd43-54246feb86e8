/**
 * This script creates Firebase Authentication users for existing users in Firestore
 * Run with: node scripts/create-auth-users.js
 */

const admin = require('firebase-admin');
const crypto = require('crypto');
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Generate a random secure password
function generateSecurePassword(length = 12) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()';
  let password = '';

  // Ensure we have at least one character from each category
  password += charset.substring(0, 26).charAt(Math.floor(Math.random() * 26)); // lowercase
  password += charset.substring(26, 52).charAt(Math.floor(Math.random() * 26)); // uppercase
  password += charset.substring(52, 62).charAt(Math.floor(Math.random() * 10)); // number
  password += charset.substring(62).charAt(Math.floor(Math.random() * (charset.length - 62))); // special char

  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  // Shuffle the password characters
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
}

async function createAuthUsers() {
  try {
    console.log('Starting creation of Firebase Auth users from Firestore users...');

    // Get all users from Firestore
    const usersSnapshot = await db.collection('users').get();
    console.log(`Found ${usersSnapshot.size} users in Firestore`);

    // Get all existing Firebase Auth users to avoid duplicates
    const existingAuthUsers = await admin.auth().listUsers();
    const existingEmails = new Set(existingAuthUsers.users.map(user => user.email.toLowerCase()));

    console.log(`Found ${existingEmails.size} existing users in Firebase Auth`);

    let createdCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Create temp password file for new users
    const passwordData = [];

    // Process each user
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;

      // Skip if email already exists in Auth
      if (existingEmails.has(userData.email.toLowerCase())) {
        console.log(`Skipping existing user: ${userData.email}`);
        skippedCount++;
        continue;
      }

      try {
        // Generate a secure random password
        const password = generateSecurePassword();

        // Create the Firebase Auth user with the same UID as in Firestore
        const firebaseUser = await admin.auth().createUser({
          uid: userId,
          email: userData.email,
          password: password,
          displayName: userData.name,
          photoURL: userData.profilePicture || null,
          disabled: false,
        });

        // Set custom claims for role-based access control
        await admin.auth().setCustomUserClaims(userId, {
          role: userData.role,
          clinicId: userData.clinicId,
        });

        console.log(
          `Created Firebase Auth user for: ${userData.email} with ID: ${firebaseUser.uid}`,
        );

        // Add to password list for admin
        passwordData.push({
          email: userData.email,
          name: userData.name,
          password: password,
          role: userData.role,
        });

        createdCount++;
      } catch (error) {
        console.error(`Error creating user ${userData.email}:`, error.message);
        errorCount++;
      }
    }

    // Save passwords to a file (in a real system, you'd email these to users or use password reset)
    require('fs').writeFileSync('temp-user-passwords.json', JSON.stringify(passwordData, null, 2));

    console.log('\nSummary:');
    console.log(`- Created: ${createdCount} users`);
    console.log(`- Skipped: ${skippedCount} users (already exist)`);
    console.log(`- Errors: ${errorCount} users`);
    console.log('\nTemporary passwords have been saved to temp-user-passwords.json');
    console.log(
      'IMPORTANT: For security, delete this file after sending passwords to users or using password reset!',
    );
  } catch (error) {
    console.error('Error in createAuthUsers:', error);
    process.exit(1);
  }
}

// Run the function
createAuthUsers()
  .then(() => {
    console.log('Completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to create auth users:', error);
    process.exit(1);
  });
