// <PERSON>ript to call the migrate-patients API endpoint
require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');
const https = require('https');
const http = require('http');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
    process.exit(1);
  }
}

async function createCustomToken() {
  try {
    // Create a custom token for admin access
    const customToken = await admin.auth().createCustomToken('admin-migration-script');
    console.log('Created custom Firebase token');
    return customToken;
  } catch (error) {
    console.error('Error creating custom token:', error);
    throw error;
  }
}

async function callMigrationEndpoint() {
  try {
    // Get custom token for authentication
    const customToken = await createCustomToken();

    // Use the custom token to get an ID token (which is what the API expects)
    console.log('Calling migration endpoint...');

    // Make a POST request to the API endpoint
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/debug/migrate-patients',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${customToken}`,
      },
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, res => {
        let data = '';

        res.on('data', chunk => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            console.log('Migration response:', response);
            resolve(response);
          } catch (e) {
            console.log('Raw response:', data);
            reject(e);
          }
        });
      });

      req.on('error', error => {
        console.error('Error calling API:', error);
        reject(error);
      });

      req.end();
    });
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Run the migration
callMigrationEndpoint()
  .then(() => console.log('Migration process completed'))
  .catch(error => {
    console.error('Migration process failed:', error);
    process.exit(1);
  });
