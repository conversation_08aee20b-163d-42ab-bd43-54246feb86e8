#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Phase 4.2: User Migration Script
 *
 * This script migrates existing users to the new location model,
 * sets default locations, and validates data integrity.
 *
 * Tasks:
 * 1. Migrate existing users to new location model
 * 2. Set default location for all users
 * 3. Validate data integrity
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const usersCollection = db.collection('users');
const locationsCollection = db.collection('locations');
const practicesCollection = db.collection('practices');

/**
 * Step 1: Migrate existing users to new location model
 */
async function migrateUsersToLocationModel() {
  console.log('=== Phase 4.2: Migrating Users to New Location Model ===');

  const usersSnapshot = await usersCollection.get();
  console.log(`Found ${usersSnapshot.size} users`);

  let migratedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const userId = userDoc.id;

    try {
      // Skip if user already migrated (has locationIds)
      if (user.locationIds && user.locationIds.length > 0) {
        console.log(`⏭️  User ${userId} (${user.email}) already migrated`);
        skippedCount++;
        continue;
      }

      // Skip super admins (they don't need location assignments)
      if (user.role === 'SUPER_ADMIN') {
        console.log(`⏭️  Skipping SUPER_ADMIN user ${userId} (${user.email})`);
        skippedCount++;
        continue;
      }

      const clinicId = user.clinicId;
      if (!clinicId) {
        console.log(`⚠️  User ${userId} (${user.email}) has no clinicId, skipping`);
        skippedCount++;
        continue;
      }

      // Get all locations for this user's clinic
      const locationsSnapshot = await locationsCollection.where('clinicId', '==', clinicId).get();

      if (locationsSnapshot.empty) {
        console.log(`⚠️  No locations found for clinic ${clinicId} (user ${user.email})`);
        skippedCount++;
        continue;
      }

      // Prepare location assignments
      const locationIds = locationsSnapshot.docs.map(doc => doc.id);
      const currentLocationId = locationIds[0]; // Set first location as current

      // Get practice IDs from locations (derived field)
      const practiceIds = Array.from(
        new Set(
          locationsSnapshot.docs.map(doc => doc.data().practiceId).filter(practiceId => practiceId),
        ),
      );

      // Update user with new location model
      const updateData = {
        locationIds: locationIds,
        currentLocationId: currentLocationId,
        practiceIds: practiceIds, // Derived field for convenience
        updatedAt: admin.firestore.Timestamp.now(),
      };

      await userDoc.ref.update(updateData);

      console.log(`✅ Migrated user ${userId} (${user.email})`);
      console.log(`   Assigned to ${locationIds.length} locations`);
      console.log(`   Current location: ${currentLocationId}`);
      console.log(`   Practice IDs: ${practiceIds.join(', ')}`);

      migratedCount++;
    } catch (error) {
      console.error(`❌ Error migrating user ${userId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 User Migration Summary:`);
  console.log(`   Migrated: ${migratedCount} users`);
  console.log(`   Skipped: ${skippedCount} users (already migrated or super admin)`);
  console.log(`   Errors: ${errorCount} users`);

  return { migratedCount, skippedCount, errorCount };
}

/**
 * Step 2: Set default location for users without currentLocationId
 */
async function setDefaultLocations() {
  console.log('\n=== Phase 4.2: Setting Default Locations ===');

  const usersSnapshot = await usersCollection.where('role', '!=', 'SUPER_ADMIN').get();

  console.log(`Found ${usersSnapshot.size} non-super-admin users`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const userId = userDoc.id;

    try {
      // Skip if user already has a current location
      if (user.currentLocationId) {
        console.log(`⏭️  User ${userId} (${user.email}) already has current location`);
        skippedCount++;
        continue;
      }

      // Skip if user has no location assignments
      if (!user.locationIds || user.locationIds.length === 0) {
        console.log(`⚠️  User ${userId} (${user.email}) has no location assignments`);
        skippedCount++;
        continue;
      }

      // Set the first location as current
      const currentLocationId = user.locationIds[0];

      await userDoc.ref.update({
        currentLocationId: currentLocationId,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`✅ Set default location for user ${userId} (${user.email})`);
      console.log(`   Current location: ${currentLocationId}`);

      updatedCount++;
    } catch (error) {
      console.error(`❌ Error setting default location for user ${userId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 Default Location Summary:`);
  console.log(`   Updated: ${updatedCount} users`);
  console.log(`   Skipped: ${skippedCount} users (already have current location)`);
  console.log(`   Errors: ${errorCount} users`);

  return { updatedCount, skippedCount, errorCount };
}

/**
 * Step 3: Validate data integrity
 */
async function validateDataIntegrity() {
  console.log('\n=== Phase 4.2: Validating Data Integrity ===');

  const validationResults = {
    totalUsers: 0,
    usersWithLocationAccess: 0,
    usersWithCurrentLocation: 0,
    usersWithValidPractices: 0,
    orphanedUsers: [],
    invalidLocationReferences: [],
    invalidPracticeReferences: [],
  };

  // Get all users
  const usersSnapshot = await usersCollection.get();
  validationResults.totalUsers = usersSnapshot.size;

  console.log(`Validating ${usersSnapshot.size} users...`);

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const userId = userDoc.id;

    // Skip super admins
    if (user.role === 'SUPER_ADMIN') {
      continue;
    }

    // Check location access
    if (user.locationIds && user.locationIds.length > 0) {
      validationResults.usersWithLocationAccess++;

      // Validate location references
      for (const locationId of user.locationIds) {
        const locationDoc = await locationsCollection.doc(locationId).get();
        if (!locationDoc.exists) {
          validationResults.invalidLocationReferences.push({
            userId,
            email: user.email,
            invalidLocationId: locationId,
          });
        }
      }
    } else {
      validationResults.orphanedUsers.push({
        userId,
        email: user.email,
        clinicId: user.clinicId,
      });
    }

    // Check current location
    if (user.currentLocationId) {
      validationResults.usersWithCurrentLocation++;

      // Validate current location reference
      const currentLocationDoc = await locationsCollection.doc(user.currentLocationId).get();
      if (!currentLocationDoc.exists) {
        validationResults.invalidLocationReferences.push({
          userId,
          email: user.email,
          invalidLocationId: user.currentLocationId,
          type: 'currentLocation',
        });
      }
    }

    // Check practice access
    if (user.practiceIds && user.practiceIds.length > 0) {
      validationResults.usersWithValidPractices++;

      // Validate practice references
      for (const practiceId of user.practiceIds) {
        const practiceDoc = await practicesCollection.doc(practiceId).get();
        if (!practiceDoc.exists) {
          validationResults.invalidPracticeReferences.push({
            userId,
            email: user.email,
            invalidPracticeId: practiceId,
          });
        }
      }
    }
  }

  // Print validation results
  console.log(`\n📊 Data Integrity Validation Results:`);
  console.log(`   Total Users: ${validationResults.totalUsers}`);
  console.log(`   Users with Location Access: ${validationResults.usersWithLocationAccess}`);
  console.log(`   Users with Current Location: ${validationResults.usersWithCurrentLocation}`);
  console.log(`   Users with Valid Practices: ${validationResults.usersWithValidPractices}`);

  if (validationResults.orphanedUsers.length > 0) {
    console.log(
      `\n⚠️  Orphaned Users (no location access): ${validationResults.orphanedUsers.length}`,
    );
    validationResults.orphanedUsers.forEach(user => {
      console.log(`   - ${user.email} (${user.userId}) - Clinic: ${user.clinicId}`);
    });
  }

  if (validationResults.invalidLocationReferences.length > 0) {
    console.log(
      `\n❌ Invalid Location References: ${validationResults.invalidLocationReferences.length}`,
    );
    validationResults.invalidLocationReferences.forEach(ref => {
      console.log(`   - ${ref.email} (${ref.userId}) - Invalid Location: ${ref.invalidLocationId}`);
    });
  }

  if (validationResults.invalidPracticeReferences.length > 0) {
    console.log(
      `\n❌ Invalid Practice References: ${validationResults.invalidPracticeReferences.length}`,
    );
    validationResults.invalidPracticeReferences.forEach(ref => {
      console.log(`   - ${ref.email} (${ref.userId}) - Invalid Practice: ${ref.invalidPracticeId}`);
    });
  }

  const isValid =
    validationResults.orphanedUsers.length === 0 &&
    validationResults.invalidLocationReferences.length === 0 &&
    validationResults.invalidPracticeReferences.length === 0;

  if (isValid) {
    console.log(`\n✅ Data integrity validation passed!`);
  } else {
    console.log(`\n⚠️  Data integrity issues found. Please review and fix the issues above.`);
  }

  return validationResults;
}

/**
 * Main migration function for Phase 4.2
 */
async function runPhase4UserMigration() {
  console.log('🚀 Starting Phase 4.2: User Migration');
  console.log('=====================================\n');

  try {
    // Step 1: Migrate users to new location model
    const migrationResults = await migrateUsersToLocationModel();

    // Step 2: Set default locations
    const defaultLocationResults = await setDefaultLocations();

    // Step 3: Validate data integrity
    const validationResults = await validateDataIntegrity();

    // Final summary
    console.log('\n🎉 Phase 4.2 User Migration Complete!');
    console.log('====================================');
    console.log(`📊 Final Summary:`);
    console.log(`   Users Migrated: ${migrationResults.migratedCount}`);
    console.log(`   Default Locations Set: ${defaultLocationResults.updatedCount}`);
    console.log(
      `   Total Errors: ${migrationResults.errorCount + defaultLocationResults.errorCount}`,
    );
    console.log(
      `   Data Integrity: ${
        validationResults.orphanedUsers.length === 0 &&
        validationResults.invalidLocationReferences.length === 0 &&
        validationResults.invalidPracticeReferences.length === 0
          ? 'PASSED'
          : 'ISSUES FOUND'
      }`,
    );

    if (
      migrationResults.errorCount + defaultLocationResults.errorCount === 0 &&
      validationResults.orphanedUsers.length === 0 &&
      validationResults.invalidLocationReferences.length === 0 &&
      validationResults.invalidPracticeReferences.length === 0
    ) {
      console.log('\n✅ User migration completed successfully with no errors!');
    } else {
      console.log('\n⚠️  User migration completed with some issues. Please review the logs above.');
    }
  } catch (error) {
    console.error('💥 User migration failed:', error);
    process.exit(1);
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  runPhase4UserMigration()
    .then(() => {
      console.log('\n🏁 User migration script finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 User migration script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runPhase4UserMigration,
  migrateUsersToLocationModel,
  setDefaultLocations,
  validateDataIntegrity,
};
