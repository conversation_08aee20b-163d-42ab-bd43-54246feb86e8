/**
 * Simple script to check if users have the canTakeAppointments flag
 */

const admin = require('firebase-admin');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const db = admin.firestore();
const usersCollection = db.collection('users');

async function checkUsers() {
  try {
    // Get all users
    const usersSnapshot = await usersCollection.get();
    console.log(`Found ${usersSnapshot.size} users in Firestore`);

    let usersWithFlag = 0;
    let usersCanTakeAppointments = 0;

    // Process each user
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;

      if ('canTakeAppointments' in userData) {
        usersWithFlag++;
        if (userData.canTakeAppointments === true) {
          usersCanTakeAppointments++;
          console.log(`User ${userData.name || userData.email} (${userId}) can take appointments`);
        }
      } else {
        console.log(
          `User ${
            userData.name || userData.email
          } (${userId}) is missing the canTakeAppointments flag`,
        );
      }
    }

    console.log('\nSummary:');
    console.log(`- Total users: ${usersSnapshot.size}`);
    console.log(`- Users with canTakeAppointments flag: ${usersWithFlag}`);
    console.log(`- Users who can take appointments: ${usersCanTakeAppointments}`);
  } catch (error) {
    console.error('Error checking users:', error);
    throw error;
  }
}

// Run the function
checkUsers()
  .then(() => {
    console.log('Check complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Check failed:', error);
    process.exit(1);
  });
