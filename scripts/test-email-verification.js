/**
 * This is a simple script to help test email verification links
 * Run it with Node.js: node scripts/test-email-verification.js
 */

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

console.log('=== Email Verification Link Tester ===');
console.log('This script helps you test Firebase email verification links during development.');
console.log('Paste the verification link from the console logs to analyze it.\n');

rl.question('Paste the verification link: ', link => {
  try {
    // Parse the URL
    const url = new URL(link);

    // Extract the query parameters
    const params = url.searchParams;

    console.log('\n=== Link Analysis ===');
    console.log('Base URL:', `${url.protocol}//${url.host}${url.pathname}`);
    console.log('\nParameters:');

    // Log all parameters
    for (const [key, value] of params.entries()) {
      console.log(`- ${key}: ${value}`);
    }

    // Extract specific parameters of interest
    const mode = params.get('mode');
    const oobCode = params.get('oobCode');
    const apiKey = params.get('apiKey');
    const continueUrl = params.get('continueUrl');

    console.log('\n=== Action Details ===');
    if (mode) {
      console.log('Action Type:', mode);

      switch (mode) {
        case 'verifyEmail':
          console.log('This is an email verification link.');
          break;
        case 'resetPassword':
          console.log('This is a password reset link.');
          break;
        case 'verifyAndChangeEmail':
          console.log('This is a link to verify and change email.');
          break;
        default:
          console.log(`Unknown action type: ${mode}`);
      }
    } else {
      console.log('No action type (mode) specified in the link.');
    }

    if (oobCode) {
      console.log('Action Code:', oobCode);
      console.log('This code is used by Firebase to verify the action.');
    }

    if (continueUrl) {
      console.log('\nAfter verification, user will be redirected to:', continueUrl);
    }

    console.log('\n=== Manual Testing Instructions ===');
    console.log('1. Open the link in a browser to test the verification flow.');
    console.log('2. For development, you can also use the /dev-email-handler page.');
    console.log('3. To simulate clicking the link, visit:');
    console.log(
      `   http://localhost:3000/dev-email-handler?mode=${mode || ''}&oobCode=${oobCode || ''}&continueUrl=${encodeURIComponent(continueUrl || '')}`,
    );
  } catch (error) {
    console.error('Error parsing the link:', error.message);
  }

  rl.close();
});

rl.on('close', () => {
  console.log('\nThank you for using the Email Verification Link Tester!');
  process.exit(0);
});
