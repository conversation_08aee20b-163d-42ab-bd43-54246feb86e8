#!/usr/bin/env node

/**
 * Firestore Index Management Script
 *
 * This script helps you manage Firestore indexes by:
 * 1. Retrieving all indexes from Firestore
 * 2. Comparing local indexes with remote indexes
 * 3. Deploying indexes to Firestore
 * 4. Generating missing indexes from query errors
 *
 * Usage:
 *   node scripts/firestore-index-manager.js list                  # List all remote indexes
 *   node scripts/firestore-index-manager.js compare              # Compare local vs remote
 *   node scripts/firestore-index-manager.js deploy               # Deploy local indexes
 *   node scripts/firestore-index-manager.js generate-missing     # Generate missing indexes from errors
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const PROJECT_ID = process.env.FIREBASE_PROJECT_ID || 'frontdesk-454309';
const LOCAL_INDEXES_FILE = path.join(__dirname, '..', 'firestore.indexes.json');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

/**
 * Execute a shell command and return a promise
 */
function executeCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject({ error, stderr });
        return;
      }
      resolve(stdout);
    });
  });
}

/**
 * Get all indexes from Firestore using gcloud CLI
 */
async function getRemoteIndexes() {
  try {
    console.log(colorize('📡 Fetching remote indexes from Firestore...', 'blue'));

    const command = `gcloud firestore indexes list --project=${PROJECT_ID} --format=json`;
    const output = await executeCommand(command);

    if (!output.trim()) {
      console.log(colorize('⚠️  No remote indexes found', 'yellow'));
      return [];
    }

    const indexes = JSON.parse(output);
    console.log(colorize(`✅ Found ${indexes.length} remote indexes`, 'green'));

    return indexes;
  } catch (error) {
    console.error(colorize('❌ Error fetching remote indexes:', 'red'));
    console.error(error.stderr || error.error?.message);

    if (error.stderr?.includes('gcloud')) {
      console.log(
        colorize('\n💡 Install gcloud CLI: https://cloud.google.com/sdk/docs/install', 'cyan'),
      );
    }

    throw error;
  }
}

/**
 * Get local indexes from firestore.indexes.json
 */
function getLocalIndexes() {
  try {
    if (!fs.existsSync(LOCAL_INDEXES_FILE)) {
      console.log(colorize('⚠️  No local indexes file found', 'yellow'));
      return { indexes: [], fieldOverrides: [] };
    }

    const content = fs.readFileSync(LOCAL_INDEXES_FILE, 'utf8');
    const config = JSON.parse(content);

    console.log(colorize(`📁 Found ${config.indexes?.length || 0} local indexes`, 'blue'));

    return config;
  } catch (error) {
    console.error(colorize('❌ Error reading local indexes:', 'red'));
    console.error(error.message);
    throw error;
  }
}

/**
 * Compare two index objects for equality
 */
function areIndexesEqual(index1, index2) {
  if (index1.collectionGroup !== index2.collectionGroup) return false;
  if (index1.queryScope !== index2.queryScope) return false;

  if (!index1.fields || !index2.fields) return false;
  if (index1.fields.length !== index2.fields.length) return false;

  for (let i = 0; i < index1.fields.length; i++) {
    const field1 = index1.fields[i];
    const field2 = index2.fields[i];

    if (field1.fieldPath !== field2.fieldPath) return false;
    if (field1.order !== field2.order) return false;
    if (field1.arrayConfig !== field2.arrayConfig) return false;
  }

  return true;
}

/**
 * Normalize index for comparison (remove Firebase-specific fields)
 */
function normalizeIndex(index) {
  return {
    collectionGroup: index.collectionGroup || index.collectionId,
    queryScope: index.queryScope || 'COLLECTION',
    fields: index.fields || [],
  };
}

/**
 * Compare local and remote indexes
 */
async function compareIndexes() {
  try {
    console.log(colorize('\n🔍 Comparing local and remote indexes...', 'bright'));

    const localConfig = getLocalIndexes();
    const remoteIndexes = await getRemoteIndexes();

    const localIndexes = localConfig.indexes || [];
    const normalizedRemote = remoteIndexes.map(normalizeIndex);

    // Find missing indexes (in local but not in remote)
    const missingIndexes = [];
    const existingIndexes = [];

    for (const localIndex of localIndexes) {
      const found = normalizedRemote.find(remoteIndex => areIndexesEqual(localIndex, remoteIndex));

      if (found) {
        existingIndexes.push(localIndex);
      } else {
        missingIndexes.push(localIndex);
      }
    }

    // Find extra indexes (in remote but not in local)
    const extraIndexes = [];
    for (const remoteIndex of normalizedRemote) {
      const found = localIndexes.find(localIndex => areIndexesEqual(localIndex, remoteIndex));

      if (!found) {
        extraIndexes.push(remoteIndex);
      }
    }

    console.log(colorize('\n📊 Comparison Results:', 'bright'));
    console.log(colorize(`✅ Existing indexes: ${existingIndexes.length}`, 'green'));
    console.log(colorize(`❌ Missing indexes: ${missingIndexes.length}`, 'red'));
    console.log(colorize(`➕ Extra indexes: ${extraIndexes.length}`, 'yellow'));

    if (missingIndexes.length > 0) {
      console.log(colorize('\n🔴 Missing Indexes (in local but not in remote):', 'red'));
      missingIndexes.forEach((index, i) => {
        console.log(`${i + 1}. Collection: ${index.collectionGroup}`);
        console.log(
          `   Fields: ${index.fields.map(f => `${f.fieldPath} (${f.order})`).join(', ')}`,
        );
      });
    }

    if (extraIndexes.length > 0) {
      console.log(colorize('\n🟡 Extra Indexes (in remote but not in local):', 'yellow'));
      extraIndexes.forEach((index, i) => {
        console.log(`${i + 1}. Collection: ${index.collectionGroup}`);
        console.log(
          `   Fields: ${index.fields.map(f => `${f.fieldPath} (${f.order || 'ASCENDING'})`).join(', ')}`,
        );
      });
    }

    return {
      missing: missingIndexes,
      extra: extraIndexes,
      existing: existingIndexes,
    };
  } catch (error) {
    console.error(colorize('❌ Error comparing indexes:', 'red'));
    console.error(error.message);
    throw error;
  }
}

/**
 * Deploy local indexes to Firestore
 */
async function deployIndexes() {
  try {
    console.log(colorize('\n🚀 Deploying indexes to Firestore...', 'blue'));

    const command = `firebase deploy --only firestore:indexes --project=${PROJECT_ID}`;
    console.log(colorize(`Executing: ${command}`, 'cyan'));

    const output = await executeCommand(command);
    console.log(output);

    console.log(colorize('✅ Indexes deployed successfully!', 'green'));
  } catch (error) {
    console.error(colorize('❌ Error deploying indexes:', 'red'));
    console.error(error.stderr || error.error?.message);

    if (error.stderr?.includes('firebase')) {
      console.log(colorize('\n💡 Install Firebase CLI: npm install -g firebase-tools', 'cyan'));
    }

    throw error;
  }
}

/**
 * Generate missing index from query error
 */
function generateMissingIndex(errorMessage) {
  try {
    console.log(colorize('\n🔧 Generating missing index from error...', 'blue'));

    // Extract collection and fields from error URL
    const urlMatch = errorMessage.match(/create_composite=([^&\s]+)/);
    if (!urlMatch) {
      throw new Error('Could not parse index URL from error message');
    }

    const encodedData = urlMatch[1];
    console.log(colorize(`📝 Parsing index definition...`, 'cyan'));

    // Decode the base64-like data (this is a simplified approach)
    // In practice, you might need to visit the URL or use Firebase Admin SDK
    console.log(colorize('🔗 Index creation URL found in error:', 'yellow'));
    console.log(
      `https://console.firebase.google.com/v1/r/project/${PROJECT_ID}/firestore/indexes?create_composite=${encodedData}`,
    );

    console.log(colorize('\n💡 To create the index:', 'cyan'));
    console.log('1. Visit the URL above');
    console.log('2. Click "Create Index" in Firebase Console');
    console.log('3. Wait for index creation to complete');
    console.log('4. Run this script again to verify');

    return {
      url: `https://console.firebase.google.com/v1/r/project/${PROJECT_ID}/firestore/indexes?create_composite=${encodedData}`,
      encodedData,
    };
  } catch (error) {
    console.error(colorize('❌ Error generating missing index:', 'red'));
    console.error(error.message);
    throw error;
  }
}

/**
 * List all remote indexes in a formatted way
 */
async function listRemoteIndexes() {
  try {
    const indexes = await getRemoteIndexes();

    if (indexes.length === 0) {
      console.log(colorize('📭 No indexes found', 'yellow'));
      return;
    }

    console.log(colorize('\n📋 Remote Firestore Indexes:', 'bright'));

    const groupedByCollection = {};
    indexes.forEach(index => {
      const collection = index.collectionGroup || index.collectionId || 'unknown';
      if (!groupedByCollection[collection]) {
        groupedByCollection[collection] = [];
      }
      groupedByCollection[collection].push(index);
    });

    Object.keys(groupedByCollection)
      .sort()
      .forEach(collection => {
        console.log(colorize(`\n📁 Collection: ${collection}`, 'blue'));

        groupedByCollection[collection].forEach((index, i) => {
          const state = index.state || 'UNKNOWN';
          const stateColor = state === 'READY' ? 'green' : state === 'CREATING' ? 'yellow' : 'red';

          console.log(`   ${i + 1}. ${colorize(state, stateColor)}`);
          if (index.fields) {
            console.log(
              `      Fields: ${index.fields
                .map(f => `${f.fieldPath} (${f.order || f.arrayConfig || 'ASCENDING'})`)
                .join(', ')}`,
            );
          }
        });
      });
  } catch (error) {
    console.error(colorize('❌ Error listing indexes:', 'red'));
    console.error(error.message);
  }
}

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];

  console.log(colorize('🔥 Firestore Index Manager', 'bright'));
  console.log(colorize(`📁 Project: ${PROJECT_ID}`, 'cyan'));

  try {
    switch (command) {
      case 'list':
        await listRemoteIndexes();
        break;

      case 'compare':
        await compareIndexes();
        break;

      case 'deploy':
        await deployIndexes();
        break;

      case 'generate-missing':
        const errorMessage = process.argv[3] || process.env.FIRESTORE_ERROR;
        if (!errorMessage) {
          console.log(
            colorize(
              '❌ Please provide error message as argument or FIRESTORE_ERROR env var',
              'red',
            ),
          );
          process.exit(1);
        }
        generateMissingIndex(errorMessage);
        break;

      default:
        console.log(colorize('\n📖 Usage:', 'bright'));
        console.log('  list              - List all remote indexes');
        console.log('  compare           - Compare local vs remote indexes');
        console.log('  deploy            - Deploy local indexes to Firestore');
        console.log('  generate-missing  - Generate missing index from error message');
        console.log('');
        console.log(colorize('Examples:', 'cyan'));
        console.log('  node scripts/firestore-index-manager.js list');
        console.log('  node scripts/firestore-index-manager.js compare');
        console.log('  node scripts/firestore-index-manager.js deploy');
        break;
    }
  } catch (error) {
    console.error(colorize('\n💥 Script failed:', 'red'));
    console.error(error.message);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
