import admin from 'firebase-admin';

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.applicationDefault(),
  });
}

const db = admin.firestore();

interface ValidationResult {
  collection: string;
  passed: boolean;
  errors: string[];
  warnings: string[];
  documentCount: number;
}

// Required fields for each collection
const REQUIRED_FIELDS = {
  'agent-location-mappings': [
    'agentId',
    'locationId',
    'clinicId',
    'isActive',
    'createdAt',
    'updatedAt',
  ],
  'on-call-schedules': [
    'id',
    'doctorId',
    'doctorName',
    'doctorPhone',
    'locationId',
    'clinicId',
    'date',
    'startTime',
    'endTime',
    'isActive',
    'timezone',
    'createdAt',
    'updatedAt',
    'createdBy',
  ],
  'on-call-notifications': [
    'id',
    'scheduleId',
    'callSessionId',
    'doctorId',
    'clinicId',
    'notificationTime',
    'status',
    'createdAt',
  ],
};

// Field type validations
const FIELD_TYPES = {
  'agent-location-mappings': {
    agentId: 'string',
    locationId: 'string',
    clinicId: 'number',
    isActive: 'boolean',
  },
  'on-call-schedules': {
    id: 'string',
    doctorId: 'string',
    doctorName: 'string',
    doctorPhone: 'string',
    locationId: 'string',
    clinicId: 'number',
    date: 'string',
    startTime: 'string',
    endTime: 'string',
    isActive: 'boolean',
    timezone: 'string',
    createdBy: 'string',
  },
  'on-call-notifications': {
    id: 'string',
    scheduleId: 'string',
    callSessionId: 'string',
    doctorId: 'string',
    clinicId: 'number',
    status: 'string',
  },
};

// Valid enum values
const ENUM_VALUES = {
  'on-call-notifications': {
    status: ['sent', 'failed', 'delivered'],
  },
};

async function validateCollection(collectionName: string): Promise<ValidationResult> {
  const result: ValidationResult = {
    collection: collectionName,
    passed: true,
    errors: [],
    warnings: [],
    documentCount: 0,
  };

  try {
    console.log(`\n🔍 Validating ${collectionName} collection...`);

    const snapshot = await db.collection(collectionName).get();
    result.documentCount = snapshot.size;

    if (snapshot.empty) {
      result.warnings.push(`Collection ${collectionName} is empty`);
      return result;
    }

    const requiredFields = REQUIRED_FIELDS[collectionName as keyof typeof REQUIRED_FIELDS];
    const fieldTypes = FIELD_TYPES[collectionName as keyof typeof FIELD_TYPES];
    const enumValues = ENUM_VALUES[collectionName as keyof typeof ENUM_VALUES];

    for (const doc of snapshot.docs) {
      const data = doc.data();
      const docId = doc.id;

      // Check required fields
      for (const field of requiredFields) {
        if (!(field in data)) {
          result.errors.push(`Document ${docId}: Missing required field '${field}'`);
          result.passed = false;
        }
      }

      // Check field types
      if (fieldTypes) {
        for (const [field, expectedType] of Object.entries(fieldTypes)) {
          if (field in data) {
            const actualType = typeof data[field];
            if (actualType !== expectedType) {
              result.errors.push(
                `Document ${docId}: Field '${field}' should be ${expectedType}, got ${actualType}`,
              );
              result.passed = false;
            }
          }
        }
      }

      // Check enum values
      if (enumValues) {
        for (const [field, validValues] of Object.entries(enumValues)) {
          if (field in data && !validValues.includes(data[field])) {
            result.errors.push(
              `Document ${docId}: Field '${field}' has invalid value '${data[field]}'. Valid values: ${validValues.join(', ')}`,
            );
            result.passed = false;
          }
        }
      }

      // Collection-specific validations
      if (collectionName === 'on-call-schedules') {
        await validateOnCallSchedule(data, docId, result);
      } else if (collectionName === 'on-call-notifications') {
        await validateOnCallNotification(data, docId, result);
      } else if (collectionName === 'agent-location-mappings') {
        await validateAgentLocationMapping(data, docId, result);
      }
    }

    console.log(`✅ Validated ${result.documentCount} documents in ${collectionName}`);
  } catch (error) {
    result.errors.push(`Failed to validate collection: ${error}`);
    result.passed = false;
  }

  return result;
}

async function validateOnCallSchedule(
  data: Record<string, unknown>,
  docId: string,
  result: ValidationResult,
): Promise<void> {
  // Validate date format (YYYY-MM-DD)
  if (data.date && typeof data.date === 'string' && !/^\d{4}-\d{2}-\d{2}$/.test(data.date)) {
    result.errors.push(
      `Document ${docId}: Invalid date format '${data.date}'. Expected YYYY-MM-DD`,
    );
    result.passed = false;
  }

  // Validate time format (HH:MM)
  if (
    data.startTime &&
    typeof data.startTime === 'string' &&
    !/^\d{2}:\d{2}$/.test(data.startTime)
  ) {
    result.errors.push(
      `Document ${docId}: Invalid startTime format '${data.startTime}'. Expected HH:MM`,
    );
    result.passed = false;
  }

  if (data.endTime && typeof data.endTime === 'string' && !/^\d{2}:\d{2}$/.test(data.endTime)) {
    result.errors.push(
      `Document ${docId}: Invalid endTime format '${data.endTime}'. Expected HH:MM`,
    );
    result.passed = false;
  }

  // Validate phone number format
  if (
    data.doctorPhone &&
    typeof data.doctorPhone === 'string' &&
    !/^\+\d{10,15}$/.test(data.doctorPhone)
  ) {
    result.warnings.push(
      `Document ${docId}: Phone number '${data.doctorPhone}' may not be in international format (+1234567890)`,
    );
  }

  // Validate timezone
  if (data.timezone && typeof data.timezone === 'string') {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: data.timezone });
    } catch (error) {
      result.errors.push(`Document ${docId}: Invalid timezone '${data.timezone}'`);
      result.passed = false;
    }
  }

  // Check if location exists
  if (data.locationId && typeof data.locationId === 'string') {
    try {
      const locationDoc = await db.collection('locations').doc(data.locationId).get();
      if (!locationDoc.exists) {
        result.warnings.push(
          `Document ${docId}: Referenced location '${data.locationId}' does not exist`,
        );
      }
    } catch (error) {
      result.warnings.push(`Document ${docId}: Could not verify location reference: ${error}`);
    }
  }
}

async function validateOnCallNotification(
  data: Record<string, unknown>,
  docId: string,
  result: ValidationResult,
): Promise<void> {
  // Check if referenced schedule exists
  if (data.scheduleId && typeof data.scheduleId === 'string') {
    try {
      const scheduleDoc = await db.collection('on-call-schedules').doc(data.scheduleId).get();
      if (!scheduleDoc.exists) {
        result.warnings.push(
          `Document ${docId}: Referenced schedule '${data.scheduleId}' does not exist`,
        );
      }
    } catch (error) {
      result.warnings.push(`Document ${docId}: Could not verify schedule reference: ${error}`);
    }
  }

  // Validate phone number format
  if (
    data.callerPhone &&
    typeof data.callerPhone === 'string' &&
    !/^\+\d{10,15}$/.test(data.callerPhone)
  ) {
    result.warnings.push(
      `Document ${docId}: Caller phone '${data.callerPhone}' may not be in international format`,
    );
  }

  // Check if error message exists for failed status
  if (data.status === 'failed' && !data.errorMessage) {
    result.warnings.push(`Document ${docId}: Failed notification should have errorMessage`);
  }
}

async function validateAgentLocationMapping(
  data: Record<string, unknown>,
  docId: string,
  result: ValidationResult,
): Promise<void> {
  // Check if location exists
  if (data.locationId) {
    try {
      const locationDoc = await db.collection('locations').doc(data.locationId).get();
      if (!locationDoc.exists) {
        result.warnings.push(
          `Document ${docId}: Referenced location '${data.locationId}' does not exist`,
        );
      }
    } catch (error) {
      result.warnings.push(`Document ${docId}: Could not verify location reference: ${error}`);
    }
  }

  // Check for duplicate agent mappings
  if (data.agentId && data.isActive) {
    try {
      const duplicateSnapshot = await db
        .collection('agent-location-mappings')
        .where('agentId', '==', data.agentId)
        .where('isActive', '==', true)
        .get();

      if (duplicateSnapshot.size > 1) {
        result.warnings.push(
          `Document ${docId}: Agent '${data.agentId}' has multiple active mappings`,
        );
      }
    } catch (error) {
      result.warnings.push(`Document ${docId}: Could not check for duplicate mappings: ${error}`);
    }
  }
}

async function validateIndexes(): Promise<void> {
  console.log('\n🔍 Validating Firestore indexes...');

  // This is a basic check - in a real implementation, you'd want to
  // actually test the indexes by running queries that require them

  try {
    // Test agent-location-mappings indexes
    await db.collection('agent-location-mappings').where('isActive', '==', true).limit(1).get();

    console.log('✅ agent-location-mappings indexes working');

    // Test on-call-schedules indexes
    await db
      .collection('on-call-schedules')
      .where('isActive', '==', true)
      .orderBy('date')
      .limit(1)
      .get();

    console.log('✅ on-call-schedules indexes working');

    // Test on-call-notifications indexes
    await db.collection('on-call-notifications').orderBy('createdAt', 'desc').limit(1).get();

    console.log('✅ on-call-notifications indexes working');
  } catch (error) {
    console.error('❌ Index validation failed:', error);
  }
}

async function main(): Promise<void> {
  try {
    console.log('🚀 Starting database integrity validation...');

    const collections = ['agent-location-mappings', 'on-call-schedules', 'on-call-notifications'];

    const results: ValidationResult[] = [];

    for (const collection of collections) {
      const result = await validateCollection(collection);
      results.push(result);
    }

    // Validate indexes
    await validateIndexes();

    // Print summary
    console.log('\n📊 VALIDATION SUMMARY');
    console.log('='.repeat(50));

    let allPassed = true;
    let totalDocs = 0;
    let totalErrors = 0;
    let totalWarnings = 0;

    for (const result of results) {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.collection}: ${result.documentCount} documents`);

      if (result.errors.length > 0) {
        console.log(`   Errors: ${result.errors.length}`);
        result.errors.forEach(error => console.log(`     - ${error}`));
        allPassed = false;
      }

      if (result.warnings.length > 0) {
        console.log(`   Warnings: ${result.warnings.length}`);
        result.warnings.forEach(warning => console.log(`     - ${warning}`));
      }

      totalDocs += result.documentCount;
      totalErrors += result.errors.length;
      totalWarnings += result.warnings.length;
    }

    console.log('\n' + '='.repeat(50));
    console.log(`Total documents validated: ${totalDocs}`);
    console.log(`Total errors: ${totalErrors}`);
    console.log(`Total warnings: ${totalWarnings}`);

    if (allPassed) {
      console.log('🎉 All validations passed!');
      process.exit(0);
    } else {
      console.log('💥 Validation failed - please fix errors above');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Validation script failed:', error);
    process.exit(1);
  }
}

// Export functions for use in other scripts
export { validateCollection, validateIndexes };

// Run main function if this script is executed directly
if (require.main === module) {
  main();
}
