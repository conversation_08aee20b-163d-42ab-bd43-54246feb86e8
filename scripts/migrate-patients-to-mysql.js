// Copy patients collection from Firestore to MySQL
require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');
const mysql = require('mysql2/promise');

const BATCH_SIZE = Number(process.argv[2]) || 100; // allow batch size override as first arg

(async () => {
  try {
    // Init Firebase
    if (!admin.apps.length) {
      const { FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, FIREBASE_PRIVATE_KEY } = process.env;
      if (!FIREBASE_PROJECT_ID || !FIREBASE_CLIENT_EMAIL || !FIREBASE_PRIVATE_KEY) {
        throw new Error('Missing Firebase env vars');
      }
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: FIREBASE_PROJECT_ID,
          clientEmail: FIREBASE_CLIENT_EMAIL,
          privateKey: FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }),
      });
    }
    const db = admin.firestore();

    // Init MySQL
    const pool = await mysql.createPool({
      host: process.env.MYSQL_HOST || process.env.DB_HOST,
      port: Number(process.env.MYSQL_PORT || process.env.DB_PORT || 3306),
      user: process.env.MYSQL_USER || process.env.DB_USER,
      password: process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD,
      database: process.env.MYSQL_DATABASE || process.env.DB_NAME,
      ssl: process.env.MYSQL_SSL === 'true' ? {} : undefined,
      connectionLimit: 10,
    });

    console.log('🚀 Migrating Firestore patients -> MySQL');

    const snapshot = await db.collection('patients').get();
    console.log(`Found ${snapshot.size} patients`);

    const docs = snapshot.docs;
    let migrated = 0;

    const toDate = v => (v && v.toDate ? v.toDate() : v ? new Date(v) : new Date());

    const normalizeJson = v => JSON.stringify(v || null);

    for (let i = 0; i < docs.length; i += BATCH_SIZE) {
      const batch = docs.slice(i, i + BATCH_SIZE);
      const rows = batch.map(doc => {
        const d = doc.data();
        return [
          doc.id,
          d.firstName || '',
          d.lastName || '',
          d.dateOfBirth || null,
          d.gender || null,
          d.email || null,
          d.phoneNumber || null,
          normalizeJson(d.address),
          (d.providerInfo && d.providerInfo.provider) || d.provider || null,
          (d.providerInfo && d.providerInfo.externalId) || d.providerId || null,
          d.notes || null,
          normalizeJson(d.insurances),
          normalizeJson(d.identifiers),
          toDate(d.createdAt),
          toDate(d.updatedAt),
        ];
      });

      const conn = await pool.getConnection();
      try {
        await conn.beginTransaction();
        const placeholders = rows.map(() => '(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)').join(',');
        const sql = `INSERT INTO patients 
          (id, first_name, last_name, date_of_birth, gender, email, phone_number, address, provider, provider_id, notes, insurances, identifiers, created_at, updated_at)
          VALUES ${placeholders}
          ON DUPLICATE KEY UPDATE 
            first_name = VALUES(first_name),
            last_name = VALUES(last_name),
            date_of_birth = VALUES(date_of_birth),
            gender = VALUES(gender),
            email = VALUES(email),
            phone_number = VALUES(phone_number),
            address = VALUES(address),
            provider = VALUES(provider),
            provider_id = VALUES(provider_id),
            notes = VALUES(notes),
            insurances = VALUES(insurances),
            identifiers = VALUES(identifiers),
            updated_at = VALUES(updated_at)`;
        await conn.query(sql, rows.flat());
        await conn.commit();
        migrated += rows.length;
        console.log(`✅ Migrated ${migrated}/${docs.length}`);
      } catch (err) {
        await conn.rollback();
        console.error('❌ Batch failed', err);
      } finally {
        conn.release();
      }
    }

    console.log('🎉 Migration complete. Total migrated:', migrated);
    await pool.end();
    process.exit(0);
  } catch (err) {
    console.error('💥 Migration error:', err);
    process.exit(1);
  }
})();
