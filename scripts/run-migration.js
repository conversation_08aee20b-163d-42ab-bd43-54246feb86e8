// Script to migrate patients to clients collection
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
    process.exit(1);
  }
}

// Function to migrate patients to clients collection
async function migratePatientToClientCollection() {
  try {
    const db = admin.firestore();
    const patientsCollection = db.collection('patients');
    const clientsCollection = db.collection('clients');

    console.log('Starting migration from patients to clients collection...');

    // Get all patients
    const patientsSnapshot = await patientsCollection.get();

    if (patientsSnapshot.empty) {
      console.log('No patients to migrate');
      return { migrated: 0 };
    }

    console.log(`Found ${patientsSnapshot.size} patients to migrate`);

    // Copy each patient to clients collection
    let migrated = 0;
    const batch = db.batch();

    patientsSnapshot.forEach(doc => {
      const patientData = doc.data();
      const clientRef = clientsCollection.doc(doc.id);
      batch.set(clientRef, patientData);
      migrated++;
    });

    await batch.commit();
    console.log(`Successfully migrated ${migrated} patients to clients collection`);

    return { migrated };
  } catch (error) {
    console.error('Error migrating patients to clients:', error);
    throw error;
  }
}

// Run the migration
async function runMigration() {
  try {
    const result = await migratePatientToClientCollection();
    console.log(`Migration complete! ${result.migrated} patients migrated to clients collection.`);
    process.exit(0);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

runMigration();
