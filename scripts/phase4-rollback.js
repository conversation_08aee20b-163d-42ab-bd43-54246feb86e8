#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Phase 4 Rollback Script
 *
 * This script provides rollback capabilities for the Phase 4 migration.
 * It can undo all changes made by the migration scripts.
 *
 * Rollback Operations:
 * 1. Remove new fields from users (locationIds, currentLocationId, practiceIds)
 * 2. Remove practiceId from locations
 * 3. Delete created practices
 * 4. Restore original data structure
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const usersCollection = db.collection('users');
const locationsCollection = db.collection('locations');
const practicesCollection = db.collection('practices');

/**
 * Step 1: Remove new fields from users
 */
async function rollbackUserFields() {
  console.log('=== Rollback: Removing New User Fields ===');

  const usersSnapshot = await usersCollection.get();
  console.log(`Found ${usersSnapshot.size} users to process`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    const userId = userDoc.id;

    try {
      const updates = {};
      let hasUpdates = false;

      // Remove locationIds field
      if (user.hasOwnProperty('locationIds')) {
        updates.locationIds = admin.firestore.FieldValue.delete();
        hasUpdates = true;
      }

      // Remove currentLocationId field
      if (user.hasOwnProperty('currentLocationId')) {
        updates.currentLocationId = admin.firestore.FieldValue.delete();
        hasUpdates = true;
      }

      // Remove practiceIds field
      if (user.hasOwnProperty('practiceIds')) {
        updates.practiceIds = admin.firestore.FieldValue.delete();
        hasUpdates = true;
      }

      if (hasUpdates) {
        updates.updatedAt = admin.firestore.Timestamp.now();
        await userDoc.ref.update(updates);
        console.log(`✅ Rolled back user ${userId} (${user.email})`);
        updatedCount++;
      } else {
        console.log(`⏭️  User ${userId} (${user.email}) - no fields to remove`);
        skippedCount++;
      }
    } catch (error) {
      console.error(`❌ Error rolling back user ${userId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 User Rollback Summary:`);
  console.log(`   Updated: ${updatedCount} users`);
  console.log(`   Skipped: ${skippedCount} users (no changes needed)`);
  console.log(`   Errors: ${errorCount} users`);

  return { updatedCount, skippedCount, errorCount };
}

/**
 * Step 2: Remove practiceId from locations
 */
async function rollbackLocationFields() {
  console.log('\n=== Rollback: Removing practiceId from Locations ===');

  const locationsSnapshot = await locationsCollection.get();
  console.log(`Found ${locationsSnapshot.size} locations to process`);

  let updatedCount = 0;
  let skippedCount = 0;
  let errorCount = 0;

  for (const locationDoc of locationsSnapshot.docs) {
    const location = locationDoc.data();
    const locationId = locationDoc.id;

    try {
      if (location.hasOwnProperty('practiceId')) {
        await locationDoc.ref.update({
          practiceId: admin.firestore.FieldValue.delete(),
          updatedAt: admin.firestore.Timestamp.now(),
        });
        console.log(`✅ Removed practiceId from location ${locationId} (${location.name})`);
        updatedCount++;
      } else {
        console.log(`⏭️  Location ${locationId} (${location.name}) - no practiceId to remove`);
        skippedCount++;
      }
    } catch (error) {
      console.error(`❌ Error rolling back location ${locationId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 Location Rollback Summary:`);
  console.log(`   Updated: ${updatedCount} locations`);
  console.log(`   Skipped: ${skippedCount} locations (no changes needed)`);
  console.log(`   Errors: ${errorCount} locations`);

  return { updatedCount, skippedCount, errorCount };
}

/**
 * Step 3: Delete created practices
 */
async function rollbackPractices() {
  console.log('\n=== Rollback: Deleting Created Practices ===');

  const practicesSnapshot = await practicesCollection.get();
  console.log(`Found ${practicesSnapshot.size} practices to delete`);

  let deletedCount = 0;
  let errorCount = 0;

  for (const practiceDoc of practicesSnapshot.docs) {
    const practice = practiceDoc.data();
    const practiceId = practiceDoc.id;

    try {
      await practiceDoc.ref.delete();
      console.log(`✅ Deleted practice ${practiceId} (${practice.name})`);
      deletedCount++;
    } catch (error) {
      console.error(`❌ Error deleting practice ${practiceId}:`, error.message);
      errorCount++;
    }
  }

  console.log(`\n📊 Practice Deletion Summary:`);
  console.log(`   Deleted: ${deletedCount} practices`);
  console.log(`   Errors: ${errorCount} practices`);

  return { deletedCount, errorCount };
}

/**
 * Step 4: Validate rollback completion
 */
async function validateRollback() {
  console.log('\n=== Rollback: Validation ===');

  const validation = {
    usersWithNewFields: 0,
    locationsWithPracticeId: 0,
    remainingPractices: 0,
    issues: [],
  };

  // Check users for remaining new fields
  const usersSnapshot = await usersCollection.get();
  for (const userDoc of usersSnapshot.docs) {
    const user = userDoc.data();
    if (user.locationIds || user.currentLocationId || user.practiceIds) {
      validation.usersWithNewFields++;
      validation.issues.push({
        type: 'user',
        id: userDoc.id,
        email: user.email,
        issue: 'Still has new fields',
      });
    }
  }

  // Check locations for remaining practiceId
  const locationsSnapshot = await locationsCollection.get();
  for (const locationDoc of locationsSnapshot.docs) {
    const location = locationDoc.data();
    if (location.practiceId) {
      validation.locationsWithPracticeId++;
      validation.issues.push({
        type: 'location',
        id: locationDoc.id,
        name: location.name,
        issue: 'Still has practiceId',
      });
    }
  }

  // Check for remaining practices
  const practicesSnapshot = await practicesCollection.get();
  validation.remainingPractices = practicesSnapshot.size;
  if (practicesSnapshot.size > 0) {
    practicesSnapshot.docs.forEach(doc => {
      const practice = doc.data();
      validation.issues.push({
        type: 'practice',
        id: doc.id,
        name: practice.name,
        issue: 'Practice still exists',
      });
    });
  }

  console.log(`📊 Rollback Validation Results:`);
  console.log(`   Users with new fields: ${validation.usersWithNewFields}`);
  console.log(`   Locations with practiceId: ${validation.locationsWithPracticeId}`);
  console.log(`   Remaining practices: ${validation.remainingPractices}`);
  console.log(`   Total issues: ${validation.issues.length}`);

  if (validation.issues.length === 0) {
    console.log(`\n✅ Rollback validation passed - all changes successfully reverted!`);
  } else {
    console.log(`\n⚠️  Rollback validation found issues:`);
    validation.issues.forEach(issue => {
      console.log(`   - ${issue.type} ${issue.id}: ${issue.issue}`);
    });
  }

  return validation;
}

/**
 * Main rollback function
 */
async function runPhase4Rollback(options = {}) {
  console.log('🔄 Starting Phase 4 Migration Rollback');
  console.log('=====================================');
  console.log('⚠️  WARNING: This will undo all Phase 4 migration changes!');
  console.log('This will:');
  console.log('  - Remove locationIds, currentLocationId, practiceIds from users');
  console.log('  - Remove practiceId from locations');
  console.log('  - Delete all created practices');
  console.log('=====================================\n');

  if (!options.skipConfirmation) {
    console.log('⏸️  Rollback paused. Run with --confirm to proceed.');
    console.log('   Example: node scripts/phase4-rollback.js --confirm');
    return;
  }

  const startTime = Date.now();

  try {
    // Step 1: Remove user fields
    const userResults = await rollbackUserFields();

    // Step 2: Remove location fields
    const locationResults = await rollbackLocationFields();

    // Step 3: Delete practices
    const practiceResults = await rollbackPractices();

    // Step 4: Validate rollback
    const validation = await validateRollback();

    // Final summary
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);

    console.log('\n🎉 Phase 4 Rollback Complete!');
    console.log('=============================');
    console.log(`📊 Final Summary:`);
    console.log(`   Users Updated: ${userResults.updatedCount}`);
    console.log(`   Locations Updated: ${locationResults.updatedCount}`);
    console.log(`   Practices Deleted: ${practiceResults.deletedCount}`);
    console.log(
      `   Total Errors: ${userResults.errorCount + locationResults.errorCount + practiceResults.errorCount}`,
    );
    console.log(`   Validation Issues: ${validation.issues.length}`);
    console.log(`   Rollback Time: ${duration} seconds`);

    if (validation.issues.length === 0) {
      console.log(
        '\n✅ Rollback completed successfully! Your data has been restored to pre-migration state.',
      );
    } else {
      console.log(
        '\n⚠️  Rollback completed with some issues. Please review the validation results above.',
      );
    }

    // Save rollback report
    const rollbackReport = {
      rollbackDate: new Date().toISOString(),
      duration: duration,
      results: {
        users: userResults,
        locations: locationResults,
        practices: practiceResults,
      },
      validation: validation,
    };

    const reportPath = path.join(__dirname, '../rollback-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(rollbackReport, null, 2));
    console.log(`\n📄 Rollback report saved: ${reportPath}`);
  } catch (error) {
    console.error('💥 Rollback failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const options = {
  skipConfirmation: args.includes('--confirm'),
};

// Run the rollback if this script is executed directly
if (require.main === module) {
  runPhase4Rollback(options)
    .then(() => {
      console.log('\n🏁 Rollback script finished.');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Rollback script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runPhase4Rollback,
  rollbackUserFields,
  rollbackLocationFields,
  rollbackPractices,
  validateRollback,
};
