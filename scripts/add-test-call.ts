/**
 * <PERSON><PERSON><PERSON> to add a test empty call to the database for a specific session ID
 *
 * Usage: node scripts/add-test-call.ts
 */

// Use CommonJS style imports for consistency with other scripts
const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');
const dotenv = require('dotenv');

// Define CallType value - matching the enum in models/CallTypes.ts
const CallType = {
  OTHER: 0,
  VOICEMAIL: 1,
  TRANSFER_TO_HUMAN: 2,
  NEW_PATIENT_NEW_APPOINTMENT: 3,
  NEW_APPOINTMENT_EXISTING_PATIENT: 4,
  RESCHEDULE: 5,
  CANCELLATION: 6,
};

// Load environment variables
dotenv.config();

// Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set
const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    console.error(`Error: Missing required environment variable ${envVar}`);
    process.exit(1);
  }
}

// Initialize Firebase Admin SDK if not already initialized
if (!admin.apps.length) {
  try {
    const serviceAccount = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n') || '',
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });
    console.log('Firebase Admin SDK initialized successfully');
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

// Get Firestore instance
const db = admin.firestore();
console.log('Firestore initialized');

// Collection references
const callSessionsCollection = db.collection('callSessions');
const callsCollection = db.collection('calls');

async function addTestEmptyCall() {
  try {
    const sessionId = '065tlJ8BiLaT4OJGJW8X5J6yA';

    console.log(`Adding test empty call for session ID: ${sessionId}`);

    // Create minimal call data
    const callId = uuidv4();
    const now = admin.firestore.Timestamp.fromDate(new Date());

    const callData = {
      id: callId,
      sessionId,
      phoneNumber: 'Test Phone',
      reason: 'Test empty call',
      notes: '',
      hasVoiceMail: false,
      type: CallType.OTHER,
      date: now,
      clientId: 'unknown',
    };

    // Create call in Firestore
    await callsCollection.doc(callId).set(callData);
    console.log(`Created call with ID: ${callId}`);

    // Check if session exists
    const sessionSnapshot = await callSessionsCollection
      .where('sessionId', '==', sessionId)
      .limit(1)
      .get();

    if (!sessionSnapshot.empty) {
      // Update existing session
      const sessionDoc = sessionSnapshot.docs[0];
      await sessionDoc.ref.update({
        callId: callId,
        updatedAt: now,
      });
      console.log(`Updated existing session ${sessionDoc.id} with callId: ${callId}`);
    } else {
      // Create new session
      const newSessionId = uuidv4();
      await callSessionsCollection.doc(newSessionId).set({
        id: newSessionId,
        sessionId,
        callId,
        callerPhone: 'Test Phone',
        createdAt: now,
        updatedAt: now,
      });
      console.log(`Created new session with ID: ${newSessionId} for sessionId: ${sessionId}`);
    }

    console.log('Done!');

    return callId;
  } catch (error) {
    console.error('Error creating test call:', error);
    throw error;
  }
}

// Run the function
addTestEmptyCall()
  .then(callId => {
    console.log(`Successfully added test call with ID: ${callId}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to add test call:', error);
    process.exit(1);
  });
