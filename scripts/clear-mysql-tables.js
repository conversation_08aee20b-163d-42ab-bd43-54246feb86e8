#!/usr/bin/env node

const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

/**
 * Clear all tables in MySQL database
 * This prevents duplicate data when re-running migrations
 */
class TableCleaner {
  constructor() {
    this.config = {
      host: process.env.MYSQL_HOST,
      port: process.env.MYSQL_PORT || 3306,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: process.env.MYSQL_DATABASE,
    };
  }

  async clearAllTables() {
    console.log('🧹 Starting MySQL table cleanup...');

    try {
      // Create connection
      const connection = await mysql.createConnection(this.config);
      console.log(`✅ Connected to MySQL database: ${this.config.database}`);

      // Disable foreign key checks to allow deletion
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
      console.log('🔓 Disabled foreign key checks');

      // Get all table names
      const [tables] = await connection.execute(
        `
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = ?
        AND table_type = 'BASE TABLE'
      `,
        [this.config.database],
      );

      console.log(`📋 Found ${tables.length} tables to clear`);

      // Clear each table
      for (const table of tables) {
        const tableName = table.table_name || table.TABLE_NAME;

        try {
          const [result] = await connection.execute(`DELETE FROM ${tableName}`);
          const affectedRows = result.affectedRows || 0;
          console.log(`🗑️  Cleared table '${tableName}': ${affectedRows} rows deleted`);
        } catch (error) {
          console.error(`❌ Error clearing table '${tableName}':`, error.message);
        }
      }

      // Re-enable foreign key checks
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
      console.log('🔒 Re-enabled foreign key checks');

      await connection.end();
      console.log('🎉 Database cleanup completed successfully!');
    } catch (error) {
      console.error('💥 Error during database cleanup:', error);
      throw error;
    }
  }
}

// Run the cleanup if called directly
if (require.main === module) {
  const cleaner = new TableCleaner();
  cleaner
    .clearAllTables()
    .then(() => {
      console.log('✨ Ready for fresh migration!');
      process.exit(0);
    })
    .catch(error => {
      console.error('🔥 Cleanup failed:', error);
      process.exit(1);
    });
}

module.exports = TableCleaner;
