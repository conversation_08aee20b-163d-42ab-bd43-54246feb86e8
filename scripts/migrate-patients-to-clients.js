// Script to migrate patients to clients collection
require('dotenv').config({ path: '.env.local' });

// Next.js uses @ alias for imports. In Node.js scripts, we need to use a relative path
// Use the absolute path to the firestore.ts file
const path = require('path');
const firestore = require(path.join(process.cwd(), 'utils', 'firestore'));

async function runMigration() {
  try {
    console.log('Starting migration of patients to clients collection...');
    const result = await firestore.migratePatientToClientCollection();
    console.log(`Migration complete! ${result.migrated} patients migrated to clients collection.`);
  } catch (error) {
    console.error('Error performing migration:', error);
    process.exit(1);
  }
}

runMigration();
