/**
 * <PERSON><PERSON><PERSON> to create agent-location mappings collection in Firestore
 * Based on the existing migration pattern used in other scripts
 * Run with: node scripts/create-agent-location-mappings.js
 */

require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('🚀 Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Firebase Admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

/**
 * Create sample agent-location mappings to establish the collection
 */
async function createAgentLocationMappings() {
  try {
    console.log('🏗️ Creating agent-location-mappings collection...');

    // Create sample mappings to establish the collection structure
    const sampleMappings = [
      {
        agentId: 'sample-agent-downtown',
        locationId: 'sample-location-downtown',
        clinicId: 1,
        isActive: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      {
        agentId: 'sample-agent-northside',
        locationId: 'sample-location-northside',
        clinicId: 1,
        isActive: true,
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
      {
        agentId: 'sample-agent-southside',
        locationId: 'sample-location-southside',
        clinicId: 2,
        isActive: false, // Example of inactive mapping
        createdAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      },
    ];

    let created = 0;
    let skipped = 0;

    for (const mapping of sampleMappings) {
      try {
        // Check if mapping already exists
        const existingDoc = await db
          .collection('agent-location-mappings')
          .doc(mapping.agentId)
          .get();

        if (existingDoc.exists) {
          console.log(`⏭️  Skipping existing mapping: ${mapping.agentId}`);
          skipped++;
          continue;
        }

        // Create the mapping
        await db.collection('agent-location-mappings').doc(mapping.agentId).set(mapping);
        console.log(
          `✅ Created mapping: ${mapping.agentId} -> ${mapping.locationId} (clinic ${mapping.clinicId})`,
        );
        created++;
      } catch (error) {
        console.error(`❌ Failed to create mapping for ${mapping.agentId}:`, error);
      }
    }

    console.log('\n📊 Creation Summary:');
    console.log(`   Created: ${created}`);
    console.log(`   Skipped: ${skipped}`);
    console.log(`   Total:   ${sampleMappings.length}`);

    if (created > 0) {
      console.log('\n🎉 agent-location-mappings collection created successfully!');
      console.log('\nℹ️  Next Steps:');
      console.log('   1. Replace sample mappings with real DialogFlow agent IDs');
      console.log('   2. Update locationId values to match your actual locations');
      console.log('   3. Set correct clinicId values for your organization');
      console.log('   4. Run validation: node scripts/validate-database-integrity.js');
    } else {
      console.log('✅ All mappings already exist, collection is ready');
    }

    return { created, skipped };
  } catch (error) {
    console.error('💥 Error creating agent-location mappings:', error);
    throw error;
  }
}

/**
 * Validate that the collection was created properly
 */
async function validateCollection() {
  try {
    console.log('\n🔍 Validating collection...');

    const snapshot = await db.collection('agent-location-mappings').limit(5).get();

    if (snapshot.empty) {
      console.log('⚠️  Collection is empty');
      return false;
    }

    console.log(`✅ Collection exists with ${snapshot.size} documents`);

    // Show sample document structure
    const firstDoc = snapshot.docs[0];
    const data = firstDoc.data();
    console.log('\n📋 Sample document structure:');
    console.log(`   Document ID: ${firstDoc.id}`);
    console.log(`   Agent ID: ${data.agentId}`);
    console.log(`   Location ID: ${data.locationId}`);
    console.log(`   Clinic ID: ${data.clinicId}`);
    console.log(`   Is Active: ${data.isActive}`);
    console.log(`   Created At: ${data.createdAt ? data.createdAt.toDate() : 'pending'}`);

    return true;
  } catch (error) {
    console.error('❌ Error validating collection:', error);
    return false;
  }
}

/**
 * Main function
 */
async function main() {
  try {
    console.log('🚀 Starting agent-location-mappings collection setup...');

    const result = await createAgentLocationMappings();
    const isValid = await validateCollection();

    if (isValid) {
      console.log('\n🎉 Setup completed successfully!');
      process.exit(0);
    } else {
      console.log('\n⚠️  Setup completed but validation failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Setup failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
