// Using ES module import
import { mapFHIRPractitionerToUser } from '@/lib/external-api/v2/providers/nextech/services';

// Sample FHIR Practitioner response from Nextech API
const samplePractitioner = {
  resourceType: 'Practitioner',
  id: '1',
  identifier: [
    { use: 'official', value: '1' },
    { system: 'http://hl7.org/fhir/sid/us-npi', value: '**********' },
  ],
  active: true,
  name: [{ use: 'official', text: '<PERSON>', family: '<PERSON>', given: ['Jack'] }],
  telecom: [
    { system: 'phone', value: '(*************', use: 'work' },
    { system: 'fax', use: 'work' },
    { system: 'phone', use: 'mobile' },
    { system: 'email', value: '<EMAIL>', use: 'work' },
    { system: 'url', value: 'www.gainesvilleeye.com', use: 'work' },
  ],
  address: [{ use: 'home', type: 'both', country: 'US' }],
};

// Test the mapper function
const mappedUser = mapFHIRPractitionerToUser(samplePractitioner);

// Print the result
console.log('Mapped User:');
console.log(JSON.stringify(mappedUser, null, 2));
