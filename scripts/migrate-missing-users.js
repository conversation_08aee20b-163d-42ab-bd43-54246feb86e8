#!/usr/bin/env node

require('dotenv').config({ path: '.env' });

const admin = require('firebase-admin');
const mysql = require('mysql2/promise');

/**
 * <PERSON><PERSON>t to migrate users from Firestore to MySQL that don't already exist in MySQL
 *
 * This script:
 * 1. Connects to both Firestore and MySQL
 * 2. Retrieves all users from Firestore
 * 3. Checks which users don't exist in MySQL (by email)
 * 4. Migrates only the missing users
 * 5. Provides detailed logging and statistics
 */

class MissingUsersMigrator {
  constructor(options = {}) {
    this.options = {
      dryRun: false,
      batchSize: 50,
      ...options,
    };
    this.db = null;
    this.mysqlConnection = null;
    this.stats = {
      totalFirestoreUsers: 0,
      existingInMySQL: 0,
      toMigrate: 0,
      migrated: 0,
      failed: 0,
      skipped: 0,
      errors: [],
    };
  }

  /**
   * Generate a new UUID v4
   */
  generateUUID() {
    return require('crypto').randomUUID();
  }

  /**
   * Initialize Firebase and MySQL connections
   */
  async initialize() {
    console.log('🚀 Initializing Missing Users Migrator...');

    // Initialize Firebase
    if (!admin.apps.length) {
      const firebaseConfig = {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      };

      if (!firebaseConfig.projectId || !firebaseConfig.clientEmail || !firebaseConfig.privateKey) {
        throw new Error(
          'Missing Firebase configuration. Please check FIREBASE_* environment variables.',
        );
      }

      admin.initializeApp({
        credential: admin.credential.cert(firebaseConfig),
      });
    }

    this.db = admin.firestore();
    console.log('✅ Firebase initialized');

    // Initialize MySQL connection
    const mysqlConfig = {
      host: process.env.MYSQL_HOST || process.env.DB_HOST,
      port: parseInt(process.env.MYSQL_PORT || process.env.DB_PORT || '3306'),
      user: process.env.MYSQL_USER || process.env.DB_USER,
      password: process.env.MYSQL_PASSWORD || process.env.DB_PWD,
      database: process.env.MYSQL_DATABASE || process.env.DB_NAME,
      connectionLimit: 10,
      timezone: 'Z',
    };

    // Add socket path if available (for Cloud SQL)
    const instanceConnectionName =
      process.env.INSTANCE_CONNECTION_NAME || process.env.CLOUD_SQL_CONNECTION_NAME;
    if (process.env.DB_SOCKET_PATH) {
      mysqlConfig.socketPath = process.env.DB_SOCKET_PATH;
    } else if (instanceConnectionName && process.env.NODE_ENV === 'production') {
      mysqlConfig.socketPath = `/cloudsql/${instanceConnectionName}`;
    }

    this.mysqlConnection = await mysql.createConnection(mysqlConfig);
    console.log('✅ MySQL connected');
  }

  /**
   * Get all existing user emails from MySQL
   */
  async getExistingMySQLUserEmails() {
    console.log('📊 Fetching existing users from MySQL...');

    const [rows] = await this.mysqlConnection.execute('SELECT email FROM users');

    const existingEmails = new Set(rows.map(row => row.email.toLowerCase()));
    console.log(`📧 Found ${existingEmails.size} existing users in MySQL`);

    return existingEmails;
  }

  /**
   * Get all users from Firestore
   */
  async getFirestoreUsers() {
    console.log('🔥 Fetching all users from Firestore...');

    const usersSnapshot = await this.db.collection('users').get();
    const users = [];

    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      users.push({
        id: doc.id,
        ...userData,
      });
    });

    console.log(`👥 Found ${users.length} users in Firestore`);
    return users;
  }

  /**
   * Transform Firestore user data to MySQL format
   */
  transformUserForMySQL(firestoreUser) {
    // Handle timestamps
    const createdAt = this.parseTimestamp(firestoreUser.createdAt);
    const updatedAt = this.parseTimestamp(firestoreUser.updatedAt);

    // Provide sensible defaults for missing fields
    const defaults = {
      canTakeAppointments: false, // Default to false if not specified
      role: 'STAFF', // Default role if missing
      locationIds: [],
      practiceIds: [],
    };

    // Log if we're applying defaults for debugging
    const appliedDefaults = [];
    if (firestoreUser.canTakeAppointments === undefined || firestoreUser.canTakeAppointments === null) {
      appliedDefaults.push(`canTakeAppointments -> ${defaults.canTakeAppointments}`);
    }
    if (!firestoreUser.role) {
      appliedDefaults.push(`role -> ${defaults.role}`);
    }

    if (appliedDefaults.length > 0) {
      console.log(`  📝 Applying defaults for ${firestoreUser.email}: ${appliedDefaults.join(', ')}`);
    }

    return {
      id: this.generateUUID(), // Generate new UUID for primary key
      firebase_uid: firestoreUser.id, // Store original Firestore document ID
      email: firestoreUser.email,
      phone: firestoreUser.phone || null,
      name: firestoreUser.name,
      role: firestoreUser.role || defaults.role,
      specialty: firestoreUser.specialty || null,
      clinic_id: firestoreUser.clinicId || null,
      profile_picture: firestoreUser.profilePicture || null,
      can_take_appointments: firestoreUser.canTakeAppointments !== undefined 
          ? (firestoreUser.canTakeAppointments ? 1 : 0) 
          : (defaults.canTakeAppointments ? 1 : 0),
      location_ids: firestoreUser.locationIds 
          ? JSON.stringify(firestoreUser.locationIds) 
          : JSON.stringify(defaults.locationIds),
      current_location_id: firestoreUser.currentLocationId || null,
      practice_ids: firestoreUser.practiceIds 
          ? JSON.stringify(firestoreUser.practiceIds) 
          : JSON.stringify(defaults.practiceIds),
      preferences: firestoreUser.preferences ? JSON.stringify(firestoreUser.preferences) : null,
      created_at: createdAt,
      updated_at: updatedAt,
    };
  }

  /**
   * Parse Firestore timestamp to MySQL datetime
   */
  parseTimestamp(timestamp) {
    if (!timestamp) {
      return new Date();
    }

    if (timestamp._seconds) {
      return new Date(timestamp._seconds * 1000);
    }

    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    if (timestamp instanceof Date) {
      return timestamp;
    }

    if (typeof timestamp === 'string') {
      return new Date(timestamp);
    }

    return new Date();
  }

  /**
   * Validate user data before migration
   */
  validateUser(user) {
    const errors = [];

    if (!user.email?.trim()) {
      errors.push('Email is required');
    }

    if (!user.name?.trim()) {
      errors.push('Name is required');
    }

    if (!user.role) {
      errors.push('Role is required');
    } else {
      const validRoles = ['SUPER_ADMIN', 'CLINIC_ADMIN', 'STAFF', 'GUEST'];
      if (!validRoles.includes(user.role)) {
        errors.push(`Invalid role: ${user.role}. Valid roles are: ${validRoles.join(', ')}`);
      }
    }

    // After transformation, the field is called can_take_appointments (not camelCase)
    // and we provide defaults, so this should always be set
    if (user.can_take_appointments === undefined || user.can_take_appointments === null) {
      errors.push('can_take_appointments flag is required after transformation');
    }

    return errors;
  }

  /**
   * Insert user into MySQL
   */
  async insertUserIntoMySQL(user) {
    const transformedUser = this.transformUserForMySQL(user);

    // Validate the user data
    const validationErrors = this.validateUser(transformedUser);
    if (validationErrors.length > 0) {
      throw new Error(`Validation failed: ${validationErrors.join(', ')}`);
    }

    if (this.options.dryRun) {
      console.log(`[DRY RUN] Would insert user: ${transformedUser.email}`);
      return;
    }

    const insertQuery = `
      INSERT INTO users (
        id, firebase_uid, email, phone, name, role, specialty, clinic_id, 
        profile_picture, can_take_appointments, location_ids, 
        current_location_id, practice_ids, preferences, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      transformedUser.id,
      transformedUser.firebase_uid,
      transformedUser.email,
      transformedUser.phone,
      transformedUser.name,
      transformedUser.role,
      transformedUser.specialty,
      transformedUser.clinic_id,
      transformedUser.profile_picture,
      transformedUser.can_take_appointments,
      transformedUser.location_ids,
      transformedUser.current_location_id,
      transformedUser.practice_ids,
      transformedUser.preferences,
      transformedUser.created_at,
      transformedUser.updated_at,
    ];

    await this.mysqlConnection.execute(insertQuery, values);
  }

  /**
   * Run the migration process
   */
  async migrate() {
    try {
      await this.initialize();

      console.log('\n🔍 Starting missing users analysis...');

      // Get data from both sources
      const [firestoreUsers, existingEmails] = await Promise.all([
        this.getFirestoreUsers(),
        this.getExistingMySQLUserEmails(),
      ]);

      this.stats.totalFirestoreUsers = firestoreUsers.length;
      this.stats.existingInMySQL = existingEmails.size;

      // Find users that don't exist in MySQL
      const missingUsers = firestoreUsers.filter(user => {
        if (!user.email) {
          console.warn(`⚠️  User ${user.id} has no email, skipping`);
          this.stats.skipped++;
          return false;
        }
        return !existingEmails.has(user.email.toLowerCase());
      });

      this.stats.toMigrate = missingUsers.length;

      console.log('\n📊 Migration Analysis:');
      console.log(`📈 Total Firestore users: ${this.stats.totalFirestoreUsers}`);
      console.log(`✅ Already in MySQL: ${this.stats.existingInMySQL}`);
      console.log(`🔄 To migrate: ${this.stats.toMigrate}`);
      console.log(`⏭️  Skipped (no email): ${this.stats.skipped}`);

      if (this.stats.toMigrate === 0) {
        console.log('\n🎉 No missing users found! All users are already in MySQL.');
        return;
      }

      if (this.options.dryRun) {
        console.log('\n🔍 DRY RUN MODE - Showing users that would be migrated:');
        missingUsers.forEach((user, index) => {
          console.log(`${index + 1}. ${user.email} (${user.name}) - Role: ${user.role}`);
        });
        return;
      }

      console.log('\n🚀 Starting migration of missing users...');

      // Migrate users in batches
      for (let i = 0; i < missingUsers.length; i += this.options.batchSize) {
        const batch = missingUsers.slice(i, i + this.options.batchSize);

        console.log(
          `\n📦 Processing batch ${Math.floor(i / this.options.batchSize) + 1}/${Math.ceil(
            missingUsers.length / this.options.batchSize,
          )}`,
        );

        for (const user of batch) {
          try {
            await this.insertUserIntoMySQL(user);
            this.stats.migrated++;
            console.log(`✅ Migrated: ${user.email} (${user.name})`);
          } catch (error) {
            this.stats.failed++;
            const errorMsg = `Failed to migrate ${user.email}: ${error.message}`;
            this.stats.errors.push(errorMsg);
            console.error(`❌ ${errorMsg}`);
          }
        }

        // Progress update
        console.log(
          `📈 Progress: ${this.stats.migrated + this.stats.failed}/${this.stats.toMigrate} processed`,
        );
      }

      // Final statistics
      console.log('\n🎉 Migration Complete!');
      console.log(`✅ Successfully migrated: ${this.stats.migrated}`);
      console.log(`❌ Failed: ${this.stats.failed}`);

      if (this.stats.errors.length > 0) {
        console.log('\n❌ Errors encountered:');
        this.stats.errors.forEach(error => console.log(`   - ${error}`));
      }
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    } finally {
      // Cleanup connections
      if (this.mysqlConnection) {
        await this.mysqlConnection.end();
        console.log('🔌 MySQL connection closed');
      }

      if (admin.apps.length > 0) {
        await admin.app().delete();
        console.log('🔌 Firebase connection closed');
      }
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]) || 50;
        break;
      case '--help':
        console.log(`
Missing Users Migration Tool

Usage: node scripts/migrate-missing-users.js [options]

Options:
  --dry-run                Show what would be migrated without making changes
  --batch-size <number>    Number of users per batch (default: 50)
  --help                   Show this help message

Examples:
  node scripts/migrate-missing-users.js --dry-run
  node scripts/migrate-missing-users.js --batch-size 25
`);
        process.exit(0);
      default:
        if (arg.startsWith('--')) {
          console.warn(`Unknown option: ${arg}`);
        }
        break;
    }
  }

  // Create and run migrator
  const migrator = new MissingUsersMigrator(options);
  await migrator.migrate();
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => {
      console.log('🎯 Migration process completed successfully');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Migration process failed:', error);
      process.exit(1);
    });
}

module.exports = { MissingUsersMigrator };
