/**
 * <PERSON>ript to update User model with canTakeAppointments flag and
 * create test calendar slots for users who can take appointments
 * Run with: node scripts/update-users-calendar-slots.js
 */

const admin = require('firebase-admin');
const dotenv = require('dotenv');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Firebase Admin SDK
const serviceAccount = {
  projectId: process.env.FIREBASE_PROJECT_ID,
  clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
  privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
};

if (!serviceAccount.projectId || !serviceAccount.clientEmail || !serviceAccount.privateKey) {
  console.error('Missing Firebase admin credentials in environment variables');
  process.exit(1);
}

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

const db = admin.firestore();
const usersCollection = db.collection('users');
const calendarSlotsCollection = db.collection('availableCalendarSlots');

// Helper function to create default time slots (30-minute intervals)
function createDefaultTimeSlots(startHour = 9, endHour = 17) {
  const slots = [];

  for (let hour = startHour; hour < endHour; hour++) {
    for (const minutes of ['00', '30']) {
      slots.push({
        id: uuidv4(),
        time: `${hour.toString().padStart(2, '0')}:${minutes}`,
        isAvailable: true,
      });
    }
  }

  return slots;
}

// Generate dates for the next 30 days
function generateDatesForNextDays(numDays = 30) {
  const dates = [];
  const today = new Date();

  for (let i = 0; i < numDays; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    // Skip weekends (Saturday and Sunday)
    if (date.getDay() !== 0 && date.getDay() !== 6) {
      dates.push(date.toISOString().split('T')[0]); // Format as YYYY-MM-DD
    }
  }

  return dates;
}

async function updateUsersAndCreateCalendarSlots() {
  try {
    console.log('Starting user updates and calendar slot generation...');

    // Get all users
    const usersSnapshot = await usersCollection.get();
    console.log(`Found ${usersSnapshot.size} users in Firestore`);

    let updatedCount = 0;
    let slotsCreatedCount = 0;
    const usersWithAppointments = [];

    // Process each user
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();
      const userId = userDoc.id;

      // Determine if user can take appointments (assuming STAFF role members with specialty can take appointments)
      const canTakeAppointments = !!(
        userData.role === 'STAFF' &&
        userData.specialty &&
        userData.specialty.trim() !== ''
      );

      // Update user with new flag
      await usersCollection.doc(userId).update({
        canTakeAppointments: canTakeAppointments,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      updatedCount++;
      console.log(
        `Updated user ${userData.email} (${userId}): canTakeAppointments=${canTakeAppointments}`,
      );

      // If user can take appointments, save for calendar slot generation
      if (canTakeAppointments) {
        usersWithAppointments.push({
          id: userId,
          name: userData.name,
          specialty: userData.specialty,
        });
      }
    }

    // Generate calendar slots for users who can take appointments
    const dates = generateDatesForNextDays(30);

    for (const user of usersWithAppointments) {
      for (const date of dates) {
        const calendarSlot = {
          id: uuidv4(),
          userId: user.id,
          date,
          timeSlots: createDefaultTimeSlots(),
        };

        await calendarSlotsCollection.doc(calendarSlot.id).set(calendarSlot);
        slotsCreatedCount++;

        // Log progress every 10 slots
        if (slotsCreatedCount % 10 === 0) {
          console.log(`Created ${slotsCreatedCount} calendar slots...`);
        }
      }

      console.log(`Created calendar slots for user ${user.name} (${user.id})`);
    }

    console.log('\nSummary:');
    console.log(`- Updated: ${updatedCount} users`);
    console.log(`- Users who can take appointments: ${usersWithAppointments.length}`);
    console.log(`- Calendar slots created: ${slotsCreatedCount}`);
  } catch (error) {
    console.error('Error updating users and creating calendar slots:', error);
    throw error;
  }
}

// Run the update function
updateUsersAndCreateCalendarSlots()
  .then(() => {
    console.log('Update process complete');
    process.exit(0);
  })
  .catch(error => {
    console.error('Update process failed:', error);
    process.exit(1);
  });
