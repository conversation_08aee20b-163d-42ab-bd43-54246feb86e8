require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function checkCollection(collectionName, limit = 1) {
  try {
    console.log(`\n🔍 ${collectionName} collection:`);

    const snapshot = await db.collection(collectionName).limit(limit).get();

    console.log(`  📊 Found ${snapshot.size} documents`);

    if (snapshot.size > 0) {
      const doc = snapshot.docs[0];
      const data = doc.data();
      console.log(`  📋 Fields:`);
      Object.keys(data).forEach(key => {
        const value = data[key];
        let type = typeof value;

        if (value && value.toDate && typeof value.toDate === 'function') {
          type = 'timestamp';
        } else if (Array.isArray(value)) {
          type = 'array';
        } else if (type === 'object' && value !== null) {
          type = 'object';
        }

        console.log(`    - ${key} (${type})`);
      });
    }

    return snapshot.size;
  } catch (error) {
    console.error(`❌ Error checking ${collectionName}:`, error.message);
    return 0;
  }
}

async function main() {
  const collections = [
    'users',
    'locations',
    'clients',
    'callSessions',
    'calls',
    'availableCalendarSlots',
    'appointments',
    'emails',
    'otp',
  ];

  console.log('🚀 Checking Firestore collection schemas...\n');

  for (const collection of collections) {
    await checkCollection(collection);
  }

  console.log('\n✅ Analysis complete!');
  process.exit(0);
}

main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
