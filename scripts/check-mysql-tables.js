require('dotenv').config({ path: '.env.local' });

const mysql = require('mysql2/promise');

async function checkMySQLTables() {
  let connection;

  try {
    console.log('🔍 Connecting to MySQL database...');

    connection = await mysql.createConnection({
      host: process.env.MYSQL_HOST || process.env.DB_HOST,
      port: parseInt(process.env.MYSQL_PORT || process.env.DB_PORT || '3306'),
      user: process.env.MYSQL_USER || process.env.DB_USERNAME,
      password: process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD,
      database: process.env.MYSQL_DATABASE || process.env.DB_NAME,
      ssl: process.env.MYSQL_SSL === 'true' ? {} : false,
    });

    console.log('✅ Connected to MySQL database');

    // Get list of tables
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`\n📋 Found ${tables.length} tables in database:`);

    // Check row count for each table
    for (const tableRow of tables) {
      const tableName = Object.values(tableRow)[0];
      try {
        const [countResult] = await connection.execute(
          `SELECT COUNT(*) as count FROM \`${tableName}\``,
        );
        const count = countResult[0].count;
        console.log(`  📊 ${tableName}: ${count} rows`);
      } catch (error) {
        console.log(`  ❌ ${tableName}: Error counting rows - ${error.message}`);
      }
    }

    // Specifically check our key tables
    const keyTables = ['users', 'clients', 'calls', 'call_sessions', 'locations'];
    console.log('\n🔍 Key Migration Tables:');

    for (const table of keyTables) {
      try {
        const [countResult] = await connection.execute(
          `SELECT COUNT(*) as count FROM \`${table}\``,
        );
        const count = countResult[0].count;
        console.log(`  📊 ${table}: ${count} rows`);
      } catch (error) {
        console.log(`  ❌ ${table}: Table doesn't exist or error - ${error.message}`);
      }
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 MySQL connection closed');
    }
  }
}

if (require.main === module) {
  checkMySQLTables().catch(console.error);
}

module.exports = { checkMySQLTables };
