/**
 * This script sends password reset emails to existing users in Firebase Authentication
 * Run with: node scripts/reset-user-passwords.js [email1,email2,...]
 *
 * If no emails are provided, it will send reset emails to all users in Firestore
 */

const admin = require('firebase-admin');
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function resetUserPasswords() {
  try {
    // Check if specific emails were provided
    const providedEmails = process.argv[2] ? process.argv[2].split(',') : null;

    if (providedEmails) {
      console.log(`Sending password reset emails to ${providedEmails.length} specified users...`);
    } else {
      console.log('Sending password reset emails to all users in Firestore...');
    }

    // Get users from Firestore
    let usersQuery = db.collection('users');
    if (providedEmails) {
      // If emails are provided, only get those users
      usersQuery = usersQuery.where('email', 'in', providedEmails);
    }

    const usersSnapshot = await usersQuery.get();
    console.log(`Found ${usersSnapshot.size} users to process`);

    let successCount = 0;
    let errorCount = 0;

    // Process each user
    for (const userDoc of usersSnapshot.docs) {
      const userData = userDoc.data();

      try {
        // Generate password reset link
        const link = await admin.auth().generatePasswordResetLink(userData.email);

        // In a real app, you would send this link via email
        // For demo purposes, we'll just log it
        console.log(`Password reset link for ${userData.email}: ${link}`);

        // Here you would integrate with your email service
        // Example with nodemailer (you would need to install it):
        /*
        const transporter = nodemailer.createTransport({
          service: 'gmail',
          auth: {
            user: process.env.EMAIL_USER,
            pass: process.env.EMAIL_PASS
          }
        });
        
        await transporter.sendMail({
          from: '"Plato Health" <<EMAIL>>',
          to: userData.email,
          subject: 'Reset Your Plato Health Password',
          html: `
            <h1>Plato Health Password Reset</h1>
            <p>Hello ${userData.name},</p>
            <p>You have been invited to use the Plato Health Front Desk Portal.</p>
            <p>Please click the link below to set your password:</p>
            <p><a href="${link}">Reset Password</a></p>
            <p>This link will expire in 1 hour.</p>
          `
        });
        */

        successCount++;
      } catch (error) {
        console.error(`Error processing user ${userData.email}:`, error.message);
        errorCount++;
      }
    }

    console.log('\nSummary:');
    console.log(`- Success: ${successCount} users`);
    console.log(`- Errors: ${errorCount} users`);

    if (successCount > 0) {
      console.log(
        '\nNOTE: In a production environment, integrate with an email service to send these links to users.',
      );
      console.log('The password reset links are valid for a limited time only.');
    }
  } catch (error) {
    console.error('Error in resetUserPasswords:', error);
    process.exit(1);
  }
}

// Run the function
resetUserPasswords()
  .then(() => {
    console.log('Completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to reset user passwords:', error);
    process.exit(1);
  });
