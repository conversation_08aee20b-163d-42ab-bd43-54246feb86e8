/**
 * Migration script to add 'externalId' field while keeping 'providerId' in appointmentReferences collection
 *
 * This script performs the following:
 * 1. Adds 'externalId' field to all appointment references that have 'providerId' but not 'externalId'
 * 2. Sets 'externalId' to the same value as 'providerId' for backward compatibility
 * 3. Keeps the 'providerId' field intact
 *
 * Usage:
 * 1. Make sure you have the necessary Firebase admin credentials in .env.local or serviceAccountKey.json
 * 2. Run: node scripts/migrate-providerId-to-externalId.js
 */

require('dotenv').config({ path: '.env.local' });
const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    // First try to use environment variables
    if (
      process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_CLIENT_EMAIL &&
      process.env.FIREBASE_PRIVATE_KEY
    ) {
      admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        }),
      });
      console.log('Firebase initialized using environment variables');
    }
    // Fall back to service account file
    else {
      const serviceAccountPath = path.join(process.cwd(), 'serviceAccountKey.json');
      if (fs.existsSync(serviceAccountPath)) {
        const serviceAccount = require(serviceAccountPath);
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
        console.log('Firebase initialized using serviceAccountKey.json');
      } else {
        throw new Error(
          'No Firebase credentials found. Please set environment variables or provide serviceAccountKey.json',
        );
      }
    }
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();
const appointmentReferencesCollection = db.collection('appointmentReferences');

// Maximum batch size for Firestore
const MAX_BATCH_SIZE = 500;

/**
 * Add externalId field to appointment references
 */
async function migrateProviderIdToExternalId() {
  console.log('=== Starting migration: Adding externalId field to appointment references ===');

  try {
    // Get all appointment references
    const snapshot = await appointmentReferencesCollection.get();

    if (snapshot.empty) {
      console.log('No appointment references found. Nothing to migrate.');
      return;
    }

    console.log(`Found ${snapshot.size} appointment references to process.`);

    let batch = db.batch();
    let batchCount = 0;
    let totalProcessed = 0;
    let totalMigrated = 0;
    let alreadyMigrated = 0;

    for (const doc of snapshot.docs) {
      const data = doc.data();
      totalProcessed++;

      // Only process documents that have providerId but not externalId
      if (data.providerId && !data.externalId) {
        // Create an update object with the new field
        const updateData = {
          externalId: data.providerId,
          updatedAt: new Date(),
        };

        // Add to batch
        batch.update(appointmentReferencesCollection.doc(doc.id), updateData);
        batchCount++;
        totalMigrated++;

        // If we've reached the batch limit, commit and start a new batch
        if (batchCount >= MAX_BATCH_SIZE) {
          await batch.commit();
          console.log(`Committed batch of ${batchCount} updates.`);
          batch = db.batch();
          batchCount = 0;
        }
      } else if (data.providerId && data.externalId) {
        alreadyMigrated++;
      }
    }

    // Commit any remaining updates
    if (batchCount > 0) {
      await batch.commit();
      console.log(`Committed final batch of ${batchCount} updates.`);
    }

    console.log(`\nMigration Summary:`);
    console.log(`- Total processed: ${totalProcessed} documents`);
    console.log(`- Already migrated: ${alreadyMigrated} documents`);
    console.log(`- Newly migrated: ${totalMigrated} documents`);
    console.log(`- Total with externalId: ${alreadyMigrated + totalMigrated} documents`);
  } catch (error) {
    console.error('Error during migration:', error);
    process.exit(1);
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('Starting migration process...');

  try {
    // Add externalId field to appointment references
    await migrateProviderIdToExternalId();

    console.log('\nMigration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
