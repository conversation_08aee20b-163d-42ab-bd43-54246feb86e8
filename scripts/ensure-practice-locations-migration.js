#!/usr/bin/env node

// Load environment variables
require('dotenv').config({ path: '.env.local' });

/**
 * Practice Locations Migration Script
 *
 * This script ensures:
 * 1. All practices have at least one location
 * 2. Admin users have access to all locations in their clinic
 * 3. Locations are properly associated with practices
 */

const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
if (!admin.apps.length) {
  // Check for required environment variables
  const requiredEnvVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_CLIENT_EMAIL', 'FIREBASE_PRIVATE_KEY'];
  const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

  if (missingEnvVars.length > 0) {
    console.error('❌ Missing required environment variables:');
    missingEnvVars.forEach(envVar => console.error(`   - ${envVar}`));
    console.error(
      '💡 Make sure FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL, and FIREBASE_PRIVATE_KEY are set in .env.local',
    );
    process.exit(1);
  }

  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('❌ Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

// Collection references
const clinicsCollection = db.collection('clinics');
const practicesCollection = db.collection('practices');
const locationsCollection = db.collection('locations');
const usersCollection = db.collection('users');

/**
 * Step 1: Ensure all practices have at least one location
 */
async function ensurePracticesHaveLocations() {
  console.log('=== Step 1: Ensuring All Practices Have Locations ===');

  const practicesSnapshot = await practicesCollection.where('isActive', '==', true).get();
  console.log(`Found ${practicesSnapshot.size} active practices`);

  let practicesWithLocations = 0;
  let practicesNeedingLocations = 0;
  let locationsCreated = 0;

  for (const practiceDoc of practicesSnapshot.docs) {
    const practice = practiceDoc.data();
    const practiceId = practice.id || practiceDoc.id;

    // Check if practice has any locations
    const locationsSnapshot = await locationsCollection
      .where('practiceId', '==', practiceId)
      .where('isActive', '==', true)
      .get();

    if (locationsSnapshot.empty) {
      console.log(`📍 Practice "${practice.name}" (${practiceId}) has no locations`);
      practicesNeedingLocations++;

      // Create a default location for this practice
      try {
        const locationId = uuidv4();
        const now = admin.firestore.Timestamp.now();

        const locationData = {
          id: locationId,
          clinicId: practice.clinicId,
          practiceId: practiceId,
          name: `${practice.name} - Main Location`,
          address: 'Address to be updated',
          officeHours: {
            monday: { start: '09:00', end: '17:00' },
            tuesday: { start: '09:00', end: '17:00' },
            wednesday: { start: '09:00', end: '17:00' },
            thursday: { start: '09:00', end: '17:00' },
            friday: { start: '09:00', end: '17:00' },
            saturday: null,
            sunday: null,
          },
          isActive: true,
          createdAt: now,
          updatedAt: now,
        };

        await locationsCollection.doc(locationId).set(locationData);
        locationsCreated++;

        console.log(`   ✅ Created default location: "${locationData.name}"`);
        console.log(`      Location ID: ${locationId}`);
      } catch (error) {
        console.error(
          `   ❌ Error creating location for practice "${practice.name}":`,
          error.message,
        );
      }
    } else {
      console.log(`✅ Practice "${practice.name}" has ${locationsSnapshot.size} location(s)`);
      practicesWithLocations++;
    }
  }

  console.log(`\n📊 Practices Summary:`);
  console.log(`   - Practices with locations: ${practicesWithLocations}`);
  console.log(`   - Practices needing locations: ${practicesNeedingLocations}`);
  console.log(`   - Locations created: ${locationsCreated}`);

  return { practicesWithLocations, practicesNeedingLocations, locationsCreated };
}

/**
 * Step 2: Update admin users to have access to all locations in their clinic
 */
async function updateAdminUserAccess() {
  console.log('\n=== Step 2: Updating Admin User Access ===');

  // Get all admin users
  const adminUsersSnapshot = await usersCollection
    .where('role', 'in', ['CLINIC_ADMIN', 'SUPER_ADMIN'])
    .get();

  console.log(`Found ${adminUsersSnapshot.size} admin users`);

  let adminUsersUpdated = 0;
  let adminUsersSkipped = 0;

  for (const userDoc of adminUsersSnapshot.docs) {
    const user = userDoc.data();
    const userId = user.id || userDoc.id;

    if (!user.clinicId) {
      console.log(`⚠️  Skipping admin user ${user.name} (${userId}) - no clinic ID`);
      adminUsersSkipped++;
      continue;
    }

    // Get all active locations for this clinic
    const clinicLocationsSnapshot = await locationsCollection
      .where('clinicId', '==', user.clinicId)
      .where('isActive', '==', true)
      .get();

    const allLocationIds = clinicLocationsSnapshot.docs.map(doc => doc.id);
    const currentLocationIds = user.locationIds || [];

    // Check if user already has access to all locations
    const hasAllLocations = allLocationIds.every(locationId =>
      currentLocationIds.includes(locationId),
    );

    if (hasAllLocations && currentLocationIds.length === allLocationIds.length) {
      console.log(
        `✅ Admin user ${user.name} already has access to all ${allLocationIds.length} locations`,
      );
      adminUsersSkipped++;
      continue;
    }

    // Update user to have access to all locations
    try {
      const updateData = {
        locationIds: allLocationIds,
        updatedAt: admin.firestore.Timestamp.now(),
      };

      // If user has no current location set, set the first one as current
      if (!user.currentLocationId && allLocationIds.length > 0) {
        updateData.currentLocationId = allLocationIds[0];
        console.log(`   🎯 Setting current location for ${user.name}: ${allLocationIds[0]}`);
      }

      await usersCollection.doc(userId).update(updateData);
      adminUsersUpdated++;

      console.log(`✅ Updated admin user ${user.name} (${user.role})`);
      console.log(`   - Locations access: ${currentLocationIds.length} → ${allLocationIds.length}`);
    } catch (error) {
      console.error(`❌ Error updating admin user ${user.name}:`, error.message);
    }
  }

  console.log(`\n📊 Admin Users Summary:`);
  console.log(`   - Admin users updated: ${adminUsersUpdated}`);
  console.log(`   - Admin users skipped: ${adminUsersSkipped}`);

  return { adminUsersUpdated, adminUsersSkipped };
}

/**
 * Step 3: Validate data consistency
 */
async function validateDataConsistency() {
  console.log('\n=== Step 3: Validating Data Consistency ===');

  // Check for practices without locations
  const practicesSnapshot = await practicesCollection.where('isActive', '==', true).get();
  let practicesWithoutLocations = 0;

  for (const practiceDoc of practicesSnapshot.docs) {
    const practice = practiceDoc.data();
    const practiceId = practice.id || practiceDoc.id;

    const locationsSnapshot = await locationsCollection
      .where('practiceId', '==', practiceId)
      .where('isActive', '==', true)
      .get();

    if (locationsSnapshot.empty) {
      console.log(`⚠️  Practice "${practice.name}" still has no locations`);
      practicesWithoutLocations++;
    }
  }

  // Check for admin users without full location access
  const adminUsersSnapshot = await usersCollection
    .where('role', 'in', ['CLINIC_ADMIN', 'SUPER_ADMIN'])
    .get();

  let adminUsersWithLimitedAccess = 0;

  for (const userDoc of adminUsersSnapshot.docs) {
    const user = userDoc.data();

    if (!user.clinicId) continue;

    const clinicLocationsSnapshot = await locationsCollection
      .where('clinicId', '==', user.clinicId)
      .where('isActive', '==', true)
      .get();

    const allLocationIds = clinicLocationsSnapshot.docs.map(doc => doc.id);
    const userLocationIds = user.locationIds || [];

    if (userLocationIds.length < allLocationIds.length) {
      console.log(
        `⚠️  Admin user ${user.name} has limited access: ${userLocationIds.length}/${allLocationIds.length} locations`,
      );
      adminUsersWithLimitedAccess++;
    }
  }

  console.log(`\n📊 Validation Summary:`);
  console.log(`   - Practices without locations: ${practicesWithoutLocations}`);
  console.log(`   - Admin users with limited access: ${adminUsersWithLimitedAccess}`);

  return {
    practicesWithoutLocations,
    adminUsersWithLimitedAccess,
  };
}

/**
 * Main migration function
 */
async function runMigration() {
  console.log('🚀 Starting Practice Locations Migration...\n');

  try {
    // Step 1: Ensure practices have locations
    const step1Results = await ensurePracticesHaveLocations();

    // Step 2: Update admin user access
    const step2Results = await updateAdminUserAccess();

    // Step 3: Validate consistency
    const validationResults = await validateDataConsistency();

    // Final summary
    console.log('\n=== Migration Complete ===');
    console.log('📊 Final Summary:');
    console.log(`   - Locations created: ${step1Results.locationsCreated}`);
    console.log(`   - Admin users updated: ${step2Results.adminUsersUpdated}`);
    console.log(`   - Practices without locations: ${validationResults.practicesWithoutLocations}`);
    console.log(
      `   - Admin users with limited access: ${validationResults.adminUsersWithLimitedAccess}`,
    );

    if (
      validationResults.practicesWithoutLocations === 0 &&
      validationResults.adminUsersWithLimitedAccess === 0
    ) {
      console.log(
        '\n✅ Migration completed successfully! All practices have locations and admin users have full access.',
      );
    } else {
      console.log('\n⚠️  Migration completed with warnings. Please review the issues above.');
    }
  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    throw error;
  }
}

// Run the migration if called directly
if (require.main === module) {
  runMigration()
    .then(() => {
      console.log('\n🎉 Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runMigration,
  ensurePracticesHaveLocations,
  updateAdminUserAccess,
  validateDataConsistency,
};
