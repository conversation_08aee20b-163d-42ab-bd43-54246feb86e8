require('dotenv').config({ path: '.env.local' });

const admin = require('firebase-admin');
const mysql = require('mysql2/promise');

// Collection migration order (respecting foreign-key / logical dependencies)
// Note: callDetails is merged into calls table during calls migration
const MIGRATION_ORDER = [
  // Core reference entities
  'practices',
  'clinics',
  'users',
  'locations',
  'clients',
  'patients',

  // Operational data
  'callSessions',
  'calls', // callDetails merged here

  // Scheduling & availability
  'availableCalendarSlots',
  'appointments',
  'on-call-schedules',

  // Notifications related to on-call logic
  'on-call-notifications',

  // Mappings & references
  'agent-location-mappings',
  'staffInviteCodes',
  'patientReferences',
  'appointmentReferences',

  // Misc/supporting collections
  'emails',
  'email-templates',
  'otp',
];

// Default configuration
const DEFAULT_CONFIG = {
  dryRun: false,
  validateOnly: false,
  batchSize: 100,
  collections: MIGRATION_ORDER,
  resumeFromCollection: null,
  autoDiscoverCollections: true,
  callsLimit: 1600, // Limit calls to prevent overload
  noBackup: false,
};

class FirestoreToMySQLMigrator {
  constructor(config = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.db = null;
    this.mysqlConnection = null;
    this.stats = {
      totalDocuments: 0,
      migratedDocuments: 0,
      failedDocuments: 0,
      skippedDocuments: 0,
      startTime: new Date(),
      collectionStats: {},
    };
  }

  /**
   * Initialize Firebase and MySQL connections
   */
  async initialize() {
    console.log('🚀 Initializing Firestore to MySQL Migrator...');

    // Initialize Firebase
    if (!admin.apps.length) {
      const firebaseConfig = {
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      };

      if (!firebaseConfig.projectId || !firebaseConfig.clientEmail || !firebaseConfig.privateKey) {
        throw new Error(
          'Missing Firebase configuration. Please check FIREBASE_* environment variables.',
        );
      }

      admin.initializeApp({
        credential: admin.credential.cert(firebaseConfig),
      });
    }

    this.db = admin.firestore();
    console.log('✅ Firebase initialized');

    // Initialize MySQL connection
    const mysqlConfig = {
      host: process.env.MYSQL_HOST || process.env.DB_HOST,
      port: parseInt(process.env.MYSQL_PORT || process.env.DB_PORT || '3306'),
      user: process.env.MYSQL_USER || process.env.DB_USER,
      password: process.env.MYSQL_PASSWORD || process.env.DB_PASSWORD,
      database: process.env.MYSQL_DATABASE || process.env.DB_NAME,
      ssl: process.env.MYSQL_SSL === 'true' ? {} : false,
      connectionLimit: parseInt(process.env.MYSQL_CONNECTION_LIMIT || '20'),
    };

    if (!mysqlConfig.host || !mysqlConfig.user || !mysqlConfig.password || !mysqlConfig.database) {
      throw new Error(
        'Missing MySQL configuration. Please check MYSQL_* or DB_* environment variables.',
      );
    }

    this.mysqlConnection = await mysql.createConnection(mysqlConfig);
    console.log('✅ MySQL connection established');

    // Test the connection
    const [rows] = await this.mysqlConnection.execute('SELECT 1 as test');
    console.log('✅ MySQL connection tested successfully');
  }

  /**
   * Discover all collections from Firestore
   */
  async discoverAllCollections() {
    console.log('🔍 Auto-discovering Firestore collections...');

    const collections = await this.db.listCollections();
    const collectionNames = collections.map(col => col.id);

    console.log(`📋 Found ${collectionNames.length} collections:`, collectionNames);

    // Filter to only collections we know how to migrate
    const knownCollections = collectionNames.filter(name => {
      const tableName = this.getTableName(name);
      return tableName; // Has a valid table mapping
    });

    console.log(`✅ Will migrate ${knownCollections.length} collections:`, knownCollections);

    // Sort by dependency order
    const orderedCollections = [];
    for (const collection of MIGRATION_ORDER) {
      if (knownCollections.includes(collection)) {
        orderedCollections.push(collection);
      }
    }

    // Add any remaining collections not in the predefined order
    for (const collection of knownCollections) {
      if (!orderedCollections.includes(collection)) {
        orderedCollections.push(collection);
      }
    }

    return orderedCollections;
  }

  /**
   * Get MySQL table name for Firestore collection
   */
  getTableName(collectionName) {
    const tableMap = {
      // Core refs
      practices: 'practices',
      clinics: 'clinics',

      // Entities already handled
      clients: 'clients',
      calls: 'calls', // callDetails handled separately
      locations: 'locations',
      appointments: 'appointments',
      callSessions: 'call_sessions',
      users: 'users',
      availableCalendarSlots: 'calendar_slots',

      // Supporting & misc
      emails: 'emails',
      'email-templates': 'email_templates',
      otp: 'otp',
      'on-call-schedules': 'on_call_schedules',
      'on-call-notifications': 'on_call_notifications',
      'agent-location-mappings': 'agent_location_mappings',
      staffInviteCodes: 'staff_invite_codes',
      patientReferences: 'patient_references',
      appointmentReferences: 'appointment_references',
      patients: 'patients',
    };

    return tableMap[collectionName] || null;
  }

  /**
   * Transform Firestore timestamp to MySQL datetime
   */
  transformTimestamp(timestamp) {
    if (!timestamp) return null;

    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    if (typeof timestamp === 'string') {
      return new Date(timestamp);
    }

    if (timestamp instanceof Date) {
      return timestamp;
    }

    return null;
  }

  /**
   * Generate a new UUID v4
   */
  generateUUID() {
    return require('crypto').randomUUID();
  }

  /**
   * Transform call document with merged callDetail data
   */
  async transformCallWithDetails(data, docId) {
    // First get the base call data
    const baseCall = {
      id: docId,
      client_id: data.clientId || null,
      client_name: data.clientName || null,
      user_id: data.userId || '',
      clinic_id: data.clinicId || 0,
      location_id: data.locationId || 0,
      agent_id: data.agentId || null,
      call_date: this.transformTimestamp(data.date) || new Date(),
      reason: data.reason || '',
      summary: data.summary || '', // Will be overwritten by callDetail if exists
      transcription: data.transcription || '', // Will be overwritten by callDetail if exists
      recording_url: data.recordingUrl || '',
      notes: data.notes || '',
      priority_score: data.priorityScore || 0,
      is_urgent: Boolean(data.urgent),
      tags: JSON.stringify(data.tags || []),
      phone_number: data.phoneNumber || '',
      session_id: data.sessionId || '',
      has_voicemail: Boolean(data.hasVoiceMail),
      is_outbound_call: Boolean(data.isOutboundCall),
      voicemail_url: data.voicemailUrl || '',
      transcription_with_audio: data.transcriptionWithAudio || '', // Will be overwritten by callDetail if exists
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };

    // Try to find related callDetail document
    try {
      const callDetailsSnapshot = await this.db
        .collection('callDetails')
        .where('callId', '==', docId)
        .limit(1)
        .get();

      if (!callDetailsSnapshot.empty) {
        const callDetailDoc = callDetailsSnapshot.docs[0];
        const callDetailData = callDetailDoc.data();

        console.log(`📝 Found callDetail for call ${docId}`);

        // Merge callDetail data into the call
        if (callDetailData.summary) {
          baseCall.summary = callDetailData.summary;
        }
        if (callDetailData.voicemailSummary) {
          baseCall.voicemail_summary = callDetailData.voicemailSummary;
        }
        if (callDetailData.transcription) {
          baseCall.transcription = callDetailData.transcription;
        }
        if (callDetailData.transcriptionWithAudio) {
          baseCall.transcription_with_audio = callDetailData.transcriptionWithAudio;
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not fetch callDetail for call ${docId}:`, error.message);
    }

    // Normalize single call_type value to numeric enum (0 = OTHER)
    baseCall.call_type = (() => {
      const raw = data.type;
      if (typeof raw === 'number') return raw;
      if (typeof raw === 'string') {
        if (/^\d+$/.test(raw)) return Number(raw);
        if (raw.toLowerCase() === 'other') return 0;
      }
      return 0;
    })();

    // Store numeric array in call_types JSON column
    baseCall.call_types = JSON.stringify([
      (() => {
        const raw = data.type;
        if (typeof raw === 'number') return raw;
        if (typeof raw === 'string') {
          if (/^\d+$/.test(raw)) return Number(raw);
          if (raw.toLowerCase() === 'other') return 0;
        }
        return 0;
      })(),
    ]);

    return baseCall;
  }

  /**
   * Transform Firestore document to MySQL format
   */
  async transformDocument(collectionName, data, docId) {
    // Collection-specific transformations
    switch (collectionName) {
      case 'practices':
        return this.transformPractice(data, docId);
      case 'clinics':
        return this.transformClinic(data, docId);
      case 'clients':
        return this.transformClient(data, docId);
      case 'calls':
        return this.transformCallWithDetails(data, docId);
      case 'locations':
        return this.transformLocation(data, docId);
      case 'appointments':
        return this.transformAppointment(data, docId);
      case 'callSessions':
        return this.transformCallSession(data, docId);
      case 'users':
        return this.transformUser(data, docId);
      case 'availableCalendarSlots':
        return this.transformCalendarSlot(data, docId);
      case 'emails':
        return this.transformEmail(data, docId);
      case 'email-templates':
        return this.transformEmailTemplate(data, docId);
      case 'otp':
        return this.transformOtp(data, docId);
      case 'on-call-schedules':
        return this.transformOnCallSchedule(data, docId);
      case 'on-call-notifications':
        return this.transformOnCallNotification(data, docId);
      case 'agent-location-mappings':
        return this.transformAgentLocationMapping(data, docId);
      case 'staffInviteCodes':
        return this.transformStaffInviteCode(data, docId);
      case 'patientReferences':
        return this.transformPatientReference(data, docId);
      case 'appointmentReferences':
        return this.transformAppointmentReference(data, docId);
      case 'patients':
        return this.transformPatient(data, docId);
      default:
        throw new Error(`No transformer found for collection: ${collectionName}`);
    }
  }

  /**
   * Transform practice document
   */
  transformPractice(data, docId) {
    return {
      // INT id (PK) auto-increment — omit so MySQL assigns
      uuid: data.uuid || docId,
      clinic_id: data.clinicId || null,
      name: data.name || '',
      description: data.description || null,
      is_active: data.isActive !== false,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform clinic document
   */
  transformClinic(data, docId) {
    return {
      uuid: data.uuid || docId,
      practice_id: data.practiceId || null,
      name: data.name || '',
      description: data.description || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform client document
   */
  transformClient(data, docId) {
    return {
      id: data.id || docId, // Use document ID as UUID primary key
      full_name: data.fullName || '',
      phone_number: data.phoneNumber || '',
      email: data.email || null,
      clinic_id: data.clinicId || null,
      birthday: this.transformTimestamp(data.birthday), // Date field
      insurance_company: data.insuranceCompany || null,
      insurance_group_number: data.insuranceGroupNumber || null,
      subscriber_name: data.subscriberName || null,
      medical_history: data.medicalHistory || null,
      recent_notes: data.recentNotes || null,
      list_of_calls: JSON.stringify(data.listOfCalls || []),
      tags: JSON.stringify(data.tags || []),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform location document
   */
  transformLocation(data, docId) {
    return {
      id: data.id || docId, // Use document ID as UUID primary key
      clinic_id: data.clinicId || null,
      practice_id: data.practiceId || null,
      name: data.name || '',
      address: data.address || '',
      phone: data.phone || '',
      time_zone: data.timeZone || '',
      is_active: Boolean(data.isActive !== false),
      practice_name: data.practiceName || '',
      office_hours: JSON.stringify(data.officeHours || {}),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform appointment document
   */
  transformAppointment(data, docId) {
    return {
      id: data.id || docId, // Use document ID as UUID primary key
      user_id: data.userId || null, // UUID foreign key
      client_id: data.clientId || null, // UUID foreign key
      client_name: data.clientName || '',
      slot_id: data.slotId || '',
      call_id: data.callId || null, // UUID foreign key
      appointment_date: data.date || '',
      appointment_time: data.time || '',
      status: data.status || 'active',
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform call session document
   */
  transformCallSession(data, docId) {
    return {
      id: docId, // Use document ID as UUID primary key
      session_id: data.sessionId || '',
      agent_id: data.agentId || null,
      has_voicemail: Boolean(data.hasVoiceMail),
      is_redirected: Boolean(data.isRedirected),
      call_type: data.callType || null,
      caller_phone: data.callerPhone || null,
      client_id: data.clientId || null, // UUID foreign key
      appointment_id: data.appointmentId || null, // UUID foreign key
      call_id: data.callId || null, // UUID foreign key
      trigger_event: data.triggerEvent || null,
      status: data.status || null,
      telephony_call_id: data.telephonyCallId || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform user document
   */
  transformUser(data, docId) {
    return {
      id: this.generateUUID(), // Generate new UUID for primary key
      firebase_uid: docId, // Use document ID as firebase_uid
      name: data.name || '',
      email: data.email || '',
      phone: data.phone || null,
      clinic_id: data.clinicId || null,
      role: data.role || 'user',
      specialty: data.specialty || null,
      profile_picture: data.profilePicture || null,
      can_take_appointments: data.canTakeAppointments ?? false,
      location_ids: JSON.stringify(data.locationIds || []),
      current_location_id: data.currentLocationId || null,
      practice_ids: JSON.stringify(data.practiceIds || []),
      is_active: Boolean(data.isActive !== false),
      preferences: JSON.stringify(data.preferences || {}),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform calendar slot document
   */
  transformCalendarSlot(data, docId) {
    return {
      id: docId, // Use document ID as UUID primary key
      user_id: data.userId || null, // UUID foreign key
      location_id: data.locationId || null, // UUID foreign key
      slot_date: data.date || '',
      time_slots: JSON.stringify(data.timeSlots || []),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform email document
   */
  transformEmail(data, docId) {
    // Prepare recipients array with proper structure
    const recipients = [];
    if (data.to && Array.isArray(data.to)) {
      recipients.push(...data.to.map(email => ({ email })));
    }
    if (data.toUids && Array.isArray(data.toUids)) {
      recipients.push(...data.toUids.map(uid => ({ uid })));
    }

    return {
      id: docId, // Use document ID as UUID primary key
      recipients: JSON.stringify(recipients),
      from_address: data.from || '',
      template_name: data.template?.name || '',
      template_data: JSON.stringify(data.template?.data || {}),
      status: data.delivery?.state || data.status || 'pending',
      error_message: data.delivery?.error?.message || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
      processed_at: this.transformTimestamp(data.delivery?.endTime) || null,
    };
  }

  /**
   * Transform email template document
   */
  transformEmailTemplate(data, docId) {
    return {
      // Don't include 'id' - it's auto-increment primary key
      name: docId, // Use document ID as name (unique constraint)
      subject: data.subject || '',
      html_content: data.html || '', // Firestore uses 'html', MySQL expects 'html_content'
      is_active: Boolean(data.isActive !== false), // Default to true if not specified
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform OTP document
   */
  transformOtp(data, docId) {
    return {
      // Primary key is otp_key (not id) for this table
      otp_key: docId, // Use document ID as otp_key (primary key)
      email: data.email || '',
      code: data.code || '',
      expire_at: this.transformTimestamp(data.expireAt) || new Date(),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      // Note: otp table doesn't have updated_at column in MySQL schema
    };
  }

  /**
   * Transform on-call schedule document
   */
  transformOnCallSchedule(data, docId) {
    return {
      id: docId, // UUID primary key (keep Firestore ID)
      doctor_id: data.doctorId || null,
      doctor_name: data.doctorName || '',
      doctor_phone: data.doctorPhone || null,
      location_id: data.locationId || null,
      clinic_id: data.clinicId || null,
      schedule_date: data.date || '', // YYYY-MM-DD
      start_time: data.startTime || '', // HH:MM
      end_time: data.endTime || '',
      is_active: data.isActive !== false,
      timezone: data.timezone || 'UTC',
      notes: data.notes || '',
      created_by: data.createdBy || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform on-call notification document
   */
  transformOnCallNotification(data, docId) {
    return {
      id: docId, // UUID
      schedule_id: data.scheduleId || null,
      call_session_id: data.callSessionId || null,
      doctor_id: data.doctorId || null,
      clinic_id: data.clinicId || null,
      notification_time: this.transformTimestamp(data.notificationTime) || new Date(),
      sms_message_id: data.smsMessageId || null,
      status: data.status || 'pending',
      call_type: data.callType || null,
      caller_phone: data.callerPhone || null,
      error_message: data.errorMessage || null,
      retry_count: data.retryCount || 0,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
    };
  }

  /**
   * Transform agent-location mapping document
   */
  transformAgentLocationMapping(data, docId) {
    return {
      agent_id: data.agentId || docId, // Use docId (agentId) if field missing
      location_id: data.locationId || null,
      clinic_id: data.clinicId || null,
      is_active: data.isActive !== false,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform staff invite code document
   */
  transformStaffInviteCode(data, docId) {
    return {
      id: docId, // UUID
      code: data.code || '',
      clinic_id: data.clinicId || null,
      used: Boolean(data.used),
      expires_at: this.transformTimestamp(data.expiresAt) || null,
      created_by: data.createdBy || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      used_at: this.transformTimestamp(data.usedAt) || null,
    };
  }

  /**
   * Transform patient reference document
   */
  transformPatientReference(data, docId) {
    return {
      id: docId,
      provider: data.provider || '',
      provider_id: data.providerId || '',
      phone_number: data.phoneNumber || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform appointment reference document
   */
  transformAppointmentReference(data, docId) {
    return {
      id: docId,
      provider: data.provider || '',
      external_id: data.externalId || '',
      provider_id: data.providerId || '',
      patient_id: data.patientId || '',
      patient_name: data.patientName || null,
      practitioner_id: data.practitionerId || '',
      practitioner_name: data.practitionerName || null,
      location_id: data.locationId || '',
      location_name: data.locationName || null,
      start_time: this.transformTimestamp(data.startTime) || null,
      end_time: this.transformTimestamp(data.endTime) || null,
      type: data.type || null,
      status: data.status || null,
      reason: data.reason || null,
      notes: data.notes || null,
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Transform patient document
   */
  transformPatient(data, docId) {
    return {
      id: data.id || docId,
      first_name: data.firstName || '',
      last_name: data.lastName || '',
      date_of_birth: data.dateOfBirth || null,
      gender: data.gender || null,
      email: data.email || null,
      phone_number: data.phoneNumber || null,
      address: JSON.stringify(data.address || {}),
      provider: (data.providerInfo && data.providerInfo.provider) || null,
      provider_id: (data.providerInfo && data.providerInfo.externalId) || data.providerId || null,
      notes: data.notes || null,
      insurances: JSON.stringify(data.insurances || []),
      identifiers: JSON.stringify(data.identifiers || []),
      created_at: this.transformTimestamp(data.createdAt) || new Date(),
      updated_at: this.transformTimestamp(data.updatedAt) || new Date(),
    };
  }

  /**
   * Migrate a single collection
   */
  async migrateCollection(collectionName) {
    const tableName = this.getTableName(collectionName);
    if (!tableName) {
      console.log(`⏭️ Skipping collection: ${collectionName} (no table mapping)`);
      return { migrated: 0, failed: 0, total: 0 };
    }

    console.log(`\n📂 Migrating collection: ${collectionName} → ${tableName}`);

    let query = this.db.collection(collectionName);

    // Special handling for calls collection - limit to latest entries
    if (collectionName === 'calls' && this.config.callsLimit > 0) {
      console.log(`🔢 Limiting calls to latest ${this.config.callsLimit} entries`);
      query = query.orderBy('date', 'desc').limit(this.config.callsLimit);
    }

    // Special handling for callSessions collection - limit to latest entries
    if (collectionName === 'callSessions' && this.config.callsLimit > 0) {
      console.log(`🔢 Limiting callSessions to latest ${this.config.callsLimit} entries`);
      query = query.orderBy('updatedAt', 'desc').limit(this.config.callsLimit);
    }

    const snapshot = await query.get();
    const totalDocs = snapshot.size;

    if (totalDocs === 0) {
      console.log(`📭 Collection ${collectionName} is empty`);
      return { migrated: 0, failed: 0, total: 0 };
    }

    console.log(`📊 Found ${totalDocs} documents in ${collectionName}`);

    if (collectionName === 'calls' && this.config.callsLimit > 0) {
      console.log(
        `✂️ Limited to ${Math.min(totalDocs, this.config.callsLimit)} calls out of ${totalDocs} total`,
      );
    }

    if (collectionName === 'callSessions' && this.config.callsLimit > 0) {
      console.log(
        `✂️ Limited to ${Math.min(totalDocs, this.config.callsLimit)} callSessions out of ${totalDocs} total`,
      );
    }

    const stats = { migrated: 0, failed: 0, total: totalDocs };
    const batchSize = this.config.batchSize;
    const docs = snapshot.docs;

    // Process in batches
    for (let i = 0; i < docs.length; i += batchSize) {
      const batch = docs.slice(i, i + batchSize);
      console.log(
        `📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(docs.length / batchSize)} (${batch.length} docs)`,
      );

      // Process batch with concurrency control
      const batchPromises = batch.map(async (doc, index) => {
        try {
          const data = doc.data();
          const transformedData = await this.transformDocument(collectionName, data, doc.id);

          if (this.config.dryRun || this.config.validateOnly) {
            console.log(`✅ [DRY RUN] Would migrate ${collectionName}/${doc.id}`);
            return { success: true };
          }

          // Insert into MySQL
          const columns = Object.keys(transformedData);
          const values = Object.values(transformedData);
          const placeholders = columns.map(() => '?').join(', ');

          const sql = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders}) 
                       ON DUPLICATE KEY UPDATE ${columns.map(col => `${col} = VALUES(${col})`).join(', ')}`;

          await this.mysqlConnection.execute(sql, values);
          return { success: true };
        } catch (error) {
          console.error(`❌ Failed to migrate ${collectionName}/${doc.id}:`, error.message);
          return { success: false, error: error.message };
        }
      });

      // Execute batch with limited concurrency
      const batchResults = await Promise.all(batchPromises);

      // Update stats
      batchResults.forEach(result => {
        if (result.success) {
          stats.migrated++;
        } else {
          stats.failed++;
        }
      });

      console.log(`📈 Progress: ${stats.migrated}/${stats.total} migrated, ${stats.failed} failed`);
    }

    console.log(
      `✅ Collection ${collectionName} completed: ${stats.migrated} migrated, ${stats.failed} failed`,
    );
    return stats;
  }

  /**
   * Run the migration
   */
  async migrate() {
    try {
      console.log('🎯 Starting Firestore to MySQL Migration');
      console.log('Configuration:', this.config);

      await this.initialize();

      // Determine collections to migrate
      let collections = this.config.collections;
      if (this.config.autoDiscoverCollections) {
        collections = await this.discoverAllCollections();
      }

      if (this.config.resumeFromCollection) {
        const resumeIndex = collections.indexOf(this.config.resumeFromCollection);
        if (resumeIndex !== -1) {
          collections = collections.slice(resumeIndex);
          console.log(`🔄 Resuming from collection: ${this.config.resumeFromCollection}`);
        }
      }

      console.log(`📋 Will process ${collections.length} collections:`, collections);

      // Migrate each collection
      for (const collectionName of collections) {
        const collectionStats = await this.migrateCollection(collectionName);
        this.stats.collectionStats[collectionName] = collectionStats;
        this.stats.totalDocuments += collectionStats.total;
        this.stats.migratedDocuments += collectionStats.migrated;
        this.stats.failedDocuments += collectionStats.failed;
      }

      // Final summary
      this.stats.endTime = new Date();
      const duration = this.stats.endTime - this.stats.startTime;

      console.log('\n🎉 Migration Summary:');
      console.log(`⏱️  Duration: ${Math.round(duration / 1000)}s`);
      console.log(`📊 Total Documents: ${this.stats.totalDocuments}`);
      console.log(`✅ Migrated: ${this.stats.migratedDocuments}`);
      console.log(`❌ Failed: ${this.stats.failedDocuments}`);
      console.log(`⏭️  Skipped: ${this.stats.skippedDocuments}`);

      console.log('\n📋 Collection Details:');
      Object.entries(this.stats.collectionStats).forEach(([collection, stats]) => {
        console.log(`  ${collection}: ${stats.migrated}/${stats.total} (${stats.failed} failed)`);
      });

      if (this.config.dryRun) {
        console.log('\n🔍 This was a DRY RUN - no data was actually migrated');
      } else if (this.config.validateOnly) {
        console.log('\n✅ VALIDATION COMPLETE - data structure is valid');
      } else {
        console.log('\n🚀 Migration completed successfully!');
      }
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    } finally {
      // Cleanup connections
      if (this.mysqlConnection) {
        await this.mysqlConnection.end();
        console.log('🔌 MySQL connection closed');
      }

      if (admin.apps.length > 0) {
        await admin.app().delete();
        console.log('🔌 Firebase connection closed');
      }
    }
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  const config = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];

    switch (arg) {
      case '--dry-run':
        config.dryRun = true;
        break;
      case '--validate-only':
        config.validateOnly = true;
        break;
      case '--collections':
        config.collections = args[++i]?.split(',') || [];
        config.autoDiscoverCollections = false;
        break;
      case '--batch-size':
        config.batchSize = parseInt(args[++i]) || 100;
        break;
      case '--calls-limit':
        config.callsLimit = parseInt(args[++i]) || 250;
        break;
      case '--resume-from-collection':
        config.resumeFromCollection = args[++i];
        break;
      case '--no-backup':
        config.noBackup = true;
        break;
      case '--auto-discover':
        config.autoDiscoverCollections = true;
        break;
      case '--no-auto-discover':
        config.autoDiscoverCollections = false;
        break;
      case '--help':
        console.log(`
Firestore to MySQL Migration Tool

Usage: node scripts/migrate-firestore-to-mysql.js [options]

Options:
  --dry-run                    Preview migration without making changes
  --validate-only              Only validate data structure
  --collections <list>         Comma-separated list of collections to migrate
  --batch-size <number>        Number of documents per batch (default: 100)
  --calls-limit <number>       Limit calls migration to latest N calls (default: 250)
  --resume-from-collection <name>  Resume migration from specific collection
  --no-backup                  Skip creating backups
  --auto-discover              Auto-discover all collections (default)
  --no-auto-discover           Only migrate specified collections
  --help                       Show this help message

Examples:
  node scripts/migrate-firestore-to-mysql.js --dry-run
  node scripts/migrate-firestore-to-mysql.js --collections calls,clients --calls-limit 100
  node scripts/migrate-firestore-to-mysql.js --validate-only --auto-discover
  node scripts/migrate-firestore-to-mysql.js --resume-from-collection calls
`);
        process.exit(0);
      default:
        if (arg.startsWith('--')) {
          console.warn(`Unknown option: ${arg}`);
        }
        break;
    }
  }

  // Create and run migrator
  const migrator = new FirestoreToMySQLMigrator(config);
  await migrator.migrate();
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => {
      console.log('🎯 Migration process completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Migration process failed:', error);
      process.exit(1);
    });
}

module.exports = { FirestoreToMySQLMigrator };
