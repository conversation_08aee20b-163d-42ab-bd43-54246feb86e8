# Missing Users Migration Script

A utility script to migrate users from Firestore to MySQL that don't already exist in the MySQL database.

## Overview

This script performs a smart migration by:
1. Connecting to both Firestore and MySQL databases
2. Retrieving all users from Firestore
3. Checking which users don't exist in MySQL (by email comparison)
4. Migrating only the missing users
5. Providing detailed logging and statistics

## Features

- ✅ **Smart Duplicate Detection**: Only migrates users that don't already exist in MySQL
- ✅ **Batch Processing**: Processes users in configurable batches for better performance
- ✅ **Dry Run Mode**: Preview what would be migrated without making changes
- ✅ **Data Validation**: Validates user data before migration
- ✅ **Error Handling**: Robust error handling with detailed logging
- ✅ **Progress Tracking**: Real-time progress updates and statistics
- ✅ **Connection Management**: Proper cleanup of database connections

## Prerequisites

1. **Environment Variables**: Ensure the following environment variables are set in your `.env.local` file:
   ```bash
   # Firebase Configuration
   FIREBASE_PROJECT_ID=your-firebase-project-id
   FIREBASE_CLIENT_EMAIL=your-firebase-client-email
   FIREBASE_PRIVATE_KEY=your-firebase-private-key

   # MySQL Configuration
   MYSQL_HOST=your-mysql-host
   MYSQL_PORT=3306
   MYSQL_USER=your-mysql-user
   MYSQL_PASSWORD=your-mysql-password
   MYSQL_DATABASE=your-mysql-database

   # Cloud SQL (if applicable)
   INSTANCE_CONNECTION_NAME=your-instance-connection-name
   DB_SOCKET_PATH=/path/to/socket (optional)
   ```

2. **Database Access**: Ensure the MySQL user has INSERT permissions on the `users` table

3. **Node.js Dependencies**: All required dependencies should be installed via `pnpm install`

## Usage

### Basic Usage

```bash
# Run the migration
node scripts/migrate-missing-users.js

# Run with dry-run to see what would be migrated
node scripts/migrate-missing-users.js --dry-run

# Run with custom batch size
node scripts/migrate-missing-users.js --batch-size 25
```

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--dry-run` | Show what would be migrated without making changes | false |
| `--batch-size <number>` | Number of users per batch | 50 |
| `--help` | Show help message | - |

### Examples

```bash
# Preview migration without making changes
node scripts/migrate-missing-users.js --dry-run

# Run migration with smaller batch size for slower connections
node scripts/migrate-missing-users.js --batch-size 10

# Full migration run
node scripts/migrate-missing-users.js
```

## Sample Output

### Dry Run Mode
```
🚀 Initializing Missing Users Migrator...
✅ Firebase initialized
✅ MySQL connected

🔍 Starting missing users analysis...
📊 Fetching existing users from MySQL...
📧 Found 150 existing users in MySQL
🔥 Fetching all users from Firestore...
👥 Found 175 users in Firestore

📊 Migration Analysis:
📈 Total Firestore users: 175
✅ Already in MySQL: 150
🔄 To migrate: 25
⏭️  Skipped (no email): 0

🔍 DRY RUN MODE - Showing users that would be migrated:
1. <EMAIL> (John Doe) - Role: STAFF
2. <EMAIL> (Jane Smith) - Role: CLINIC_ADMIN
3. <EMAIL> (Bob Wilson) - Role: STAFF
...
```

### Actual Migration
```
📦 Processing batch 1/1

✅ Migrated: <EMAIL> (John Doe)
✅ Migrated: <EMAIL> (Jane Smith)
✅ Migrated: <EMAIL> (Bob Wilson)
📈 Progress: 25/25 processed

🎉 Migration Complete!
✅ Successfully migrated: 25
❌ Failed: 0

🔌 MySQL connection closed
🔌 Firebase connection closed
🎯 Migration process completed successfully
```

## Data Mapping

The script maps Firestore user data to MySQL schema as follows:

| Firestore Field | MySQL Column | Notes |
|------------------|--------------|-------|
| `id` (document ID) | `firebase_uid` | Original Firestore document ID |
| *(generated)* | `id` | New UUID generated as primary key |
| `email` | `email` | Required field |
| `phone` | `phone` | Optional |
| `name` | `name` | Required field |
| `role` | `role` | Must be valid role |
| `specialty` | `specialty` | Optional |
| `clinicId` | `clinic_id` | Optional |
| `profilePicture` | `profile_picture` | Optional |
| `canTakeAppointments` | `can_take_appointments` | Boolean to integer |
| `locationIds` | `location_ids` | JSON array |
| `currentLocationId` | `current_location_id` | Optional |
| `practiceIds` | `practice_ids` | JSON array |
| `preferences` | `preferences` | JSON object |
| `createdAt` | `created_at` | Timestamp conversion |
| `updatedAt` | `updated_at` | Timestamp conversion |

### UUID Generation

The script generates a new UUID v4 for each user as the primary key (`id` field), while preserving the original Firestore document ID in the `firebase_uid` field. This ensures:

- **Unique Primary Keys**: Each MySQL record has a proper UUID primary key
- **Firestore Traceability**: Original Firestore document IDs are preserved for reference
- **Data Integrity**: Maintains relationships and prevents conflicts

## Data Validation

The script validates the following before migration:
- **Email** is required and present
- **Name** is required and present  
- **Role** is required and valid (`SUPER_ADMIN`, `CLINIC_ADMIN`, `STAFF`, `GUEST`)

### Smart Defaults

The script provides sensible defaults for missing fields to handle incomplete Firestore data:

| Field | Default Value | Notes |
|-------|---------------|-------|
| `canTakeAppointments` | `false` | If not set in Firestore |
| `role` | `'STAFF'` | If missing, defaults to STAFF role |
| `locationIds` | `[]` | Empty array if not set |
| `practiceIds` | `[]` | Empty array if not set |

When defaults are applied, you'll see log messages like:
```
📝 Applying <NAME_EMAIL>: canTakeAppointments -> false, role -> STAFF
```

This ensures that users with incomplete data in Firestore can still be migrated successfully.

## Error Handling

- **Connection Errors**: Script will fail fast if database connections cannot be established
- **Validation Errors**: Invalid users are skipped with detailed error messages
- **Migration Errors**: Individual user migration failures are logged but don't stop the entire process
- **Cleanup**: Database connections are properly closed even if errors occur

## Safety Features

1. **Duplicate Prevention**: Uses email-based comparison to prevent duplicate users
2. **Dry Run Mode**: Always test with `--dry-run` first
3. **Batch Processing**: Processes users in small batches to avoid overwhelming the database
4. **Transaction Safety**: Each user insertion is atomic
5. **Error Isolation**: Failure to migrate one user doesn't affect others

## Troubleshooting

### Common Issues

1. **Missing Environment Variables**
   ```
   Error: Missing Firebase configuration. Please check FIREBASE_* environment variables.
   ```
   - Solution: Ensure all required environment variables are set in `.env.local`

2. **MySQL Connection Failed**
   ```
   Error: connect ECONNREFUSED 127.0.0.1:3306
   ```
   - Solution: Check MySQL host, port, and credentials

3. **Validation Errors**
   ```
   Failed <NAME_EMAIL>: Validation failed: Email is required
   ```
   - Solution: Check Firestore data quality; some users may have missing required fields

### Best Practices

1. **Always run dry-run first**: `node scripts/migrate-missing-users.js --dry-run`
2. **Use appropriate batch sizes**: Smaller batches for slower connections
3. **Monitor the output**: Watch for validation errors or connection issues
4. **Backup your data**: Always backup your MySQL database before running migrations
5. **Test on staging**: Run the script on a staging environment first

## Related Scripts

- `scripts/migrate-firestore-to-mysql.js` - Full migration script for all collections
- `scripts/phase4-user-migration.js` - Phase 4 user migration with location model updates
- `scripts/validate-database-integrity.ts` - Database integrity validation

## Support

If you encounter issues:
1. Check the environment variables are correctly set
2. Ensure database connections are working
3. Run with `--dry-run` to identify potential issues
4. Check the error messages for specific validation failures 