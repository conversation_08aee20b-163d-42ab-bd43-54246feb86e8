/**
 * This script creates Firebase Authentication users from a JSON file
 * Run with: node scripts/add-clinic-12-users.js
 */

const admin = require('firebase-admin');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

// Initialize Firebase Admin
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
  } catch (error) {
    console.error('Firebase admin initialization error:', error);
    process.exit(1);
  }
}

const db = admin.firestore();

async function addClinic12Users() {
  try {
    console.log('Starting creation of Clinic 12 users in Firebase...');

    // Read the JSON file with the users to add
    const usersFilePath = path.join(process.cwd(), 'temp-users-clinic-12.json');
    const usersData = JSON.parse(fs.readFileSync(usersFilePath, 'utf8'));

    if (!Array.isArray(usersData)) {
      throw new Error('Expected an array of users in the JSON file');
    }

    console.log(`Found ${usersData.length} users to add in the JSON file`);

    // Get all existing Firebase Auth users to avoid duplicates
    const existingAuthUsers = await admin.auth().listUsers();
    const existingEmails = new Set(existingAuthUsers.users.map(user => user.email.toLowerCase()));

    console.log(`Found ${existingEmails.size} existing users in Firebase Auth`);

    let createdCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    const createdUsers = [];

    // Process each user
    for (const userData of usersData) {
      // Skip if email already exists in Auth
      if (existingEmails.has(userData.email.toLowerCase())) {
        console.log(`Skipping existing user: ${userData.email}`);
        skippedCount++;
        continue;
      }

      try {
        // Create the Firebase Auth user
        const firebaseUser = await admin.auth().createUser({
          email: userData.email,
          password: userData.password,
          displayName: userData.name,
          disabled: false,
        });

        // Set custom claims for role-based access control
        await admin.auth().setCustomUserClaims(firebaseUser.uid, {
          role: userData.role,
          clinicId: userData.clinicId,
        });

        // Create the user document in Firestore
        await db.collection('users').doc(firebaseUser.uid).set({
          email: userData.email,
          name: userData.name,
          role: userData.role,
          clinicId: userData.clinicId,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        console.log(`Created Firebase user for: ${userData.email} with ID: ${firebaseUser.uid}`);

        createdUsers.push({
          email: userData.email,
          name: userData.name,
          password: userData.password,
          role: userData.role,
          uid: firebaseUser.uid,
        });

        createdCount++;
      } catch (error) {
        console.error(`Error creating user ${userData.email}:`, error.message);
        errorCount++;
      }
    }

    // Save details of created users to a file
    fs.writeFileSync('clinic-12-created-users.json', JSON.stringify(createdUsers, null, 2));

    console.log('\nSummary:');
    console.log(`- Created: ${createdCount} users`);
    console.log(`- Skipped: ${skippedCount} users (already exist)`);
    console.log(`- Errors: ${errorCount} users`);
    console.log('\nCreated users have been saved to clinic-12-created-users.json');
  } catch (error) {
    console.error('Error in addClinic12Users:', error);
    process.exit(1);
  }
}

// Run the function
addClinic12Users()
  .then(() => {
    console.log('Completed!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Failed to create users:', error);
    process.exit(1);
  });
