/**
 * @file Migration script to populate agent-location mappings collection
 * @description Creates initial agent-to-location mappings in Firestore for office hours integration
 */

import admin from 'firebase-admin';

import { AGENT_LOCATION_MAPPINGS_DEFAULT } from '../app-config';

interface AgentLocationMapping {
  agentId: string;
  locationId: string;
  clinicId: number;
  isActive: boolean;
  createdAt: admin.firestore.Timestamp;
  updatedAt: admin.firestore.Timestamp;
}

/**
 * Initialize Firebase Admin SDK
 */
function initializeFirebase(): void {
  if (!admin.apps.length) {
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
    });
  }
}

/**
 * Get clinic ID for a given location
 * @param locationId The location ID to look up
 * @returns The clinic ID associated with the location
 * @throws Error if location not found or missing clinicId
 */
async function getClinicIdForLocation(locationId: string): Promise<number> {
  try {
    const db = admin.firestore();
    const locationDoc = await db.collection('locations').doc(locationId).get();

    if (!locationDoc.exists) {
      throw new Error(`Location ${locationId} not found`);
    }

    const locationData = locationDoc.data();
    if (!locationData?.clinicId) {
      throw new Error(`Location ${locationId} missing clinicId`);
    }

    return locationData.clinicId;
  } catch (error) {
    console.error(`Failed to get clinic ID for location ${locationId}:`, error);
    throw error;
  }
}

/**
 * Create agent-location mapping document
 * @param agentId The DialogFlow agent ID
 * @param locationId The location ID to map to
 * @returns The created mapping document
 */
async function createAgentLocationMapping(
  agentId: string,
  locationId: string,
): Promise<AgentLocationMapping> {
  try {
    const clinicId = await getClinicIdForLocation(locationId);
    const now = admin.firestore.Timestamp.now();

    const mapping: AgentLocationMapping = {
      agentId,
      locationId,
      clinicId,
      isActive: true,
      createdAt: now,
      updatedAt: now,
    };

    const db = admin.firestore();
    await db.collection('agent-location-mappings').doc(agentId).set(mapping);

    console.log(`✅ Created mapping: ${agentId} -> ${locationId} (clinic ${clinicId})`);
    return mapping;
  } catch (error) {
    console.error(`❌ Failed to create mapping for ${agentId} -> ${locationId}:`, error);
    throw error;
  }
}

/**
 * Check if agent-location mapping already exists
 * @param agentId The agent ID to check
 * @returns True if mapping exists, false otherwise
 */
async function mappingExists(agentId: string): Promise<boolean> {
  try {
    const db = admin.firestore();
    const doc = await db.collection('agent-location-mappings').doc(agentId).get();
    return doc.exists;
  } catch (error) {
    console.error(`Failed to check if mapping exists for ${agentId}:`, error);
    return false;
  }
}

/**
 * Main migration function
 */
async function migrateAgentLocationMappings(): Promise<void> {
  try {
    console.log('🚀 Starting agent-location mappings migration...');

    initializeFirebase();

    const mappings = Object.entries(AGENT_LOCATION_MAPPINGS_DEFAULT);

    if (mappings.length === 0) {
      console.log('⚠️  No default mappings found in config');
      return;
    }

    console.log(`📋 Found ${mappings.length} mappings to process`);

    let created = 0;
    let skipped = 0;
    let failed = 0;

    for (const [agentId, locationId] of mappings) {
      try {
        const exists = await mappingExists(agentId);

        if (exists) {
          console.log(`⏭️  Skipping existing mapping: ${agentId}`);
          skipped++;
          continue;
        }

        await createAgentLocationMapping(agentId, locationId);
        created++;
      } catch (error) {
        console.error(`❌ Failed to process mapping ${agentId} -> ${locationId}:`, error);
        failed++;
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`   Created: ${created}`);
    console.log(`   Skipped: ${skipped}`);
    console.log(`   Failed:  ${failed}`);
    console.log(`   Total:   ${mappings.length}`);

    if (failed > 0) {
      throw new Error(`Migration completed with ${failed} failures`);
    }

    console.log('✅ Agent-location mappings migration completed successfully!');
  } catch (error) {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateAgentLocationMappings()
    .then(() => {
      console.log('🎉 Migration script completed');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateAgentLocationMappings };
