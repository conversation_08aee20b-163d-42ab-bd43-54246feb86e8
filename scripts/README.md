# Scripts Directory

This directory contains utility scripts for managing the Plato Health Front Desk Portal application.

## Available Scripts

### `delete-invalid-phone-calls.js`

Deletes calls from the database that have invalid phone numbers. This helps clean up data quality issues and improves system performance by removing calls that cannot be properly processed.

Usage:

```bash
# Dry run to see what would be deleted (recommended first step)
node scripts/delete-invalid-phone-calls.js --dry-run

# Actually delete the calls (requires confirmation)
node scripts/delete-invalid-phone-calls.js
```

#### What it does:

1. Searches for the latest 100 calls with invalid phone numbers
2. Invalid phone numbers are defined as:
   - Empty or undefined phoneNumber
   - phoneNumber that's just whitespace when trimmed
   - phoneNumber that equals "Unknown"
3. Orders calls by date (newest first) to prioritize recent data cleanup
4. Shows detailed analytics about the types of invalid phone numbers found
5. Deletes both the Call record and any associated CallDetail records for data consistency
6. Processes deletions in batches to avoid overwhelming Firestore

#### Safety Features:

- **Dry run mode**: Use `--dry-run` flag to preview what would be deleted without making changes
- **User confirmation**: Requires typing "yes" to proceed with actual deletions
- **Detailed logging**: Shows exactly which calls are being processed and any errors
- **Transaction safety**: Uses Firestore transactions to ensure data consistency
- **Batch processing**: Processes calls in small batches with delays to be gentle on the database

#### Important Notes:

- Always run with `--dry-run` first to review what will be deleted
- The script will show examples of invalid calls and date ranges before deletion
- Deletion is permanent - ensure you have database backups if needed
- Associated CallDetail records are automatically cleaned up

### `seed-firestore.js`

Seeds the Firestore database with initial data from JSON files in the `/data` directory.

Usage:

```bash
node scripts/seed-firestore.js
```

### `check-missing-auth-users.js`

Identifies Firestore users that don't have corresponding Firebase Authentication accounts. This is useful to run before using the other auth-related scripts.

Usage:

```bash
node scripts/check-missing-auth-users.js
```

#### What it does:

1. Retrieves all users from Firestore and Firebase Authentication
2. Identifies users that exist in Firestore but not in Firebase Authentication (by both UID and email)
3. Displays detailed information about missing users
4. Provides a comma-separated list of emails that can be used with the reset-user-passwords.js script
5. Suggests next steps based on the results

### `create-auth-users.js`

Creates Firebase Authentication users for existing users in Firestore. This is useful for migrating users who are already in the Firestore database but don't have corresponding auth accounts.

Usage:

```bash
node scripts/create-auth-users.js
```

#### What it does:

1. Retrieves all users from Firestore
2. For each user not already in Firebase Auth, creates a new auth account using their Firestore data
3. Sets appropriate custom claims for role-based access control
4. Generates secure random passwords for each user
5. Saves temporary passwords to a file for admin distribution

#### Important Security Notes:

- The script generates a `temp-user-passwords.json` file containing temporary passwords for all created users
- For security, delete this file after distributing passwords to users
- In a production environment, consider using password reset emails instead of temporary passwords

### `reset-user-passwords.js`

Generates password reset links for existing users in Firebase Authentication. This is a more secure alternative to `create-auth-users.js` for production environments.

Usage:

```bash
# Reset passwords for all users
node scripts/reset-user-passwords.js

# Reset passwords for specific users
node scripts/reset-user-passwords.js <EMAIL>,<EMAIL>
```

#### What it does:

1. Generates secure password reset links for each user
2. Displays these links in the console (in a production environment, you would integrate with an email service)
3. The links allow users to set their own passwords securely

#### Integration with Email Services:

The script includes commented code showing how to integrate with an email service (using nodemailer as an example). To use this feature:

1. Install nodemailer: `pnpm add nodemailer`
2. Add your email credentials to `.env.local`:
   ```
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   ```
3. Uncomment and customize the email sending code in the script

### `update-users-calendar-slots.js`

Updates User models with a `canTakeAppointments` flag and generates test calendar slots for users who can take appointments.

Usage:

```bash
node scripts/update-users-calendar-slots.js
```

#### What it does:

1. Updates all users in the Firestore database with a new `canTakeAppointments` field
2. Determines which users can take appointments (currently set for STAFF with a specialty)
3. For users who can take appointments, creates calendar slots for the next 30 days (excluding weekends)
4. Each day includes default time slots at 30-minute intervals from 9 AM to 5 PM

#### Generated Data Structure:

- Each calendar slot includes:
  - A unique ID
  - The user ID of the staff member
  - A date in ISO format (YYYY-MM-DD)
  - An array of time slots with:
    - Time in 24-hour format (HH:MM)
    - Availability flag (default: true)

## Recommended Workflow

For migrating existing Firestore users to Firebase Authentication:

1. **Check:** Run `check-missing-auth-users.js` to identify which users need to be migrated
2. **Migrate:** Choose one of the following approaches:
   - For development/testing: Run `create-auth-users.js` to create accounts with temporary passwords
   - For production: Run `reset-user-passwords.js` to generate and send password reset links

## Environment Setup

All scripts require Firebase Admin SDK credentials in your `.env.local` file:

```
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour Firebase private key here\n-----END PRIVATE KEY-----"
```

Ensure these environment variables are properly set before running any scripts.
