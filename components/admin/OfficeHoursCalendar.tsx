import React, { useState, useEffect } from 'react';
import {
  FaCalendarAlt as Calendar,
  FaClock as Clock,
  <PERSON>a<PERSON><PERSON> as User,
  FaCog as Settings,
  FaTimes as X,
  FaChevronLeft as ChevronLeft,
  FaChevronRight as ChevronRight,
} from 'react-icons/fa';
import { Location } from '@/models/Location';
import { OfficeHoursService, OfficeHoursConfig } from '@/lib/services/office-hours';
import { EligibleDoctor } from '@/models/EligibleDoctor';

interface OnCallDoctor {
  id: string;
  name: string;
  phone: string;
  email: string;
  specialties: string[];
}

interface OnCallSchedule {
  id: string;
  doctorId: string;
  doctorName: string;
  locationId: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  isPrimary: boolean;
  contactInfo: {
    phone: string;
    email: string;
  };
}

interface OfficeHoursCalendarProps {
  currentLocation: Location | null;
  onUpdate: (locationId: string, updates: Record<string, unknown>) => Promise<void>;
}

const DAYS_OF_WEEK = [
  { key: '1', label: 'Monday', short: 'Mon' },
  { key: '2', label: 'Tuesday', short: 'Tue' },
  { key: '3', label: 'Wednesday', short: 'Wed' },
  { key: '4', label: 'Thursday', short: 'Thu' },
  { key: '5', label: 'Friday', short: 'Fri' },
  { key: '6', label: 'Saturday', short: 'Sat' },
  { key: '7', label: 'Sunday', short: 'Sun' },
];

const TIMEZONES = [
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
  'America/Phoenix',
  'Europe/London',
  'Europe/Paris',
  'Asia/Tokyo',
  'UTC',
];

export default function OfficeHoursCalendar({
  currentLocation,
  onUpdate,
}: OfficeHoursCalendarProps) {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(currentLocation);

  const [currentDate, setCurrentDate] = useState(new Date());
  const [showOfficeHoursModal, setShowOfficeHoursModal] = useState(false);
  const [showOnCallModal, setShowOnCallModal] = useState(false);
  const [editingHours, setEditingHours] = useState<OfficeHoursConfig>({});
  const [editingTimezone, setEditingTimezone] = useState('America/Chicago');
  const [onCallDoctors, setOnCallDoctors] = useState<OnCallDoctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(false);
  const [onCallSchedules, setOnCallSchedules] = useState<OnCallSchedule[]>([]);
  const [saving, setSaving] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'schedules' | 'doctors'>('schedules');
  const [showScheduleModal, setShowScheduleModal] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<OnCallDoctor | null>(null);
  const [scheduleForm, setScheduleForm] = useState({
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    isPrimary: true,
  });
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [loadingSchedules, setLoadingSchedules] = useState(false);

  // Show loading state in UI when needed
  console.log('Loading schedules:', loadingSchedules);

  // Load eligible doctors when location changes
  useEffect(() => {
    if (selectedLocation) {
      loadEligibleDoctors(selectedLocation.id);
    }
  }, [selectedLocation]);

  const loadEligibleDoctors = async (locationId: string) => {
    setLoadingDoctors(true);
    try {
      const response = await fetch(`/api/locations/${locationId}/eligible-doctors`);
      if (response.ok) {
        const data = await response.json();
        const doctors = data.doctors.map((doctor: EligibleDoctor) => ({
          id: doctor.id,
          name: doctor.name,
          phone: doctor.phone || 'N/A',
          email: doctor.email || 'N/A',
          specialties: doctor.specialty ? [doctor.specialty] : ['General Practice'],
        }));
        setOnCallDoctors(doctors);
      } else {
        console.error('Failed to load eligible doctors:', response.statusText);
        setOnCallDoctors([]);
      }
    } catch (error) {
      console.error('Error loading eligible doctors:', error);
      setOnCallDoctors([]);
    } finally {
      setLoadingDoctors(false);
    }
  };

  // Load on-call schedules from database when location changes
  useEffect(() => {
    if (selectedLocation) {
      loadOnCallSchedules(selectedLocation.id);
    }
  }, [selectedLocation]);

  const loadOnCallSchedules = async (locationId: string) => {
    setLoadingSchedules(true);
    try {
      const response = await fetch(`/api/locations/${locationId}/on-call-schedules`);
      if (response.ok) {
        const data = await response.json();
        // Convert date strings back to Date objects
        const schedules = data.schedules.map((schedule: OnCallSchedule) => ({
          ...schedule,
          startDate: new Date(schedule.startDate),
          endDate: new Date(schedule.endDate),
        }));
        setOnCallSchedules(schedules);
        console.log('Loaded schedules from database:', schedules);
      } else {
        console.error('Failed to load schedules:', response.statusText);
        // Keep existing schedules or set empty array
        setOnCallSchedules([]);
      }
    } catch (error) {
      console.error('Error loading schedules:', error);
      // Keep existing schedules or set empty array
      setOnCallSchedules([]);
    } finally {
      setLoadingSchedules(false);
    }
  };

  const handleEditOfficeHours = () => {
    if (!selectedLocation) return;

    setEditingHours(selectedLocation.officeHours || {});
    setEditingTimezone(selectedLocation.timeZone || 'America/Chicago');
    setShowOfficeHoursModal(true);
  };

  const handleSaveOfficeHours = async () => {
    if (!selectedLocation) return;

    setSaving(true);
    try {
      // Validate office hours
      const validation = OfficeHoursService.validateOfficeHoursWithErrors(editingHours);
      if (!validation.isValid) {
        showError('Validation Error: ' + validation.errors.join(', '));
        return;
      }

      // Validate timezone
      if (!OfficeHoursService.validateTimezone(editingTimezone)) {
        showError('Invalid timezone. Please select a valid timezone.');
        return;
      }

      await onUpdate(selectedLocation.id, {
        officeHours: editingHours,
        timeZone: editingTimezone,
      });

      // Update local state
      const updatedLocation = {
        ...selectedLocation,
        officeHours: editingHours,
        timeZone: editingTimezone,
      };
      setSelectedLocation(updatedLocation);

      setShowOfficeHoursModal(false);
      showSuccess('Office hours updated successfully!');
    } catch {
      showError('Failed to update office hours. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleTimeChange = (day: string, field: 'start' | 'end', value: string) => {
    setEditingHours(prev => ({
      ...prev,
      [day]: prev[day]
        ? { ...prev[day], [field]: value }
        : { start: '09:00', end: '17:00', [field]: value },
    }));
  };

  const toggleDayOpen = (day: string, isOpen: boolean) => {
    setEditingHours(prev => ({
      ...prev,
      [day]: isOpen ? { start: '09:00', end: '17:00' } : null,
    }));
  };

  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessModal(true);
    // Auto-close after 3 seconds
    setTimeout(() => {
      setShowSuccessModal(false);
    }, 3000);
  };

  const showError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  const handleScheduleDoctor = (doctor: OnCallDoctor) => {
    console.log('Scheduling doctor:', doctor.name);
    setSelectedDoctor(doctor);
    setShowScheduleModal(true);

    // Set default dates - start today at 5 PM (after typical office hours)
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    setScheduleForm({
      startDate: today.toISOString().split('T')[0],
      startTime: '17:00', // 5 PM - after typical office hours
      endDate: tomorrow.toISOString().split('T')[0],
      endTime: '09:00', // 9 AM next day - before office hours start
      isPrimary: true,
    });
  };

  const handleSaveSchedule = async () => {
    console.log('Saving schedule...');
    if (!selectedDoctor || !selectedLocation) {
      console.error('Missing doctor or location');
      showError('Missing doctor or location information');
      return;
    }

    const startDateTime = new Date(`${scheduleForm.startDate}T${scheduleForm.startTime}`);
    const endDateTime = new Date(`${scheduleForm.endDate}T${scheduleForm.endTime}`);

    console.log('Schedule details:', {
      doctor: selectedDoctor.name,
      location: selectedLocation.name,
      start: startDateTime,
      end: endDateTime,
    });

    try {
      // Call API to save to database
      const response = await fetch(`/api/locations/${selectedLocation.id}/on-call-schedules`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          doctorId: selectedDoctor.id,
          doctorName: selectedDoctor.name,
          doctorPhone: selectedDoctor.phone,
          startDate: startDateTime.toISOString(),
          endDate: endDateTime.toISOString(),
          notes: `On-call schedule for ${selectedLocation.name}`,
          isPrimary: scheduleForm.isPrimary,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save schedule');
      }

      const result = await response.json();
      console.log('Schedule saved successfully:', result);

      // Reload schedules from server to get the most up-to-date data (including deactivated past schedules)
      await loadOnCallSchedules(selectedLocation.id);

      setShowScheduleModal(false);
      setSelectedDoctor(null);
      showSuccess(`On-call schedule saved to database successfully for ${selectedDoctor.name}!`);
    } catch (error) {
      console.error('Error saving schedule:', error);
      showError(
        `Failed to save schedule: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  };

  const handleRemoveSchedule = async (schedule: OnCallSchedule) => {
    if (!selectedLocation) {
      showError('Location information is missing');
      return;
    }

    try {
      console.log('Removing schedule:', schedule.id);

      // Call DELETE endpoint to deactivate the schedule
      const response = await fetch(
        `/api/locations/${selectedLocation.id}/on-call-schedules?scheduleId=${schedule.id}`,
        {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to remove schedule');
      }

      console.log('Schedule removed successfully');

      // Reload schedules to reflect the change
      await loadOnCallSchedules(selectedLocation.id);

      showSuccess(`Successfully removed on-call schedule for ${schedule.doctorName}`);
    } catch (error) {
      console.error('Error removing schedule:', error);
      showError(
        `Failed to remove schedule: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  };

  const getMonthDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of the month
    const firstDay = new Date(year, month, 1);

    // Get the first Monday of the calendar (might be from previous month)
    const startDate = new Date(firstDay);
    const dayOfWeek = firstDay.getDay();
    const daysToSubtract = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Monday = 0
    startDate.setDate(firstDay.getDate() - daysToSubtract);

    // Generate 6 weeks (42 days) to ensure full month coverage
    const days = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      days.push(date);
    }

    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const getCurrentStatus = () => {
    if (!selectedLocation) return null;

    return OfficeHoursService.checkOfficeHours(
      selectedLocation.officeHours || {},
      selectedLocation.timeZone || 'America/Chicago',
    );
  };

  const getScheduleForDay = (date: Date) => {
    if (!selectedLocation) return { officeHours: null, onCall: [] };

    const dayOfWeek = date.getDay();
    const dayKey = (dayOfWeek === 0 ? 7 : dayOfWeek).toString();
    const officeHours = selectedLocation.officeHours?.[dayKey] || null;

    // Get all on-call schedules for this day
    const allOnCall = onCallSchedules.filter(schedule => {
      if (schedule.locationId !== selectedLocation.id || !schedule.isActive) return false;

      const dateStart = new Date(date);
      dateStart.setHours(0, 0, 0, 0);
      const dateEnd = new Date(date);
      dateEnd.setHours(23, 59, 59, 999);

      return schedule.startDate <= dateEnd && schedule.endDate >= dateStart;
    });

    // Deduplicate schedules by doctorId and isPrimary to avoid showing duplicates
    // when a doctor has multi-day schedules that span across multiple database records
    const uniqueOnCall = allOnCall.reduce((unique: OnCallSchedule[], schedule) => {
      const existingIndex = unique.findIndex(
        existing =>
          existing.doctorId === schedule.doctorId && existing.isPrimary === schedule.isPrimary,
      );

      if (existingIndex === -1) {
        unique.push(schedule);
      }

      return unique;
    }, []);

    return {
      officeHours,
      onCall: uniqueOnCall,
    };
  };

  // Compute unique on-call doctors for the selected location (active only)
  const uniqueOnCallDoctorCount = selectedLocation
    ? new Set(
        onCallSchedules
          .filter(s => s.locationId === selectedLocation.id && s.isActive)
          .map(s => s.doctorId),
      ).size
    : 0;

  const status = getCurrentStatus();
  const monthDays = getMonthDays();

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Calendar className="mr-3 text-blue-600" size={32} />
              Office Hours & On-Call Management
            </h1>
            <p className="text-gray-600 mt-2">
              Manage location schedules and on-call doctor assignments
            </p>
          </div>
        </div>

        {selectedLocation && (
          <>
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="flex items-center">
                  <div className="p-3 bg-blue-100 rounded-lg">
                    <Calendar className="text-blue-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Current Status</p>
                    <p className="text-lg font-semibold">
                      {status?.isOpen ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Open
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Closed
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="flex items-center">
                  <div className="p-3 bg-orange-100 rounded-lg">
                    <User className="text-orange-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">On-Call Doctors</p>
                    <p className="text-lg font-semibold">{uniqueOnCallDoctorCount}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="flex items-center">
                  <div className="p-3 bg-green-100 rounded-lg">
                    <Clock className="text-green-600" size={24} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Timezone</p>
                    <p className="text-lg font-semibold text-gray-900">
                      {selectedLocation.timeZone || 'America/Chicago'}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4 mb-6">
              <button
                onClick={handleEditOfficeHours}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Settings className="mr-2" size={16} />
                Edit Office Hours
              </button>
              <button
                onClick={() => setShowOnCallModal(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <User className="mr-2" size={16} />
                Manage On-Call
              </button>
            </div>

            {/* Calendar View */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-900">
                    Schedule Calendar - {selectedLocation.name}
                  </h2>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => navigateMonth('prev')}
                        className="p-2 hover:bg-gray-100 rounded-lg"
                      >
                        <ChevronLeft size={16} />
                      </button>
                      <span className="text-sm font-medium px-3">
                        {currentDate.toLocaleDateString('en-US', {
                          month: 'long',
                          year: 'numeric',
                        })}
                      </span>
                      <button
                        onClick={() => navigateMonth('next')}
                        className="p-2 hover:bg-gray-100 rounded-lg"
                      >
                        <ChevronRight size={16} />
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-7 gap-4">
                  {/* Day Headers */}
                  {DAYS_OF_WEEK.map(day => (
                    <div key={day.key} className="text-center">
                      <div className="font-medium text-gray-900">{day.short}</div>
                    </div>
                  ))}

                  {/* Day Cells */}
                  {monthDays.map((date: Date, index: number) => {
                    const daySchedule = getScheduleForDay(date);
                    const isToday = date.toDateString() === new Date().toDateString();
                    const isCurrentMonth = date.getMonth() === currentDate.getMonth();

                    return (
                      <div
                        key={index}
                        className={`p-2 border rounded-lg min-h-[120px] ${
                          isToday
                            ? 'border-blue-300 bg-blue-50'
                            : isCurrentMonth
                              ? 'border-gray-200 bg-white'
                              : 'border-gray-100 bg-gray-50'
                        }`}
                      >
                        <div
                          className={`text-sm font-medium mb-2 ${
                            isToday
                              ? 'text-blue-900'
                              : isCurrentMonth
                                ? 'text-gray-900'
                                : 'text-gray-400'
                          }`}
                        >
                          {date.getDate()}
                        </div>

                        <div className="space-y-2">
                          {/* Office Hours */}
                          {daySchedule.officeHours ? (
                            <div className="text-xs p-2 bg-green-100 text-green-800 rounded border-l-2 border-green-500">
                              <div className="flex items-center">
                                <Clock size={12} className="mr-1" />
                                <span>Office Hours</span>
                              </div>
                              <div className="font-mono text-xs">
                                {daySchedule.officeHours.start} - {daySchedule.officeHours.end}
                              </div>
                            </div>
                          ) : (
                            <div className="text-xs p-2 bg-gray-100 text-gray-600 rounded">
                              <div className="flex items-center">
                                <X size={12} className="mr-1" />
                                <span>Closed</span>
                              </div>
                            </div>
                          )}

                          {/* On-Call During Office Hours */}
                          {daySchedule.onCall.map(schedule => (
                            <div
                              key={schedule.id}
                              className={`text-xs p-2 rounded border-l-2 \
                                ${
                                  schedule.isPrimary
                                    ? 'bg-orange-100 text-orange-800 border-orange-500'
                                    : 'bg-blue-100 text-blue-800 border-blue-500'
                                }
                              `}
                            >
                              <div className="flex items-center">
                                <User size={12} className="mr-1" />
                                <span>{schedule.isPrimary ? 'On-Call' : 'On-Call Backup'}</span>
                              </div>
                              <div className="font-medium text-xs truncate">
                                {schedule.doctorName}
                              </div>
                              <div className="text-xs opacity-75">{schedule.contactInfo.phone}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </>
        )}
      </div>

      {/* Office Hours Modal */}
      {showOfficeHoursModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Edit Office Hours - {selectedLocation?.name}
              </h2>
            </div>

            <div className="p-6 space-y-6">
              {/* Timezone Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                <select
                  value={editingTimezone}
                  onChange={e => setEditingTimezone(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {TIMEZONES.map(tz => (
                    <option key={tz} value={tz}>
                      {tz}
                    </option>
                  ))}
                </select>
              </div>

              {/* Days of Week */}
              <div className="space-y-4">
                <label className="block text-sm font-medium text-gray-700">Weekly Schedule</label>
                {DAYS_OF_WEEK.map(day => {
                  const dayHours = editingHours[day.key];
                  const isOpen = dayHours !== null && dayHours !== undefined;

                  return (
                    <div
                      key={day.key}
                      className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg"
                    >
                      <div className="w-20">
                        <span className="font-medium text-sm">{day.short}</span>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={isOpen}
                          onChange={e => toggleDayOpen(day.key, e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label className="ml-2 text-sm text-gray-700">Open</label>
                      </div>

                      {isOpen && (
                        <div className="flex items-center space-x-2 flex-1">
                          <input
                            type="time"
                            value={dayHours?.start || '09:00'}
                            onChange={e => handleTimeChange(day.key, 'start', e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                          <span className="text-gray-500">to</span>
                          <input
                            type="time"
                            value={dayHours?.end || '17:00'}
                            onChange={e => handleTimeChange(day.key, 'end', e.target.value)}
                            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          />
                        </div>
                      )}

                      {!isOpen && (
                        <div className="flex-1">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Closed
                          </span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => setShowOfficeHoursModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveOfficeHours}
                  disabled={saving}
                  className="px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Hours'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* On-Call Management Modal */}
      {showOnCallModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Manage On-Call Doctors - {selectedLocation?.name}
              </h2>
            </div>

            <div className="p-6">
              <div className="flex space-x-1 mb-6">
                <button
                  onClick={() => setSelectedTab('schedules')}
                  className={`px-4 py-2 text-sm font-medium rounded-lg ${
                    selectedTab === 'schedules'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Current Schedules
                </button>
                <button
                  onClick={() => setSelectedTab('doctors')}
                  className={`px-4 py-2 text-sm font-medium rounded-lg ${
                    selectedTab === 'doctors'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  Available Doctors
                </button>
              </div>

              {selectedTab === 'schedules' && (
                <div className="space-y-4">
                  <div className="space-y-3">
                    {onCallSchedules
                      .filter(schedule => schedule.locationId === selectedLocation?.id)
                      .map(schedule => (
                        <div key={schedule.id} className="p-4 border border-gray-200 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <User className="text-orange-600" size={20} />
                              <div>
                                <p className="font-medium">{schedule.doctorName}</p>
                                <p className="text-sm text-gray-600">
                                  {schedule.startDate.toLocaleDateString()}{' '}
                                  {schedule.startDate.toLocaleTimeString()} -
                                  {schedule.endDate.toLocaleDateString()}{' '}
                                  {schedule.endDate.toLocaleTimeString()}
                                </p>
                                <p className="text-sm text-gray-500">
                                  {schedule.contactInfo.phone}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  schedule.isActive
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {schedule.isActive ? 'Active' : 'Inactive'}
                              </span>
                              <span
                                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                  schedule.isPrimary
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-gray-100 text-gray-800'
                                }`}
                              >
                                {schedule.isPrimary ? 'Primary' : 'Backup'}
                              </span>
                              {schedule.isActive && (
                                <button
                                  onClick={() => handleRemoveSchedule(schedule)}
                                  className="px-3 py-1 bg-red-600 text-white text-xs rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                  Remove
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>

                  <div className="text-center p-4 text-gray-500 text-sm">
                    To schedule a new on-call period, go to the &quot;Available Doctors&quot; tab
                    and click &quot;Schedule&quot; next to a doctor.
                  </div>
                </div>
              )}

              {selectedTab === 'doctors' && (
                <div className="space-y-3">
                  {loadingDoctors ? (
                    <div className="flex items-center justify-center p-8">
                      <div className="flex items-center space-x-2 text-gray-600">
                        <svg
                          className="animate-spin h-5 w-5"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        <span>Loading eligible doctors...</span>
                      </div>
                    </div>
                  ) : onCallDoctors.length === 0 ? (
                    <div className="text-center p-8 text-gray-500">
                      <User className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                      <p className="text-lg font-medium text-gray-900 mb-2">
                        No eligible doctors found
                      </p>
                      <p className="text-sm">
                        No doctors are currently eligible for on-call duty at this location.
                      </p>
                    </div>
                  ) : (
                    onCallDoctors.map(doctor => (
                      <div key={doctor.id} className="p-4 border border-gray-200 rounded-lg">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <User className="text-blue-600" size={20} />
                            <div>
                              <p className="font-medium">{doctor.name}</p>
                              <p className="text-sm text-gray-600">
                                {doctor.phone} • {doctor.email}
                              </p>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {doctor.specialties.map(specialty => (
                                  <span
                                    key={specialty}
                                    className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                                  >
                                    {specialty}
                                  </span>
                                ))}
                              </div>
                            </div>
                          </div>
                          <button
                            onClick={() => {
                              console.log('Schedule button clicked for:', doctor.name);
                              handleScheduleDoctor(doctor);
                            }}
                            className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Schedule
                          </button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}

              <div className="flex justify-end pt-4 border-t border-gray-200 mt-6">
                <button
                  onClick={() => setShowOnCallModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Schedule Modal */}
      {showScheduleModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                Schedule On-Call Period
                {selectedDoctor && (
                  <div className="text-sm font-normal text-gray-600 mt-1">
                    {selectedDoctor.name}
                  </div>
                )}
              </h2>
            </div>

            <div className="p-6 space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                  <input
                    type="date"
                    value={scheduleForm.startDate}
                    onChange={e =>
                      setScheduleForm(prev => ({ ...prev, startDate: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                  <input
                    type="time"
                    value={scheduleForm.startTime}
                    onChange={e =>
                      setScheduleForm(prev => ({ ...prev, startTime: e.target.value }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                  <input
                    type="date"
                    value={scheduleForm.endDate}
                    onChange={e => setScheduleForm(prev => ({ ...prev, endDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                  <input
                    type="time"
                    value={scheduleForm.endTime}
                    onChange={e => setScheduleForm(prev => ({ ...prev, endTime: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={!scheduleForm.isPrimary}
                    onChange={e =>
                      setScheduleForm(prev => ({ ...prev, isPrimary: !e.target.checked }))
                    }
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    id="isBackup"
                  />
                  <label htmlFor="isBackup" className="text-sm font-medium text-gray-700 mb-0">
                    Backup Doctor
                  </label>
                </div>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  onClick={() => {
                    setShowScheduleModal(false);
                    setSelectedDoctor(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveSchedule}
                  className="px-4 py-2 bg-blue-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Schedule
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
            <div className="p-6 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Success!</h3>
              <p className="text-sm text-gray-600 mb-6">{successMessage}</p>
              <button
                onClick={() => setShowSuccessModal(false)}
                className="w-full px-4 py-2 bg-green-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Continue
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Modal */}
      {showErrorModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShowErrorModal(false)}
        >
          <div
            className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4"
            onClick={e => e.stopPropagation()}
          >
            <div className="p-6">
              {/* Close button in top right */}
              <div className="flex justify-end mb-2">
                <button
                  onClick={() => setShowErrorModal(false)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>

              <div className="text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg
                    className="h-6 w-6 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
                <p className="text-sm text-gray-600 mb-6">{errorMessage}</p>

                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowErrorModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => setShowErrorModal(false)}
                    className="flex-1 px-4 py-2 bg-red-600 border border-transparent rounded-lg text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
