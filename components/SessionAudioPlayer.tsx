import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { isVoicemailUrl } from '@/utils/voicemail';

interface SessionAudioPlayerProps {
  voicemailUrl: string;
  minTimestamp?: number;
  bucketName?: string;
  autoPlay?: boolean;
  onError?: (error: string) => void;
}

/**
 * Component for playing the latest audio file for a specific session ID
 */
const SessionAudioPlayer = forwardRef<HTMLDivElement, SessionAudioPlayerProps>(
  ({ voicemailUrl, minTimestamp, bucketName, autoPlay = false, onError }, ref) => {
    const [audioUrl, setAudioUrl] = useState<string | null>(null);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [fileName, setFileName] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    // State to track audio playback status
    const audioRef = useRef<HTMLAudioElement>(null);

    useEffect(() => {
      const fetchSessionAudio = async () => {
        if (!voicemailUrl) return;

        try {
          setLoading(true);
          setError(null);
          try {
            // Check if the URL is already a storage reference
            if (!isVoicemailUrl(voicemailUrl)) {
              console.log('URL is not a storage reference, using direct URL');
            } else {
              console.log('URL is a storage reference, using storage reference format');
            }

            // Always use the conversion API
            const convertUrl = `/api/storage/convert-audio?url=${encodeURIComponent(voicemailUrl)}&format=mp3`;
            setAudioUrl(convertUrl);

            // Extract file name from voicemail URL if it's a storage reference
            if (isVoicemailUrl(voicemailUrl)) {
              const match = voicemailUrl.match(/^gcp:\/\/[^\/]+\/(.+)$/);
              if (match && match[1]) {
                setFileName(match[1]);
              } else {
                setFileName('Voicemail Recording');
              }
            } else {
              setFileName('Voicemail Recording');
            }
          } catch (jsonError) {
            console.error('Failed to parse JSON response:', jsonError);
            throw new Error('Invalid response format from server');
          }
        } catch (err) {
          const errorMsg =
            err instanceof Error
              ? err.message
              : 'Failed to load audio file. Please try again later.';

          console.error('Error fetching session audio:', err);
          setError(errorMsg);
          if (onError) onError(errorMsg);
        } finally {
          setLoading(false);
        }
      };

      fetchSessionAudio();
    }, [voicemailUrl, minTimestamp, bucketName, onError]);

    // Function to format file name was removed as it's not being used

    // Handle audio play/pause events
    const handlePlay = () => {
      // Audio started playing
    };

    const handlePause = () => {
      // Audio paused
    };

    // Handle audio error
    const handleAudioError = () => {
      console.error('Audio playback failed');
      setError(
        'Unable to play the audio file. The conversion may have failed or the file format is not supported.',
      );
      if (onError) onError('Audio playback failed');
    };

    if (loading) {
      return <div className="audio-loading">Loading audio file...</div>;
    }

    if (error) {
      return <div className="audio-error">{error}</div>;
    }

    if (!audioUrl) {
      return <div className="audio-not-found">No audio file found for this session.</div>;
    }

    return (
      <div className="session-audio-player" ref={ref}>
        {/*<div className="header">*/}
        {/*  <h3>{getDisplayName()}</h3>*/}
        {/*</div>*/}

        <audio
          ref={audioRef}
          controls
          autoPlay={autoPlay}
          src={audioUrl}
          onPlay={handlePlay}
          onPause={handlePause}
          onError={handleAudioError}
        >
          Your browser does not support the audio element.
        </audio>

        <style jsx>{`
          .session-audio-player {
            margin: 15px 0;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
          }

          .header {
            margin-bottom: 10px;
          }

          h3 {
            margin: 0;
            font-size: 16px;
            color: #333;
          }

          audio {
            width: 100%;
          }

          .audio-loading,
          .audio-error,
          .audio-not-found {
            padding: 10px;
            border-radius: 4px;
            text-align: center;
          }

          .audio-loading {
            background-color: #f0f0f0;
            color: #666;
          }

          .audio-error {
            background-color: #fff0f0;
            color: #d32f2f;
          }

          .audio-not-found {
            background-color: #f0f0f0;
            color: #666;
          }
        `}</style>
      </div>
    );
  },
);

// Add display name to the component
SessionAudioPlayer.displayName = 'SessionAudioPlayer';

export default SessionAudioPlayer;
