'use client';

import { useEffect, useState } from 'react';
import SwaggerUIReact from 'swagger-ui-react';
import 'swagger-ui-react/swagger-ui.css';

interface SwaggerUIProps {
  url?: string;
  spec?: Record<string, unknown>;
}

/**
 * SwaggerUI component for displaying API documentation
 * This component can either load a spec from a URL or use a provided spec object
 * It handles fetching the spec from the URL to avoid CORS issues
 */
const SwaggerUI = ({ url, spec: initialSpec }: SwaggerUIProps) => {
  const [isClient, setIsClient] = useState(false);
  const [spec, setSpec] = useState<Record<string, unknown> | undefined>(initialSpec);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch the spec from the URL if provided
  useEffect(() => {
    if (url && !initialSpec) {
      setIsLoading(true);
      setError(null);

      fetch(url)
        .then(response => {
          if (!response.ok) {
            throw new Error(`Failed to fetch spec: ${response.status} ${response.statusText}`);
          }
          return response.json();
        })
        .then(data => {
          setSpec(data);
          setIsLoading(false);
        })
        .catch(err => {
          console.error('Error fetching Swagger spec:', err);
          setError(err.message || 'Failed to fetch API specification');
          setIsLoading(false);
        });
    }
  }, [url, initialSpec]);

  // This ensures the component only renders on the client side
  // to avoid SSR issues with Swagger UI
  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <div>Loading API documentation...</div>;
  }

  if (isLoading) {
    return <div>Loading API specification...</div>;
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded mb-4">
        <h3 className="font-bold">Error Loading API Documentation</h3>
        <p>{error}</p>
        <p className="mt-2">
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Retry
          </button>
        </p>
      </div>
    );
  }

  // Always use the spec object, which is either provided directly or fetched from the URL
  return <SwaggerUIReact spec={spec} />;
};

export default SwaggerUI;
