'use client';

import { Button as FlowbiteButton, ButtonProps } from 'flowbite-react';
import React from 'react';

// This component wraps the Flowbite Button component and applies our custom styling
const CustomButton: React.FC<ButtonProps> = ({
  children,
  className = '',
  color = 'blue', // Default to blue which will use our custom color
  ...props
}) => {
  // Add our custom class to ensure the button uses our styling
  const buttonClassName = `flowbite-button ${className}`;

  return (
    <FlowbiteButton className={buttonClassName} color={color} {...props}>
      {children}
    </FlowbiteButton>
  );
};

export default CustomButton;
