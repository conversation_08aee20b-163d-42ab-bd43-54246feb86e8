import { useState, FormEvent } from 'react';
import { Label, TextInput, Alert } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { updateUserPassword } from '@/utils/firebase';
import { HiCheck, HiExclamation } from 'react-icons/hi';

interface PasswordChangeFormProps {
  onSuccess?: () => void;
}

const PasswordChangeForm = ({ onSuccess }: PasswordChangeFormProps) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Password validation
  const validatePassword = (password: string): boolean => {
    // Password must be at least 8 characters long and contain at least one number
    return password.length >= 8 && /\d/.test(password);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate passwords
    if (!validatePassword(newPassword)) {
      setError('New password must be at least 8 characters long and contain at least one number');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      await updateUserPassword(currentPassword, newPassword);
      setSuccess('Password changed successfully');

      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('re-authenticate')) {
          setError('Current password is incorrect');
        } else {
          setError(`Failed to change password: ${error.message}`);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
      console.error('Password change error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Change Password</h3>

      {error && (
        <Alert color="failure" icon={HiExclamation}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" icon={HiCheck}>
          {success}
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <div className="mb-1">
            <Label htmlFor="current-password" value="Current Password" />
          </div>
          <TextInput
            id="current-password"
            type="password"
            placeholder="••••••••"
            value={currentPassword}
            onChange={e => setCurrentPassword(e.target.value)}
            required
          />
        </div>

        <div>
          <div className="mb-1">
            <Label htmlFor="new-password" value="New Password" />
          </div>
          <TextInput
            id="new-password"
            type="password"
            placeholder="••••••••"
            value={newPassword}
            onChange={e => setNewPassword(e.target.value)}
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            Password must be at least 8 characters long and contain at least one number
          </p>
        </div>

        <div>
          <div className="mb-1">
            <Label htmlFor="confirm-password" value="Confirm New Password" />
          </div>
          <TextInput
            id="confirm-password"
            type="password"
            placeholder="••••••••"
            value={confirmPassword}
            onChange={e => setConfirmPassword(e.target.value)}
            required
          />
        </div>

        <CustomButton type="submit" color="blue" disabled={isLoading}>
          {isLoading ? 'Changing Password...' : 'Change Password'}
        </CustomButton>
      </form>
    </div>
  );
};

export default PasswordChangeForm;
