import React, { useState } from 'react';
import { FiChevronDown } from 'react-icons/fi';
import { HiOfficeBuilding } from 'react-icons/hi';
import { usePracticeContext } from './PracticeContext';

interface PracticeSelectorProps {
  className?: string;
}

const PracticeSelector: React.FC<PracticeSelectorProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { currentPractice, availablePractices, loading, switchPractice } = usePracticeContext();

  const handlePracticeChange = (practiceId: string) => {
    switchPractice(practiceId);
    setIsOpen(false);
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-white/10 rounded-lg px-3 py-2 text-sm">
          <div className="h-4 bg-white/20 rounded w-32"></div>
        </div>
      </div>
    );
  }

  // Don't show selector if there's only one practice or no practices
  if (availablePractices.length <= 1) {
    return null;
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 text-white/90 hover:text-white transition-colors duration-200 bg-white/10 hover:bg-white/20 rounded-lg px-3 py-2 text-sm font-medium w-full"
      >
        <HiOfficeBuilding className="h-4 w-4" />
        <span className="flex-1 text-left truncate">
          {currentPractice?.name || 'Select Practice'}
        </span>
        <FiChevronDown
          className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-10" onClick={() => setIsOpen(false)} />

          {/* Dropdown */}
          <div className="absolute left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 z-20 max-h-64 overflow-y-auto">
            {availablePractices.map(practice => (
              <button
                key={practice.id}
                onClick={() => handlePracticeChange(practice.id)}
                className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors duration-150 border-b border-gray-100 last:border-b-0 ${
                  currentPractice?.id === practice.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <HiOfficeBuilding className="h-4 w-4 text-gray-400" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{practice.name}</div>
                    {practice.description && (
                      <div className="text-sm text-gray-500 truncate">{practice.description}</div>
                    )}
                  </div>
                  {currentPractice?.id === practice.id && (
                    <div className="h-2 w-2 bg-blue-600 rounded-full"></div>
                  )}
                </div>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default PracticeSelector;
