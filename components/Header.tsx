'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { fetchCurrentUser } from '@/utils/auth';
import { User, UserRole } from '@/models/auth';
import LocationSelector from './LocationSelector';

// Enum for page paths and titles
enum PageInfo {
  DASHBOARD = '/dashboard',
  PROFILE = '/dashboard/profile',
  CALLS = '/dashboard/calls',
  CALL_DETAILS = '/dashboard/calls/',
  CLIENT_INFO = '/dashboard/client/',
  STAFF = '/dashboard/staff',
  STAFF_DETAILS = '/dashboard/staff/',
  SETTINGS = '/dashboard/settings',
}

// Mapping from page paths to titles
const PAGE_TITLES: Record<PageInfo, string> = {
  [PageInfo.DASHBOARD]: 'Dashboard',
  [PageInfo.PROFILE]: 'My Profile',
  [PageInfo.CALLS]: 'Patient Call Records',
  [PageInfo.CALL_DETAILS]: 'Call Details',
  [PageInfo.CLIENT_INFO]: 'Patient Information',
  [PageInfo.STAFF]: 'Staff Management',
  [PageInfo.STAFF_DETAILS]: 'Staff Details',
  [PageInfo.SETTINGS]: 'Settings',
};

const Header = () => {
  const [staff, setStaff] = useState<User | null>(null);
  const pathname = usePathname();

  useEffect(() => {
    let isMounted = true;

    // Only fetch user data once when the component mounts
    const loadStaff = async () => {
      try {
        // Check if we already have staff data to avoid unnecessary fetch
        if (!staff) {
          const staffData = await fetchCurrentUser();
          if (isMounted) {
            setStaff(staffData);
          }
        }
      } catch (error) {
        console.error('Error loading staff data:', error);
      }
    };

    loadStaff();

    // Cleanup function to prevent setting state on unmounted component
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run on mount

  const getPageTitle = () => {
    // Check for exact matches first
    if (pathname === PageInfo.DASHBOARD || pathname === `${PageInfo.DASHBOARD}/`) {
      return PAGE_TITLES[PageInfo.DASHBOARD];
    }
    if (pathname === PageInfo.PROFILE) {
      return PAGE_TITLES[PageInfo.PROFILE];
    }
    if (pathname === PageInfo.CALLS) {
      return PAGE_TITLES[PageInfo.CALLS];
    }
    if (pathname === PageInfo.STAFF) {
      return PAGE_TITLES[PageInfo.STAFF];
    }

    // Check for path starts with patterns
    if (pathname?.startsWith(PageInfo.CALL_DETAILS)) {
      return PAGE_TITLES[PageInfo.CALL_DETAILS];
    }
    if (pathname?.startsWith(PageInfo.CLIENT_INFO)) {
      return PAGE_TITLES[PageInfo.CLIENT_INFO];
    }
    if (pathname?.startsWith(PageInfo.STAFF_DETAILS)) {
      return PAGE_TITLES[PageInfo.STAFF_DETAILS];
    }
    if (pathname?.startsWith(PageInfo.SETTINGS)) {
      return PAGE_TITLES[PageInfo.SETTINGS];
    }

    // Default title if no match is found
    return staff?.role === UserRole.STAFF ? 'Staff Portal' : 'Doctor Portal';
  };

  return (
    <header className="bg-frontdesk-primary shadow-sm px-4 py-4 md:py-8">
      <div className="flex justify-between items-center">
        <div className="flex-1 ml-12 md:ml-0 text-left md:flex-none">
          <h1 className="text-xl md:text-2xl font-bold text-white">{getPageTitle()}</h1>
        </div>
        <div className="flex items-center space-x-4">
          <LocationSelector />
        </div>
      </div>
    </header>
  );
};

export default Header;
