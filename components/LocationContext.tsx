import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Location } from '@/models/Location';
import { Practice } from '@/models/Practice';
import { getToken } from '@/utils/auth';

interface LocationContextType {
  currentLocation: Location | null;
  availableLocations: Location[];
  allLocations: Location[];
  availablePractices: Practice[];
  loading: boolean;
  switchLocation: (locationId: string) => Promise<void>;
  refreshLocationContext: () => Promise<void>;
}

interface LocationContextResponse {
  currentLocation?: Location;
  availableLocations: Location[];
  availablePractices: Practice[];
}

const LocationContext = createContext<LocationContextType | undefined>(undefined);

export const useLocationContext = (): LocationContextType => {
  const context = useContext(LocationContext);
  if (!context) {
    throw new Error('useLocationContext must be used within a LocationProvider');
  }
  return context;
};

interface LocationProviderProps {
  children: ReactNode;
}

export const LocationProvider: React.FC<LocationProviderProps> = ({ children }) => {
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [availableLocations, setAvailableLocations] = useState<Location[]>([]);
  const [allLocations, setAllLocations] = useState<Location[]>([]);
  const [availablePractices, setAvailablePractices] = useState<Practice[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch user's location context
  const fetchLocationContext = async () => {
    try {
      setLoading(true);

      // Fetch user's available location context
      const response = await fetch('/api/staff/me/location', {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch location context');
      }

      const data: LocationContextResponse = await response.json();
      setCurrentLocation(data.currentLocation || null);
      setAvailableLocations(data.availableLocations || []);
      setAvailablePractices(data.availablePractices || []);

      // Try to fetch all locations for the clinic
      // If this fails, we'll fallback to using available locations
      try {
        const allLocationsResponse = await fetch('/api/locations?includeInactive=true', {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (allLocationsResponse.ok) {
          const allLocationsData = await allLocationsResponse.json();
          setAllLocations(allLocationsData.locations || []);
        } else {
          console.warn('Failed to fetch all locations, using available locations as fallback');
          setAllLocations(data.availableLocations || []);
        }
      } catch (allLocationsError) {
        console.warn(
          'Error fetching all locations, using available locations as fallback:',
          allLocationsError,
        );
        // Fallback: use available locations as all locations
        setAllLocations(data.availableLocations || []);
      }
    } catch (error) {
      console.error('Error fetching location context:', error);
    } finally {
      setLoading(false);
    }
  };

  // Switch to a different location
  const switchLocation = async (locationId: string) => {
    try {
      const response = await fetch('/api/staff/me/location', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify({ locationId }),
      });

      if (!response.ok) {
        throw new Error('Failed to switch location');
      }

      const data = await response.json();
      setCurrentLocation(data.currentLocation);

      // Refresh the page to update all location-scoped data
      window.location.reload();
    } catch (error) {
      console.error('Error switching location:', error);
      throw error;
    }
  };

  // Refresh location context
  const refreshLocationContext = async () => {
    await fetchLocationContext();
  };

  useEffect(() => {
    fetchLocationContext();
  }, []);

  const value: LocationContextType = {
    currentLocation,
    availableLocations,
    allLocations,
    availablePractices,
    loading,
    switchLocation,
    refreshLocationContext,
  };

  return <LocationContext.Provider value={value}>{children}</LocationContext.Provider>;
};
