'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from './Sidebar';
import Header from './Header';
import { isAuthenticated } from '@/utils/auth';
import { HiMenu } from 'react-icons/hi';
import { PracticeProvider } from './PracticeContext';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    if (!isAuthenticated()) {
      router.push('/login');
    } else {
      setIsLoading(false);
    }
  }, [router]);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (window.innerWidth < 768 && sidebarOpen && !target.closest('.sidebar-container')) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarOpen]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <PracticeProvider>
      <div className="flex h-screen bg-gray-50 relative overflow-hidden">
        {/* Mobile menu button */}
        <button
          className="md:hidden fixed top-3 left-4 z-20 p-2 rounded-md bg-white shadow-md"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          <HiMenu className="h-6 w-6" />
        </button>

        {/* Sidebar - hidden on mobile unless toggled */}
        <div
          className={`sidebar-container fixed md:relative z-10 h-full transition-transform duration-300 ${
            sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
          }`}
        >
          <Sidebar closeSidebar={() => setSidebarOpen(false)} />
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col w-full max-w-full overflow-hidden">
          <Header />
          <main className="flex-1 p-4 md:p-6 w-full max-w-full overflow-y-auto overflow-x-hidden">
            {children}
          </main>
        </div>
      </div>
    </PracticeProvider>
  );
};

export default DashboardLayout;
