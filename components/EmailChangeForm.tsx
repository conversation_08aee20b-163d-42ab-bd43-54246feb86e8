import { useState, FormEvent } from 'react';
import { Label, TextInput, Alert } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { HiCheck, HiExclamation } from 'react-icons/hi';
import { updateUserEmail } from '@/utils/firebase';

interface EmailChangeFormProps {
  currentEmail: string;
  onSuccess?: () => void;
}

const EmailChangeForm = ({ currentEmail, onSuccess }: EmailChangeFormProps) => {
  const [password, setPassword] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Email validation
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate email
    if (!validateEmail(newEmail)) {
      setError('Please enter a valid email address');
      return;
    }

    // Check if new email is different from current email
    if (newEmail === currentEmail) {
      setError('New email must be different from your current email');
      return;
    }

    setIsLoading(true);

    try {
      // Use the updated updateUserEmail function which handles re-authentication and email update
      await updateUserEmail(password, newEmail);

      // If successful, show success message
      setSuccess(
        'A verification email has been sent to your new address. Please check your inbox (and spam folder) and click the verification link to complete the email change. For testing purposes, the link may also open in a new tab.',
      );

      // Clear form
      setPassword('');
      setNewEmail('');

      // Force reload the user data after a short delay to allow Firebase to update
      setTimeout(() => {
        if (onSuccess) {
          onSuccess();
        }
      }, 1000);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('re-authenticate') || error.message.includes('incorrect')) {
          setError('Password is incorrect');
        } else if (error.message.includes('email-already-in-use')) {
          setError('This email is already in use by another account');
        } else if (
          error.message.includes('must be verified') ||
          error.message.includes('verification is required')
        ) {
          setError(error.message);
          // If verification is required, we should have sent a verification email automatically
          setSuccess(
            'A verification email has been sent. Please check your inbox and spam folder.',
          );
        } else if (error.message.includes('invalid-email')) {
          setError('Please enter a valid email address');
        } else if (error.message.includes('requires-recent-login')) {
          setError(
            'For security reasons, please log out and log back in before changing your email',
          );
        } else {
          setError(`Failed to change email: ${error.message}`);
        }
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
      console.error('Email change error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Change Email Address</h3>

      {error && (
        <Alert color="failure" icon={HiExclamation}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" icon={HiCheck}>
          {success}
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <div className="mb-1">
            <Label htmlFor="current-email" value="Current Email" />
          </div>
          <TextInput id="current-email" type="email" value={currentEmail} disabled />
        </div>

        <div>
          <div className="mb-1">
            <Label htmlFor="new-email" value="New Email" />
          </div>
          <TextInput
            id="new-email"
            type="email"
            placeholder="<EMAIL>"
            value={newEmail}
            onChange={e => setNewEmail(e.target.value)}
            required
          />
        </div>

        <div>
          <div className="mb-1">
            <Label htmlFor="password" value="Current Password" />
          </div>
          <TextInput
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={e => setPassword(e.target.value)}
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            For security, please enter your current password to confirm this change
          </p>
        </div>

        <CustomButton type="submit" color="blue" disabled={isLoading}>
          {isLoading ? 'Changing Email...' : 'Change Email'}
        </CustomButton>
      </form>
    </div>
  );
};

export default EmailChangeForm;
