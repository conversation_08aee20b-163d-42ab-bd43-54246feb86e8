import React, { useState, useEffect, useRef } from 'react';
import { getVoicemailSignedUrl, isVoicemailUrl } from '@/utils/voicemail';

interface VoicemailPlayerProps {
  voicemailUrl: string;
  autoPlay?: boolean;
  onError?: (error: string) => void;
  className?: string;
}

const VoicemailPlayer: React.FC<VoicemailPlayerProps> = ({
  voicemailUrl,
  autoPlay = false,
  onError,
  className = '',
}) => {
  const [signedUrl, setSignedUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const fetchSignedUrl = async () => {
      if (!voicemailUrl) return;

      try {
        setLoading(true);
        setError(null);

        // Check if it's a voicemail URL
        if (!isVoicemailUrl(voicemailUrl)) {
          setSignedUrl(voicemailUrl);
          return;
        }

        // Get a signed URL for the voicemail
        const url = await getVoicemailSignedUrl(voicemailUrl);
        setSignedUrl(url);
      } catch (err) {
        const errorMsg =
          err instanceof Error ? err.message : 'Failed to load voicemail. Please try again later.';

        console.error('Error fetching voicemail signed URL:', err);
        setError(errorMsg);
        if (onError) onError(errorMsg);
      } finally {
        setLoading(false);
      }
    };

    fetchSignedUrl();

    // Set up a timer to refresh the signed URL before it expires
    const refreshTimer = setInterval(
      async () => {
        if (isVoicemailUrl(voicemailUrl) && isPlaying) {
          try {
            const url = await getVoicemailSignedUrl(voicemailUrl, true);
            setSignedUrl(url);
          } catch (err) {
            console.error('Error refreshing voicemail signed URL:', err);
          }
        }
      },
      10 * 60 * 1000,
    ); // Refresh every 10 minutes

    return () => {
      clearInterval(refreshTimer);
    };
  }, [voicemailUrl, isPlaying, onError]);

  // Handle audio play/pause events
  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  // Handle audio error
  const handleAudioError = () => {
    console.error('Audio playback failed');
    setError(
      'Unable to play the voicemail file. The file format may not be supported by your browser.',
    );
    if (onError) onError('Audio playback failed');
  };

  if (loading) {
    return <div className="voicemail-player loading">Loading voicemail...</div>;
  }

  if (error) {
    return <div className="voicemail-player error">Error: {error}</div>;
  }

  if (!signedUrl) {
    return <div className="voicemail-player error">No voicemail URL provided</div>;
  }

  return (
    <div className={`voicemail-player ${className}`}>
      <audio
        ref={audioRef}
        controls
        autoPlay={autoPlay}
        src={signedUrl}
        onPlay={handlePlay}
        onPause={handlePause}
        onError={handleAudioError}
      >
        Your browser does not support the audio element.
      </audio>

      <style jsx>{`
        .voicemail-player {
          margin: 15px 0;
          padding: 10px;
          background-color: #f9f9f9;
          border-radius: 4px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .loading,
        .error {
          padding: 10px;
          text-align: center;
          color: #666;
        }

        .error {
          color: #d32f2f;
        }

        audio {
          width: 100%;
        }
      `}</style>
    </div>
  );
};

export default VoicemailPlayer;
