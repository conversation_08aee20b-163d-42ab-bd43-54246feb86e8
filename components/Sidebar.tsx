'use client';

import { useRouter, usePathname } from 'next/navigation';
import { Sidebar as FlowbiteSidebar } from 'flowbite-react';
import {
  Hi<PERSON>ser,
  HiPhone,
  HiLogout,
  HiUserGroup,
  HiCog,
  HiShieldCheck,
  HiLocationMarker,
  HiCalendar,
  HiChartPie,
} from 'react-icons/hi';
import { signOut as firebaseSignOut } from '@/utils/firebase';
import { fetchCurrentUser, removeToken } from '@/utils/auth';
import React, { useState, useEffect } from 'react';
import { User, UserRole } from '@/models/auth';
import { useLocationContext } from '@/components/LocationContext';
import PracticeSelector from '@/components/PracticeSelector';
import { getRoleDisplayName } from '@/utils/role-utils';

interface SidebarProps {
  closeSidebar?: () => void;
}

const Sidebar = ({ closeSidebar }: SidebarProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const [staff, setStaff] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const locationContext = useLocationContext();

  useEffect(() => {
    let isMounted = true;

    // Only fetch user data once when the component mounts
    const loadStaff = async () => {
      try {
        // Only fetch if we don't already have staff data
        if (!staff) {
          const staffData = await fetchCurrentUser();
          if (isMounted) {
            setStaff(staffData);
          }
        }
      } catch (error) {
        console.error('Error loading staff data:', error);
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadStaff();

    // Cleanup function to prevent setting state on unmounted component
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array - only run on mount

  const handleLogout = async () => {
    await firebaseSignOut();
    removeToken();
    router.push('/login');
  };

  const handleNavigation = (path: string) => {
    if (closeSidebar) {
      closeSidebar();
    }
    router.push(path);
  };

  // Create a custom theme for the Sidebar
  const sidebarTheme = {
    root: {
      inner: 'h-full overflow-y-auto overflow-x-hidden bg-frontdesk-primary px-3 py-4 text-white',
    },
    item: {
      base: 'flex items-center justify-center rounded-lg p-2 text-base font-normal text-white hover:bg-frontdesk-primary hover:bg-opacity-80',
      active: 'bg-frontdesk-hover bg-opacity-80',
      icon: {
        base: 'h-6 w-6 shrink-0 text-white transition duration-75 group-hover:text-white',
        active: 'text-white',
      },
      content: {
        base: 'flex-1 whitespace-nowrap px-3 text-white',
      },
    },
    itemGroup: {
      base: 'mt-4 space-y-2 border-t border-gray-700 pt-4 first:mt-0 first:border-t-0 first:pt-0',
    },
    logo: {
      base: 'mb-5 flex items-center pl-2.5 text-white',
      collapsed: {
        on: 'hidden',
        off: 'self-center whitespace-nowrap text-xl font-semibold text-white',
      },
      img: 'mr-3 h-6 sm:h-7',
    },
  };

  return (
    <div className="w-fit h-full shadow-md">
      <FlowbiteSidebar
        aria-label="Doctor portal sidebar"
        className="text-white"
        theme={sidebarTheme}
      >
        <div className="flex flex-col h-full justify-between">
          <div>
            <FlowbiteSidebar.Logo
              href="#"
              onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                e.preventDefault();
                handleNavigation('/dashboard');
              }}
              img="/images/logo.svg"
              imgAlt="Doctor Portal logo"
              className="mb-3 md:mt-5 ml-12 md:ml-0 mt-1"
            >
              {/* logo */}
              <div className="flex items-center relative">
                <span className="logo-text">FrontDesk</span>
                <span className="relative top-[-5px]">AI</span>
              </div>
            </FlowbiteSidebar.Logo>

            {/* Practice Selector */}
            <div className="px-3 mb-9">
              <PracticeSelector />
            </div>

            <FlowbiteSidebar.Items>
              <FlowbiteSidebar.ItemGroup>
                <FlowbiteSidebar.Item
                  href="#"
                  onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    e.preventDefault();
                    handleNavigation('/dashboard/analytics');
                  }}
                  icon={HiChartPie}
                  active={pathname === '/dashboard/analytics'}
                >
                  Dashboard
                </FlowbiteSidebar.Item>

                <FlowbiteSidebar.Item
                  href="#"
                  onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    e.preventDefault();
                    handleNavigation('/dashboard/profile');
                  }}
                  icon={HiUser}
                  active={pathname === '/dashboard/profile'}
                >
                  Profile
                </FlowbiteSidebar.Item>
                <FlowbiteSidebar.Item
                  href="#"
                  onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    e.preventDefault();
                    handleNavigation('/dashboard/calls');
                  }}
                  icon={HiPhone}
                  active={
                    pathname === '/dashboard/calls' || pathname?.startsWith('/dashboard/calls/')
                  }
                >
                  Call Records
                </FlowbiteSidebar.Item>

                {/* Only visible to admins */}
                {staff &&
                  (staff.role === UserRole.SUPER_ADMIN || staff.role === UserRole.CLINIC_ADMIN) && (
                    <FlowbiteSidebar.Item
                      href="#"
                      onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                        e.preventDefault();
                        handleNavigation('/dashboard/staff');
                      }}
                      icon={HiUserGroup}
                      active={
                        pathname === '/dashboard/staff' || pathname?.startsWith('/dashboard/staff/')
                      }
                    >
                      Staff Management
                    </FlowbiteSidebar.Item>
                  )}

                {/* Office Hours & On-Call Management - Only visible to admins */}
                {staff &&
                  (staff.role === UserRole.SUPER_ADMIN || staff.role === UserRole.CLINIC_ADMIN) && (
                    <FlowbiteSidebar.Item
                      href="#"
                      onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                        e.preventDefault();
                        handleNavigation('/admin/office-hours');
                      }}
                      icon={HiCalendar}
                      active={pathname === '/admin/office-hours'}
                    >
                      Office Hours
                    </FlowbiteSidebar.Item>
                  )}

                {/* Admin Tools - Only visible to admins */}
                {staff &&
                  (staff.role === UserRole.SUPER_ADMIN || staff.role === UserRole.CLINIC_ADMIN) && (
                    <FlowbiteSidebar.Item
                      href="#"
                      onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                        e.preventDefault();
                        handleNavigation('/dashboard/admin-tools');
                      }}
                      icon={HiShieldCheck}
                      active={pathname === '/dashboard/admin-tools'}
                    >
                      Admin Tools
                    </FlowbiteSidebar.Item>
                  )}

                <FlowbiteSidebar.Item
                  href="#"
                  onClick={(e: React.MouseEvent<HTMLAnchorElement>) => {
                    e.preventDefault();
                    handleNavigation('/dashboard/settings');
                  }}
                  icon={HiCog}
                  active={
                    pathname === '/dashboard/settings' ||
                    pathname?.startsWith('/dashboard/settings')
                  }
                >
                  Settings
                </FlowbiteSidebar.Item>

                <FlowbiteSidebar.Item
                  icon={HiLogout}
                  onClick={handleLogout}
                  className="cursor-pointer"
                >
                  Logout
                </FlowbiteSidebar.Item>
              </FlowbiteSidebar.ItemGroup>
            </FlowbiteSidebar.Items>
          </div>

          {/* User info section */}
          <div className="mt-auto p-4 border-t border-gray-700">
            {loading ? (
              <span>Loading...</span>
            ) : staff ? (
              <div className="">
                {/* <p className="text-xs md:text-sm">Welcome,</p> */}
                <p className="font-medium text-sm md:text-base">{staff.name}</p>
                <p className="text-xs opacity-80">{staff.email}</p>
                <p className="text-xs opacity-80">
                  {staff.specialty || getRoleDisplayName(staff.role)}
                </p>
                {locationContext.currentLocation && (
                  <div className="flex items-center mt-1">
                    <HiLocationMarker className="h-3 w-3 mr-1 opacity-60" />
                    <p className="text-xs opacity-80">{locationContext.currentLocation.name}</p>
                  </div>
                )}
              </div>
            ) : (
              <span>Not logged in</span>
            )}
          </div>
        </div>
      </FlowbiteSidebar>
    </div>
  );
};

export default Sidebar;
