import { useState, FormEvent } from 'react';
import { Label, TextInput, Alert, Card, Spinner } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { HiCheck, HiExclamation, HiSearch } from 'react-icons/hi';

interface AdminEmailChangeFormProps {
  onSuccess?: () => void;
}

const AdminEmailChangeForm = ({ onSuccess }: AdminEmailChangeFormProps) => {
  const [userId, setUserId] = useState('');
  const [newEmail, setNewEmail] = useState('');
  const [searchEmail, setSearchEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  // Define a proper interface for the user object
  interface FirebaseUser {
    name?: string;
    displayName?: string;
    email?: string;
    uid?: string;
    emailVerified?: boolean;
    role?: string;
  }

  const [foundUser, setFoundUser] = useState<FirebaseUser | null>(null);

  // Email validation
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Function to search for a user by email
  const handleSearchUser = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setFoundUser(null);

    if (!validateEmail(searchEmail)) {
      setError('Please enter a valid email address to search');
      return;
    }

    setIsSearching(true);

    try {
      const response = await fetch('/api/auth/find-user-by-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: searchEmail }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 404) {
          setError(`No user found with email ${searchEmail}`);
        } else {
          throw new Error(data.message || 'Failed to find user');
        }
        return;
      }

      setFoundUser(data.user);
      setUserId(data.user.uid);
    } catch (error) {
      if (error instanceof Error) {
        setError(`Error searching for user: ${error.message}`);
      } else {
        setError('An unexpected error occurred during search');
      }
      console.error('User search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate inputs
    if (!userId.trim()) {
      setError('Please enter a user ID');
      return;
    }

    if (!validateEmail(newEmail)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      // Call the admin API endpoint to update email
      const response = await fetch('/api/auth/admin-update-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, newEmail }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to update email');
      }

      setSuccess(`Email for user ${userId} has been updated to ${newEmail} successfully`);

      // Clear form
      setUserId('');
      setNewEmail('');

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      if (error instanceof Error) {
        setError(`Failed to update email: ${error.message}`);
      } else {
        setError('An unexpected error occurred. Please try again.');
      }
      console.error('Admin email change error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Admin Email Change Tool</h3>
      <p className="text-sm text-gray-500">
        This tool allows administrators to directly update a user&apos;s email address without
        verification. Use only for development/testing purposes.
      </p>

      {error && (
        <Alert color="failure" icon={HiExclamation}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" icon={HiCheck}>
          {success}
        </Alert>
      )}

      {/* Search for user by email */}
      <Card>
        <h4 className="text-md font-medium mb-2">Find User by Email</h4>
        <form onSubmit={handleSearchUser} className="space-y-4">
          <div className="flex items-end gap-2">
            <div className="flex-grow">
              <Label htmlFor="search-email" value="Email to Search" className="mb-1" />
              <TextInput
                id="search-email"
                type="email"
                placeholder="<EMAIL>"
                value={searchEmail}
                onChange={e => setSearchEmail(e.target.value)}
                required
              />
            </div>
            <CustomButton type="submit" color="light" disabled={isSearching} className="mb-0">
              {isSearching ? <Spinner size="sm" /> : <HiSearch className="mr-2" />}
              {isSearching ? 'Searching...' : 'Search'}
            </CustomButton>
          </div>
        </form>

        {foundUser && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h5 className="font-medium">User Found:</h5>
            <div className="mt-2 space-y-1 text-sm">
              <p>
                <strong>Name:</strong> {foundUser.name || foundUser.displayName || 'N/A'}
              </p>
              <p>
                <strong>Email:</strong> {foundUser.email || 'N/A'}
              </p>
              <p>
                <strong>User ID:</strong> {foundUser.uid || 'N/A'}
              </p>
              <p>
                <strong>Email Verified:</strong>{' '}
                {foundUser.emailVerified !== undefined
                  ? foundUser.emailVerified
                    ? 'Yes'
                    : 'No'
                  : 'N/A'}
              </p>
              <p>
                <strong>Role:</strong> {foundUser.role || 'N/A'}
              </p>
            </div>
          </div>
        )}
      </Card>

      {/* Update email form */}
      <form onSubmit={handleSubmit} className="space-y-4 mt-6">
        <div>
          <div className="mb-1">
            <Label htmlFor="user-id" value="User ID" />
          </div>
          <TextInput
            id="user-id"
            type="text"
            placeholder="Firebase User ID"
            value={userId}
            onChange={e => setUserId(e.target.value)}
            required
          />
          <p className="mt-1 text-xs text-gray-500">Enter the Firebase User ID (UID) of the user</p>
        </div>

        <div>
          <div className="mb-1">
            <Label htmlFor="new-email" value="New Email" />
          </div>
          <TextInput
            id="new-email"
            type="email"
            placeholder="<EMAIL>"
            value={newEmail}
            onChange={e => setNewEmail(e.target.value)}
            required
          />
        </div>

        <CustomButton type="submit" color="blue" disabled={isLoading}>
          {isLoading ? 'Updating Email...' : 'Update Email'}
        </CustomButton>
      </form>
    </div>
  );
};

export default AdminEmailChangeForm;
