import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveC<PERSON>r, Legend, Tooltip } from 'recharts';

interface AppointmentData {
  reschedule: number;
  newAppointment: number;
  cancel: number;
}

interface AppointmentSplitChartProps {
  data: AppointmentData;
  className?: string;
}

const AppointmentSplitChart: React.FC<AppointmentSplitChartProps> = ({ data, className = '' }) => {
  // Transform data for Recharts
  const chartData = [
    { name: 'Reschedule', value: data.reschedule, color: '#3B82F6' },
    { name: 'New Appointment', value: data.newAppointment, color: '#10B981' },
    { name: 'Cancel', value: data.cancel, color: '#EF4444' },
  ];

  // Filter out zero values
  const filteredData = chartData.filter(item => item.value > 0);

  // Custom label function
  const renderCustomizedLabel = (props: {
    cx?: number;
    cy?: number;
    midAngle?: number;
    innerRadius?: number;
    outerRadius?: number;
    percent?: number;
  }) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, percent } = props;

    if (!cx || !cy || midAngle === undefined || !innerRadius || !outerRadius || !percent) {
      return null;
    }

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (filteredData.length === 0) {
    return (
      <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Appointment Breakdown</h3>
        <div className="flex items-center justify-center h-64 text-gray-500">
          No appointment data available for selected date
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Appointment Breakdown</h3>
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={filteredData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {filteredData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AppointmentSplitChart;
