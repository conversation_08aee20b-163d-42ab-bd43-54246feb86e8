import React, { useState, useEffect } from 'react';
import { HiCalendar, HiX } from 'react-icons/hi';
import dayjs from 'dayjs';

interface DateRangeSelectorProps {
  startDate: string;
  endDate: string;
  onDateRangeChange: (startDate: string, endDate: string) => void;
  onClose?: () => void;
}

export default function DateRangeSelector({
  startDate,
  endDate,
  onDateRangeChange,
  onClose,
}: DateRangeSelectorProps) {
  const [localStartDate, setLocalStartDate] = useState(startDate);
  const [localEndDate, setLocalEndDate] = useState(endDate);
  const [selectedPreset, setSelectedPreset] = useState<string | null>(null);

  // Check which preset is currently selected based on dates
  useEffect(() => {
    const today = dayjs();
    const start = dayjs(localStartDate);
    const end = dayjs(localEndDate);

    // Check if dates match any preset
    if (end.isSame(today, 'day')) {
      const diffDays = today.diff(start, 'day');
      if (diffDays === 7) {
        setSelectedPreset('7days');
      } else if (diffDays === 30) {
        setSelectedPreset('30days');
      } else if (diffDays === 90) {
        setSelectedPreset('90days');
      } else {
        setSelectedPreset(null);
      }
    } else if (start.isSame(dayjs('2025-06-01'), 'day') && end.isSame(dayjs('2025-06-30'), 'day')) {
      setSelectedPreset('june2025');
    } else {
      setSelectedPreset(null);
    }
  }, [localStartDate, localEndDate]);

  const handleApply = () => {
    if (localStartDate && localEndDate) {
      // Validate that start date is before end date
      if (dayjs(localStartDate).isAfter(dayjs(localEndDate))) {
        alert('Start date must be before end date');
        return;
      }
      onDateRangeChange(localStartDate, localEndDate);
    }
  };

  const handleReset = () => {
    // Reset to default: Last 30 days
    const end = dayjs().format('YYYY-MM-DD');
    const start = dayjs().subtract(30, 'day').format('YYYY-MM-DD');
    setLocalStartDate(start);
    setLocalEndDate(end);
    setSelectedPreset('30days');
    onDateRangeChange(start, end);
  };

  const handlePresetRange = (days: number, presetId: string) => {
    const end = dayjs().format('YYYY-MM-DD');
    const start = dayjs().subtract(days, 'day').format('YYYY-MM-DD');
    setLocalStartDate(start);
    setLocalEndDate(end);
    setSelectedPreset(presetId);
    onDateRangeChange(start, end);
  };

  const handleJune2025 = () => {
    setLocalStartDate('2025-06-01');
    setLocalEndDate('2025-06-30');
    setSelectedPreset('june2025');
    onDateRangeChange('2025-06-01', '2025-06-30');
  };

  const getButtonClass = (presetId: string) => {
    const baseClass = 'px-3 py-2 text-sm rounded-md transition-colors';
    const isSelected = selectedPreset === presetId;

    if (presetId === 'june2025') {
      return `${baseClass} ${
        isSelected ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
      }`;
    }

    return `${baseClass} ${
      isSelected ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
    }`;
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-80">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <HiCalendar className="h-5 w-5" />
          Select Date Range
        </h3>
        {onClose && (
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors">
            <HiX className="h-5 w-5" />
          </button>
        )}
      </div>

      <div className="space-y-4">
        {/* Date Inputs */}
        <div className="grid grid-cols-1 gap-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
            <input
              type="date"
              value={localStartDate}
              onChange={e => {
                setLocalStartDate(e.target.value);
                setSelectedPreset(null); // Clear preset when manually changing dates
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
            <input
              type="date"
              value={localEndDate}
              onChange={e => {
                setLocalEndDate(e.target.value);
                setSelectedPreset(null); // Clear preset when manually changing dates
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Preset Ranges */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Quick Ranges</label>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => handlePresetRange(7, '7days')}
              className={getButtonClass('7days')}
            >
              Last 7 days
            </button>
            <button
              onClick={() => handlePresetRange(30, '30days')}
              className={getButtonClass('30days')}
            >
              Last 30 days
            </button>
            <button
              onClick={() => handlePresetRange(90, '90days')}
              className={getButtonClass('90days')}
            >
              Last 90 days
            </button>
            <button onClick={handleJune2025} className={getButtonClass('june2025')}>
              June 2025
            </button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <button
            onClick={handleApply}
            className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Apply
          </button>
          <button
            onClick={handleReset}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );
}
