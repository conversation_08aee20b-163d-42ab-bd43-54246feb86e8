import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON>, Tooltip } from 'recharts';
import { CallType, CallTypeInfo } from '@/models/CallTypes';

interface IssuesSplitChartProps {
  data: Record<string, number>;
  className?: string;
  overrideTotal?: number;
}

const IssuesSplitChart: React.FC<IssuesSplitChartProps> = ({ data, className = '' }) => {
  // Define colors for different call types
  const getColorForIssueType = (typeName: string): string => {
    const colorMap: Record<string, string> = {
      // Multi-issues
      'Multi-Issues': '#8B5CF6', // Purple

      // Transfer types
      TRANSFER_TO_HUMAN: '#6B7280', // Gray
      IMMEDIATE_TRANSFER: '#EF4444', // Red
      TRANSFER_DUE_TO_SCHEDULING: '#F59E0B', // Amber
      TRANSFER_DUE_TO_UNABLE_TO_ASSIST: '#8B5CF6', // <PERSON>
      TRANSFER_TO_CLINIC: '#6366F1', // Indigo

      // Appointment types
      NEW_PATIENT_NEW_APPOINTMENT: '#10B981', // Green
      NEW_APPOINTMENT_EXISTING_PATIENT: '#059669', // Emerald
      RESCHEDULE: '#F59E0B', // Amber
      CANCELLATION: '#EF4444', // Red
      CONFIRM_APPOINTMENT: '#3B82F6', // Blue

      // Communication types
      VOICEMAIL: '#8B5CF6', // Purple
      VOICEMAIL_SYSTEM_ERROR: '#DC2626', // Red
      GENERAL_INFO: '#06B6D4', // Cyan
      LOOKUP: '#EC4899', // Pink

      // Other types
      OTHER: '#6B7280', // Gray
      DISCONNECTED: '#374151', // Dark gray
      AFTER_HOURS: '#7C3AED', // Violet
    };

    return colorMap[typeName] || '#6B7280'; // Default to gray
  };

  // Transform data for Recharts
  const chartData = Object.entries(data)
    .filter(([, value]) => value > 0)
    .map(([key, value]) => {
      let displayName = key;

      // If it's a CallType enum key, get the display name
      if (key !== 'Multi-Issues' && CallType[key as keyof typeof CallType] !== undefined) {
        const callTypeValue = CallType[key as keyof typeof CallType];
        displayName = CallTypeInfo[callTypeValue]?.name || key;
      }

      return {
        name: displayName,
        value,
        color: getColorForIssueType(key),
      };
    })
    .sort((a, b) => b.value - a.value); // Sort by value descending

  // Custom label function
  const renderCustomizedLabel = (props: {
    cx?: number;
    cy?: number;
    midAngle?: number;
    innerRadius?: number;
    outerRadius?: number;
    percent?: number;
  }) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, percent } = props;

    if (!cx || !cy || midAngle === undefined || !innerRadius || !outerRadius || !percent) {
      return null;
    }

    // Only show label if percentage is significant enough
    if (percent < 0.05) return null; // Hide labels for slices less than 5%

    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text
        x={x}
        y={y}
        fill="white"
        textAnchor={x > cx ? 'start' : 'end'}
        dominantBaseline="central"
        fontSize={12}
        fontWeight="bold"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (chartData.length === 0) {
    return (
      <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Calls Breakdown{' '}
            <span className="text-sm text-gray-500">(call may have multiple types)</span>
          </h3>
          <span className="text-sm text-gray-500">Total: 0</span>
        </div>
        <div className="flex items-center justify-center h-64 text-gray-500">
          No issue data available for selected date range
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          Calls Breakdown{' '}
          <span className="text-sm text-gray-400">(call may have multiple types)</span>
        </h3>
      </div>
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderCustomizedLabel}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value, name) => [`${value} calls`, name]} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default IssuesSplitChart;
