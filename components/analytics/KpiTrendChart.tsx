import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { DashboardSummary } from '@/lib/services/dashboard-metrics-service';

interface KpiTrendChartProps {
  data: DashboardSummary;
  className?: string;
}

const KpiTrendChart: React.FC<KpiTrendChartProps> = ({ data, className = '' }) => {
  // Transform the dashboard summary into chart data
  const chartData = [
    {
      name: 'Current Period',
      'Total Calls': data.totalCalls,
      'Time Saved (min)': Math.round(data.totalTimeSavedSeconds / 60),
      Appointments:
        data.appointments.reschedule + data.appointments.newAppointment + data.appointments.cancel,
    },
  ];

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'Time Saved (min)') {
      const hours = Math.floor(value / 60);
      const minutes = value % 60;
      if (hours > 0) {
        return [`${hours}h ${minutes}m`, name];
      }
      return [`${minutes}m`, name];
    }
    return [value, name];
  };

  return (
    <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-100 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Metrics Overview</h3>
      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip formatter={formatTooltipValue} />
          <Bar dataKey="Total Calls" fill="#3B82F6" name="Total Calls" />
          <Bar dataKey="Time Saved (min)" fill="#10B981" name="Time Saved (min)" />
          <Bar dataKey="Appointments" fill="#F59E0B" name="Appointments" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default KpiTrendChart;
