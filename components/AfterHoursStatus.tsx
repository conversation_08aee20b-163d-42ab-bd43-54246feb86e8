import { useState, useEffect, useCallback } from 'react';
import { Card, Button, Checkbox, Label, Textarea, Spinner, Alert } from 'flowbite-react';
import { Hi<PERSON><PERSON>, HiUser, HiCheckCircle } from 'react-icons/hi';
import { getToken, fetchCurrentUser } from '@/utils/auth';

interface LogEntry {
  type: 'viewed' | 'contacted';
  message: string;
  timestamp: string;
  userName: string;
}

interface AfterHoursStatusProps {
  callId: string;
  callTimestamp?: string;
}

const AfterHoursStatus: React.FC<AfterHoursStatusProps> = ({ callId, callTimestamp }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [isContacted, setIsContacted] = useState(false);
  const [notes, setNotes] = useState('');
  const [existingNotes, setExistingNotes] = useState<string | null>(null);
  const [saveLoading, setSaveLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [currentUser, setCurrentUser] = useState<{
    id: string;
    name?: string;
    email: string;
  } | null>(null);
  const [callCreatedAt, setCallCreatedAt] = useState<string>('');

  const fetchAfterHoursData = useCallback(async () => {
    try {
      setLoading(true);

      // Fetch current user
      const user = await fetchCurrentUser();
      setCurrentUser(user);

      // Fetch after-hours call logs
      const response = await fetch(`/api/calls/after-hours/call-logs/${callId}`, {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          // This is not an after-hours call, but set callCreatedAt if timestamp was provided
          setLogs([]);
          if (callTimestamp) {
            setCallCreatedAt(callTimestamp);
          }
          return;
        }
        throw new Error('Failed to fetch after-hours data');
      }

      const result = await response.json();
      if (result.success && result.data) {
        setLogs(result.data);

        // Check if already contacted - any log indicates contact attempt
        const hasContactedLog = result.data.some((log: LogEntry) => log.type === 'contacted');
        setIsContacted(hasContactedLog);

        // Use the passed callTimestamp if available, otherwise use earliest log or current date
        if (callTimestamp) {
          setCallCreatedAt(callTimestamp);
        } else if (result.data.length > 0) {
          const earliestLog = result.data.reduce((earliest: LogEntry, current: LogEntry) =>
            new Date(current.timestamp) < new Date(earliest.timestamp) ? current : earliest,
          );
          setCallCreatedAt(earliestLog.timestamp);
        } else {
          setCallCreatedAt(new Date().toISOString());
        }
      }

      // Fetch existing notes from after-hours call record
      try {
        const notesResponse = await fetch(`/api/calls/after-hours/${callId}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (notesResponse.ok) {
          const notesResult = await notesResponse.json();
          if (notesResult.success && notesResult.data && notesResult.data.notes) {
            setExistingNotes(notesResult.data.notes);
          }
        }
      } catch (notesError) {
        console.warn('Could not fetch existing notes:', notesError);
        // Don't show error to user, just continue without existing notes
      }
    } catch (error) {
      console.error('Error fetching after-hours data:', error);
      setError('Failed to load after-hours data');
    } finally {
      setLoading(false);
    }
  }, [callId, callTimestamp]);

  const handleContactedChange = async (checked: boolean) => {
    if (checked && !isContacted && currentUser) {
      try {
        setSaveLoading(true);

        const response = await fetch('/api/calls/after-hours/contact', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${getToken()}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            callId,
            doctorId: currentUser.id,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            `Failed to mark as contacted: ${errorData.message || response.statusText}`,
          );
        }

        setIsContacted(true);
        setSuccessMessage('Successfully marked as contacted');

        // Refresh data to get updated contact history
        await fetchAfterHoursData();
      } catch (error) {
        console.error('Error marking as contacted:', error);
        setError(
          `Failed to mark as contacted: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      } finally {
        setSaveLoading(false);
      }
    } else {
      console.log('AfterHoursStatus Debug - Contact change skipped:', {
        checked,
        alreadyContacted: isContacted,
        hasCurrentUser: !!currentUser,
      });
    }
  };

  const handleSaveNotes = async () => {
    try {
      setSaveLoading(true);
      const response = await fetch(`/api/calls/after-hours/notes/${callId}`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${getToken()}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          notes,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save notes');
      }

      const result = await response.json();
      if (result.success && result.data && result.data.notes) {
        // Update existing notes with the saved formatted notes
        setExistingNotes(result.data.notes);
      }

      // Show appropriate success message based on Practice+ save status
      if (result.data?.savedToPractice) {
        setSuccessMessage('Notes saved successfully');
      } else {
        setSuccessMessage('Notes saved to our system only. Practice+ save failed.');
        // If there's a specific error, you could show it:
        if (result.data?.practiceError) {
          console.warn('Practice+ save error:', result.data.practiceError);
        }
      }
      setNotes(''); // Clear input field after saving
    } catch (error) {
      console.error('Error saving notes:', error);
      setError('Failed to save notes');
    } finally {
      setSaveLoading(false);
    }
  };

  useEffect(() => {
    fetchAfterHoursData();
  }, [fetchAfterHoursData]);

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-40">
          <Spinner size="xl" />
        </div>
      </Card>
    );
  }

  // Component will show for after-hours calls regardless of existing logs
  return (
    <Card>
      <h2 className="text-xl font-bold mb-4">After-Hour Status</h2>

      {error && (
        <Alert color="failure" className="mb-4" onDismiss={() => setError('')}>
          {error}
        </Alert>
      )}

      {successMessage && (
        <Alert color="success" className="mb-4" onDismiss={() => setSuccessMessage('')}>
          {successMessage}
        </Alert>
      )}

      <div className="space-y-6">
        {/* View History Section */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <HiClock className="w-5 h-5 mr-2 text-blue-500" />
            View History
          </h3>
          <div className="space-y-2">
            {callCreatedAt && (
              <div className="text-sm text-gray-600">
                Patient Called in at{' '}
                {new Date(callCreatedAt).toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: true,
                })}
              </div>
            )}
            {logs
              .filter(log => log.type === 'viewed')
              .map((log, index) => (
                <div key={index} className="text-sm text-gray-600">
                  {log.message}
                </div>
              ))}
          </div>
        </div>

        {/* Contact History Section */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center">
            <HiUser className="w-5 h-5 mr-2 text-green-500" />
            Contact History
          </h3>

          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Checkbox
                id="contacted"
                checked={isContacted}
                onChange={e => handleContactedChange(e.target.checked)}
                disabled={isContacted || saveLoading}
              />
              <Label htmlFor="contacted" className="flex items-center">
                <HiCheckCircle className="w-5 h-5 text-green-500 mr-1" />
                Contacted
              </Label>
              {saveLoading && <Spinner size="sm" />}
            </div>

            {logs
              .filter(log => log.type === 'contacted')
              .map((log, index) => (
                <div key={index} className="text-sm text-green-600 ml-6 font-medium">
                  Contacted by {log.userName} at{' '}
                  {new Date(log.timestamp).toLocaleTimeString('en-US', {
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                  })}{' '}
                  on{' '}
                  {new Date(log.timestamp).toLocaleDateString('en-US', {
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric',
                  })}
                </div>
              ))}

            {/* Show contacted status even if no contact log exists (for when FK constraint fails) */}
            {isContacted && logs.filter(log => log.type === 'contacted').length === 0 && (
              <div className="text-sm text-green-600 ml-6 font-medium">
                Contacted by {currentUser?.name || 'Doctor'} at{' '}
                {new Date().toLocaleTimeString('en-US', {
                  hour: 'numeric',
                  minute: '2-digit',
                  hour12: true,
                })}{' '}
                on{' '}
                {new Date().toLocaleDateString('en-US', {
                  month: 'long',
                  day: 'numeric',
                  year: 'numeric',
                })}
              </div>
            )}
          </div>
        </div>

        {/* Notes Section - Always visible */}
        <div>
          <h3 className="text-lg font-semibold mb-3">Notes</h3>
          <div className="space-y-3">
            {/* Display existing notes if they exist */}
            {existingNotes && (
              <div className="bg-gray-50 p-3 rounded-md border">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Current Saved Notes:</h4>
                <div className="text-sm text-gray-600 whitespace-pre-wrap">{existingNotes}</div>
              </div>
            )}

            <Textarea
              id="notes"
              placeholder={
                existingNotes
                  ? 'Modify the existing notes...'
                  : 'Enter notes that will be saved to Practice+ and our system...'
              }
              value={notes}
              onChange={e => setNotes(e.target.value)}
              rows={4}
              disabled={!isContacted}
            />
            <Button
              onClick={handleSaveNotes}
              disabled={saveLoading || !notes.trim() || !isContacted}
              isProcessing={saveLoading}
              processingSpinner={<Spinner size="sm" light={true} />}
            >
              {existingNotes ? 'Update Notes' : 'Add Notes'}
            </Button>
            {!isContacted && (
              <p className="text-sm text-gray-500">
                Please mark as &quot;Contacted&quot; first before adding notes.
              </p>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default AfterHoursStatus;
