import { useState, useEffect } from 'react';
import { Label, TextInput, Alert, Modal } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { TWO_FA_CODE_LENGTH, TWO_FA_RESEND_CODE_RATE_LIMIT_IN_SECONDS } from '@/app-config';

interface TwoFactorVerificationModalProps {
  show: boolean;
  onClose: () => void;
  email: string;
  onVerifySuccess: () => void;
  successMessage?: string;
}

const TwoFactorVerificationModal = ({
  show,
  onClose,
  email,
  onVerifySuccess,
  successMessage,
}: TwoFactorVerificationModalProps) => {
  const [verificationCode, setVerificationCode] = useState('');
  const [is2FALoading, setIs2FALoading] = useState(false);
  const [verification2FAError, setVerification2FAError] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [success, setSuccess] = useState('');
  const startCountdown = () => {
    setResendDisabled(true);
    setResendCountdown(TWO_FA_RESEND_CODE_RATE_LIMIT_IN_SECONDS);

    const countdownInterval = setInterval(() => {
      setResendCountdown(prevCount => {
        if (prevCount <= 1) {
          clearInterval(countdownInterval);
          setResendDisabled(false);
          return 0;
        }
        return prevCount - 1;
      });
    }, 1000);

    return countdownInterval;
  };

  // Start countdown timer when modal is opened
  useEffect(() => {
    if (show) {
      setVerificationCode('');
      setVerification2FAError('');
      const countdownInterval = startCountdown();

      // Clean up the interval when component unmounts or modal closes
      return () => clearInterval(countdownInterval);
    }
  }, [show]);

  // Update success message when it changes
  useEffect(() => {
    if (successMessage) {
      setSuccess(successMessage);
    }
  }, [successMessage]);

  const handle2FAVerification = async () => {
    if (!verificationCode || verificationCode.length !== TWO_FA_CODE_LENGTH) {
      setVerification2FAError(`Please enter a valid ${TWO_FA_CODE_LENGTH}-character code`);
      return;
    }

    setIs2FALoading(true);
    setVerification2FAError('');
    setSuccess('');

    try {
      const response = await fetch('/api/auth/2fa/2fa-verification-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          code: verificationCode,
        }),
      });

      const responseData = await response.json();
      if (!response.ok) {
        // Handle specific HTTP 400 error for invalid code
        if (response.status === 400) {
          setVerification2FAError(
            responseData.message || 'The code you entered is invalid. Please try again.',
          );
          // Clear the input field to allow the user to enter a new code
          setVerificationCode('');
        } else {
          setVerification2FAError(responseData.message || 'Verification failed. Please try again.');
        }
        return;
      }

      // 2FA verification successful, now call the parent callback
      onVerifySuccess();
    } catch (error) {
      setVerification2FAError('An error occurred during verification');
      console.error(error);
    } finally {
      setIs2FALoading(false);
    }
  };

  const handleResendVerificationCode = async () => {
    setVerification2FAError('');
    setSuccess('');
    setIs2FALoading(true);

    try {
      const response = await fetch('/api/auth/2fa/send-2fa-verification-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const responseData = await response.json();

      if (!response.ok) {
        if (response.status === 429) {
          // Rate limit exceeded
          setVerification2FAError('Too many attempts. Please try again later.');
          // Start a countdown timer
          startCountdown();
        } else {
          setVerification2FAError(responseData.message || 'Failed to resend code');
        }
        return;
      }

      // Start a countdown timer
      startCountdown();
      // Clear any previous verification code input
      setVerificationCode('');
      // Show success message
      setSuccess('A new verification code has been sent to your email');
    } catch (error) {
      setVerification2FAError('Failed to resend verification code');
      console.error(error);
    } finally {
      setIs2FALoading(false);
    }
  };

  return (
    <Modal show={show} onClose={onClose}>
      <Modal.Header>Two-Factor Authentication</Modal.Header>
      <Modal.Body>
        <div className="space-y-4">
          {verification2FAError && <Alert color="failure">{verification2FAError}</Alert>}
          {success && <Alert color="success">{success}</Alert>}
          <p className="text-sm text-gray-600">
            A verification code has been sent to {email}. Please enter the code below.
          </p>
          <div>
            <Label htmlFor="verification-code" value="Verification Code" />
            <TextInput
              id="verification-code"
              type="text"
              placeholder="Enter the code"
              maxLength={TWO_FA_CODE_LENGTH}
              value={verificationCode}
              onChange={e =>
                setVerificationCode(e.target.value.replace(/[^0-9a-zA-Z]/g, '').toUpperCase())
              }
              required
            />
          </div>
          <div className="flex justify-end">
            <button
              type="button"
              className={`text-sm text-blue-600 hover:underline bg-transparent border-none cursor-pointer ${resendDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={handleResendVerificationCode}
              disabled={resendDisabled || is2FALoading}
            >
              {resendDisabled && resendCountdown > 0
                ? `Resend Code (${resendCountdown}s)`
                : 'Resend Code'}
            </button>
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <CustomButton onClick={handle2FAVerification} color="blue" disabled={is2FALoading}>
          {is2FALoading ? 'Verifying...' : 'Verify'}
        </CustomButton>
        <CustomButton color="gray" onClick={onClose}>
          Cancel
        </CustomButton>
      </Modal.Footer>
    </Modal>
  );
};

export default TwoFactorVerificationModal;
