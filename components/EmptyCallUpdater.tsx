import { useState, useCallback } from 'react';
import { <PERSON><PERSON>, Spin<PERSON> } from 'flowbite-react';
import { getToken } from '@/utils/auth';
import { CallType } from '@/models/CallTypes';

interface EmptyCallUpdaterProps {
  callId: string;
  sessionId?: string;
  onSuccess: () => void;
  buttonText?: string;
}

/**
 * Component for automatically updating empty call records
 * Instead of showing a modal, this directly calls the API using session data
 */
const EmptyCallUpdater = ({
  callId,
  sessionId,
  onSuccess,
  buttonText = 'Update from Session Data',
}: EmptyCallUpdaterProps) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Use useCallback to memoize the function
  const handleUpdateCall = useCallback(async () => {
    if (!sessionId) {
      setError('No session ID available');
      return;
    }

    // Avoid duplicate calls if already loading
    if (loading) return;

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // 1. First fetch the session data using internal API
      const sessionResponse = await fetch(`/api/call-sessions/${sessionId}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (!sessionResponse.ok) {
        throw new Error('Failed to fetch session data');
      }

      const sessionData = await sessionResponse.json();

      // 2. Prepare update data from session
      const updateData = {
        callId,
        sessionId,
        phoneNumber: sessionData.callerPhone || '',
        type: sessionData.callType !== undefined ? sessionData.callType : CallType.OTHER,
        patientId: sessionData.patientId || '',
        reason: 'Incoming call', // Default reason
      };

      // 3. Call the update API - using internal endpoint
      const updateResponse = await fetch('/api/calls/update-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${getToken()}`,
        },
        body: JSON.stringify(updateData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.message || 'Failed to update call');
      }

      // 4. Handle success
      setSuccess(true);

      // Use setTimeout to debounce the onSuccess callback
      // This prevents rapid re-renders causing multiple API calls
      setTimeout(() => {
        onSuccess();
      }, 100);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [callId, sessionId, loading, onSuccess]); // Include dependencies for useCallback

  return (
    <div>
      {error && <div className="p-3 mb-3 text-sm text-red-800 rounded-lg bg-red-50">{error}</div>}

      {success && (
        <div className="p-3 mb-3 text-sm text-green-800 rounded-lg bg-green-50">
          Call updated successfully!
        </div>
      )}

      <Button
        color="warning"
        size="sm"
        onClick={handleUpdateCall}
        disabled={loading}
        isProcessing={loading}
        processingSpinner={<Spinner size="sm" light={true} />}
      >
        {buttonText}
      </Button>
    </div>
  );
};

export default EmptyCallUpdater;
