import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Practice } from '@/models/Practice';
import { getToken } from '@/utils/auth';

interface PracticeContextType {
  currentPractice: Practice | null;
  availablePractices: Practice[];
  loading: boolean;
  switchPractice: (practiceId: string) => void;
  refreshPracticeContext: () => Promise<void>;
}

interface PracticeProviderProps {
  children: ReactNode;
}

// Create the context with default values
const PracticeContext = createContext<PracticeContextType>({
  currentPractice: null,
  availablePractices: [],
  loading: true,
  switchPractice: () => {},
  refreshPracticeContext: async () => {},
});

// Custom hook to use the Practice context
export const usePracticeContext = (): PracticeContextType => {
  const context = useContext(PracticeContext);
  if (!context) {
    throw new Error('usePracticeContext must be used within a PracticeProvider');
  }
  return context;
};

export const PracticeProvider: React.FC<PracticeProviderProps> = ({ children }) => {
  const [currentPractice, setCurrentPractice] = useState<Practice | null>(null);
  const [availablePractices, setAvailablePractices] = useState<Practice[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch user's practice context
  const fetchPracticeContext = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/practices', {
        headers: {
          Authorization: `Bearer ${getToken()}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch practices');
      }

      const data = await response.json();
      const practices = data.practices || [];
      setAvailablePractices(practices);

      // Get current practice from localStorage or default to first practice
      const storedPracticeId = localStorage.getItem('currentPracticeId');
      let selectedPractice: Practice | null = null;

      if (storedPracticeId) {
        selectedPractice = practices.find((p: Practice) => p.id === storedPracticeId) || null;
      }

      // If no stored practice or stored practice not found, default to first practice
      if (!selectedPractice && practices.length > 0) {
        selectedPractice = practices[0];
      }

      setCurrentPractice(selectedPractice);

      // Store the current practice ID in localStorage
      if (selectedPractice) {
        localStorage.setItem('currentPracticeId', selectedPractice.id);
      }
    } catch (error) {
      console.error('Error fetching practice context:', error);
    } finally {
      setLoading(false);
    }
  };

  // Switch to a different practice
  const switchPractice = (practiceId: string) => {
    const selectedPractice = availablePractices.find(p => p.id === practiceId);
    if (selectedPractice) {
      setCurrentPractice(selectedPractice);
      localStorage.setItem('currentPracticeId', practiceId);
    }
  };

  // Refresh practice context
  const refreshPracticeContext = async () => {
    await fetchPracticeContext();
  };

  // Load practice context on component mount
  useEffect(() => {
    fetchPracticeContext();
  }, []);

  const value: PracticeContextType = {
    currentPractice,
    availablePractices,
    loading,
    switchPractice,
    refreshPracticeContext,
  };

  return <PracticeContext.Provider value={value}>{children}</PracticeContext.Provider>;
};

export default PracticeContext;
