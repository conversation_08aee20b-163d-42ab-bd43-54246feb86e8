import React from 'react';
import { Badge } from 'flowbite-react';
import { CallType, getCallTypeName } from '@/models/CallTypes';
import { twMerge } from 'tailwind-merge';

interface CallTypeDisplayProps {
  /** Single call type enum value or an ordered array of call types */
  type?: CallType | CallType[] | null;
  className?: string;
}

/**
 * Component to display call type as a badge with appropriate color
 */
const CallTypeDisplay: React.FC<CallTypeDisplayProps> = ({ type, className = '' }) => {
  // Combine default styles with any provided className
  const baseClassName = 'inline-block w-auto whitespace-nowrap';
  const mergedClassName = twMerge(baseClassName, className);

  // Normalize `type` prop to an array so we can render one badge per type when multiple are supplied
  const typesArray: Array<CallType | undefined | null> = Array.isArray(type)
    ? type
    : type !== undefined && type !== null
      ? [type]
      : [];

  // If no valid type is provided, render Unknown badge
  if (typesArray.length === 0) {
    return (
      <Badge color="gray" className={mergedClassName}>
        Unknown
      </Badge>
    );
  }

  // Helper to map call type to Flowbite badge color
  const getBadgeColor = (
    callType: CallType,
  ): 'info' | 'gray' | 'success' | 'warning' | 'failure' | 'indigo' | 'purple' | 'pink' => {
    switch (callType) {
      case CallType.VOICEMAIL:
        return 'purple';
      case CallType.TRANSFER_TO_HUMAN:
      case CallType.TRANSFER_TO_CLINIC:
        return 'indigo';
      case CallType.IMMEDIATE_TRANSFER:
        return 'failure'; // Red for immediate transfers (≤35s)
      case CallType.TRANSFER_DUE_TO_SCHEDULING:
        return 'warning'; // Yellow for scheduling issues
      case CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST:
        return 'indigo'; // Purple/indigo for unable to assist
      case CallType.NEW_PATIENT_NEW_APPOINTMENT:
        return 'success';
      case CallType.NEW_APPOINTMENT_EXISTING_PATIENT:
        return 'info';
      case CallType.RESCHEDULE:
        return 'warning';
      case CallType.CANCELLATION:
      case CallType.VOICEMAIL_SYSTEM_ERROR:
        return 'failure';
      case CallType.GENERAL_INFO:
        return 'info';
      case CallType.LOOKUP:
        return 'pink';
      default:
        return 'gray';
    }
  };

  return (
    <>
      {typesArray.map((t, idx) => {
        if (t === undefined || t === null) return null;
        return (
          <Badge
            key={idx}
            color={getBadgeColor(t)}
            className={twMerge(mergedClassName, idx > 0 && 'ml-1')}
          >
            {getCallTypeName(t)}
          </Badge>
        );
      })}
    </>
  );
};

export default CallTypeDisplay;
