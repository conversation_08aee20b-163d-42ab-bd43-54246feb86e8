import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Hi<PERSON><PERSON>, HiOutlineUserCircle } from 'react-icons/hi';
import SessionAudioPlayer from './SessionAudioPlayer';

interface TranscriptItem {
  text: string;
  recordUrl: string;
}

interface TranscriptWithAudioProps {
  transcriptionWithAudio: string | TranscriptItem[];
}

const TranscriptWithAudio: React.FC<TranscriptWithAudioProps> = ({ transcriptionWithAudio }) => {
  const [activeAudioIndex, setActiveAudioIndex] = useState<number | null>(null);
  const [audioErrors, setAudioErrors] = useState<Record<number, string>>({});
  const [parseError, setParseError] = useState<string | null>(null);

  // Parse the transcriptionWithAudio JSON string or array
  const [transcriptItems, setTranscriptItems] = useState<TranscriptItem[]>([]);

  // Reference to keep track of audio elements
  const audioRefs = useRef<Record<number, HTMLAudioElement | null>>({});

  // Register audio element reference
  const registerAudioRef = (index: number, element: HTMLAudioElement | null) => {
    audioRefs.current[index] = element;
  };

  // Cleanup effect when component unmounts or when active audio changes
  useEffect(() => {
    // This effect runs when activeAudioIndex changes
    // Store the current audio reference to use in cleanup
    const currentAudioIndex = activeAudioIndex;
    const currentAudioRef =
      currentAudioIndex !== null ? audioRefs.current[currentAudioIndex] : null;

    // Return cleanup function to handle any necessary cleanup
    return () => {
      // If we're closing an audio player, pause it
      if (currentAudioIndex !== null && currentAudioRef) {
        try {
          currentAudioRef.pause();
        } catch (e) {
          console.error('Error pausing audio:', e);
        }
      }
    };
  }, [activeAudioIndex]);

  useEffect(() => {
    try {
      if (!transcriptionWithAudio) {
        return;
      }

      // Handle the case where we already have an array
      if (Array.isArray(transcriptionWithAudio)) {
        setTranscriptItems(transcriptionWithAudio as TranscriptItem[]);
        return;
      }

      // Otherwise, try to parse the string value
      const parsed = JSON.parse(transcriptionWithAudio);
      if (Array.isArray(parsed)) {
        setTranscriptItems(parsed);
      } else {
        setParseError('Invalid transcript format: expected an array');
      }
    } catch (error) {
      console.error('Failed to parse transcriptionWithAudio:', error);
      setParseError('Failed to parse transcript data');
    }
  }, [transcriptionWithAudio]);

  // Toggle audio player for a specific transcript item
  const toggleAudio = (index: number) => {
    // Prevent re-triggering the same action if it's already in progress
    if (activeAudioIndex === index) {
      // Pause the audio if it's playing
      if (audioRefs.current[index]) {
        try {
          audioRefs.current[index]?.pause();
        } catch (e) {
          console.error('Error pausing audio:', e);
        }
      }
      setActiveAudioIndex(null); // Close the active player
    } else {
      // Close any currently playing audio before opening a new one
      if (activeAudioIndex !== null && audioRefs.current[activeAudioIndex]) {
        try {
          audioRefs.current[activeAudioIndex]?.pause();
        } catch (e) {
          console.error('Error pausing audio:', e);
        }

        // Reset error for the previously active audio
        setAudioErrors(prev => {
          if (prev[activeAudioIndex]) {
            const newErrors = { ...prev };
            delete newErrors[activeAudioIndex];
            return newErrors;
          }
          return prev; // No change needed if no error exists
        });
      }
      // Set the new active audio index
      setActiveAudioIndex(index);
    }
  };

  if (parseError) {
    return <div className="p-4 bg-red-50 rounded-lg text-red-700">Error: {parseError}</div>;
  }

  if (!transcriptItems || transcriptItems.length === 0) {
    return (
      <div className="p-4 bg-gray-50 rounded-lg text-gray-700">
        No transcript with audio available for this call.
      </div>
    );
  }

  return (
    <div className="transcript-with-audio">
      {transcriptItems.map((item, index) => (
        <div
          key={index}
          className={`transcript-item mb-4 ${activeAudioIndex === index ? 'active-audio' : ''}`}
        >
          <div className="flex items-start">
            {/* Play button */}
            <button
              onClick={() => toggleAudio(index)}
              className="mr-2 p-2 rounded-full hover:bg-gray-200 transition-colors"
              aria-label={activeAudioIndex === index ? 'Pause audio' : 'Play audio'}
            >
              {activeAudioIndex === index ? (
                <HiPause className="w-5 h-5 text-blue-600" />
              ) : (
                <HiPlay className="w-5 h-5 text-blue-600" />
              )}
            </button>

            {/* Transcript text */}
            <div className="flex-1">
              <div className="p-3 bg-gray-50 rounded-lg text-gray-700">
                {item.text.startsWith('Patient:') ? (
                  <div className="flex items-start">
                    <HiUser className="w-4 h-4 mt-1 mr-2 text-blue-600 flex-shrink-0" />
                    <span>{item.text}</span>
                  </div>
                ) : item.text.startsWith('Heather:') || item.text.startsWith('Agent:') ? (
                  <div className="flex items-start">
                    <HiOutlineUserCircle className="w-4 h-4 mt-1 mr-2 text-green-600 flex-shrink-0" />
                    <span>{item.text}</span>
                  </div>
                ) : (
                  <span>{item.text}</span>
                )}
              </div>

              {/* Audio player (shown only when active) */}
              {activeAudioIndex === index && (
                <div className="mt-2">
                  <SessionAudioPlayer
                    voicemailUrl={item.recordUrl}
                    autoPlay={true}
                    onError={errorMsg => {
                      console.error('Audio player error:', errorMsg);
                      // Only update if the error is different to prevent re-render loops
                      setAudioErrors(prev => {
                        if (prev[index] !== errorMsg) {
                          return {
                            ...prev,
                            [index]: errorMsg,
                          };
                        }
                        return prev;
                      });
                    }}
                    ref={element => {
                      if (element) {
                        // Get the audio element from the SessionAudioPlayer
                        const audioElement = element.querySelector('audio');
                        if (audioElement instanceof HTMLAudioElement) {
                          registerAudioRef(index, audioElement);
                        }
                      } else {
                        registerAudioRef(index, null);
                      }
                    }}
                  />
                  {audioErrors[index] && (
                    <div className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                      Error: {audioErrors[index]}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}

      <style jsx>{`
        .transcript-item:nth-child(odd) .bg-gray-50 {
          background-color: #f0f7ff;
          border-left: 3px solid #3b82f6;
        }

        .transcript-item:nth-child(even) .bg-gray-50 {
          background-color: #f5f5f5;
          border-left: 3px solid #10b981;
        }

        .active-audio .bg-gray-50 {
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
        }
      `}</style>
    </div>
  );
};

export default TranscriptWithAudio;
