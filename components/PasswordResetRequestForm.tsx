import { useState, FormEvent } from 'react';
import { Label, TextInput, Alert } from 'flowbite-react';
import CustomButton from '@/components/CustomButton';
import { HiCheck, HiExclamation } from 'react-icons/hi';

interface PasswordResetRequestFormProps {
  onSuccess?: () => void;
}

const PasswordResetRequestForm = ({ onSuccess }: PasswordResetRequestFormProps) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Email validation
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validate email
    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to send password reset email');
      }

      setSuccess('Password reset email sent. Please check your inbox and follow the instructions.');

      // Clear form
      setEmail('');

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Password reset error:', error);
      setError((error as Error)?.message || 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Reset Password</h3>

      {error && (
        <Alert color="failure" icon={HiExclamation}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert color="success" icon={HiCheck}>
          {success}
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <div className="mb-1">
            <Label htmlFor="email" value="Email" />
          </div>
          <TextInput
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
          />
          <p className="mt-1 text-xs text-gray-500">
            Enter the email address associated with your account
          </p>
        </div>

        <CustomButton type="submit" color="blue" disabled={isLoading}>
          {isLoading ? 'Sending Reset Link...' : 'Send Reset Link'}
        </CustomButton>
      </form>
    </div>
  );
};

export default PasswordResetRequestForm;
