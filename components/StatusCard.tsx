import { useState, useEffect } from 'react';
import { Card, Badge, Button, Select, Checkbox, Label, Spinner } from 'flowbite-react';
import { HiExclamation } from 'react-icons/hi';
import { getToken } from '@/utils/auth';
import AfterHoursStatus from './AfterHoursStatus';
import { CallType } from '@/models/CallTypes';

interface StatusCardProps {
  callId: string;
  callType?: CallType | CallType[];
  callTimestamp?: string;
  priorityScore: number;
  setPriorityScore: (score: number) => void;
  isUrgent: boolean;
  setIsUrgent: (urgent: boolean) => void;
  handleUpdateCall: () => void;
  isSaving: boolean;
  getPriorityClass: (score: number) => string;
}

const StatusCard: React.FC<StatusCardProps> = ({
  callId,
  callType,
  callTimestamp,
  priorityScore,
  setPriorityScore,
  isUrgent,
  setIsUrgent,
  handleUpdateCall,
  isSaving,
  getPriorityClass,
}) => {
  const [isAfterHours, setIsAfterHours] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkIfAfterHours = async () => {
      try {
        setLoading(true);

        // First check if the call type is AFTER_HOURS
        const isAfterHoursType = Array.isArray(callType)
          ? callType.includes(CallType.AFTER_HOURS)
          : callType === CallType.AFTER_HOURS;

        if (isAfterHoursType) {
          setIsAfterHours(true);
          setLoading(false);
          return;
        }

        // If not an after-hours call type, check for existing logs
        const response = await fetch(`/api/calls/after-hours/call-logs/${callId}`, {
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
        });

        if (response.ok) {
          const result = await response.json();
          const hasLogs = result.success && result.data && result.data.length > 0;
          setIsAfterHours(hasLogs);
        } else {
          setIsAfterHours(false);
        }
      } catch (error) {
        console.error('Error checking after-hours status:', error);
        setIsAfterHours(false);
      } finally {
        setLoading(false);
      }
    };

    checkIfAfterHours();
  }, [callId, callType]);

  if (loading) {
    return (
      <Card>
        <div className="flex justify-center items-center h-40">
          <Spinner size="lg" />
        </div>
      </Card>
    );
  }

  // Show After-Hours Status if this is an after-hours call
  if (isAfterHours) {
    return <AfterHoursStatus callId={callId} callTimestamp={callTimestamp} />;
  }

  // Show regular Priority & Status card for non-after-hours calls
  return (
    <Card>
      <h2 className="text-xl font-bold mb-4">Priority & Status</h2>

      <div className="space-y-6">
        <div>
          <div className="flex justify-between items-center mb-2">
            <Label htmlFor="priority" value="Priority Score" />
            <Badge className={getPriorityClass(priorityScore)}>{priorityScore}/10</Badge>
          </div>
          <Select
            id="priority"
            value={priorityScore.toString()}
            onChange={e => setPriorityScore(parseInt(e.target.value))}
          >
            <option value="0">0 - No priority</option>
            <option value="1">1 - Very low</option>
            <option value="2">2 - Low</option>
            <option value="3">3 - Below average</option>
            <option value="4">4 - Average</option>
            <option value="5">5 - Moderate</option>
            <option value="6">6 - Above average</option>
            <option value="7">7 - High</option>
            <option value="8">8 - Very high</option>
            <option value="9">9 - Urgent</option>
            <option value="10">10 - Critical</option>
          </Select>
        </div>

        <div className="flex items-center gap-2">
          <Checkbox id="urgent" checked={isUrgent} onChange={e => setIsUrgent(e.target.checked)} />
          <Label htmlFor="urgent" className="flex items-center">
            <HiExclamation className="w-5 h-5 text-red-500 mr-1" />
            Mark as Urgent
          </Label>
        </div>

        <Button
          onClick={handleUpdateCall}
          disabled={isSaving}
          isProcessing={isSaving}
          processingSpinner={<Spinner size="sm" light={true} />}
        >
          Update Status
        </Button>
      </div>
    </Card>
  );
};

export default StatusCard;
