/**
 * Client-safe utility functions for call duration parsing and display logic
 * These functions don't depend on Firebase or server-side code
 */

import { CallType } from '@/models/CallTypes';

/**
 * Structure of transcript items in transcriptionWithAudio
 */
interface TranscriptItem {
  text: string;
  recordUrl?: string;
}

/**
 * Type guard to check if an unknown value is a valid TranscriptItem
 */
function isTranscriptItem(item: unknown): item is TranscriptItem {
  return (
    typeof item === 'object' &&
    item !== null &&
    'text' in item &&
    typeof (item as Record<string, unknown>).text === 'string'
  );
}

/**
 * Parse call duration string to seconds
 * Supports formats: "HH:MM:SS", "MM:SS", "393.473262801s", "5 min", "5 mins", "13 sec", "13 secs", plain numbers
 * @param durationStr Duration string to parse
 * @returns Duration in seconds, or 0 if invalid
 */
export function parseDurationToSeconds(durationStr?: string | null): number {
  if (!durationStr) return 0;
  const trimmed = durationStr.trim();

  // Dialogflow duration format: "393.473262801s"
  const dialogflowMatch = trimmed.match(/^([\d.]+)s$/);
  if (dialogflowMatch) {
    const seconds = parseFloat(dialogflowMatch[1]);
    return isNaN(seconds) ? 0 : Math.round(seconds);
  }

  // HH:MM:SS format
  if (/^\d{1,2}:\d{2}:\d{2}$/.test(trimmed)) {
    const [h, m, s] = trimmed.split(':').map(Number);
    return h * 3600 + m * 60 + s;
  }

  // MM:SS format
  if (/^\d{1,2}:\d{2}$/.test(trimmed)) {
    const [m, s] = trimmed.split(':').map(Number);
    return m * 60 + s;
  }

  // "5 min" / "5 mins" / "3.0 min" / "1.7 min" format
  const minMatch = trimmed.match(/^(\d+(?:\.\d+)?)\s*min(s)?/i);
  if (minMatch) {
    return Math.round(parseFloat(minMatch[1]) * 60);
  }

  // "13 sec" / "13 secs" format
  const secMatch = trimmed.match(/^(\d+(?:\.\d+)?)\s*sec(s)?/i);
  if (secMatch) {
    return Math.round(parseFloat(secMatch[1]));
  }

  // Plain numeric seconds
  const seconds = Number(trimmed);
  return Number.isFinite(seconds) ? seconds : 0;
}

/**
 * Calculate call duration based on transcriptionWithAudio content
 * Uses word count and conversation analysis as a fallback for calls with duration 0
 * @param transcriptionWithAudio JSON string or array containing transcript with audio records
 * @returns Estimated duration in seconds, or 0 if cannot calculate
 */
export function calculateDurationFromTranscript(
  transcriptionWithAudio?: string | unknown[] | null,
): number {
  if (!transcriptionWithAudio) return 0;

  try {
    let transcriptItems: unknown[];

    // Handle both JSON string and already-parsed array
    if (typeof transcriptionWithAudio === 'string') {
      // Parse the JSON string
      transcriptItems = JSON.parse(transcriptionWithAudio);
    } else if (Array.isArray(transcriptionWithAudio)) {
      // Already an array
      transcriptItems = transcriptionWithAudio;
    } else {
      // Invalid format
      return 0;
    }

    // Ensure it's an array
    if (!Array.isArray(transcriptItems) || transcriptItems.length === 0) {
      return 0;
    }

    // Check if we have both Patient and Heather interactions
    const hasPatientInteraction = transcriptItems.some(
      item => isTranscriptItem(item) && item.text.startsWith('Patient:'),
    );
    const hasHeatherInteraction = transcriptItems.some(
      item =>
        isTranscriptItem(item) &&
        (item.text.startsWith('Heather:') || item.text.startsWith('Agent:')),
    );

    // Only calculate if we have both sides of the conversation
    if (!hasPatientInteraction || !hasHeatherInteraction) {
      return 0;
    }

    // Calculate total word count from all interactions
    let totalWords = 0;
    let interactionCount = 0;

    for (const item of transcriptItems) {
      if (isTranscriptItem(item)) {
        // Remove the speaker prefix (e.g., "Patient: " or "Heather: ")
        const cleanText = item.text.replace(/^(Patient|Heather|Agent):\s*/i, '').trim();

        if (cleanText.length > 0) {
          // Count words (split by whitespace and filter out empty strings)
          const words = cleanText.split(/\s+/).filter(word => word.length > 0);
          totalWords += words.length;
          interactionCount++;
        }
      }
    }

    // If no meaningful content, return 0
    if (totalWords === 0 || interactionCount === 0) {
      return 0;
    }

    // Calculate duration using multiple factors:
    // 1. Average speaking rate: ~150 words per minute (2.5 words per second)
    // 2. Conversation overhead: pauses, thinking time, etc.
    // 3. Interaction count: more back-and-forth takes longer

    const baseSecondsFromWords = totalWords / 2.5; // Base time for speaking
    const pauseTime = interactionCount * 2; // 2 seconds pause per interaction
    const minimumCallTime = 10; // Minimum realistic call time

    // Calculate estimated duration
    const estimatedDuration = Math.max(
      minimumCallTime,
      Math.round(baseSecondsFromWords + pauseTime),
    );

    // Cap at reasonable maximum (10 minutes = 600 seconds)
    // If transcript suggests longer, something might be wrong with the data
    return Math.min(estimatedDuration, 600);
  } catch {
    // If JSON parsing fails or any other error, return 0
    return 0;
  }
}

/**
 * Get effective call duration, with fallback calculation for calls with duration 0
 * @param call Call object with duration and transcriptionWithAudio
 * @returns Duration in seconds
 */
export function getEffectiveCallDuration(call: {
  duration?: string | null;
  transcriptionWithAudio?: string | unknown[] | null;
}): number {
  const originalDuration = parseDurationToSeconds(call.duration);

  // If we have a valid duration > 0, use it
  if (originalDuration > 0) {
    return originalDuration;
  }

  // Otherwise, try to calculate from transcript
  return calculateDurationFromTranscript(call.transcriptionWithAudio);
}

/**
 * Check if a call should be displayed in the portal
 * Filters out calls under 5 seconds as per business requirements
 * @param call The call to check
 * @returns True if call should be displayed, false otherwise
 */
export function shouldDisplayCallInPortal(call: { duration?: string | null }): boolean {
  const durationSeconds = parseDurationToSeconds(call.duration);
  return durationSeconds >= 5; // Hide calls under 5 seconds
}

/**
 * Check if a call should be automatically marked as DISCONNECTED
 * For calls over 20 seconds with no meaningful call type recorded
 * @param call The call to check
 * @returns True if call should be marked as disconnected
 */
export function shouldMarkAsDisconnected(call: {
  duration?: string | null;
  type?: CallType | null;
  callTypes?: CallType[] | null;
}): boolean {
  const durationSeconds = parseDurationToSeconds(call.duration);

  // Must be over 20 seconds
  if (durationSeconds <= 20) {
    return false;
  }

  // Check if call has any meaningful types recorded
  const hasCallTypes = call.callTypes && call.callTypes.length > 0;
  const hasCallType = call.type !== undefined && call.type !== null;

  if (!hasCallTypes && !hasCallType) {
    // No types recorded at all - mark as disconnected
    return true;
  }

  // Check if only has "OTHER" type (which means no meaningful type)
  if (hasCallTypes) {
    const meaningfulTypes = call.callTypes?.filter(type => type !== CallType.OTHER) || [];
    if (meaningfulTypes.length === 0) {
      return true; // Only has OTHER type(s)
    }
  } else if (hasCallType && call.type === CallType.OTHER) {
    return true; // Only has OTHER type
  }

  return false;
}

/**
 * Format Dialogflow conversation duration from seconds string to human-readable format
 * @param durationStr Duration string from Dialogflow API (e.g., "393.473262801s")
 * @returns Formatted duration string (e.g., "6.6 min" or "45 sec") or empty string if invalid
 */
export function formatDialogflowDuration(durationStr: string): string {
  if (!durationStr) return '';

  // Parse the duration string (e.g., "49.221030099s")
  const durationMatch = durationStr.match(/^([\d.]+)s$/);
  if (!durationMatch || !durationMatch[1]) return '';

  const durationSeconds = parseFloat(durationMatch[1]);
  if (isNaN(durationSeconds)) return '';

  // Format the duration consistently
  if (durationSeconds < 60) {
    return `${durationSeconds.toFixed(0)} sec`;
  } else {
    const minutes = (durationSeconds / 60).toFixed(1);
    return `${minutes} min`;
  }
}
