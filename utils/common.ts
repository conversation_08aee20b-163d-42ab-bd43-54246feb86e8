/**
 * Generates a random code of a given length
 * @param length - The length of the code to generate
 * @returns A random code of the given length
 */
export const generateRandomCode = (length: number = 6) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = '';
  for (let i = 0; i < length; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return code;
};
