import { CallType } from '@/models/CallTypes';
import { mergeCallTypes } from '@/lib/external-api/v2/utils/call-type-utils';
import logger from '@/lib/external-api/v2/utils/logger';

/**
 * Detects if a call is an after-hours call based on transcript content
 * @param transcript The call transcript text
 * @returns true if after-hours call is detected
 */
export function isAfterHoursCall(transcript: string | null | undefined): boolean {
  if (!transcript) return false;
  
  // Check for the specific after-hours greeting message
  return transcript.includes('You have reached the on call Center for University Retina');
}

/**
 * Adds AFTER_HOURS type to call types array if after-hours call is detected
 * @param transcript The call transcript text
 * @param existingCallTypes Current call types array
 * @param callId Call ID for logging
 * @returns Updated call types array with AFTER_HOURS added if detected
 */
export function addAfterHoursTypeIfDetected(
  transcript: string | null | undefined,
  existingCallTypes: number[] | null | undefined,
  callId?: string
): number[] | null {
  if (!isAfterHoursCall(transcript)) {
    return existingCallTypes || null;
  }

  // Check if AFTER_HOURS is already present
  if (existingCallTypes && existingCallTypes.includes(CallType.AFTER_HOURS)) {
    return existingCallTypes;
  }

  // Add AFTER_HOURS type
  const updatedCallTypes = mergeCallTypes(existingCallTypes, CallType.AFTER_HOURS);
  
  logger.info(
    { 
      callId, 
      originalCallTypes: existingCallTypes, 
      updatedCallTypes 
    },
    'Auto-detected AFTER_HOURS call based on transcript content'
  );

  return updatedCallTypes;
}

/**
 * Processes a call object and adds AFTER_HOURS type if detected
 * @param call Call object with transcript and callTypes
 * @returns Updated call object with AFTER_HOURS type added if detected
 */
export function processCallForAfterHours<T extends {
  id?: string;
  transcription?: string | null;
  callTypes?: number[] | null;
}>(call: T): T {
  if (!call.transcription) {
    return call;
  }

  const updatedCallTypes = addAfterHoursTypeIfDetected(
    call.transcription,
    call.callTypes,
    call.id
  );

  if (updatedCallTypes !== call.callTypes) {
    return {
      ...call,
      callTypes: updatedCallTypes
    };
  }

  return call;
}
