import {
  Storage,
  GetFilesOptions,
  GetSignedUrlConfig,
  StorageOptions,
} from '@google-cloud/storage';
import {
  getDialogflowConversation,
  DialogflowConversation,
  DialogflowMessage,
} from '@/lib/dialogflow';
import { DialogflowAuthService } from '@/lib/dialogflow/auth';
import logger from '@/lib/external-api/v2/utils/logger';

// Add new interface for transcript data with timestamps
export interface TranscriptEntry {
  text: string;
  timestamp: number;
  speaker: string;
}

// Filter phrases to exclude from transcript entries
const TRANSCRIPT_FILTER_PHRASES = [
  'Yes',
  "Let's see",
  'Wait a moment while I look you up',
  'Just a second, let me search you in the system',
  "I am sorry. I didn't hear you. Can you repeat?",
  "Oops. I didn't catch that. Can you say it again?",
  'Thank you for calling University Retina. Have a great rest of your day.',
  'Hold on please while I check your appointments',
  'One second, let me check your appointments',
  'Wait a moment while I check available time slots',
  'Just a second, let me search for available time slots',
  'Just a moment, let me book a new appointment for you',
  'Wait a second while I schedule an appointment for you',
  'Wait a moment while I check available time slots',
  'Just a second, let me search for available time slots',
  'Hold on please while I check your existing appointments',
  "One second, I'll check your existing appointments",
  'Just a moment, let me reschedule your appointment',
  'Wait a second while I reschedule your appointment',
  'Hold on please while I check your appointments',
  "One second, I'll check your appointments",
  'Just a moment, let me cancel your appointment',
  'Wait a second while I cancel your appointment',
];

/**
 * Google Cloud Storage utility for accessing files in GCP buckets
 * Provides methods to list, download, and get signed URLs for files
 */
export class GcpStorageService {
  private storage: Storage;

  /**
   * Create a new GCP Storage service
   * Uses application default credentials, provided keyFilename, or credentials object
   * @param options Configuration options
   */
  constructor(options: StorageOptions = {}) {
    // Initialize the storage client with the provided options
    this.storage = new Storage(options);
  }

  /**
   * Get the latest patient audio file for a specific session ID
   * This function returns only patient audio records, not agent records
   * If no patient audio records are found, it returns null
   * @param bucketName The name of the bucket
   * @param sessionId The session ID to find audio for
   * @param minTimestamp Optional minimum timestamp to filter files (Unix timestamp in ms)
   * @returns Promise with the file name and signed URL, or null if no patient audio is found
   * @returns Promise with the file name and signed URL, or null if no patient audio is found
   */
  async getLatestAudioFileForSession(
    bucketName: string,
    sessionId: string,
    minTimestamp?: number,
  ): Promise<{ fileName: string; url: string } | null> {
    try {
      // Get transcript with audio records to identify patient records
      const transcriptWithAudio = await this.getTranscriptWithAudioRecords(
        bucketName,
        sessionId,
        minTimestamp,
      );

      // If we have transcript data with audio records
      if (transcriptWithAudio && transcriptWithAudio.length > 0) {
        // Filter for patient entries only (text starts with "Patient: ")
        const patientEntries = transcriptWithAudio.filter(
          entry => entry.text && entry.text.startsWith('Patient: '),
        );

        // If we have patient entries, get the latest one by timestamp
        if (patientEntries.length > 0) {
          // Sort by timestamp (descending) to get the latest
          patientEntries.sort((a, b) => {
            const aTimestamp = a.audioTimestamp || a.transcriptTimestamp || 0;
            const bTimestamp = b.audioTimestamp || b.transcriptTimestamp || 0;
            return bTimestamp - aTimestamp; // Descending order
          });

          // Get the latest patient entry
          const latestPatientEntry = patientEntries[0];

          // Extract the file name from the recordUrl
          // Format: gcp://bucket-name/file-path
          const match = latestPatientEntry.recordUrl.match(/^gcp:\/\/[^\/]+\/(.+)$/);
          if (match && match[1]) {
            const fileName = match[1];

            return {
              fileName,
              url: latestPatientEntry.recordUrl,
            };
          }
        }
      }

      // If we couldn't find a patient audio file, return null
      return null;
    } catch (error) {
      console.error('Error getting latest audio file for session:', error);
      throw new Error(
        `Failed to get latest audio file for session ${sessionId}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Get all audio files for a specific session ID with precise filtering
   * @param bucketName The name of the bucket
   * @param sessionId The session ID to find audio for
   * @param minTimestamp Optional minimum timestamp to filter files (Unix timestamp in ms)
   * @param maxResults Optional maximum number of results to return (default: 100)
   * @returns Promise with array of objects containing file name, signed URL, and extracted text
   */
  async getAllAudioFilesForSession(
    bucketName: string,
    sessionId: string,
    minTimestamp?: number,
    maxResults = 200,
  ): Promise<Array<{ fileName: string; url: string; text: string }>> {
    logger.info(
      {
        context: 'GcpStorageService.getAllAudioFilesForSession',
        bucketName,
        sessionId,
        minTimestamp,
        maxResults,
      },
      'Getting audio files for session with precise filtering',
    );

    try {
      // Get a reference to the bucket
      const bucket = this.storage.bucket(bucketName);

      // Use environment variable for project ID instead of hardcoded value
      const projectId = process.env.GCP_PROJECT_ID || 'frontdesk-454309';

      // Use the correct precise prefix that includes the session ID to reduce the number of files fetched
      // This significantly reduces memory usage by avoiding fetching all audio files
      const prefix = `audio/${projectId}_${sessionId}`;

      let allMatchingFiles: import('@google-cloud/storage').File[] = [];

      try {
        const [files] = await bucket.getFiles({
          prefix,
          maxResults: maxResults * 2, // Get a bit more than needed for filtering
        });

        // Filter files that actually contain the session ID
        const sessionFiles = files.filter(file => {
          // Skip directories or empty files
          if (file.name.endsWith('/') || file.name === prefix + '/') {
            return false;
          }

          // Double-check that the file name contains the full session ID
          return file.name.includes(sessionId);
        });

        if (sessionFiles.length > 0) {
          allMatchingFiles.push(...sessionFiles);
          logger.info(
            {
              context: 'GcpStorageService.getAllAudioFilesForSession',
              prefix,
              foundFiles: sessionFiles.length,
            },
            'Found files with precise prefix',
          );
        }
      } catch (prefixError) {
        // Continue if this fails
        logger.warn(
          {
            context: 'GcpStorageService.getAllAudioFilesForSession',
            prefix,
            error: prefixError instanceof Error ? prefixError.message : String(prefixError),
          },
          'Failed to fetch files with prefix',
        );
      }

      // If no files found with precise prefixes, fall back to a limited general search
      if (allMatchingFiles.length === 0) {
        logger.info(
          {
            context: 'GcpStorageService.getAllAudioFilesForSession',
            sessionId,
          },
          'No files found with precise prefixes, falling back to limited general search',
        );

        const [files] = await bucket.getFiles({
          prefix: 'audio/',
          maxResults: 1000, // Limit to 1000 files max to prevent memory overflow
        });

        allMatchingFiles = files.filter(file => {
          // Skip directories or empty files
          if (file.name.endsWith('/') || file.name === 'audio/') {
            return false;
          }

          // Check if the file name contains the session ID
          return file.name.includes(sessionId);
        });
      }

      // Apply timestamp filtering if provided
      if (minTimestamp && allMatchingFiles.length > 0) {
        allMatchingFiles = allMatchingFiles.filter(file => {
          // Try to get timestamp from metadata if available
          if (file.metadata && file.metadata.timeCreated) {
            const fileTimestamp = new Date(file.metadata.timeCreated).getTime();
            return fileTimestamp > minTimestamp;
          }

          // If no metadata, try to extract timestamp from filename
          const match = file.name.match(/_([0-9]+)(?:\.[^.]+)?$/);
          if (match && match[1]) {
            let fileTimestamp = parseInt(match[1], 10);

            // Handle different timestamp formats
            if (fileTimestamp.toString().length > 13) {
              // Truncate overly precise timestamps to milliseconds
              fileTimestamp = parseInt(fileTimestamp.toString().substring(0, 13), 10);
            } else if (fileTimestamp > 0 && fileTimestamp.toString().length === 10) {
              fileTimestamp = fileTimestamp * 1000; // Convert seconds to milliseconds
            }

            return fileTimestamp > minTimestamp;
          }

          return true; // Include if we can't determine timestamp
        });
      }

      // Limit results to prevent memory issues
      if (allMatchingFiles.length > maxResults) {
        allMatchingFiles = allMatchingFiles.slice(0, maxResults);
        logger.warn(
          {
            context: 'GcpStorageService.getAllAudioFilesForSession',
            totalFound: allMatchingFiles.length,
            maxResults,
          },
          'Truncated results to prevent memory issues',
        );
      }

      // If no matching files, return empty array
      if (allMatchingFiles.length === 0) {
        return [];
      }

      // Sort files by timestamp (ascending) to get them in chronological order
      allMatchingFiles.sort((a, b) => {
        const getTimestamp = (file: { name: string; metadata?: { timeCreated?: string } }) => {
          // First try to get timestamp from metadata
          if (file.metadata && file.metadata.timeCreated) {
            return new Date(file.metadata.timeCreated).getTime();
          }

          // Extract timestamp from filename
          const match = file.name.match(/_([0-9]+)(?:\.[^.]+)?$/);
          if (match && match[1]) {
            let timestamp = parseInt(match[1], 10);

            // Handle different timestamp formats
            if (timestamp.toString().length > 13) {
              timestamp = parseInt(timestamp.toString().substring(0, 13), 10);
            } else if (timestamp > 0 && timestamp.toString().length === 10) {
              timestamp = timestamp * 1000;
            }

            return timestamp;
          }

          return 0;
        };

        const aTimestamp = getTimestamp(a);
        const bTimestamp = getTimestamp(b);

        return aTimestamp - bTimestamp; // Ascending order for chronological sequence
      });

      // Generate signed URLs for files (limit concurrent requests to prevent overwhelming)
      const batchSize = 10;
      const result: Array<{ fileName: string; url: string; text: string }> = [];

      for (let i = 0; i < allMatchingFiles.length; i += batchSize) {
        const batch = allMatchingFiles.slice(i, i + batchSize);

        const batchResults = await Promise.all(
          batch.map(async (file, batchIndex) => {
            try {
              // Generate a signed URL for the file
              const [url] = await file.getSignedUrl({
                action: 'read',
                expires: Date.now() + 15 * 60 * 1000, // 15 minutes
              });

              return {
                fileName: file.name,
                url,
                text: `Audio recording ${i + batchIndex + 1}`,
              };
            } catch (urlError) {
              logger.warn(
                {
                  context: 'GcpStorageService.getAllAudioFilesForSession',
                  fileName: file.name,
                  error: urlError instanceof Error ? urlError.message : String(urlError),
                },
                'Failed to generate signed URL for file',
              );
              return null;
            }
          }),
        );

        // Add non-null results
        result.push(...batchResults.filter(r => r !== null));
      }

      logger.info(
        {
          context: 'GcpStorageService.getAllAudioFilesForSession',
          audioFileCount: result.length,
          sessionId,
        },
        'Successfully got audio files for session with precise filtering',
      );

      return result;
    } catch (error) {
      logger.error(
        {
          context: 'GcpStorageService.getAllAudioFilesForSession',
          sessionId,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error getting audio files for session',
      );
      throw new Error(
        `Failed to get audio files for session ${sessionId}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * List files in a bucket with optional prefix filter
   * @param bucketName The name of the bucket
   * @param options Options for listing files (prefix, delimiter, etc.)
   * @returns Promise with array of file names
   */
  async listFiles(bucketName: string, options: GetFilesOptions = {}): Promise<string[]> {
    try {
      // Get a reference to the bucket
      const bucket = this.storage.bucket(bucketName);

      // Get files from the bucket with the provided options
      const [files] = await bucket.getFiles(options);

      // Return just the file names
      return files.map(file => file.name);
    } catch (error) {
      console.error('Error listing files from GCP bucket:', error);
      throw new Error(
        `Failed to list files from bucket ${bucketName}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Get a signed URL for a file (for direct browser access)
   * @param bucketName The name of the bucket
   * @param fileName The name of the file
   * @param options Options for the signed URL (expiration, etc.)
   * @returns Promise with the signed URL
   */
  async getSignedUrl(
    bucketName: string,
    fileName: string,
    options: Omit<GetSignedUrlConfig, 'action'> = {
      expires: Date.now() + 15 * 60 * 1000,
    }, // Default 15 minutes
  ): Promise<string> {
    try {
      // Get a reference to the file
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      // Generate a signed URL
      const [url] = await file.getSignedUrl({
        action: 'read',
        ...options,
      });

      return url;
    } catch (error) {
      console.error('Error generating signed URL for GCP bucket file:', error);
      throw new Error(
        `Failed to generate signed URL for file ${fileName} in bucket ${bucketName}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Check if a file exists in the bucket
   * @param bucketName The name of the bucket
   * @param fileName The name of the file to check
   * @returns Promise with boolean indicating if the file exists
   */
  async fileExists(bucketName: string, fileName: string): Promise<boolean> {
    try {
      // Get a reference to the file
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      // Check if the file exists
      const [exists] = await file.exists();
      return exists;
    } catch (error) {
      console.error('Error checking if file exists in GCP bucket:', error);
      throw new Error(
        `Failed to check if file ${fileName} exists in bucket ${bucketName}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Get metadata for a file
   * @param bucketName The name of the bucket
   * @param fileName The name of the file
   * @returns Promise with the file metadata
   */
  async getFileMetadata(bucketName: string, fileName: string): Promise<Record<string, unknown>> {
    try {
      // Get a reference to the file
      const bucket = this.storage.bucket(bucketName);
      const file = bucket.file(fileName);

      // Get the metadata
      const [metadata] = await file.getMetadata();
      return metadata;
    } catch (error) {
      console.error('Error getting file metadata from GCP bucket:', error);
      throw new Error(
        `Failed to get metadata for file ${fileName} in bucket ${bucketName}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }

  /**
   * Extract transcript with timestamps from Dialogflow conversation
   * @param conversation Dialogflow conversation data
   * @returns Array of transcript entries with text, timestamp and speaker info
   */
  private extractTranscriptWithTimestamps(conversation: DialogflowConversation): TranscriptEntry[] {
    try {
      if (!conversation?.interactions || conversation.interactions.length === 0) {
        return [];
      }

      // 1. Sort interactions chronologically (older first)
      const interactions = [...conversation.interactions].sort((a, b) => {
        const aTime = a.createTime ? new Date(a.createTime as string | number | Date).getTime() : 0;
        const bTime = b.createTime ? new Date(b.createTime as string | number | Date).getTime() : 0;
        return aTime - bTime;
      });

      const transcriptEntries: TranscriptEntry[] = [];

      // Helper function to extract text from message objects
      const pickText = (msg: DialogflowMessage): string | null => {
        if (msg.text?.text?.length) return msg.text.text[0];
        if (msg.outputAudioText?.text) return msg.outputAudioText.text;
        if (msg.outputAudioText?.ssml)
          return msg.outputAudioText.ssml
            .replace(/<speak>/g, '')
            .replace(/<\/speak>/g, '')
            .trim();
        return null;
      };

      // Process each interaction to extract messages with timestamps
      for (const interaction of interactions) {
        const interactionTime =
          interaction.createTime &&
          (typeof interaction.createTime === 'string' ||
            typeof interaction.createTime === 'number' ||
            interaction.createTime instanceof Date)
            ? new Date(interaction.createTime).getTime()
            : Date.now();

        /* ----------  USER  ---------- */
        // Check for transcript in queryResult
        if (
          interaction.response?.queryResult?.transcript &&
          typeof interaction.response.queryResult.transcript === 'string' &&
          interaction.response.queryResult.transcript.trim()
        ) {
          transcriptEntries.push({
            text: interaction.response.queryResult.transcript.trim(),
            timestamp: interactionTime - 1000, // subtract 1 second to ensure agent messages come after customer
            speaker: 'Patient',
          });
        }
        // The most reliable source for customer utterances is queryText in the queryResult
        else if (
          interaction.response?.queryResult?.queryText &&
          typeof interaction.response.queryResult.queryText === 'string' &&
          interaction.response.queryResult.queryText.trim()
        ) {
          transcriptEntries.push({
            text: interaction.response.queryResult.queryText.trim(),
            timestamp: interactionTime - 1000,
            speaker: 'Patient',
          });
        }
        // Check request object which sometimes contains user messages
        else if (
          interaction.request?.text &&
          typeof interaction.request.text === 'string' &&
          interaction.request.text.trim()
        ) {
          transcriptEntries.push({
            text: interaction.request.text.trim(),
            timestamp: interactionTime - 1000,
            speaker: 'Patient',
          });
        }
        // Check for text in queryInput
        else if (
          interaction.request?.queryInput?.text?.text &&
          typeof interaction.request.queryInput.text.text === 'string' &&
          interaction.request.queryInput.text.text.trim()
        ) {
          transcriptEntries.push({
            text: interaction.request.queryInput.text.text.trim(),
            timestamp: interactionTime - 1000,
            speaker: 'Patient',
          });
        }
        // Other possible locations for user messages
        else if (
          interaction.request?.transcript &&
          typeof interaction.request.transcript === 'string' &&
          interaction.request.transcript.trim()
        ) {
          transcriptEntries.push({
            text: interaction.request.transcript.trim(),
            timestamp: interactionTime - 1000,
            speaker: 'Patient',
          });
        }
        // In some Dialogflow formats, the transcript is directly on the interaction
        else if (
          interaction.transcript &&
          typeof interaction.transcript === 'string' &&
          interaction.transcript.trim()
        ) {
          transcriptEntries.push({
            text: interaction.transcript.trim(),
            timestamp: interactionTime - 1000,
            speaker: 'Patient',
          });
        }

        /* ----------  AGENT (partials) ---------- */
        // Process partial responses from the agent - combine multiple messages into one entry
        const partialResponses =
          (interaction.partialResponses as {
            queryResult?: { responseMessages?: DialogflowMessage[] };
          }[]) || [];
        for (const partialResponse of partialResponses) {
          if (partialResponse.queryResult?.responseMessages) {
            const responseMessages = partialResponse.queryResult.responseMessages;

            if (Array.isArray(responseMessages)) {
              // Collect all text parts from all messages in this partial response
              const allTexts: string[] = [];

              for (const msg of responseMessages) {
                const text = pickText(msg);
                if (text && text.trim()) {
                  allTexts.push(text.trim());
                }
              }

              // If we have any text, combine it into a single entry
              if (allTexts.length > 0) {
                transcriptEntries.push({
                  text: allTexts.join(' '),
                  timestamp: interactionTime,
                  speaker: 'Heather',
                });
              }
            }
          }
        }

        /* ----------  AGENT (final) ---------- */
        // Process final responses from the agent - combine multiple messages into one entry
        if (interaction.response?.queryResult?.responseMessages) {
          const responseMessages = interaction.response.queryResult.responseMessages;

          if (Array.isArray(responseMessages)) {
            // Collect all text parts from all messages in this final response
            const allTexts: string[] = [];

            for (const msg of responseMessages) {
              // Text responses
              if (msg.text?.text && Array.isArray(msg.text.text)) {
                for (const text of msg.text.text) {
                  if (typeof text === 'string' && text.trim()) {
                    allTexts.push(text.trim());
                  }
                }
              }
              // Check for other text formats using the pickText helper
              else {
                const text = pickText(msg);
                if (text && text.trim()) {
                  allTexts.push(text.trim());
                }
              }
            }

            // If we have any text, combine it into a single entry
            if (allTexts.length > 0) {
              transcriptEntries.push({
                text: allTexts.join(' '),
                timestamp: interactionTime,
                speaker: 'Heather',
              });
            }
          }
        }

        // Specific location for the final agent response
        if (
          interaction.response?.queryResult?.fulfillmentText &&
          typeof interaction.response.queryResult.fulfillmentText === 'string' &&
          interaction.response.queryResult.fulfillmentText.trim()
        ) {
          transcriptEntries.push({
            text: interaction.response.queryResult.fulfillmentText.trim(),
            timestamp: interactionTime,
            speaker: 'Heather',
          });
        }

        // Other possible locations
        if (
          interaction.response?.text &&
          typeof interaction.response.text === 'string' &&
          interaction.response.text.trim()
        ) {
          transcriptEntries.push({
            text: interaction.response.text.trim(),
            timestamp: interactionTime,
            speaker: 'Heather',
          });
        }
      }

      // Sort entries by timestamp to ensure chronological order
      return transcriptEntries.sort((a, b) => a.timestamp - b.timestamp);
    } catch (error) {
      console.error('Error extracting transcript with timestamps:', error);
      return [];
    }
  }

  /**
   * Get all audio files for a session with transcript data - minimized version
   * @param bucketName The name of the bucket
   * @param sessionId The session ID to find audio for
   * @param minTimestamp Optional minimum timestamp to filter files (Unix timestamp in ms)
   * @param minimized When true, returns only text and recordUrl fields for better performance
   */
  async getTranscriptWithAudioRecords(
    bucketName: string,
    sessionId: string,
    minTimestamp: number | undefined,
    minimized: true,
  ): Promise<Array<{ text: string; recordUrl: string }>>;

  /**
   * Get all audio files for a session with transcript data - full version
   * @param bucketName The name of the bucket
   * @param sessionId The session ID to find audio for
   * @param minTimestamp Optional minimum timestamp to filter files (Unix timestamp in ms)
   * @param minimized When false or undefined, returns all fields including timestamps
   */
  async getTranscriptWithAudioRecords(
    bucketName: string,
    sessionId: string,
    minTimestamp?: number,
    minimized?: false,
  ): Promise<
    Array<{
      text: string;
      recordUrl: string;
      timestamp?: number;
      transcriptTimestamp?: number;
      audioTimestamp?: number;
      transcriptTimestampUtc?: string;
      audioTimestampUtc?: string;
    }>
  >;

  /**
   * Get all audio files for a session with transcript data
   * @param bucketName The name of the bucket
   * @param sessionId The session ID to find audio for
   * @param minTimestamp Optional minimum timestamp to filter files (Unix timestamp in ms)
   * @param minimized When true, returns only text and recordUrl fields for better performance
   * @returns Promise with array of objects containing:
   *   - text: The transcript text with speaker prefix
   *   - recordUrl: The GCP storage URL for the audio file
   *   - timestamp: The transcript timestamp (for backward compatibility) - only when minimized is false
   *   - transcriptTimestamp: The timestamp of the transcript entry in milliseconds - only when minimized is false
   *   - audioTimestamp: The timestamp of the audio file in milliseconds (extracted from filename) - only when minimized is false
   *   - transcriptTimestampUtc: The transcript timestamp in UTC ISO format - only when minimized is false
   *   - audioTimestampUtc: The audio timestamp in UTC ISO format - only when minimized is false
   *
   * Note: If the audio filename contains a timestamp with microseconds or nanoseconds precision
   * (more than 13 digits), it will be truncated to milliseconds precision for proper date handling.
   */
  async getTranscriptWithAudioRecords(
    bucketName: string,
    sessionId: string,
    minTimestamp?: number,
    minimized = true,
  ): Promise<
    Array<{
      text: string;
      recordUrl: string;
      timestamp?: number;
      transcriptTimestamp?: number;
      audioTimestamp?: number;
      transcriptTimestampUtc?: string;
      audioTimestampUtc?: string;
    }>
  > {
    try {
      // Get all audio files for the session
      const audioFiles = await this.getAllAudioFilesForSession(bucketName, sessionId, minTimestamp);

      if (!audioFiles || audioFiles.length === 0) {
        return [];
      }

      // Get the Dialogflow conversation data
      let transcriptEntries: TranscriptEntry[] = [];
      try {
        // Get GCP API credentials from environment variables
        const projectId = process.env.GCP_PROJECT_ID;
        const locationId = process.env.GCP_LOCATION_ID || 'global';
        const agentId = process.env.GCP_AGENT_ID;

        if (projectId && agentId) {
          // Get access token for Dialogflow API
          const accessToken = await DialogflowAuthService.getAccessToken();

          if (accessToken) {
            // Fetch conversation data from Dialogflow API
            const conversation = await getDialogflowConversation({
              projectId,
              locationId,
              agentId,
              sessionId,
              accessToken,
            });

            // Extract transcript with timestamps from conversation data
            const transcript = this.extractTranscriptWithTimestamps(conversation);
            logger.info(
              {
                agentId,
                sessionId,
                transcriptCount: transcript.length,
              },
              `Extracted transcript with timestamps from DialogFlow`,
            );

            if (transcript.length > 0) {
              transcriptEntries = transcript.filter(
                entry => !TRANSCRIPT_FILTER_PHRASES.includes(entry.text.trim()),
              );
            }
          }
        }
      } catch (dialogflowError) {
        // Continue with empty transcript
        logger.warn(
          {
            context: 'GcpStorageService.getTranscriptWithAudioRecords',
            innerError: dialogflowError,
          },
          'Failed to fetch Dialogflow conversation:',
        );
      }

      // If no transcript data is available, return empty array
      if (transcriptEntries.length === 0) {
        return [];
      }

      // Parse audio file timestamps from filenames for better matching
      const audioRecordsWithTimestamps = audioFiles.map(file => {
        // Extract the exact timestamp from the end of the filename
        // The format is typically: frontdesk-454309_065H6bnHYQTSE-luF1mmXO53A_audio-encoding-mulaw_8000_1_1746443169552013
        // We want to extract the last part (1746443169552013)

        // First, get the filename without extension
        const filenameWithoutExt = file.fileName.replace(/\.[^/.]+$/, '');

        // Split by underscore and get the last part
        const parts = filenameWithoutExt.split('_');
        const lastPart = parts[parts.length - 1];

        // Try to parse as a number
        let timestamp = 0;
        if (lastPart && /^\d+$/.test(lastPart)) {
          // Parse the raw timestamp
          let parsedTimestamp = parseInt(lastPart, 10);

          // Check if the timestamp is too long (more than milliseconds precision)
          // JavaScript Date can handle milliseconds (13 digits for current timestamps)
          if (parsedTimestamp.toString().length > 13) {
            // Truncate to milliseconds by dividing and keeping only the milliseconds part
            if (parsedTimestamp.toString().length === 16) {
              // microseconds (additional 3 digits)
              parsedTimestamp = Math.floor(parsedTimestamp / 1000);
            } else if (parsedTimestamp.toString().length === 19) {
              // nanoseconds (additional 6 digits)
              parsedTimestamp = Math.floor(parsedTimestamp / 1000000);
            } else {
              // For any other length, just take the first 13 digits (milliseconds precision)
              parsedTimestamp = parseInt(parsedTimestamp.toString().substring(0, 13), 10);
            }
          }

          timestamp = parsedTimestamp;
        }

        // Store the original extracted timestamp for debugging
        const originalExtractedTimestamp = timestamp;

        // If we couldn't extract a valid timestamp, try the old method as fallback
        if (!timestamp) {
          const match = file.fileName.match(/_([0-9]+)(?:\.[^.]+)?$/);

          if (match && match[1]) {
            // Parse the raw timestamp
            let parsedTimestamp = parseInt(match[1], 10);

            // Check if the timestamp is too long (more than milliseconds precision)
            if (parsedTimestamp.toString().length > 13) {
              // Truncate to milliseconds by dividing and keeping only the milliseconds part
              if (parsedTimestamp.toString().length === 16) {
                // microseconds (additional 3 digits)
                parsedTimestamp = Math.floor(parsedTimestamp / 1000);
              } else if (parsedTimestamp.toString().length === 19) {
                // nanoseconds (additional 6 digits)
                parsedTimestamp = Math.floor(parsedTimestamp / 1000000);
              } else {
                // For any other length, just take the first 13 digits (milliseconds precision)
                parsedTimestamp = parseInt(parsedTimestamp.toString().substring(0, 13), 10);
              }
            }

            timestamp = parsedTimestamp;
          } else {
            timestamp = 0;
          }
        }

        // Check if timestamp is in seconds (10 digits) and convert to milliseconds if needed
        if (timestamp > 0 && timestamp.toString().length === 10) {
          timestamp = timestamp * 1000; // Convert seconds to milliseconds
        }

        // Validate the timestamp is reasonable (between 2000 and 2100)
        const timestampDate = new Date(timestamp);
        const year = timestampDate.getFullYear();

        if (year < 2000 || year > 2100) {
          timestamp = Date.now(); // Use current time as fallback
        }

        // Compare with the original extracted timestamp
        if (timestamp !== originalExtractedTimestamp) {
          if (
            originalExtractedTimestamp > 0 &&
            originalExtractedTimestamp.toString().length >= 13
          ) {
            // Check if the original timestamp produces a reasonable date
            const originalDate = new Date(originalExtractedTimestamp);
            const originalYear = originalDate.getFullYear();
            if (originalYear >= 2000 && originalYear <= 2100) {
              timestamp = originalExtractedTimestamp;
            }
          }
        }

        return {
          fileName: file.fileName,
          timestamp,
          url: file.url,
        };
      });

      // IMPORTANT: Ensure transcriptEntries are sorted by timestamp
      // (should already be sorted from Dialogflow, but let's make sure)
      transcriptEntries.sort((a, b) => a.timestamp - b.timestamp);

      // Create a copy of audioRecordsWithTimestamps for matching
      // We'll remove items as they're matched to avoid duplicates
      const availableAudioRecords = [...audioRecordsWithTimestamps];

      // Prepare array for matched interactions
      const matchedInteractions: Array<{
        text: string;
        recordUrl: string;
        transcriptTimestamp: number;
        audioTimestamp: number;
      }> = [];

      // Match each transcript entry with the closest audio file by timestamp
      for (const entry of transcriptEntries) {
        // Skip entries with empty text
        if (!entry.text.trim()) {
          continue;
        }

        // Find the audio file with the closest timestamp
        const closestAudio = availableAudioRecords.reduce(
          (closest, current) => {
            if (!closest) return current;

            const currentDiff = Math.abs(current.timestamp - entry.timestamp);
            const closestDiff = Math.abs(closest.timestamp - entry.timestamp);

            return currentDiff < closestDiff ? current : closest;
          },
          null as (typeof availableAudioRecords)[0] | null,
        );

        // Only add if we found a matching audio file
        if (closestAudio) {
          // Extract the exact timestamp from the filename for the final output
          // This ensures we're using the exact timestamp from the filename
          const filenameWithoutExt = closestAudio.fileName.replace(/\.[^/.]+$/, '');
          const parts = filenameWithoutExt.split('_');
          const lastPart = parts[parts.length - 1];
          const exactTimestamp =
            lastPart && /^\d+$/.test(lastPart) ? parseInt(lastPart, 10) : closestAudio.timestamp;

          matchedInteractions.push({
            text: `${entry.speaker}: ${entry.text}`,
            recordUrl: `gcp://${bucketName}/${closestAudio.fileName}`,
            transcriptTimestamp: entry.timestamp,
            audioTimestamp: exactTimestamp, // Use the exact timestamp from the filename
          });

          // Remove this audio file from consideration to avoid duplicate matches
          const index = availableAudioRecords.findIndex(a => a.fileName === closestAudio.fileName);
          if (index !== -1) {
            availableAudioRecords.splice(index, 1);
          }
        }
      }

      // Matching complete

      // IMPORTANT: Sort strictly by transcript timestamp (text message timestamp)
      matchedInteractions.sort((a, b) => a.transcriptTimestamp - b.transcriptTimestamp);

      // Return the final interactions with both timestamps and their UTC representations included
      return matchedInteractions.map(({ text, recordUrl, transcriptTimestamp, audioTimestamp }) => {
        // If minimized flag is true, return only text and recordUrl
        if (minimized) {
          return {
            text,
            recordUrl,
          };
        }

        // Validate timestamps before converting to UTC
        // Use current time as fallback if timestamp is invalid or too large/small
        const now = Date.now();
        const maxValidTimestamp = now + 10 * 365 * 24 * 60 * 60 * 1000; // 10 years in the future
        const minValidTimestamp = now - 100 * 365 * 24 * 60 * 60 * 1000; // 100 years in the past

        // Validate transcript timestamp
        const validTranscriptTimestamp =
          transcriptTimestamp &&
          typeof transcriptTimestamp === 'number' &&
          transcriptTimestamp > minValidTimestamp &&
          transcriptTimestamp < maxValidTimestamp
            ? transcriptTimestamp
            : now;

        // Validate audio timestamp
        const validAudioTimestamp =
          audioTimestamp &&
          typeof audioTimestamp === 'number' &&
          audioTimestamp > minValidTimestamp &&
          audioTimestamp < maxValidTimestamp
            ? audioTimestamp
            : now;

        // Extract the filename from the recordUrl
        const urlParts = recordUrl.split('/');
        const filename = urlParts[urlParts.length - 1];

        // Extract the exact timestamp from the filename again as a final check
        let exactAudioTimestamp = validAudioTimestamp;
        if (filename) {
          const filenameWithoutExt = filename.replace(/\.[^/.]+$/, '');
          const parts = filenameWithoutExt.split('_');
          const lastPart = parts[parts.length - 1];
          if (lastPart && /^\d+$/.test(lastPart)) {
            // Parse the timestamp from the filename
            let parsedTimestamp = parseInt(lastPart, 10);

            // Check if the timestamp is too long (more than milliseconds precision)
            // JavaScript Date can handle milliseconds (13 digits for current timestamps)
            if (parsedTimestamp.toString().length > 13) {
              // Truncate to milliseconds by dividing and keeping only the milliseconds part
              if (parsedTimestamp.toString().length === 16) {
                // microseconds (additional 3 digits)
                parsedTimestamp = Math.floor(parsedTimestamp / 1000);
              } else if (parsedTimestamp.toString().length === 19) {
                // nanoseconds (additional 6 digits)
                parsedTimestamp = Math.floor(parsedTimestamp / 1000000);
              } else {
                // For any other length, just take the first 13 digits (milliseconds precision)
                parsedTimestamp = parseInt(parsedTimestamp.toString().substring(0, 13), 10);
              }
            }

            exactAudioTimestamp = parsedTimestamp;
          }
        }

        return {
          text,
          recordUrl,
          timestamp: validTranscriptTimestamp, // Keep original timestamp for backward compatibility
          transcriptTimestamp: validTranscriptTimestamp,
          audioTimestamp: exactAudioTimestamp, // Use the exact timestamp from the filename
          transcriptTimestampUtc: new Date(validTranscriptTimestamp).toISOString(),
          audioTimestampUtc: new Date(exactAudioTimestamp).toISOString(),
        };
      });
    } catch (error) {
      logger.error(
        {
          context: 'GcpStorageService.getTranscriptWithAudioRecords',
          innerError: error,
        },
        'Failed to get transcript with audio records',
      );
      throw new Error(
        `Failed to get transcript with audio records for session ${sessionId}: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
    }
  }
}

/**
 * Create a singleton instance of the GCP Storage service
 * Uses environment variables for authentication if available
 */
let gcpStorageInstance: GcpStorageService | null = null;

export function getGcpStorageService(): GcpStorageService {
  if (!gcpStorageInstance) {
    // Check for individual credential values
    const projectId = process.env.GCP_PROJECT_ID;
    const clientEmail = process.env.GCP_SERVICE_ACCOUNT_CLIENT_EMAIL;
    const privateKey = process.env.GCP_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n');

    if (projectId && clientEmail && privateKey) {
      gcpStorageInstance = new GcpStorageService({
        projectId,
        credentials: {
          client_email: clientEmail,
          private_key: privateKey,
        },
      });
      return gcpStorageInstance;
    }

    // Fall back to application default credentials
    gcpStorageInstance = new GcpStorageService();
  }

  return gcpStorageInstance;
}
