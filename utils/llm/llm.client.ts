import { GoogleGenAI, type GenerateContentResponse } from '@google/genai';
import { LLMGenerateContentParams } from './llm.types';
import { SUMMARIZE_CALL_PROMPT, SUMMARIZE_VOICEMAIL_PROMPT } from './prompts';
import logger from '@/lib/external-api/v2/utils/logger';
import { CLASSIFY_CALL_TYPE_PROMPT, CLASSIFY_CALL_TYPE_SCHEMA } from '@/utils/llm/prompts';
import { Call } from '@/models/Call';
import { CallType } from '@/models/CallTypes';

const { FIREBASE_PROJECT_ID, PROJECT_LOCATION = 'us-east1' } = process.env;
const DEFAULT_MODEL = 'gemini-2.0-flash-001';

export class LLMClient {
  private static instance: LLMClient;
  protected readonly ai: GoogleGenAI;

  protected constructor() {
    this.ai = new GoogleGenAI({
      vertexai: true,
      project: FIREBASE_PROJECT_ID,
      location: PROJECT_LOCATION,
    });
  }

  static getInstance() {
    if (!LLMClient.instance) {
      LLMClient.instance = new LLMClient();
    }
    return LLMClient.instance;
  }

  async generateContent(params: LLMGenerateContentParams): Promise<GenerateContentResponse> {
    return this.ai.models.generateContent({
      ...params,
      model: params.model || DEFAULT_MODEL,
    });
  }

  async summarizeCall(transcription: string): Promise<string | undefined> {
    logger.info({ transcription }, 'Summarizing call');
    const { text } = await this.generateContent({
      contents: [{ text: transcription }, { text: SUMMARIZE_CALL_PROMPT }],
      config: {
        temperature: 0.15,
      },
    });

    return text;
  }

  async summarizeVoiceMail(transcription: string): Promise<string | undefined> {
    logger.info({ transcription }, 'Summarizing voicemail');
    const { text } = await this.generateContent({
      contents: [{ text: SUMMARIZE_VOICEMAIL_PROMPT }, { text: transcription }],
      config: {
        temperature: 0.15,
      },
    });

    return text;
  }

  /**
   * Classify call types using the new comprehensive prompt (returns array of CallType IDs)
   * @param call Call object with transcription data
   * @param preserveTypes Optional array of call types to preserve in the output (e.g., AFTER_HOURS)
   * @returns Array of CallType IDs or null if classification fails
   */
  async classifyCallTypes(call: Call, preserveTypes?: CallType[]): Promise<CallType[] | null> {
    logger.info({ callId: call.id }, 'Classifying call types using comprehensive prompt');

    // Prepare call data for classification
    const callJson = {
      id: call.id,
      duration: call.duration,
      transcription:
        call.transcription ||
        ((call.transcriptionWithAudio as unknown as { text: string }[]) || [])
          .map((t: { text: string }) => t.text)
          .join('\n'),
    };

    if (!callJson.transcription) {
      logger.warn({ callId: call.id }, 'No transcription found for call');
      return null;
    }

    try {
      const response = await this.generateContent({
        contents: [{ text: CLASSIFY_CALL_TYPE_PROMPT }, { text: JSON.stringify(callJson) }],
        config: {
          ...CLASSIFY_CALL_TYPE_SCHEMA,
          temperature: 0.1, // Low temperature for consistent classification
        },
      });

      logger.debug({ callId: call.id, response }, 'Classified call types');
      const { text } = response;

      if (!text) {
        logger.warn({ callId: call.id }, 'No response text from LLM');
        return null;
      }

      const parsed = JSON.parse(text as string);
      const callTypes = parsed.call_types;

      if (!Array.isArray(callTypes)) {
        logger.warn({ callId: call.id, parsed }, 'Invalid call_types format in response');
        return null;
      }

      // Validate that all call types are valid numbers
      const validCallTypes = callTypes.filter(
        (type: unknown) => typeof type === 'number' && type >= 0 && type <= 17,
      );

      if (validCallTypes.length === 0) {
        logger.warn({ callId: call.id, callTypes }, 'No valid call types found in response');
        return [CallType.OTHER]; // Fallback to OTHER
      }

      // Preserve specified types (e.g., AFTER_HOURS) by adding them to the result
      let finalCallTypes = validCallTypes;
      if (preserveTypes && preserveTypes.length > 0) {
        // Add preserve types to the beginning of the array to maintain priority
        const typesToAdd = preserveTypes.filter(type => !validCallTypes.includes(type));
        finalCallTypes = [...typesToAdd, ...validCallTypes];

        logger.info(
          {
            callId: call.id,
            originalTypes: validCallTypes,
            preserveTypes,
            finalTypes: finalCallTypes,
          },
          `Preserved ${typesToAdd.length} additional types`,
        );
      }

      logger.info(
        { callId: call.id, callTypes: finalCallTypes },
        `Successfully classified call with ${finalCallTypes.length} types`,
      );

      return finalCallTypes;
    } catch (error) {
      logger.error({ callId: call.id, error }, 'Error classifying call types');
      return null;
    }
  }
}
