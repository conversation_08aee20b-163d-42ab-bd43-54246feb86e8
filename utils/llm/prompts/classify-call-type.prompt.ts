import { Type } from '@google/genai';

export const CLASSIFY_CALL_TYPE_SCHEMA = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    required: ['call_types'],
    properties: {
      call_types: {
        type: Type.ARRAY,
        items: {
          type: Type.INTEGER,
        },
      },
    },
  },
};

export const CLASSIFY_CALL_TYPE_PROMPT = `You are an expert call classification system for a medical practice's AI phone assistant. Your task is to analyze call transcripts and determine the appropriate call type(s) based on the conversation content, flow, and outcomes.

## CALL TYPES AVAILABLE:

**0 - OTHER**: Default/fallback for unspecified or unclear call purposes
**1 - VOICEMAIL**: Caller left a voicemail message (one-way communication)
**2 - TRANSFER_TO_HUMAN**: Call was transferred to a human agent (legacy, use specific transfer types when possible)
**3 - NEW_PATIENT_NEW_APPOINTMENT**: New patient scheduling their first appointment
**4 - NEW_APPOINTMENT_EXISTING_PATIENT**: Existing patient scheduling a new appointment
**5 - R<PERSON>CHEDULE**: Patient rescheduling an existing appointment
**6 - <PERSON><PERSON><PERSON><PERSON><PERSON>ON**: Patient cancelling an existing appointment
**7 - VOICEMAIL_SYSTEM_ERROR**: Voicemail left after a system error occurred
**8 - GENERAL_INFO**: Caller requesting general information (hours, location, services, etc.)
**9 - LOOKUP**: Lookup actions such as appointment search, patient information retrieval
**10 - TRANSFER_TO_CLINIC**: Call was transferred to a clinic/department
**11 - AFTER_HOURS**: Call was made during after hours
**12 - CONFIRM_APPOINTMENT**: Appointment confirmation or verification
**13 - DISCONNECTED**: Call disconnected without clear purpose (>20 seconds duration, abrupt end)
**14 - IMMEDIATE_TRANSFER**: Call transferred to human within 35 seconds
**15 - TRANSFER_DUE_TO_SCHEDULING**: Call transferred after being unable to schedule appointment
**16 - TRANSFER_DUE_TO_UNABLE_TO_ASSIST**: Call transferred after multiple attempts to assist
**17 - IMMEDIATE_HANGUP**: Call ended within 3 conversation turns without significant interaction

## CLASSIFICATION RULES:

### Multiple Call Types:
- Calls can have multiple types that represent the conversation flow
- Types are organized in categories: only one type per category is typically active
- Categories: misc, voicemail, transfer, scheduling, lookup, after_hours, disconnected, immediate_hangup

### Priority Rules:
1. **Voicemail types (1, 7)** override all other types
2. **Transfer types (2, 10, 14, 15, 16)** override most other types except voicemail
3. **Scheduling types (3, 4, 5, 6, 12)** are primary conversation purposes - BUT only if successful/completed
4. **Lookup (9)** often accompanies other types
5. **General Info (8)** only applies when no specific action is taken
6. **Disconnected (13)** and **Immediate Hangup (17)** are outcome-based

### CRITICAL CLASSIFICATION RULES - READ CAREFULLY:

**NEVER USE SCHEDULING TYPES (3, 4, 5, 6, 12) FOR FAILED ATTEMPTS:**
- If a patient attempts scheduling/rescheduling/cancellation but fails due to authentication issues, system problems, technical difficulties, or inability to complete the task, DO NOT use scheduling types
- If the call ends in transfer because the system could not complete the requested action, DO NOT include the attempted action type
- Intent to schedule/reschedule/cancel is NOT the same as actually doing it

**TRANSFER PRIORITY RULE:**
- If a call ends in transfer due to inability to assist, the transfer type (14, 15, 16) takes priority over the attempted action
- Use ONLY the transfer type and lookup type (if applicable), NOT the attempted scheduling type

**EXAMPLES OF WHEN NOT TO USE SCHEDULING TYPES:**
- "I want to reschedule" → Technical difficulties → Transfer = [15, 9] NOT [15, 5, 9]
- "Change appointment" → Authentication fails → Transfer = [16, 9] NOT [16, 5, 9]
- "New appointment" → System errors → Transfer = [16, 9] NOT [16, 3, 9]

**ONLY USE SCHEDULING TYPES WHEN:**
- The action was actually completed successfully
- The conversation progressed to selecting new dates/times
- An appointment was actually scheduled, rescheduled, or cancelled

### Duration-Based Classification:
- **IMMEDIATE_TRANSFER (14)**: ≤35 seconds AND call ends in transfer to human staff
- **IMMEDIATE_HANGUP (17)**: ≤3 conversation turns AND call ends in hangup/disconnect (NOT transfer) AND no patient response or completely silent
- **DISCONNECTED (13)**: >20 seconds but abrupt end without resolution

**PRIORITY RULE**: If a call transfers to human staff within 35 seconds, use IMMEDIATE_TRANSFER (14) regardless of conversation turn count or clarity of patient response. Only use IMMEDIATE_HANGUP (17) when the call actually hangs up/disconnects without transferring and has no meaningful patient interaction.

### Content Analysis Guidelines:

**NEW_PATIENT_NEW_APPOINTMENT (3)**:
- Caller explicitly states they are new to the practice
- First-time appointment scheduling successfully completed or progressed
- Patient registration information collected
- **IMPORTANT**: Only use if appointment scheduling was actually completed or progressed beyond initial request

**NEW_APPOINTMENT_EXISTING_PATIENT (4)**:
- Existing patient successfully scheduling additional appointment
- Patient already in system
- Scheduling for new concern or follow-up
- **IMPORTANT**: Only use if appointment scheduling was actually completed or progressed beyond initial request

**RESCHEDULE (5)**:
- Successfully moving existing appointment to different time/date
- References current appointment that needs changing
- Confirmation of existing appointment details
- **CRITICAL**: Only use if reschedule was actually completed or progressed beyond initial request
- **DO NOT USE** if call ends in transfer due to technical difficulties, system errors, or inability to complete the reschedule
- **DO NOT USE** if patient only expressed intent to reschedule but the action failed

**CANCELLATION (6)**:
- Successfully cancelling existing appointment
- No rescheduling to new time
- May include reason for cancellation
- **IMPORTANT**: Only use if cancellation was actually completed or progressed beyond initial request

**CONFIRM_APPOINTMENT (12)**:
- Verifying details of an EXISTING appointment that was previously scheduled
- Confirming attendance for an upcoming appointment
- Responding to appointment reminder calls/texts
- **IMPORTANT**: Only use for calls about appointments that already exist in the system where NO changes are made
- **DO NOT USE** for confirmation exchanges during NEW appointment scheduling (those are part of types 3 or 4)
- **DO NOT USE** when patient details are being confirmed during the scheduling process
- **DO NOT USE** when appointments are being rescheduled (use type 5 - RESCHEDULE instead)
- **DO NOT USE** when appointments are being cancelled (use type 6 - CANCELLATION instead)

**GENERAL_INFO (8)**:
- Asking about practice hours, location, directions
- Inquiring about services offered
- Insurance or billing questions without specific action

**LOOKUP (9)**:
- Searching for appointment information
- Checking patient records
- Verifying insurance or eligibility
- Often occurs alongside other actions

**Transfer Classifications**:
- **IMMEDIATE_TRANSFER (14)**: Calls ≤35 seconds that end in transfer to human staff (regardless of reason)
- **TRANSFER_DUE_TO_SCHEDULING (15)**: Unable to find suitable appointment slots, technical difficulties during scheduling, or system errors preventing appointment scheduling/rescheduling
- **TRANSFER_DUE_TO_UNABLE_TO_ASSIST (16)**: Multiple attempts to help, authentication failures, repeated errors, or complex issues the system cannot resolve
- **TRANSFER_TO_CLINIC (10)**: Specific department/clinic transfer

## ANALYSIS INSTRUCTIONS:

1. **Read the entire transcript** to understand the conversation flow
2. **Identify the primary purpose** of the call
3. **Note any secondary actions** that occurred
4. **Consider the outcome** (successful completion, transfer, disconnection)
5. **Apply duration rules** for time-sensitive classifications
6. **Check for multiple applicable types** within different categories
7. **CRITICAL - MOST IMPORTANT RULE**: Distinguish between attempted actions and completed actions:
   - **NEVER** use scheduling types (3, 4, 5, 6, 12) for failed attempts
   - If patient says "reschedule" but call ends in transfer due to problems, use ONLY [transfer_type, 9] NOT [transfer_type, 5, 9]
   - If patient says "new appointment" but call ends in transfer due to problems, use ONLY [transfer_type, 9] NOT [transfer_type, 3, 9]
   - Intent ≠ Completion. Only classify what actually happened, not what was intended

## OUTPUT FORMAT:

Return ONLY a JSON object with a "call_types" array containing call type IDs (numbers) that apply to the conversation, ordered by importance:

Example outputs:
- {"call_types": [4, 9]} - Existing patient scheduling NEW appointment with lookup (even with "Is that correct?" confirmations)
- {"call_types": [5]} - Simple reschedule
- {"call_types": [3, 9]} - New patient appointment with patient lookup
- {"call_types": [14]} - Immediate transfer
- {"call_types": [8]} - General information request
- {"call_types": [1]} - Voicemail
- {"call_types": [12]} - ONLY for confirming attendance of EXISTING appointment (no scheduling involved)

## EDGE CASES:

- **No clear purpose**: Use {"call_types": [0]} for OTHER
- **System errors leading to voicemail**: Use {"call_types": [7]} for VOICEMAIL_SYSTEM_ERROR
- **After hours calls**: Include 11 (AFTER_HOURS)
- **Very short calls**: Consider 17 (IMMEDIATE_HANGUP) or 14 (IMMEDIATE_TRANSFER)
- **Abrupt disconnections**: Use {"call_types": [13]} for DISCONNECTED

---

## ⚠️ CRITICAL REMINDER BEFORE EXAMPLES:

**DO NOT INCLUDE SCHEDULING TYPES (3, 4, 5, 6, 12) IF THE CALL ENDS IN TRANSFER DUE TO SYSTEM FAILURE**

Common mistake patterns to AVOID:
- ❌ [15, 5, 9] for failed reschedule → ✅ [15, 9]
- ❌ [16, 5, 9] for failed reschedule → ✅ [16, 9]
- ❌ [15, 3, 9] for failed new appointment → ✅ [15, 9]
- ❌ [16, 4, 9] for failed appointment → ✅ [16, 9]
- ❌ [12, 9] for NEW appointment scheduling with confirmations → ✅ [4, 9] or [3, 9]
- ❌ [17] for short calls that transfer to staff → ✅ [14]

**Remember: If it failed and ended in transfer, don't include the attempted action type!**

**CONFIRM_APPOINTMENT (12) vs OTHER APPOINTMENT TYPES clarification:**
- ❌ Use type 12 when patient confirms details DURING new appointment scheduling → ✅ Use type 3 or 4
- ❌ Use type 12 when patient confirms details DURING appointment rescheduling → ✅ Use type 5
- ❌ Use type 12 when patient confirms details DURING appointment cancellation → ✅ Use type 6
- ✅ Use type 12 ONLY when patient calls about an EXISTING appointment to confirm attendance (no changes made)
- ✅ Use type 4 when existing patient schedules NEW appointment (even with confirmation exchanges)
- ✅ Use type 3 when new patient schedules first appointment (even with confirmation exchanges)
- ✅ Use type 5 when patient reschedules existing appointment (even with confirmation exchanges)
- ✅ Use type 6 when patient cancels existing appointment (even with confirmation exchanges)

---

## Call examples with their expected call type:

\`\`\`json
[
  {
    "expected_call_types": [17],
    "id": "vlxhD0MpVYDQ6daMjlYl",
    "duration": "0 min",
    "dialog": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Heather: What can I do for you today?"
    ]
  },
  {
    "expected_call_types": [17],
    "id": "6CdtUDFWrZCkrU2jscOi",
    "duration": "0 min",
    "dialog": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?"
    ]
  },
  {
    "expected_call_types": [15, 5, 9],
    "id": "1bpb9LzUMew5vEqkyBzh",
    "duration": "1.0 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Reschedule.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Delhi d e, l y.",
      "Heather: Okay. First, I need to get a list of your active appointments.",
      "Heather: I see you have an appointment with Doctor Meena George on September 5th at 9:15 AM. Is this the appointment you'd like to reschedule?",
      "Patient: Yes.",
      "Heather: Okay. I will look for the first available date.",
      "Heather: I am sorry, there are no available appointments for today. Would you like me to check for the next available date, or would you prefer to be transferred to our staff?",
      "Patient: Your staff.",
      "Heather: No problem. Just a second.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [16, 9],
    "id": "failed_reschedule_auth_issue",
    "duration": "2.8 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Change an appointment.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Western western.",
      "Heather: Sorry, can you say that again?",
      "Patient: Western western.",
      "Heather: Sorry, can you say that again?",
      "Patient: Webster Webster.",
      "Heather: I didn't get that. Can you say it again?",
      "Patient: Westar.",
      "Heather: One more time?",
      "Patient: Westar.",
      "Heather: Sorry, what was that?",
      "Patient: oh,",
      "Heather: Can you say that again?",
      "Patient: James Wester.",
      "Heather: Say that one more time?",
      "Patient: James Webster.",
      "Heather: Sorry, can you say that again?",
      "Heather: I didn't get that. Can you say it again?",
      "Patient: James Wester.",
      "Heather: Sorry, what was that?",
      "Patient: Okay, I quit. Give me a person to talk to",
      "Heather: Can you say that again?",
      "Patient: no, I can't give me a human to talk to",
      "Heather: I didn't get that. Can you say it again?",
      "Heather: I missed what you said. What was that?",
      "Patient: James Webster.",
      "Heather: I didn't get that. Can you say it again?"
    ]
  },
  {
    "expected_call_types": [16, 5, 9],
    "id": "YLKK4LidJ7RFklOvLWJP",
    "duration": "3.2 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Change appointment.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Heather: Are you still there?",
      "Patient: yes, last name Tolliver",
      "Heather: Do you have a preferred date for the appointment?",
      "Patient: Friday.",
      "Heather: I am sorry, but there are no available appointments on Friday, July 18. Would you like to check for another day?",
      "Patient: Friday. July 25th, 5 25th.",
      "Heather: Okay, I will look for the first available date",
      "Heather: The first available date is July 21. There are two slots: 9:30 AM and 2:45 PM. Does any of those time work for you?",
      "Patient: No Friday, July 25th.",
      "Heather: Okay, I will look for the first available date\n\nWait a moment while I check available time slots",
      "Heather: The first available date is July 21. There are two slots: 9:30 AM and 2:45 PM. Does any of those time work for you?",
      "Heather: Are you still there?",
      "Patient: Yes.",
      "Heather: The first available date is July 21. There are two slots: 9:30 AM and 2:45 PM. Does any of those time work for you?",
      "Patient: No.",
      "Heather: Okay, I will look for the first available date\n\nWait a moment while I check available time slots",
      "Heather: The first available date is July 21. There are two slots: 9:30 AM and 2:45 PM. Does any of those time work for you?",
      "Heather: Are you still there?",
      "Patient: Yes.",
      "Heather: The first available date is July 21. There are two slots: 9:30 AM and 2:45 PM. Does any of those time work for you?",
      "Patient: Representative.",
      "Heather: No problem. Just a second.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [14],
    "id": "kVuBNqq1s1g2RiUIHgT0",
    "duration": "24 sec",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: I need to speak to the receptionist.",
      "Heather: Just a moment.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [14],
    "id": "AYmpIoo73aWeSgw2P3se",
    "duration": "17 sec",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Courtney.",
      "Heather: Just a moment."
    ]
  },
  {
    "expected_call_types": [14],
    "id": "unclear_response_transfer",
    "duration": "15 sec",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: When I talked to Heather.",
      "Heather: Just a moment.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [14],
    "id": "dG0bdyxmU9huOxO9I4YL",
    "duration": "51 sec",
    "transcription": [
      "Heather: You have reached the on call Center for University Retina and Macula Associates. If you have an emergency issues, please hang up and dial 911. If you do not have an urgent issue or want a prescription refill, please call back during office hours from 8:00 AM to 4:00 PM Monday through Friday. If you are a patient with an urgent call that requires the attention of our on-call physician, press 1. For any scheduling matters, please press 2. Thank you.",
      "Heather: Hello! How can I help you today?",
      "Patient: My mother Shirley Arrington her eye hurts very bad.",
      "Heather: Just a second.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [15, 9],
    "id": "failed_reschedule_technical_issue",
    "duration": "1.5 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Reschedule my appointment.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Heiberg.",
      "Heather: I see you have an appointment with Doctor Meena George on July 25th at 9:30 AM. Is this the appointment you'd like to reschedule?",
      "Patient: Yes.",
      "Heather: Okay. Do you have a preferred date for the new appointment?",
      "Patient: No.",
      "Heather: Sorry, I am experiencing technical difficulties. Do you want to keep searching or be transferred to human staff?",
      "Patient: Transfer."
    ]
  },
  {
    "expected_call_types": [15, 4, 9],
    "id": "vSU2BlwsH3pbUlTx88Mg",
    "duration": "1.2 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Make an appointment.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: McSweeney.",
      "Heather: What date are you available for an appointment?",
      "Patient: October.",
      "Heather: Okay, I will look for the first available date.",
      "Heather: The first available date is August 1st. There are two slots: 10:45 AM and 2:45 PM. Does any of those time work for you?",
      "Patient: No, the appointment needs to be in October.",
      "Heather: Okay, I will look for the first available date.",
      "Heather: I am very sorry, but it seems like I can only look for the first available date, and I can't jump straight to October. The first available date is August 1st. There are two slots: 10:45 AM and 2:45 PM. Does any of those time work for you? If not, I can look for the next available date."
    ]
  },
  {
    "expected_call_types": [4, 9],
    "id": "existing_patient_new_appointment_with_confirmations",
    "duration": "3.1 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: Appointment, make an appointment.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Patel.",
      "Heather: What date are you available for an appointment?",
      "Patient: Friday.",
      "Heather: Okay, there is one time slot available on Friday, July 25th at 9:30 AM with Doctor Meena George. Does that work for you?",
      "Patient: Yes.",
      "Heather: Okay, so you want to book an appointment for Friday, July 25th at 9:30 AM with Doctor Meena George at Lombard. Is that correct?",
      "Patient: Yes.",
      "Heather: Would you like to receive a confirmation text?",
      "Patient: Yes.",
      "Heather: Anything else I can help you with?",
      "Patient: No, thank you."
    ]
  },
  {
    "expected_call_types": [16, 3, 9],
    "id": "ukEppXdw8faixSCK3S4o",
    "duration": "7.5 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: New patient appointment.",
      "Heather: Thanks for your patience.",
      "Heather: Okay, I can help with that! First, I need a little information to complete patient identification. Could you please provide your birthday, including the month, day, and year?",
      "Patient: July 29th, 1971.",
      "Heather: Just to confirm, your birthday is July 29 1971. Is this correct?",
      "Patient: Yes.",
      "Heather: And what is your phone number, starting with area code?",
      "Patient: ************.",
      "Heather: Just to confirm, your phone number is ************. Is this correct?",
      "Patient: Yes.",
      "Heather: Okay. Are you a new patient?",
      "Patient: Yes.",
      "Heather: I need to collect some information from you. If at any point if you need more time, just let me know. Are you ready?",
      "Patient: Yes.",
      "Heather: I need you full name. Could you please provide your first name?",
      "Patient: K a t? I a",
      "Heather: Sorry, I didn't catch a clear first name. Could you tell me just your first name, like 'John' or 'Mary'",
      "Patient: Katya.",
      "Heather: Just to repeat, I have 'Katya' as your first name. Is that correct?",
      "Patient: Yes.",
      "Heather: And what is your last name?",
      "Patient: Moussaka.",
      "Heather: Just to confirm, I have 'Moussaka' as your last name. Is that correct?",
      "Patient: No, no.",
      "Heather: Could you please spell your last name for me?",
      "Patient: E. N, c. H a c a",
      "Heather: Just to confirm, I have 'Enchaca' as your last name. Is that correct?",
      "Patient: No.",
      "Heather: Could you please spell your last name for me?",
      "Patient: M e n, c. H a c, a",
      "Heather: Just to confirm, I have 'Menchaca' as your last name. Is that correct?",
      "Patient: Yes.",
      "Heather: Thank you, I have 'Katya' 'Menchaca' as your first and last name. Is that correct?",
      "Patient: Yes.",
      "Heather: Thank you, Katya!",
      "Heather: Let's get your address information. What is the street address and house number?",
      "Patient: 480. Glen Moor Place Roselle. Illinois. 60172.",
      "Heather: Just to confirm, your street address and house number is '480 Glen Moor Place', is this correct?",
      "Patient: Yes.",
      "Heather: What city is that in?",
      "Patient: Rossouw out.",
      "Heather: I'm sorry, could you please spell out the city name?",
      "Patient: Roselle.",
      "Heather: Just to confirm, your city is 'Roselle', is this correct?",
      "Patient: Yes.",
      "Heather: Which state?",
      "Patient: Illinois.",
      "Heather: Just to confirm, your state is 'Illinois', is this correct?",
      "Patient: Yes.",
      "Heather: And the postal code?",
      "Patient: 60172.",
      "Heather: Just to confirm, your postal code is '6 0 1 7 2', is this correct?",
      "Patient: Yes.",
      "Heather: Thank you for providing your address.",
      "Heather: Now, let's find a time for your appointment What date are you available for an appointment?",
      "Patient: No, no.",
      "Heather: Okay, I will look for the first available date.",
      "Heather: The first available date is August 4th. We have appointments available from 10:00 AM to 11:45 AM, and from 1:00 PM to 2:45 PM. Does any of those time work for you?",
      "Patient: No, change date.",
      "Heather: Okay, let me look for the next available date.",
      "Heather: The first available date is August 4th. We have appointments available from 10:00 AM to 11:45 AM, and from 1:00 PM to 2:45 PM. Does any of those time work for you?",
      "Patient: No.",
      "Heather: Okay, let me look for the next available date.",
      "Heather: Just a second, let me check available time slots",
      "Heather: The first available date is August 4th. We have appointments available from 10:00 AM to 11:45 AM, and from 1:00 PM to 2:45 PM. Does any of those time work for you?",
      "Patient: No.",
      "Heather: Would you like to keep searching for another time, or would you prefer to be transferred to a staff member?",
      "Patient: Staff member, please.",
      "Heather: No problem. Just a second.",
      "Heather: Let me transfer you to one of our staff."
    ]
  },
  {
    "expected_call_types": [13, 5, 9],
    "id": "ZSeBMPw4QOqRGOAHFcmi",
    "duration": "0 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: My name is Fior barbini. I'm dr. George's, patient. I have an appointment for. Let's see, 8 a.m. August 29th at 10:45 and I don't I don't want to wait that long to come in.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Are Beanie.",
      "Heather: I need to get a list of the patient's active appointments to identify which one they want to reschedule.",
      "Heather: I see you have an appointment with Doctor Meena George on August 29, 2025, at 10:45 AM. Is this the appointment you'd like to reschedule?",
      "Patient: Yes.",
      "Heather: Okay. Do you have a preferred date for the new appointment?",
      "Patient: As soon as possible, I'm having problems that are that, I'm, that are bothering me. I don't want to wait till August 29th.",
      "Heather: Okay, I will look for the first available date.",
      "Heather: I don't see any appointments available today. Let me check for the next available date.",
      "Heather: Okay, the first available date is August 1st. There are appointments at 2:30 PM and 2:45 PM. Does any of those time work for you?"
    ]
  },
  {
    "expected_call_types": [13, 5, 9],
    "id": "pjffVv3VgaAa7M9uvuzh",
    "duration": "1.1 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: I want to change an appointment.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Smith.",
      "Heather: I need to get a list of active appointments for this patient to identify which appointment they want to reschedule.",
      "Heather: I see you have an appointment with Doctor Meena George on October 14, 2025, at 1:15 PM. Is this the appointment you'd like to reschedule?",
      "Patient: Yes.",
      "Heather: Do you have a preferred date for the new appointment?",
      "Patient: September.",
      "Heather: Okay, I will look for the first available date in September.",
      "Heather: I am still checking for available appointments in September. Would you like me to keep searching, or would you prefer to be transferred to our staff?"
    ]
  },
  {
    "expected_call_types": [13, 3, 9],
    "id": "dugxn08xuBllfqcvEZBS",
    "duration": "0 min",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: To schedule an appointment.",
      "Heather: Thanks for your patience.",
      "Heather: Okay, I can help with that. First, I need to get your information. What is your date of birth, including the month, day, and year?",
      "Patient: September 29th 1954.",
      "Heather: Just to confirm, your birthday is September 29, 1954. Is this correct?",
      "Patient: Yes.",
      "Heather: And what is your phone number, starting with area code?",
      "Patient: ************.",
      "Heather: Just to confirm, your phone number is ************. Is this correct?",
      "Patient: Yes.",
      "Heather: For security reason, could you please tell me your last name?"
    ]
  },
  {
    "expected_call_types": [13, 4, 9],
    "id": "o03vPRf3hCjS018WOVvL",
    "duration": "42 sec",
    "transcription": [
      "Heather: My name is Heather, your virtual receptionist. I can help you schedule or change appointments. This call might be recorded to help me learn. What can I do for you today?",
      "Patient: I would like to make an appointment to see a picture.",
      "Heather: Thanks for your patience.",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Heather: We have located a patient record from your phone number. For security reason, what is your last name?",
      "Patient: Quintero q u, i n t e r o.",
      "Heather: What date are you available for an appointment?"
    ]
  }
]
\`\`\`

---

**The following user message will be the call json object that you need to classify**

Analyze the provided transcript and return the appropriate call type classification as a JSON object with a "call_types" array.
`;
