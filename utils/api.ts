import { getToken, handleApiCall } from './auth';

// API client with built-in token handling
export const apiClient = {
  get: async (url: string, customHeaders = {}) => {
    const token = getToken();
    return handleApiCall(() =>
      fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
          ...customHeaders,
        },
      }),
    );
  },

  post: async (url: string, data: Record<string, unknown>, customHeaders = {}) => {
    const token = getToken();
    return handleApiCall(() =>
      fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
          ...customHeaders,
        },
        body: JSON.stringify(data),
      }),
    );
  },

  put: async (url: string, data: Record<string, unknown>, customHeaders = {}) => {
    const token = getToken();
    return handleApiCall(() =>
      fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
          ...customHeaders,
        },
        body: JSON.stringify(data),
      }),
    );
  },

  delete: async (url: string, customHeaders = {}) => {
    const token = getToken();
    return handleApiCall(() =>
      fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          Authorization: token ? `Bearer ${token}` : '',
          ...customHeaders,
        },
      }),
    );
  },
};

// Helper to handle JSON responses
export const fetchJson = async (response: Response) => {
  if (!response.ok) {
    let errorMessage = 'API request failed';
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      // If JSON parsing fails, use status text
      errorMessage = response.statusText || errorMessage;
    }
    throw new Error(errorMessage);
  }
  return response.json();
};
