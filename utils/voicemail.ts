// API client import removed as it's not being used

/**
 * Cache for signed URLs to avoid unnecessary API calls
 * Format: { voicemailUrl: { url: string, expiresAt: number } }
 */
const urlCache: Record<string, { url: string; expiresAt: number }> = {};

/**
 * Get a signed URL for a voicemail file
 * @param voicemailUrl The voicemail URL in the format gcp://bucket-name/file-path
 * @param forceRefresh Whether to force a refresh of the URL even if cached
 * @returns Promise with the signed URL
 */
export async function getVoicemailSignedUrl(
  voicemailUrl: string,
  forceRefresh = false,
): Promise<string> {
  // If URL is not in the GCP format, return as is (might be a direct URL)
  if (!voicemailUrl.startsWith('gcp://')) {
    return voicemailUrl;
  }

  // Check if we have a cached URL that hasn't expired
  const cached = urlCache[voicemailUrl];
  const now = Date.now();

  if (!forceRefresh && cached && cached.expiresAt > now + 60000) {
    // Add 1 minute buffer
    return cached.url;
  }

  try {
    // Instead of getting a signed URL, use the conversion API directly
    // This is more reliable and avoids the need for URL refreshing
    const convertUrl = `/api/storage/convert-audio?url=${encodeURIComponent(voicemailUrl)}&format=mp3&_=${Date.now()}`;

    // Cache the URL with a 10-minute expiration
    urlCache[voicemailUrl] = {
      url: convertUrl,
      expiresAt: Date.now() + 10 * 60 * 1000, // 10 minutes
    };

    return convertUrl;
  } catch (error) {
    console.error('Error getting signed URL for voicemail:', error);
    throw new Error(
      `Failed to get signed URL for voicemail: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

/**
 * Check if a URL is a voicemail URL
 * @param url The URL to check
 * @returns True if the URL is a voicemail URL
 */
export function isVoicemailUrl(url: string | null | undefined): boolean {
  return !!url && url.startsWith('gcp://');
}

/**
 * Create a VoicemailPlayer component that handles voicemail URLs
 * @param props The component props
 * @returns JSX element
 */
// VoicemailPlayer component removed as it's not being used
