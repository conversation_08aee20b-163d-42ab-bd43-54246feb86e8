import { NextApiRequest } from 'next';
import { CloudSchedulerClient } from '@google-cloud/scheduler/build/src/v1';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

import logger from '@/lib/external-api/v2/utils/logger';

dayjs.extend(utc);
dayjs.extend(timezone);

const {
  GCP_SCHEDULER_LOCATION = 'us-east1',
  FIREBASE_PROJECT_ID,
  FIREBASE_CLIENT_EMAIL,
  FIREBASE_PRIVATE_KEY,
} = process.env;

const SCHEDULER_JOB_NAME_HEADER = 'gcp-cloud-scheduler-name';

export class Scheduler {
  private static instance: Scheduler;
  private readonly client: CloudSchedulerClient;

  constructor() {
    this.client = new CloudSchedulerClient({
      credentials: {
        projectId: FIREBASE_PROJECT_ID,
        client_email: FIREBASE_CLIENT_EMAIL,
        private_key: FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
  }

  static getInstance() {
    if (!Scheduler.instance) {
      Scheduler.instance = new Scheduler();
    }
    return Scheduler.instance;
  }

  getJobNameFromRequest(req: NextApiRequest): string | undefined {
    return req.headers[SCHEDULER_JOB_NAME_HEADER] as string | undefined;
  }

  async createJob(params: {
    name: string;
    executionTime: string;
    timeZone?: string;
    httpTarget: {
      uri: string;
      httpMethod?: string;
      jsonPayload?: object;
    };
  }) {
    const { name, httpTarget, executionTime, timeZone = 'Etc/UTC' } = params;
    const scheduleTime = dayjs.utc(executionTime);
    const schedule = `${scheduleTime.minute()} ${scheduleTime.hour()} ${scheduleTime.date()} ${scheduleTime.month() + 1} *`;
    const parent = `projects/${FIREBASE_PROJECT_ID}/locations/${GCP_SCHEDULER_LOCATION}`;
    logger.info(
      {
        context: 'Scheduler.createJob',
        name,
        schedule,
        timeZone,
        httpTarget: {
          uri: httpTarget.uri,
          httpMethod: httpTarget.httpMethod,
        },
      },
      `Creating a scheduled job`,
    );

    const [createdJob] = await this.client.createJob({
      parent,
      job: {
        name: `${parent}/jobs/${name}`,
        schedule,
        timeZone,
        httpTarget: {
          uri: httpTarget.uri,
          httpMethod: (httpTarget.httpMethod || 'POST') as never,
          headers: {
            [SCHEDULER_JOB_NAME_HEADER]: name,
            ...(httpTarget.jsonPayload ? { 'content-type': 'application/json' } : {}),
          },
          body: httpTarget.jsonPayload
            ? Buffer.from(JSON.stringify(httpTarget.jsonPayload))
            : undefined,
        },
        attemptDeadline: {
          seconds: 1800, // 30 minutes
          nanos: 0,
        },
      },
    });

    return createdJob;
  }

  async deleteJob(name: string): Promise<void> {
    logger.info(
      {
        context: 'Scheduler.deleteJob',
        name,
      },
      `Deleting a scheduled job`,
    );
    try {
      await this.client.deleteJob({
        name: `projects/${FIREBASE_PROJECT_ID}/locations/${GCP_SCHEDULER_LOCATION}/jobs/${name}`,
      });
    } catch (innerError) {
      logger.error(
        {
          context: 'Scheduler.deleteJob',
          name,
          innerError,
        },
        'Error deleting a scheduled job',
      );
    }
  }
}
