import { initializeApp, getApps, getApp } from 'firebase/app';
import {
  getAuth as getFirebaseAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  Auth,
  signInWithCustomToken,
  updatePassword,
  sendEmailVerification,
  EmailAuthProvider,
  reauthenticateWithCredential,
  verifyPasswordResetCode,
  confirmPasswordReset,
} from 'firebase/auth';
import {
  getFirestore,
  collection,
  doc,
  getDoc,
  setDoc,
  updateDoc,
  query,
  where,
  getDocs,
  Timestamp,
  serverTimestamp,
  Firestore,
} from 'firebase/firestore';
import { User, UserRole, Clinic, StaffInviteCode } from '@/models/auth';

// Initialize Firebase
let authInternal: Auth | undefined;
let dbInternal: Firestore | undefined;

const initFirebase = () => {
  if (authInternal && dbInternal) {
    return { auth: authInternal, db: dbInternal };
  }

  // Firebase configuration - use hardcoded values for client-side to avoid Next.js env issues
  const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
    measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
  };

  try {
    console.log('Initializing Firebase with project config', firebaseConfig.projectId);
    const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();
    authInternal = getFirebaseAuth(app);
    dbInternal = getFirestore(app);
    console.log('Firebase initialized successfully');
    return { auth: authInternal, db: dbInternal };
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw new Error('Failed to initialize Firebase');
  }
};

const getAuth = () => {
  const { auth } = initFirebase();
  return auth;
};

const getDb = () => {
  const { db } = initFirebase();
  return db;
};

export { getAuth };

// Authentication functions
export const signIn = (email: string, password: string) => {
  return signInWithEmailAndPassword(getAuth(), email, password);
};

export const signInWithToken = (token: string) => {
  return signInWithCustomToken(getAuth(), token);
};

export const signOut = () => {
  return firebaseSignOut(getAuth());
};

// Get current user data (including custom claims)
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const firebaseUser = getAuth().currentUser;

    if (!firebaseUser) {
      return null;
    }

    const userDoc = await getDoc(doc(getDb(), 'users', firebaseUser.uid));

    if (!userDoc.exists()) {
      return null;
    }

    const userData = userDoc.data();

    // Check if the email in Firestore is different from the one in Firebase Auth
    // This can happen after email verification when the user changes their email
    if (userData.email !== firebaseUser.email) {
      await syncUserEmail(firebaseUser.uid, firebaseUser.email || '');
      // Update the userData with the new email
      userData.email = firebaseUser.email;
    }

    return {
      id: userDoc.id,
      ...userData,
      createdAt: userData.createdAt.toDate(),
      updatedAt: userData.updatedAt.toDate(),
    } as User;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
};

// Sync user email in Firestore with Firebase Auth
export const syncUserEmail = async (userId: string, newEmail: string): Promise<boolean> => {
  try {
    // Update the user document in Firestore with the new email
    await updateDoc(doc(getDb(), 'users', userId), {
      email: newEmail,
      pendingEmail: null, // Clear any pending email
      updatedAt: serverTimestamp(),
    });

    console.log('User email synced in Firestore:', newEmail);
    return true;
  } catch (error) {
    console.error('Error syncing user email:', error);
    return false;
  }
};

// User related functions
export const createUserWithRole = async (
  email: string,
  password: string,
  name: string,
  role: UserRole,
  clinicId: number | null,
  specialty?: string,
): Promise<User> => {
  try {
    // Create the auth user
    const userCredential = await createUserWithEmailAndPassword(getAuth(), email, password);
    const firebaseUser = userCredential.user;

    // Create user document in Firestore
    const now = Timestamp.now();
    const userData = {
      email,
      name,
      role,
      clinicId,
      specialty: specialty || null, // Convert undefined to null for Firestore
      canTakeAppointments: false, // Default value, can be updated later
      createdAt: now,
      updatedAt: now,
    };

    await setDoc(doc(getDb(), 'users', firebaseUser.uid), userData);

    // Set custom claims for role-based access control
    // Note: This is a client-side function and can't directly set custom claims
    // We'll use a workaround by calling a server-side API endpoint
    try {
      // Call the server-side API to set custom claims
      const response = await fetch('/api/auth/set-custom-claims', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          uid: firebaseUser.uid,
          role,
          clinicId,
        }),
      });

      if (!response.ok) {
        console.error('Failed to set custom claims:', await response.text());
      }
    } catch (claimsError) {
      console.error('Error setting custom claims:', claimsError);
      // Continue despite claims error - user is still created
    }

    // Return the created user
    return {
      id: firebaseUser.uid,
      ...userData,
      createdAt: now.toDate(),
      updatedAt: now.toDate(),
    } as User;
  } catch (error) {
    console.error('Error creating user:', error);
    // Improve error handling to provide more specific error messages
    if (error instanceof Error) {
      // Handle Firebase auth errors
      if (error.message.includes('auth/email-already-in-use')) {
        throw new Error('auth/email-already-in-use');
      } else if (error.message.includes('auth/invalid-email')) {
        throw new Error('Invalid email format');
      } else if (error.message.includes('auth/weak-password')) {
        throw new Error('Password is too weak. Please use a stronger password.');
      }
    }
    throw error;
  }
};

// Clinic related functions
export const createClinic = async (name: string, logoUrl?: string): Promise<Clinic> => {
  try {
    const currentUser = await getCurrentUser();

    // Only super admin can create clinics
    if (!currentUser || currentUser.role !== UserRole.SUPER_ADMIN) {
      throw new Error('Unauthorized: Only super admin can create clinics');
    }

    // Create clinic document
    const now = Timestamp.now();
    const clinicData = {
      name,
      logoUrl,
      createdAt: now,
      updatedAt: now,
    };

    const clinicRef = doc(collection(getDb(), 'clinics'));
    await setDoc(clinicRef, clinicData);

    return {
      id: parseInt(clinicRef.id, 10), // Convert string ID to number
      ...clinicData,
      createdAt: now.toDate(),
      updatedAt: now.toDate(),
    } as Clinic;
  } catch (error) {
    console.error('Error creating clinic:', error);
    throw error;
  }
};

// Staff invite code functions
export const generateStaffInviteCode = async (clinicId: number): Promise<StaffInviteCode> => {
  try {
    const currentUser = await getCurrentUser();

    // Only clinic admin can generate codes
    if (
      !currentUser ||
      currentUser.role !== UserRole.CLINIC_ADMIN ||
      currentUser.clinicId !== clinicId
    ) {
      throw new Error('Unauthorized: Only clinic admin can generate staff invite codes');
    }

    // Generate a random 6-character code
    const generateRandomCode = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let code = '';
      for (let i = 0; i < 6; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return code;
    };

    const code = generateRandomCode();

    // Set expiration date (24 hours from now)
    const now = new Date();
    now.setHours(now.getHours() + 24);
    const expiresAt = Timestamp.fromDate(now);
    const createdAt = Timestamp.now();

    // Create code document
    const codeData = {
      code,
      clinicId,
      used: false,
      expiresAt,
      createdBy: currentUser.id,
      createdAt,
    };

    const codeRef = doc(collection(getDb(), 'staffInviteCodes'));
    await setDoc(codeRef, codeData);

    return {
      id: codeRef.id,
      ...codeData,
      expiresAt: expiresAt.toDate(),
      createdAt: createdAt.toDate(),
    } as StaffInviteCode;
  } catch (error) {
    console.error('Error generating staff invite code:', error);
    throw error;
  }
};

// Verify and use staff invite code
export const verifyStaffInviteCode = async (
  code: string,
): Promise<{ valid: boolean; clinicId?: number; message?: string }> => {
  try {
    if (!code || code.trim() === '') {
      return { valid: false, message: 'Invite code is required' };
    }

    // Query for the code
    const q = query(
      collection(getDb(), 'staffInviteCodes'),
      where('code', '==', code.toUpperCase().trim()),
      where('used', '==', false),
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return { valid: false, message: 'Invalid invite code. Please check and try again.' };
    }

    const codeDoc = querySnapshot.docs[0];
    const codeData = codeDoc.data();

    // Check if code is expired
    const now = new Date();
    const expiresAt = codeData.expiresAt.toDate();

    if (now > expiresAt) {
      return { valid: false, message: 'This invite code has expired. Please request a new one.' };
    }

    return {
      valid: true,
      clinicId: codeData.clinicId,
    };
  } catch (error) {
    console.error('Error verifying staff invite code:', error);
    return { valid: false, message: 'An error occurred while verifying the invite code.' };
  }
};

// Mark code as used
export const markCodeAsUsed = async (code: string): Promise<boolean> => {
  try {
    if (!code || code.trim() === '') {
      console.error('Cannot mark empty code as used');
      return false;
    }

    // Query for the code
    const q = query(
      collection(getDb(), 'staffInviteCodes'),
      where('code', '==', code.toUpperCase().trim()),
      where('used', '==', false),
    );

    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.warn('Attempted to mark non-existent or already used code as used:', code);
      return false;
    }

    const codeDoc = querySnapshot.docs[0];

    // Update the code as used
    await updateDoc(codeDoc.ref, {
      used: true,
      usedAt: serverTimestamp(),
    });

    console.log('Successfully marked code as used:', code);
    return true;
  } catch (error) {
    console.error('Error marking invite code as used:', error);
    return false;
  }
};

// Re-authenticate user before sensitive operations
export const reauthenticateUser = async (password: string): Promise<boolean> => {
  try {
    const user = getAuth().currentUser;

    if (!user || !user.email) {
      throw new Error('No authenticated user found');
    }

    const credential = EmailAuthProvider.credential(user.email, password);
    await reauthenticateWithCredential(user, credential);
    return true;
  } catch (error) {
    console.error('Error re-authenticating user:', error);
    return false;
  }
};

// Update user password
export const updateUserPassword = async (
  currentPassword: string,
  newPassword: string,
): Promise<boolean> => {
  try {
    const user = getAuth().currentUser;

    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Re-authenticate user first
    const reAuthSuccess = await reauthenticateUser(currentPassword);

    if (!reAuthSuccess) {
      throw new Error('Failed to re-authenticate. Current password may be incorrect.');
    }

    // Update password
    await updatePassword(user, newPassword);

    return true;
  } catch (error) {
    console.error('Error updating password:', error);
    throw error;
  }
};

// Update user email
export const updateUserEmail = async (
  currentPassword: string,
  newEmail: string,
): Promise<boolean> => {
  try {
    const user = getAuth().currentUser;

    if (!user) {
      throw new Error('No authenticated user found');
    }

    // Re-authenticate user first
    const reAuthSuccess = await reauthenticateUser(currentPassword);

    if (!reAuthSuccess) {
      throw new Error('Failed to re-authenticate. Current password may be incorrect.');
    }

    // Call the API endpoint to change email
    const response = await fetch('/api/auth/change-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        newEmail,
      }),
    });

    const data = await response.json();

    if (!response.ok) {
      // If the current email is not verified, show a special message
      if (data.verificationLink && data.message.includes('must be verified first')) {
        // Send verification email for current email
        await sendEmailVerification(user);
        throw new Error(
          'Your current email address must be verified before changing to a new email. A verification email has been sent to your current email address.',
        );
      } else {
        throw new Error(data.message || 'Failed to change email');
      }
    }

    // Log the verification link (in a real app, this would be sent via email)
    if (data.verificationLink) {
      console.log('Verification link for new email:', data.verificationLink);

      // In a real production app, you would send this email via your email service
      // For now, we'll just open the link in a new tab for testing purposes
      // This is a development-only convenience and should be removed in production
      if (process.env.NODE_ENV === 'development') {
        window.open(data.verificationLink, '_blank');
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating email:', error);

    // Handle specific errors
    if (error instanceof Error) {
      const errorMessage = error.message || '';

      if (errorMessage.includes('re-authenticate') || errorMessage.includes('incorrect')) {
        throw new Error('Password is incorrect');
      } else if (errorMessage.includes('email-already-in-use')) {
        throw new Error('This email is already in use by another account');
      } else if (errorMessage.includes('invalid-email')) {
        throw new Error('The email address is not valid');
      } else if (errorMessage.includes('must be verified')) {
        // Just rethrow as this is already a good error message
        throw error;
      }
    }

    throw error;
  }
};

// Verify password reset code
export const verifyResetCode = async (code: string): Promise<string> => {
  try {
    return await verifyPasswordResetCode(getAuth(), code);
  } catch (error) {
    console.error('Error verifying reset code:', error);
    throw error;
  }
};

// Confirm password reset
export const confirmResetPassword = async (code: string, newPassword: string): Promise<boolean> => {
  try {
    await confirmPasswordReset(getAuth(), code, newPassword);
    return true;
  } catch (error) {
    console.error('Error confirming password reset:', error);
    throw error;
  }
};

// Send email verification
export const sendVerificationEmail = async (): Promise<boolean> => {
  try {
    const user = getAuth().currentUser;

    if (!user) {
      throw new Error('No authenticated user found');
    }

    await sendEmailVerification(user);
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
};
