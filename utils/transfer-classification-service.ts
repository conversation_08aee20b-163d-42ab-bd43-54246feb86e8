import { CallType } from '@/models/CallTypes';
import { getEffectiveCallDuration } from '@/utils/call-duration-utils';

export interface TransferContext {
  duration?: string | null;
  callTypes?: number[] | null;
  type?: number | null;
  sessionId?: string | null;
  turnCount?: number;
  conversationLength?: number;
  schedulingAttempts?: boolean;
}

/**
 * Service for classifying transfer types based on call characteristics
 */
export class TransferClassificationService {
  /**
   * Classifies a transfer based on call characteristics
   * @param call Call object with duration and type information
   * @param context Additional context for classification
   * @returns The appropriate transfer call type
   */
  static classifyTransfer(
    call: {
      id: string;
      duration?: string | null;
      type?: number | null;
      callTypes?: number[] | null;
      sessionId?: string | null;
    },
    context?: TransferContext,
  ): CallType {
    // Check for immediate transfer (≤35 seconds)
    if (this.shouldBeImmediateTransfer(call)) {
      return CallType.IMMEDIATE_TRANSFER;
    }

    // Check for scheduling-related transfer
    if (this.hasSchedulingAttempts(call)) {
      return CallType.TRANSFER_DUE_TO_SCHEDULING;
    }

    // Check for multiple assistance attempts
    if (this.hasMultipleAssistanceAttempts(call, context)) {
      return CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST;
    }

    // Default to generic transfer for backward compatibility
    return CallType.TRANSFER_TO_HUMAN;
  }

  /**
   * Determines if a call should be classified as immediate transfer
   * @param call Call object
   * @returns True if call duration is ≤35 seconds
   */
  private static shouldBeImmediateTransfer(call: { duration?: string | null }): boolean {
    const duration = getEffectiveCallDuration(call);
    return duration !== null && duration <= 35;
  }

  /**
   * Determines if call had scheduling attempts
   * @param call Call object
   * @returns True if call types include scheduling-related types
   */
  private static hasSchedulingAttempts(call: {
    callTypes?: number[] | null;
    type?: number | null;
  }): boolean {
    const schedulingTypes = [
      CallType.NEW_PATIENT_NEW_APPOINTMENT,
      CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
      CallType.RESCHEDULE,
      CallType.CANCELLATION,
      CallType.CONFIRM_APPOINTMENT,
    ];

    // Check if any scheduling types exist in the call history
    if (call.callTypes && Array.isArray(call.callTypes)) {
      return call.callTypes.some(type => schedulingTypes.includes(type));
    }

    // Fallback to current type
    if (call.type !== undefined && call.type !== null) {
      return schedulingTypes.includes(call.type);
    }

    return false;
  }

  /**
   * Determines if call had multiple assistance attempts
   * @param call Call object
   * @param context Additional context
   * @returns True if call shows signs of multiple assistance attempts
   */
  private static hasMultipleAssistanceAttempts(
    call: {
      callTypes?: number[] | null;
      duration?: string | null;
    },
    context?: TransferContext,
  ): boolean {
    // Check for multiple different call types (indicating complexity)
    if (call.callTypes && Array.isArray(call.callTypes) && call.callTypes.length >= 2) {
      // Look for patterns that suggest multiple assistance attempts
      const uniqueCategories = new Set();
      const assistanceTypes = [
        CallType.LOOKUP,
        CallType.GENERAL_INFO,
        CallType.NEW_PATIENT_NEW_APPOINTMENT,
        CallType.NEW_APPOINTMENT_EXISTING_PATIENT,
        CallType.RESCHEDULE,
        CallType.CANCELLATION,
        CallType.CONFIRM_APPOINTMENT,
      ];

      call.callTypes.forEach(type => {
        if (assistanceTypes.includes(type)) {
          uniqueCategories.add(type);
        }
      });

      // Multiple different assistance types suggest complexity
      if (uniqueCategories.size >= 2) {
        return true;
      }
    }

    // Use context information if available
    if (context?.turnCount !== undefined && context.turnCount >= 3) {
      return true;
    }

    // Longer calls without scheduling attempts might indicate inability to assist
    const duration = getEffectiveCallDuration(call);
    if (duration !== null && duration > 120 && !this.hasSchedulingAttempts(call)) {
      return true;
    }

    return false;
  }

  /**
   * Gets a user-friendly description of why a specific transfer type was chosen
   * @param transferType The classified transfer type
   * @param call Call object used for classification
   * @returns Description of the classification reasoning
   */
  static getClassificationReason(
    transferType: CallType,
    call: {
      duration?: string | null;
      callTypes?: number[] | null;
    },
  ): string {
    const duration = getEffectiveCallDuration(call);
    const durationText = duration ? `${duration}s` : 'unknown duration';

    switch (transferType) {
      case CallType.IMMEDIATE_TRANSFER:
        return `Short call (${durationText}) transferred quickly to human`;

      case CallType.TRANSFER_DUE_TO_SCHEDULING:
        return `Call included scheduling attempts before transfer (${durationText})`;

      case CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST:
        return `Complex call with multiple assistance attempts before transfer (${durationText})`;

      case CallType.TRANSFER_TO_HUMAN:
        return `General transfer to human (${durationText})`;

      default:
        return `Transfer classification not recognized`;
    }
  }

  /**
   * Validates if a transfer classification makes sense
   * @param transferType The proposed transfer type
   * @param call Call object
   * @returns Validation result with any issues
   */
  static validateClassification(
    transferType: CallType,
    call: {
      duration?: string | null;
      callTypes?: number[] | null;
    },
  ): {
    isValid: boolean;
    issues: string[];
    suggestions: string[];
  } {
    const issues: string[] = [];
    const suggestions: string[] = [];

    const duration = getEffectiveCallDuration(call);
    const hasScheduling = this.hasSchedulingAttempts(call);

    // Validate immediate transfer
    if (transferType === CallType.IMMEDIATE_TRANSFER) {
      if (duration !== null && duration > 35) {
        issues.push(`Call duration (${duration}s) exceeds threshold for immediate transfer (35s)`);
        suggestions.push('Consider TRANSFER_DUE_TO_SCHEDULING or TRANSFER_DUE_TO_UNABLE_TO_ASSIST');
      }
    }

    // Validate scheduling transfer
    if (transferType === CallType.TRANSFER_DUE_TO_SCHEDULING) {
      if (!hasScheduling) {
        issues.push('No scheduling attempts detected in call history');
        suggestions.push('Consider IMMEDIATE_TRANSFER or TRANSFER_DUE_TO_UNABLE_TO_ASSIST');
      }
    }

    // Validate unable to assist transfer
    if (transferType === CallType.TRANSFER_DUE_TO_UNABLE_TO_ASSIST) {
      if (duration !== null && duration <= 35) {
        issues.push(`Call duration (${duration}s) too short for multiple assistance attempts`);
        suggestions.push('Consider IMMEDIATE_TRANSFER');
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      suggestions,
    };
  }
}
