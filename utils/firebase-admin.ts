import admin from 'firebase-admin';
import { NextApiRequest } from 'next';
import { User, UserRole } from '@/models/auth';
import { Location } from '@/models/Location';

let isInitialized = true;

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  isInitialized = false;
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      }),
    });
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
  }
}

// Get Firestore instance
const db = admin.firestore();
if (!isInitialized) {
  console.log('Setting Firestore settings');
  db.settings({
    ignoreUndefinedProperties: true,
  });
}

// Helper function to extract token from request
export const getTokenFromRequest = (req: NextApiRequest): string | null => {
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Also check cookies as fallback
  if (req.cookies?.auth_token) {
    return req.cookies.auth_token;
  }

  return null;
};

// Verify Firebase ID token and return the decoded token
export const verifyFirebaseIdToken = async (token: string) => {
  try {
    const decodedToken = await admin.auth().verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying Firebase ID token:', error);
    return null;
  }
};

export const decodeTokenPayloadFromRequest = async (
  req: NextApiRequest,
): Promise<Record<string, unknown> | null> => {
  const token = getTokenFromRequest(req);
  if (!token) {
    return null;
  }

  return await verifyFirebaseIdToken(token);
};

// Get user data from Firestore based on Firebase UID
export const getUserFromFirestore = async (uid: string): Promise<User | null> => {
  try {
    const userDoc = await admin.firestore().collection('users').doc(uid).get();

    if (!userDoc.exists) {
      return null;
    }

    const userData = userDoc.data();

    // Ensure proper type conversion
    return {
      id: userDoc.id,
      email: userData?.email || '',
      phone: userData?.phone || undefined,
      name: userData?.name || '',
      role: userData?.role || UserRole.STAFF,
      specialty: userData?.specialty || undefined,
      clinicId: userData?.clinicId ? Number(userData.clinicId) : null,
      profilePicture: userData?.profilePicture || undefined,
      canTakeAppointments: userData?.canTakeAppointments || false,
      locationIds: userData?.locationIds || [],
      currentLocationId: userData?.currentLocationId || undefined,
      practiceIds: userData?.practiceIds || undefined,
      createdAt: userData?.createdAt?.toDate() || new Date(),
      updatedAt: userData?.updatedAt?.toDate() || new Date(),
    };
  } catch (error) {
    console.error('Error fetching user data:', error);
    return null;
  }
};

// Auth middleware helper
export const verifyAuthAndGetUser = async (req: NextApiRequest): Promise<User | null> => {
  const token = getTokenFromRequest(req);

  if (!token) {
    return null;
  }

  const decodedToken = await verifyFirebaseIdToken(token);

  if (!decodedToken) {
    return null;
  }

  return await getUserFromFirestore(decodedToken.uid);
};

/**
 * Enhanced auth middleware that includes location context
 */
export const verifyAuthAndGetUserWithLocationContext = async (
  req: NextApiRequest,
): Promise<{
  user: User;
  currentLocation?: Location;
  availableLocations: Location[];
} | null> => {
  const user = await verifyAuthAndGetUser(req);
  if (!user) {
    return null;
  }

  try {
    const locationContext = await getUserLocationContext(user);
    return {
      user,
      currentLocation: locationContext.currentLocation,
      availableLocations: locationContext.availableLocations,
    };
  } catch (error) {
    console.error('Error getting user location context:', error);
    // Return user without location context if there's an error
    return {
      user,
      availableLocations: [],
    };
  }
};

/**
 * Get user's location context (current and available locations)
 */
export const getUserLocationContext = async (
  user: User,
): Promise<{
  currentLocation?: Location;
  availableLocations: Location[];
}> => {
  const db = admin.firestore();
  const locationsCollection = db.collection('locations');

  let currentLocation: Location | undefined;
  const availableLocations: Location[] = [];

  // Get user's available locations
  if (user.locationIds && user.locationIds.length > 0) {
    const locationDocs = await Promise.all(
      user.locationIds.map(locationId => locationsCollection.doc(locationId).get()),
    );

    for (const locationDoc of locationDocs) {
      if (locationDoc.exists) {
        const locationData = locationDoc.data();
        if (locationData && locationData.clinicId === user.clinicId) {
          const location: Location = {
            id: locationDoc.id,
            clinicId: locationData.clinicId || 0,
            practiceId: locationData.practiceId || '',
            name: locationData.name || '',
            address: locationData.address || '',
            phone: locationData.phone || undefined,
            timeZone: locationData.timeZone || 'America/New_York',
            isActive: locationData.isActive ?? true,
            practiceName: locationData.practiceName || '',
            officeHours: locationData.officeHours || undefined,
            createdAt: locationData.createdAt?.toDate() || new Date(),
            updatedAt: locationData.updatedAt?.toDate() || new Date(),
          };

          availableLocations.push(location);

          // Set current location if it matches
          if (user.currentLocationId === location.id) {
            currentLocation = location;
          }
        }
      }
    }
  }

  return {
    currentLocation,
    availableLocations,
  };
};

/**
 * Check if user has access to a specific location
 */
export const userHasLocationAccess = (user: User, locationId: string): boolean => {
  return user.locationIds?.includes(locationId) || false;
};

/**
 * Check if user has admin permissions for location management
 */
export const userCanManageLocations = (user: User): boolean => {
  return user.role === UserRole.CLINIC_ADMIN || user.role === UserRole.SUPER_ADMIN;
};

/**
 * Get location-scoped query constraints for Firestore
 */
export const getLocationScopedConstraints = (
  user: User,
  options: {
    requireCurrentLocation?: boolean;
    allowAllLocations?: boolean;
  } = {},
): Array<[string, admin.firestore.WhereFilterOp, unknown]> => {
  const constraints: Array<[string, admin.firestore.WhereFilterOp, unknown]> = [];

  // Always scope to user's clinic
  if (user.clinicId) {
    constraints.push(['clinicId', '==', user.clinicId]);
  }

  // Add location constraints based on options
  if (options.requireCurrentLocation && user.currentLocationId) {
    constraints.push(['locationId', '==', user.currentLocationId]);
  } else if (!options.allowAllLocations && user.locationIds && user.locationIds.length > 0) {
    constraints.push(['locationId', 'in', user.locationIds]);
  }

  return constraints;
};

/**
 * Apply location-scoped filtering to a Firestore query
 */
export const applyLocationScopedQuery = (
  query: admin.firestore.Query,
  user: User,
  options: {
    requireCurrentLocation?: boolean;
    allowAllLocations?: boolean;
  } = {},
): admin.firestore.Query => {
  const constraints = getLocationScopedConstraints(user, options);

  let scopedQuery = query;
  for (const constraint of constraints) {
    const [field, operator, value] = constraint;
    scopedQuery = scopedQuery.where(field, operator, value);
  }

  return scopedQuery;
};

/**
 * Validate that a location belongs to the user's clinic and they have access
 */
export const validateLocationAccess = async (
  user: User,
  locationId: string,
): Promise<{
  isValid: boolean;
  location?: Location;
  error?: string;
}> => {
  try {
    // Check if user has access to this location
    if (!userHasLocationAccess(user, locationId)) {
      return {
        isValid: false,
        error: 'User does not have access to this location',
      };
    }

    // Get location details
    const locationDoc = await admin.firestore().collection('locations').doc(locationId).get();

    if (!locationDoc.exists) {
      return {
        isValid: false,
        error: 'Location not found',
      };
    }

    const locationData = locationDoc.data();
    if (!locationData) {
      return {
        isValid: false,
        error: 'Location data not found',
      };
    }

    // Validate clinic scope
    if (locationData.clinicId !== user.clinicId) {
      return {
        isValid: false,
        error: 'Location does not belong to your clinic',
      };
    }

    const location: Location = {
      id: locationDoc.id,
      clinicId: locationData.clinicId || 0,
      practiceId: locationData.practiceId || '',
      name: locationData.name || '',
      address: locationData.address || '',
      phone: locationData.phone || undefined,
      timeZone: locationData.timeZone || 'America/New_York',
      isActive: locationData.isActive ?? true,
      practiceName: locationData.practiceName || '',
      officeHours: locationData.officeHours || undefined,
      createdAt: locationData.createdAt?.toDate() || new Date(),
      updatedAt: locationData.updatedAt?.toDate() || new Date(),
    };

    return {
      isValid: true,
      location,
    };
  } catch (error) {
    console.error('Error validating location access:', error);
    return {
      isValid: false,
      error: 'Failed to validate location access',
    };
  }
};

/**
 * Get user's practice IDs derived from their locations
 */
export const getUserPracticeIds = async (user: User): Promise<string[]> => {
  if (!user.locationIds || user.locationIds.length === 0) {
    return [];
  }

  try {
    const db = admin.firestore();
    const locationDocs = await Promise.all(
      user.locationIds.map(locationId => db.collection('locations').doc(locationId).get()),
    );

    const practiceIds = new Set<string>();
    for (const locationDoc of locationDocs) {
      if (locationDoc.exists) {
        const locationData = locationDoc.data();
        if (locationData?.practiceId && locationData.clinicId === user.clinicId) {
          practiceIds.add(locationData.practiceId);
        }
      }
    }

    return Array.from(practiceIds);
  } catch (error) {
    console.error('Error getting user practice IDs:', error);
    return [];
  }
};

export default admin;
