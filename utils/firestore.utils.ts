import { firestore } from 'firebase-admin';

/**
 * Paginates a Firestore query with cursor-based pagination
 *
 * @param queryRef - The query reference
 * @param limit - The number of items to fetch per page
 * @param startAfterDoc - Optional document to start after (for pagination)
 * @returns Object containing items, isLastPage flag, and lastDoc for pagination
 */
export const paginateQuery = async <T = firestore.DocumentData>(params: {
  queryRef: firestore.Query;
  limit: number;
  startAfterDoc?: firestore.DocumentSnapshot;
  mapper?: (doc: firestore.DocumentSnapshot) => T;
}): Promise<{
  items: T[];
  isLastPage: boolean;
  lastDoc: firestore.DocumentSnapshot | null;
}> => {
  const {
    queryRef,
    limit,
    startAfterDoc,
    mapper = doc => ({ id: doc.id, ...doc.data() }) as T,
  } = params;
  // Request one more item than needed to determine if there are more pages
  const actualLimit = limit + 1;

  // Add limit to the query
  let paginatedQuery = queryRef.limit(actualLimit);

  // If a startAfter document is provided, add it to the query
  if (startAfterDoc) {
    paginatedQuery = paginatedQuery.startAfter(startAfterDoc);
  }

  // Execute the query
  const snapshot = await paginatedQuery.get();
  const items = snapshot.docs.map(mapper);

  const hasMoreItems = items.length === actualLimit;
  const itemsToReturn = hasMoreItems ? items.slice(0, limit) : items;

  // The last document will be used as the cursor for the next query
  const lastDoc = itemsToReturn.length > 0 ? snapshot.docs[itemsToReturn.length - 1] : null;

  return {
    items: itemsToReturn,
    isLastPage: !hasMoreItems,
    lastDoc,
  };
};
