import { NextApiRequest, NextApiResponse } from 'next';

type NextApiHandler = (req: NextApiRequest, res: NextApiResponse) => Promise<void> | void;

/**
 * Middleware to ensure external API endpoints have valid API key
 * @param handler The API route handler
 * @returns A wrapped handler that validates API keys
 */
export function withExternalApiAuth(handler: NextApiHandler): NextApiHandler {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // API keys should be stored in environment variables
    const VALID_API_KEYS = [process.env.EXTERNAL_SERVICE_API_KEY].filter(Boolean); // Filter out undefined values

    // Check for API key authentication
    const apiKey = req.headers['x-api-key'] as string;

    // Validate API key
    if (!apiKey || !VALID_API_KEYS.includes(apiKey)) {
      return res.status(401).json({
        message: 'Unauthorized - Invalid or missing API key',
      });
    }

    // If API key is valid, proceed to the handler
    return handler(req, res);
  };
}
