import { createSwaggerSpec } from 'next-swagger-doc';
import pkg from '../package.json';

/**
 * Interface for OpenAPI path item object
 */
export interface OpenAPIPathItem {
  summary?: string;
  description?: string;
  get?: OpenAPIOperation;
  put?: OpenAPIOperation;
  post?: OpenAPIOperation;
  delete?: OpenAPIOperation;
  options?: OpenAPIOperation;
  head?: OpenAPIOperation;
  patch?: OpenAPIOperation;
  trace?: OpenAPIOperation;
  parameters?: OpenAPIParameter[];
}

/**
 * Interface for OpenAPI operation object
 */
export interface OpenAPIOperation {
  tags?: string[];
  summary?: string;
  description?: string;
  operationId?: string;
  parameters?: OpenAPIParameter[];
  requestBody?: OpenAPIRequestBody;
  responses?: Record<string, OpenAPIResponse>;
  deprecated?: boolean;
  security?: Record<string, string[]>[];
}

/**
 * Interface for OpenAPI parameter object
 */
export interface OpenAPIParameter {
  name: string;
  in: string;
  description?: string;
  required?: boolean;
  schema?: OpenAPISchema;
}

/**
 * Interface for OpenAPI request body object
 */
export interface OpenAPIRequestBody {
  description?: string;
  content: Record<string, { schema: OpenAPISchema }>;
  required?: boolean;
}

/**
 * Interface for OpenAPI response object
 */
export interface OpenAPIResponse {
  description: string;
  content?: Record<string, { schema: OpenAPISchema }>;
}

/**
 * Interface for OpenAPI schema object
 */
export interface OpenAPISchema {
  type?: string;
  format?: string;
  items?: OpenAPISchema;
  properties?: Record<string, OpenAPISchema>;
  required?: string[];
  enum?: (string | number)[];
  oneOf?: OpenAPISchema[];
  anyOf?: OpenAPISchema[];
  allOf?: OpenAPISchema[];
  not?: OpenAPISchema;
  additionalProperties?: boolean | OpenAPISchema;
  description?: string;
  default?: unknown;
  example?: unknown;
  [key: string]: unknown;
}

/**
 * Interface for OpenAPI security scheme object
 */
export interface OpenAPISecurityScheme {
  type: string;
  description?: string;
  name?: string;
  in?: string;
  scheme?: string;
  bearerFormat?: string;
  flows?: Record<string, unknown>;
  openIdConnectUrl?: string;
}

/**
 * Interface for OpenAPI specification
 */
export interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
    description: string;
    license?: {
      name: string;
    };
    contact?: {
      name: string;
      email: string;
    };
  };
  servers?: Array<{
    url: string;
    description: string;
  }>;
  paths?: Record<string, OpenAPIPathItem>;
  components?: {
    schemas?: Record<string, OpenAPISchema>;
    responses?: Record<string, OpenAPIResponse>;
    parameters?: Record<string, OpenAPIParameter>;
    securitySchemes?: Record<string, OpenAPISecurityScheme>;
    [key: string]: Record<string, unknown> | undefined;
  };
  tags?: Array<{
    name: string;
    description: string;
  }>;
  [key: string]: unknown;
}

/**
 * Process and fix potential issues in the OpenAPI spec
 */
const processSpec = (spec: unknown): OpenAPISpec => {
  // Create a deep copy to avoid mutating the original
  const processed = JSON.parse(JSON.stringify(spec)) as OpenAPISpec;

  // Create components section if missing
  if (!processed.components) {
    processed.components = {};
  }

  // Ensure all component sections exist
  ['schemas', 'responses', 'parameters', 'securitySchemes'].forEach(section => {
    if (!processed.components![section]) {
      processed.components![section] = {};
    }
  });

  // Create minimum required schema definitions to prevent reference errors
  if (Object.keys(processed.components!.schemas || {}).length === 0) {
    if (!processed.components!.schemas) {
      processed.components!.schemas = {};
    }

    processed.components!.schemas.Error = {
      type: 'object',
      properties: {
        message: { type: 'string' },
        error: { type: 'string' },
      },
    };

    processed.components!.schemas.Appointment = {
      type: 'object',
      properties: {
        id: { type: 'string' },
        patientId: { type: 'string' },
        providerId: { type: 'string' },
        startTime: { type: 'string', format: 'date-time' },
        endTime: { type: 'string', format: 'date-time' },
      },
    };
  }

  return processed;
};

/**
 * Generate Swagger documentation using next-swagger-doc
 * This approach is more efficient for Next.js projects than using swagger-jsdoc directly
 */
export const getSwaggerSpec = (): OpenAPISpec => {
  // Create a base specification with all required components
  const spec = createSwaggerSpec({
    apiFolder: 'pages/api',
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Front Desk Portal API Documentation',
        version: pkg.version,
        description: `
API documentation for the Front Desk Portal.

## External API

The External API uses a versioned structure:

- Base URL: /api/external-api/v1/...
- All requests require API key authentication
`,
        license: {
          name: 'Private',
        },
        contact: {
          name: 'Support',
          email: '<EMAIL>',
        },
      },
      servers: [
        {
          url: '/',
          description: 'Current Host',
        },
      ],
      components: {
        securitySchemes: {
          ApiKeyAuth: {
            type: 'apiKey',
            in: 'header',
            name: 'x-api-key',
            description: 'API key authentication for external API endpoints',
          },
        },
        // Ensure these sections exist to prevent reference errors
        schemas: {},
        responses: {},
        parameters: {},
      },
      tags: [
        {
          name: 'External API',
          description: 'Entry point for external API information',
        },
        {
          name: 'External API v1',
          description: 'Version 1 of the external API endpoints, including voice records',
        },
        {
          name: 'External API v2',
          description:
            'Version 2 of the external API endpoints, with provider-agnostic integration',
        },
        {
          name: 'Storage',
          description: 'Endpoints for accessing and managing files in cloud storage',
        },
      ],
    },
  });

  // Process and fix any issues in the spec
  return processSpec(spec);
};
