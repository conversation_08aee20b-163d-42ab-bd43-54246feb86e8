import { UserRole } from '@/models/auth';

/**
 * Converts API role values to user-friendly display names
 * @param role The UserRole enum value from the API
 * @returns A user-friendly display name for the role
 */
export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return 'Super Admin';
    case UserRole.CLINIC_ADMIN:
      return 'Clinic Admin';
    case UserRole.STAFF:
      return 'Staff';
    default:
      // Fallback to the original value if not found
      return role;
  }
};

/**
 * Gets a short description of what each role can do
 * @param role The UserRole enum value
 * @returns A brief description of the role's capabilities
 */
export const getRoleDescription = (role: UserRole): string => {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return 'Full system access across all clinics';
    case UserRole.CLINIC_ADMIN:
      return 'Manage staff and settings for their clinic';
    case UserRole.STAFF:
      return 'Handle patient calls and manage records';
    default:
      return 'Standard user privileges';
  }
};
