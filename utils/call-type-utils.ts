import { CallType } from '@/models/CallTypes';

// Frontend helper: normalize raw call.type value (number, string, 'other', array) to CallType or CallType[]
export function normalizeCallType(raw: unknown): CallType | CallType[] | undefined {
  if (raw === undefined || raw === null) return undefined;

  // If array, recursively normalize each element then filter undefined
  if (Array.isArray(raw)) {
    const arr = raw
      .map(v => normalizeCallType(v))
      .filter((v): v is CallType => typeof v === 'number');
    return arr.length ? arr : undefined;
  }

  // If already number
  if (typeof raw === 'number') return raw as CallType;

  // If numeric string
  if (typeof raw === 'string' && /^\d+$/.test(raw)) {
    return Number(raw) as CallType;
  }

  // If string enum name (case-insensitive)
  if (typeof raw === 'string') {
    const lower = raw.toLowerCase();
    switch (lower) {
      case 'other':
        return CallType.OTHER;
      case 'voicemail':
        return CallType.VOICEMAIL;
      case 'transfer_to_human':
      case 'transfer':
        return CallType.TRANSFER_TO_HUMAN;
      case 'new_patient_new_appointment':
        return CallType.NEW_PATIENT_NEW_APPOINTMENT;
      case 'new_appointment_existing_patient':
        return CallType.NEW_APPOINTMENT_EXISTING_PATIENT;
      case 'reschedule':
        return CallType.RESCHEDULE;
      case 'cancellation':
        return CallType.CANCELLATION;
      case 'voicemail_system_error':
        return CallType.VOICEMAIL_SYSTEM_ERROR;
      case 'general_info':
        return CallType.GENERAL_INFO;
      case 'lookup':
        return CallType.LOOKUP;
      case 'transfer_to_clinic':
        return CallType.TRANSFER_TO_CLINIC;
      default:
        return CallType.OTHER;
    }
  }

  return undefined;
}

/**
 * Smart normalization that handles the case where callTypes array shows [0]
 * but the single type field has a meaningful value (> 0)
 * This fixes display issues with existing data before the endConversationHandler fix
 *
 * Examples:
 * - normalizeCallTypeSmartly([0], 4) → 4 (use meaningful single type)
 * - normalizeCallTypeSmartly([4], 4) → [4] (use callTypes array)
 * - normalizeCallTypeSmartly([4, 6], 4) → [4, 6] (use callTypes array)
 * - normalizeCallTypeSmartly(undefined, 4) → 4 (fallback to single type)
 */
export function normalizeCallTypeSmartly(
  callTypes: unknown,
  singleType: unknown,
): CallType | CallType[] | undefined {
  const normalizedCallTypes = normalizeCallType(callTypes);
  const normalizedSingleType = normalizeCallType(singleType);

  // If we have a callTypes array
  if (Array.isArray(normalizedCallTypes)) {
    // Check if callTypes array only contains [0] (OTHER)
    const hasOnlyOther =
      normalizedCallTypes.length === 1 && normalizedCallTypes[0] === CallType.OTHER;

    // If callTypes is [0] but singleType has a meaningful value (> 0), use singleType
    if (
      hasOnlyOther &&
      typeof normalizedSingleType === 'number' &&
      normalizedSingleType > CallType.OTHER
    ) {
      return normalizedSingleType;
    }

    // Otherwise, use the callTypes array as is
    return normalizedCallTypes;
  }

  // If no callTypes array, fall back to single type
  return normalizedSingleType;
}
